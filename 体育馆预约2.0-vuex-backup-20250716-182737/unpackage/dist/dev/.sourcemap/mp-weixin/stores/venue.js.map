{"version": 3, "file": "venue.js", "sources": ["stores/venue.js"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport * as venueApi from '@/api/venue.js'\nimport * as timeslotApi from '@/api/timeslot.js'\nimport { showError } from '@/utils/ui.js'\n\nexport const useVenueStore = defineStore('venue', {\n  state: () => ({\n    venueList: [],\n    popularVenues: [],\n    venueDetail: null,\n    venueTypes: [],\n    timeSlots: [],\n    searchResults: [],\n    loading: false,\n    pagination: {\n      current: 1,\n      pageSize: 10,\n      total: 0,\n      totalPages: 1\n    }\n  }),\n\n  getters: {\n    // 场馆列表相关 - 这些应该是getter，返回状态值\n    venueListGetter: (state) => state.venueList,\n    popularVenuesGetter: (state) => state.popularVenues,\n    venueDetailGetter: (state) => state.venueDetail,\n    venueTypesGetter: (state) => state.venueTypes,\n    timeSlotsGetter: (state) => state.timeSlots,\n    searchResultsGetter: (state) => state.searchResults,\n    \n    // 状态相关\n    isLoading: (state) => state.loading,\n    getPagination: (state) => state.pagination,\n    \n    // 计算属性\n    totalVenues: (state) => state.venueList.length,\n    hasMoreVenues: (state) => state.pagination.current < state.pagination.totalPages,\n    \n    // 按类型筛选场馆\n    getVenuesByType: (state) => (typeId) => {\n      if (!typeId) return state.venueList\n      return state.venueList.filter(venue => venue.typeId === typeId)\n    },\n    \n    // 获取可用时间段\n    getAvailableTimeSlots: (state) => {\n      return state.timeSlots.filter(slot => slot.status === 'AVAILABLE')\n    }\n  },\n\n  actions: {\n    // 设置加载状态\n    setLoading(loading) {\n      this.loading = loading\n    },\n    \n    // 设置场馆列表\n    setVenueList({ list, pagination }) {\n      this.venueList = list\n      if (pagination) {\n        this.pagination = { ...this.pagination, ...pagination }\n      }\n    },\n    \n    // 追加场馆列表（分页加载）\n    appendVenueList(list) {\n      this.venueList = [...this.venueList, ...list]\n    },\n    \n    // 设置热门场馆\n    setPopularVenues(venues) {\n      this.popularVenues = venues\n    },\n    \n    // 设置场馆详情\n    setVenueDetail(venue) {\n      this.venueDetail = venue\n    },\n    \n    // 设置场馆类型\n    setVenueTypes(types) {\n      this.venueTypes = types\n    },\n    \n    // 设置时间段\n    setTimeSlots(slots) {\n      this.timeSlots = slots\n    },\n    \n    // 设置搜索结果\n    setSearchResults(results) {\n      this.searchResults = results\n    },\n    \n    // 设置分页信息\n    setPagination(pagination) {\n      this.pagination = { ...this.pagination, ...pagination }\n    },\n\n    // 获取场馆列表\n    async getVenueList(params = {}) {\n      try {\n        console.log('[VenueStore] 开始获取场馆列表，参数:', params)\n        this.setLoading(true)\n        \n        // 添加超时处理\n        const timeoutPromise = new Promise((_, reject) => {\n          setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时\n        })\n        \n        const apiPromise = venueApi.getVenueList(params)\n        const response = await Promise.race([apiPromise, timeoutPromise])\n        \n        console.log('[VenueStore] 场馆API响应:', response)\n        \n        // 处理响应数据\n        let list = []\n        let pagination = {\n          current: 1,\n          pageSize: 10,\n          total: 0,\n          totalPages: 1\n        }\n        \n        if (response && response.data) {\n          if (Array.isArray(response.data)) {\n            list = response.data\n            pagination = {\n              current: response.page || params.page || 1,\n              pageSize: response.pageSize || params.pageSize || 10,\n              total: response.total || response.data.length,\n              totalPages: response.totalPages || 1\n            }\n          } else {\n            console.warn('[VenueStore] API响应数据格式异常，使用空数组:', response)\n          }\n        } else if (response && Array.isArray(response)) {\n          // 直接返回数组的情况\n          list = response\n          pagination.total = response.length\n        } else {\n          console.warn('[VenueStore] API响应为空或格式错误，使用空数组:', response)\n        }\n        \n        console.log('[VenueStore] 解析的场馆列表:', list)\n        console.log('[VenueStore] 分页信息:', pagination)\n        \n        if (params.page === 1 || params.refresh) {\n          this.setVenueList({ list, pagination })\n        } else {\n          this.appendVenueList(list)\n          this.setVenueList({ list: this.venueList, pagination })\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取场馆列表失败:', error)\n        showError(error.message || '获取场馆列表失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 获取热门场馆\n    async getPopularVenues() {\n      try {\n        console.log('[VenueStore] 开始获取热门场馆')\n        const response = await venueApi.getPopularVenues()\n        \n        if (response && response.data) {\n          this.setPopularVenues(response.data)\n          console.log('[VenueStore] 热门场馆获取成功:', response.data)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取热门场馆失败:', error)\n        showError(error.message || '获取热门场馆失败')\n        throw error\n      }\n    },\n\n    // 获取场馆详情\n    async getVenueDetail(venueId) {\n      try {\n        console.log('[VenueStore] 开始获取场馆详情:', venueId)\n        this.setLoading(true)\n        \n        const response = await venueApi.getVenueDetail(venueId)\n        \n        if (response && response.data) {\n          this.setVenueDetail(response.data)\n          console.log('[VenueStore] 场馆详情获取成功:', response.data)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取场馆详情失败:', error)\n        showError(error.message || '获取场馆详情失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 获取场馆类型\n    async getVenueTypes() {\n      try {\n        console.log('[VenueStore] 开始获取场馆类型')\n        const response = await venueApi.getVenueTypes()\n        \n        if (response && response.data) {\n          this.setVenueTypes(response.data)\n          console.log('[VenueStore] 场馆类型获取成功:', response.data)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取场馆类型失败:', error)\n        showError(error.message || '获取场馆类型失败')\n        throw error\n      }\n    },\n\n    // 获取时间段\n    async getTimeSlots(venueId, date) {\n      try {\n        console.log('[VenueStore] 开始获取时间段:', { venueId, date })\n        this.setLoading(true)\n        \n        const response = await timeslotApi.getTimeSlots(venueId, date)\n        \n        if (response && response.data) {\n          this.setTimeSlots(response.data)\n          console.log('[VenueStore] 时间段获取成功:', response.data)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取时间段失败:', error)\n        showError(error.message || '获取时间段失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 搜索场馆\n    async searchVenues(keyword, filters = {}) {\n      try {\n        console.log('[VenueStore] 开始搜索场馆:', { keyword, filters })\n        this.setLoading(true)\n        \n        const response = await venueApi.searchVenues(keyword, filters)\n        \n        if (response && response.data) {\n          this.setSearchResults(response.data)\n          console.log('[VenueStore] 场馆搜索成功:', response.data)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 搜索场馆失败:', error)\n        showError(error.message || '搜索场馆失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 清空场馆详情\n    clearVenueDetail() {\n      this.venueDetail = null\n    },\n    \n    // 清空搜索结果\n    clearSearchResults() {\n      this.searchResults = []\n    },\n    \n    // 重置分页\n    resetPagination() {\n      this.pagination = {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        totalPages: 1\n      }\n    }\n  }\n})\n"], "names": ["defineStore", "uni", "venueApi.getVenueList", "showError", "venueApi.getPopularVenues", "venueApi.getVenueDetail", "venueApi.getVenueTypes", "timeslotApi.getTimeSlots", "venueApi.searchVenues"], "mappings": ";;;;;AAKY,MAAC,gBAAgBA,cAAW,YAAC,SAAS;AAAA,EAChD,OAAO,OAAO;AAAA,IACZ,WAAW,CAAE;AAAA,IACb,eAAe,CAAE;AAAA,IACjB,aAAa;AAAA,IACb,YAAY,CAAE;AAAA,IACd,WAAW,CAAE;AAAA,IACb,eAAe,CAAE;AAAA,IACjB,SAAS;AAAA,IACT,YAAY;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACb;AAAA,EACL;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,iBAAiB,CAAC,UAAU,MAAM;AAAA,IAClC,qBAAqB,CAAC,UAAU,MAAM;AAAA,IACtC,mBAAmB,CAAC,UAAU,MAAM;AAAA,IACpC,kBAAkB,CAAC,UAAU,MAAM;AAAA,IACnC,iBAAiB,CAAC,UAAU,MAAM;AAAA,IAClC,qBAAqB,CAAC,UAAU,MAAM;AAAA;AAAA,IAGtC,WAAW,CAAC,UAAU,MAAM;AAAA,IAC5B,eAAe,CAAC,UAAU,MAAM;AAAA;AAAA,IAGhC,aAAa,CAAC,UAAU,MAAM,UAAU;AAAA,IACxC,eAAe,CAAC,UAAU,MAAM,WAAW,UAAU,MAAM,WAAW;AAAA;AAAA,IAGtE,iBAAiB,CAAC,UAAU,CAAC,WAAW;AACtC,UAAI,CAAC;AAAQ,eAAO,MAAM;AAC1B,aAAO,MAAM,UAAU,OAAO,WAAS,MAAM,WAAW,MAAM;AAAA,IAC/D;AAAA;AAAA,IAGD,uBAAuB,CAAC,UAAU;AAChC,aAAO,MAAM,UAAU,OAAO,UAAQ,KAAK,WAAW,WAAW;AAAA,IAClE;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,WAAW,SAAS;AAClB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,aAAa,EAAE,MAAM,cAAc;AACjC,WAAK,YAAY;AACjB,UAAI,YAAY;AACd,aAAK,aAAa,EAAE,GAAG,KAAK,YAAY,GAAG,WAAY;AAAA,MACxD;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,MAAM;AACpB,WAAK,YAAY,CAAC,GAAG,KAAK,WAAW,GAAG,IAAI;AAAA,IAC7C;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,WAAK,aAAa;AAAA,IACnB;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,WAAK,YAAY;AAAA,IAClB;AAAA;AAAA,IAGD,iBAAiB,SAAS;AACxB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc,YAAY;AACxB,WAAK,aAAa,EAAE,GAAG,KAAK,YAAY,GAAG,WAAY;AAAA,IACxD;AAAA;AAAA,IAGD,MAAM,aAAa,SAAS,IAAI;AAC9B,UAAI;AACFC,sBAAAA,MAAY,MAAA,OAAA,0BAAA,6BAA6B,MAAM;AAC/C,aAAK,WAAW,IAAI;AAGpB,cAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,qBAAW,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,GAAG,GAAK;AAAA,QAC3D,CAAS;AAED,cAAM,aAAaC,UAAqB,aAAC,MAAM;AAC/C,cAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,YAAY,cAAc,CAAC;AAEhED,sBAAAA,MAAY,MAAA,OAAA,0BAAA,yBAAyB,QAAQ;AAG7C,YAAI,OAAO,CAAE;AACb,YAAI,aAAa;AAAA,UACf,SAAS;AAAA,UACT,UAAU;AAAA,UACV,OAAO;AAAA,UACP,YAAY;AAAA,QACb;AAED,YAAI,YAAY,SAAS,MAAM;AAC7B,cAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AAChC,mBAAO,SAAS;AAChB,yBAAa;AAAA,cACX,SAAS,SAAS,QAAQ,OAAO,QAAQ;AAAA,cACzC,UAAU,SAAS,YAAY,OAAO,YAAY;AAAA,cAClD,OAAO,SAAS,SAAS,SAAS,KAAK;AAAA,cACvC,YAAY,SAAS,cAAc;AAAA,YACpC;AAAA,UACb,OAAiB;AACLA,0BAAAA,MAAA,MAAA,QAAA,0BAAa,mCAAmC,QAAQ;AAAA,UACzD;AAAA,QACF,WAAU,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAE9C,iBAAO;AACP,qBAAW,QAAQ,SAAS;AAAA,QACtC,OAAe;AACLA,wBAAAA,MAAA,MAAA,QAAA,0BAAa,oCAAoC,QAAQ;AAAA,QAC1D;AAEDA,sBAAAA,MAAA,MAAA,OAAA,0BAAY,yBAAyB,IAAI;AACzCA,sBAAAA,MAAY,MAAA,OAAA,0BAAA,sBAAsB,UAAU;AAE5C,YAAI,OAAO,SAAS,KAAK,OAAO,SAAS;AACvC,eAAK,aAAa,EAAE,MAAM,WAAU,CAAE;AAAA,QAChD,OAAe;AACL,eAAK,gBAAgB,IAAI;AACzB,eAAK,aAAa,EAAE,MAAM,KAAK,WAAW,YAAY;AAAA,QACvD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,mBAAmB;AACvB,UAAI;AACFF,sBAAAA,MAAA,MAAA,OAAA,0BAAY,uBAAuB;AACnC,cAAM,WAAW,MAAMG,2BAA2B;AAElD,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,iBAAiB,SAAS,IAAI;AACnCH,wBAAY,MAAA,MAAA,OAAA,0BAAA,0BAA0B,SAAS,IAAI;AAAA,QACpD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe,SAAS;AAC5B,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,0BAAA,0BAA0B,OAAO;AAC7C,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMI,UAAuB,eAAC,OAAO;AAEtD,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,eAAe,SAAS,IAAI;AACjCJ,wBAAY,MAAA,MAAA,OAAA,0BAAA,0BAA0B,SAAS,IAAI;AAAA,QACpD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AACFF,sBAAAA,MAAA,MAAA,OAAA,0BAAY,uBAAuB;AACnC,cAAM,WAAW,MAAMK,wBAAwB;AAE/C,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,cAAc,SAAS,IAAI;AAChCL,wBAAY,MAAA,MAAA,OAAA,0BAAA,0BAA0B,SAAS,IAAI;AAAA,QACpD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,aAAa,SAAS,MAAM;AAChC,UAAI;AACFF,sBAAY,MAAA,MAAA,OAAA,0BAAA,yBAAyB,EAAE,SAAS,MAAM;AACtD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMM,SAAyB,SAAS,IAAI;AAE7D,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,aAAa,SAAS,IAAI;AAC/BN,wBAAY,MAAA,MAAA,OAAA,0BAAA,yBAAyB,SAAS,IAAI;AAAA,QACnD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,yBAAyB,KAAK;AAC5CE,2BAAU,MAAM,WAAW,SAAS;AACpC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,aAAa,SAAS,UAAU,IAAI;AACxC,UAAI;AACFF,sBAAA,MAAA,MAAA,OAAA,0BAAY,wBAAwB,EAAE,SAAS,SAAS;AACxD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMO,uBAAsB,SAAS,OAAO;AAE7D,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,iBAAiB,SAAS,IAAI;AACnCP,wBAAA,MAAA,MAAA,OAAA,0BAAY,wBAAwB,SAAS,IAAI;AAAA,QAClD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,wBAAwB,KAAK;AAC3CE,2BAAU,MAAM,WAAW,QAAQ;AACnC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,gBAAgB,CAAE;AAAA,IACxB;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,aAAa;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,YAAY;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}