{"version": 3, "file": "vuex-sync.js", "sources": ["stores/plugins/vuex-sync.js"], "sourcesContent": ["import store from '@/store'\n\n// 同步状态标志，防止无限递归\nlet syncInProgress = false\n\n// Pinia插件：将Pinia状态同步到Vuex\nexport function vuexSyncPlugin() {\n  return ({ store: piniaStore }) => {\n    // App store同步\n    if (piniaStore.$id === 'app') {\n      console.log('[Pinia-Vuex Sync] 初始化app store同步')\n\n      // 初始同步Vuex到Pinia\n      piniaStore.$state.loading = store.state.loading\n      piniaStore.$state.networkStatus = store.state.networkStatus\n\n      // 监听Pinia变化，同步到Vuex\n      piniaStore.$subscribe((mutation, state) => {\n        if (syncInProgress) return // 防止递归同步\n\n        console.log('[Pinia->Vuex] App同步状态变化:', mutation.type, mutation.payload)\n\n        // 简化同步逻辑：对于任何Pinia状态变化，都同步到Vuex\n        // 这样可以避免复杂的事件检测，确保状态一致性\n        try {\n          syncInProgress = true\n          store.dispatch('setLoading', state.loading)\n          store.dispatch('setNetworkStatus', state.networkStatus)\n          console.log('[Pinia->Vuex] App状态已同步:', { loading: state.loading, networkStatus: state.networkStatus })\n        } catch (error) {\n          console.error('[Pinia->Vuex] App同步失败:', error)\n        } finally {\n          syncInProgress = false\n        }\n      })\n\n      // 监听Vuex变化，同步到Pinia\n      store.subscribe((mutation, state) => {\n        // 只处理App相关的mutations，不处理其他模块\n        if (mutation.type === 'SET_LOADING') {\n          console.log('[Vuex->Pinia] App同步状态变化:', mutation.type)\n          piniaStore.loading = state.loading\n        }\n        if (mutation.type === 'SET_NETWORK_STATUS') {\n          console.log('[Vuex->Pinia] App同步状态变化:', mutation.type)\n          piniaStore.networkStatus = state.networkStatus\n        }\n        // 不处理venue/开头的mutations，那些由venue store处理\n      })\n    }\n\n    // User store同步\n    if (piniaStore.$id === 'user') {\n      console.log('[Pinia-Vuex Sync] 初始化user store同步')\n\n      // 初始同步Vuex到Pinia\n      const vuexUserState = store.state.user\n      if (vuexUserState) {\n        piniaStore.$state.token = vuexUserState.token\n        piniaStore.$state.userInfo = vuexUserState.userInfo\n        piniaStore.$state.userStats = vuexUserState.userStats\n        piniaStore.$state.isLoggedIn = vuexUserState.isLoggedIn\n        piniaStore.$state.loginChecking = vuexUserState.loginChecking\n      }\n\n      // 监听Pinia变化，同步到Vuex\n      piniaStore.$subscribe((mutation, state) => {\n        if (syncInProgress) return // 防止递归同步\n\n        console.log('[Pinia->Vuex] User同步状态变化:', mutation.type, mutation.payload)\n\n        // 简化同步逻辑：对于任何Pinia状态变化，都同步到Vuex\n        // 使用mutations而不是actions，因为这些actions在Vuex中不存在\n        try {\n          syncInProgress = true\n          store.commit('user/SET_TOKEN', state.token)\n          store.commit('user/SET_USER_INFO', state.userInfo)\n          store.commit('user/SET_LOGIN_STATUS', state.isLoggedIn)\n          store.commit('user/SET_LOGIN_CHECKING', state.loginChecking)\n          store.commit('user/SET_USER_STATS', state.userStats)\n          console.log('[Pinia->Vuex] User状态已同步')\n        } catch (error) {\n          console.error('[Pinia->Vuex] User同步失败:', error)\n        } finally {\n          syncInProgress = false\n        }\n      })\n\n      // 监听Vuex变化，同步到Pinia\n      store.subscribe((mutation, state) => {\n        if (mutation.type.startsWith('user/')) {\n          console.log('[Vuex->Pinia] User同步状态变化:', mutation.type)\n\n          const userState = state.user\n          if (mutation.type === 'user/SET_TOKEN') {\n            piniaStore.token = userState.token\n          }\n          if (mutation.type === 'user/SET_USER_INFO') {\n            piniaStore.userInfo = userState.userInfo\n          }\n          if (mutation.type === 'user/SET_LOGIN_STATUS') {\n            piniaStore.isLoggedIn = userState.isLoggedIn\n          }\n          if (mutation.type === 'user/SET_LOGIN_CHECKING') {\n            piniaStore.loginChecking = userState.loginChecking\n          }\n          if (mutation.type === 'user/SET_USER_STATS') {\n            piniaStore.userStats = userState.userStats\n          }\n          if (mutation.type === 'user/CLEAR_USER_DATA') {\n            piniaStore.token = ''\n            piniaStore.userInfo = null\n            piniaStore.userStats = { totalBookings: 0, totalSharings: 0 }\n            piniaStore.isLoggedIn = false\n            piniaStore.loginChecking = false\n          }\n        }\n      })\n    }\n\n    // Venue store同步\n    if (piniaStore.$id === 'venue') {\n      console.log('[Pinia-Vuex Sync] 初始化venue store同步')\n\n      // 初始同步Vuex到Pinia\n      const vuexVenueState = store.state.venue\n      if (vuexVenueState) {\n        piniaStore.$state.venueList = vuexVenueState.venueList\n        piniaStore.$state.popularVenues = vuexVenueState.popularVenues\n        piniaStore.$state.venueDetail = vuexVenueState.venueDetail\n        piniaStore.$state.venueTypes = vuexVenueState.venueTypes\n        piniaStore.$state.timeSlots = vuexVenueState.timeSlots\n        piniaStore.$state.searchResults = vuexVenueState.searchResults\n        piniaStore.$state.loading = vuexVenueState.loading\n        piniaStore.$state.pagination = vuexVenueState.pagination\n      }\n\n      // 监听Pinia变化，同步到Vuex\n      piniaStore.$subscribe((mutation, state) => {\n        if (syncInProgress) return // 防止递归同步\n\n        console.log('[Pinia->Vuex] Venue同步状态变化:', mutation.type)\n\n        // 优化同步逻辑：只同步必要的状态，减少性能开销\n        try {\n          syncInProgress = true\n\n          // 批量同步，减少单独的commit调用\n          const venueUpdates = {\n            venueList: state.venueList,\n            popularVenues: state.popularVenues,\n            venueDetail: state.venueDetail,\n            venueTypes: state.venueTypes,\n            timeSlots: state.timeSlots,\n            searchResults: state.searchResults,\n            loading: state.loading,\n            pagination: state.pagination\n          }\n\n          // 使用单次commit进行批量更新\n          store.commit('venue/BATCH_UPDATE', venueUpdates)\n\n          console.log('[Pinia->Vuex] Venue状态已批量同步:', Object.keys(venueUpdates))\n        } catch (error) {\n          // 如果批量更新失败，回退到单独更新\n          console.warn('[Pinia->Vuex] 批量同步失败，使用单独同步:', error)\n          try {\n            store.commit('venue/SET_VENUE_LIST', { list: state.venueList, pagination: state.pagination })\n            store.commit('venue/SET_POPULAR_VENUES', state.popularVenues)\n            store.commit('venue/SET_VENUE_DETAIL', state.venueDetail)\n            store.commit('venue/SET_VENUE_TYPES', state.venueTypes)\n            store.commit('venue/SET_TIME_SLOTS', state.timeSlots)\n            store.commit('venue/SET_SEARCH_RESULTS', state.searchResults)\n            store.commit('venue/SET_LOADING', state.loading)\n            console.log('[Pinia->Vuex] Venue状态已单独同步')\n          } catch (fallbackError) {\n            console.error('[Pinia->Vuex] Venue同步完全失败:', fallbackError)\n          }\n        } finally {\n          syncInProgress = false\n        }\n      })\n\n      // 监听Vuex变化，同步到Pinia\n      store.subscribe((mutation, state) => {\n        if (syncInProgress) return // 防止递归同步\n\n        if (mutation.type.startsWith('venue/')) {\n          console.log('[Vuex->Pinia] Venue同步状态变化:', mutation.type)\n\n          try {\n            syncInProgress = true\n            const venueState = state.venue\n\n            // 根据具体的mutation类型进行精确同步\n            switch (mutation.type) {\n              case 'venue/SET_VENUE_LIST':\n                piniaStore.venueList = venueState.venueList\n                piniaStore.pagination = venueState.pagination\n                break\n              case 'venue/APPEND_VENUE_LIST':\n                piniaStore.venueList = venueState.venueList\n                break\n              case 'venue/SET_POPULAR_VENUES':\n                piniaStore.popularVenues = venueState.popularVenues\n                break\n              case 'venue/SET_VENUE_DETAIL':\n                piniaStore.venueDetail = venueState.venueDetail\n                break\n              case 'venue/SET_VENUE_TYPES':\n                piniaStore.venueTypes = venueState.venueTypes\n                break\n              case 'venue/SET_TIME_SLOTS':\n                piniaStore.timeSlots = venueState.timeSlots\n                break\n              case 'venue/SET_SEARCH_RESULTS':\n                piniaStore.searchResults = venueState.searchResults\n                break\n              case 'venue/SET_LOADING':\n                piniaStore.loading = venueState.loading\n                break\n              case 'venue/BATCH_UPDATE':\n                // 批量更新时同步所有状态\n                piniaStore.venueList = venueState.venueList\n                piniaStore.popularVenues = venueState.popularVenues\n                piniaStore.venueDetail = venueState.venueDetail\n                piniaStore.venueTypes = venueState.venueTypes\n                piniaStore.timeSlots = venueState.timeSlots\n                piniaStore.searchResults = venueState.searchResults\n                piniaStore.loading = venueState.loading\n                piniaStore.pagination = venueState.pagination\n                console.log('[Vuex->Pinia] Venue批量同步完成')\n                break\n              default:\n                // 其他未知的venue mutations，进行全量同步\n                piniaStore.venueList = venueState.venueList\n                piniaStore.popularVenues = venueState.popularVenues\n                piniaStore.venueDetail = venueState.venueDetail\n                piniaStore.venueTypes = venueState.venueTypes\n                piniaStore.timeSlots = venueState.timeSlots\n                piniaStore.searchResults = venueState.searchResults\n                piniaStore.loading = venueState.loading\n                piniaStore.pagination = venueState.pagination\n                break\n            }\n          } finally {\n            syncInProgress = false\n          }\n        }\n      })\n    }\n\n    // Sharing store同步\n    if (piniaStore.$id === 'sharing') {\n      console.log('[Pinia-Vuex Sync] 初始化sharing store同步')\n\n      // 初始同步Vuex到Pinia\n      const vuexSharingState = store.state.sharing\n      if (vuexSharingState) {\n        piniaStore.$state.sharingOrders = vuexSharingState.sharingOrders || []\n        piniaStore.$state.mySharingOrders = vuexSharingState.mySharingOrders || []\n        piniaStore.$state.receivedRequests = vuexSharingState.receivedRequests || []\n        piniaStore.$state.sentRequests = vuexSharingState.sentRequests || []\n        piniaStore.$state.sharingOrderDetail = vuexSharingState.sharingOrderDetail\n        piniaStore.$state.loading = vuexSharingState.loading || false\n        piniaStore.$state.pagination = vuexSharingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1 }\n      }\n\n      // 监听Pinia变化，同步到Vuex\n      piniaStore.$subscribe((mutation, state) => {\n        if (syncInProgress) return // 防止递归同步\n\n        console.log('[Pinia->Vuex] Sharing同步状态变化:', mutation.type)\n\n        // 批量同步到Vuex\n        try {\n          syncInProgress = true\n\n          const sharingUpdates = {\n            sharingOrders: state.sharingOrders,\n            mySharingOrders: state.mySharingOrders,\n            receivedRequests: state.receivedRequests,\n            sentRequests: state.sentRequests,\n            sharingOrderDetail: state.sharingOrderDetail,\n            loading: state.loading,\n            pagination: state.pagination\n          }\n\n          // 尝试批量更新\n          store.commit('sharing/BATCH_UPDATE', sharingUpdates)\n          console.log('[Pinia->Vuex] Sharing状态已批量同步:', Object.keys(sharingUpdates))\n        } catch (error) {\n          // 如果批量更新失败，回退到单独更新\n          console.warn('[Pinia->Vuex] Sharing批量同步失败，使用单独同步:', error)\n          try {\n            store.commit('sharing/SET_SHARING_ORDERS', state.sharingOrders)\n            store.commit('sharing/SET_MY_SHARING_ORDERS', state.mySharingOrders)\n            store.commit('sharing/SET_RECEIVED_REQUESTS', state.receivedRequests)\n            store.commit('sharing/SET_SENT_REQUESTS', state.sentRequests)\n            store.commit('sharing/SET_SHARING_ORDER_DETAIL', state.sharingOrderDetail)\n            store.commit('sharing/SET_LOADING', state.loading)\n            store.commit('sharing/SET_PAGINATION', state.pagination)\n            console.log('[Pinia->Vuex] Sharing状态已单独同步')\n          } catch (fallbackError) {\n            console.error('[Pinia->Vuex] Sharing同步完全失败:', fallbackError)\n          }\n        } finally {\n          syncInProgress = false\n        }\n      })\n\n      // 监听Vuex变化，同步到Pinia\n      store.subscribe((mutation, state) => {\n        if (syncInProgress) return // 防止递归同步\n\n        if (mutation.type.startsWith('sharing/')) {\n          console.log('[Vuex->Pinia] Sharing同步状态变化:', mutation.type)\n\n          try {\n            syncInProgress = true\n            const sharingState = state.sharing\n\n            // 根据具体的mutation类型进行精确同步\n            switch (mutation.type) {\n              case 'sharing/SET_SHARING_ORDERS':\n                piniaStore.sharingOrders = sharingState.sharingOrders\n                break\n              case 'sharing/SET_MY_SHARING_ORDERS':\n                piniaStore.mySharingOrders = sharingState.mySharingOrders\n                break\n              case 'sharing/SET_RECEIVED_REQUESTS':\n                piniaStore.receivedRequests = sharingState.receivedRequests\n                break\n              case 'sharing/SET_SENT_REQUESTS':\n                piniaStore.sentRequests = sharingState.sentRequests\n                break\n              case 'sharing/SET_SHARING_ORDER_DETAIL':\n                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail\n                break\n              case 'sharing/SET_LOADING':\n                piniaStore.loading = sharingState.loading\n                break\n              case 'sharing/SET_PAGINATION':\n                piniaStore.pagination = sharingState.pagination\n                break\n              case 'sharing/BATCH_UPDATE':\n                // 批量更新时同步所有状态\n                piniaStore.sharingOrders = sharingState.sharingOrders\n                piniaStore.mySharingOrders = sharingState.mySharingOrders\n                piniaStore.receivedRequests = sharingState.receivedRequests\n                piniaStore.sentRequests = sharingState.sentRequests\n                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail\n                piniaStore.loading = sharingState.loading\n                piniaStore.pagination = sharingState.pagination\n                console.log('[Vuex->Pinia] Sharing批量同步完成')\n                break\n              default:\n                // 其他未知的sharing mutations，进行全量同步\n                piniaStore.sharingOrders = sharingState.sharingOrders\n                piniaStore.mySharingOrders = sharingState.mySharingOrders\n                piniaStore.receivedRequests = sharingState.receivedRequests\n                piniaStore.sentRequests = sharingState.sentRequests\n                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail\n                piniaStore.loading = sharingState.loading\n                piniaStore.pagination = sharingState.pagination\n                break\n            }\n          } finally {\n            syncInProgress = false\n          }\n        }\n      })\n    }\n\n    // Booking store同步\n    if (piniaStore.$id === 'booking') {\n      console.log('[Pinia-Vuex Sync] 初始化booking store同步')\n\n      // 初始同步Vuex到Pinia\n      const vuexBookingState = store.state.booking\n      if (vuexBookingState) {\n        piniaStore.$state.bookingList = vuexBookingState.bookingList || []\n        piniaStore.$state.bookingDetail = vuexBookingState.bookingDetail\n        piniaStore.$state.sharingOrders = vuexBookingState.sharingOrders || []\n        piniaStore.$state.userSharingOrders = vuexBookingState.userSharingOrders || []\n        piniaStore.$state.joinedSharingOrders = vuexBookingState.joinedSharingOrders || []\n        piniaStore.$state.sharingDetail = vuexBookingState.sharingDetail\n        piniaStore.$state.loading = vuexBookingState.loading || false\n        piniaStore.$state.pagination = vuexBookingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 }\n      }\n\n      // 监听Pinia变化，同步到Vuex\n      piniaStore.$subscribe((mutation, state) => {\n        if (syncInProgress) return // 防止递归同步\n\n        console.log('[Pinia->Vuex] Booking同步状态变化:', mutation.type)\n\n        // 批量同步到Vuex\n        try {\n          syncInProgress = true\n\n          const bookingUpdates = {\n            bookingList: state.bookingList,\n            bookingDetail: state.bookingDetail,\n            sharingOrders: state.sharingOrders,\n            userSharingOrders: state.userSharingOrders,\n            joinedSharingOrders: state.joinedSharingOrders,\n            sharingDetail: state.sharingDetail,\n            loading: state.loading,\n            pagination: state.pagination\n          }\n\n          // 尝试批量更新\n          store.commit('booking/BATCH_UPDATE', bookingUpdates)\n          console.log('[Pinia->Vuex] Booking状态已批量同步:', Object.keys(bookingUpdates))\n        } catch (error) {\n          // 如果批量更新失败，回退到单独更新\n          console.warn('[Pinia->Vuex] Booking批量同步失败，使用单独同步:', error)\n          try {\n            store.commit('booking/SET_BOOKING_LIST', { list: state.bookingList, pagination: state.pagination })\n            store.commit('booking/SET_BOOKING_DETAIL', state.bookingDetail)\n            store.commit('booking/SET_SHARING_ORDERS', state.sharingOrders)\n            store.commit('booking/SET_USER_SHARING_ORDERS', state.userSharingOrders)\n            store.commit('booking/SET_JOINED_SHARING_ORDERS', state.joinedSharingOrders)\n            store.commit('booking/SET_SHARING_DETAIL', state.sharingDetail)\n            store.commit('booking/SET_LOADING', state.loading)\n            store.commit('booking/SET_PAGINATION', state.pagination)\n            console.log('[Pinia->Vuex] Booking状态已单独同步')\n          } catch (fallbackError) {\n            console.error('[Pinia->Vuex] Booking同步完全失败:', fallbackError)\n          }\n        } finally {\n          syncInProgress = false\n        }\n      })\n\n      // 监听Vuex变化，同步到Pinia\n      store.subscribe((mutation, state) => {\n        if (syncInProgress) return // 防止递归同步\n\n        if (mutation.type.startsWith('booking/')) {\n          console.log('[Vuex->Pinia] Booking同步状态变化:', mutation.type)\n\n          try {\n            syncInProgress = true\n            const bookingState = state.booking\n\n            // 根据具体的mutation类型进行精确同步\n            switch (mutation.type) {\n              case 'booking/SET_BOOKING_LIST':\n                piniaStore.bookingList = bookingState.bookingList\n                piniaStore.pagination = bookingState.pagination\n                break\n              case 'booking/APPEND_BOOKING_LIST':\n                piniaStore.bookingList = bookingState.bookingList\n                break\n              case 'booking/SET_BOOKING_DETAIL':\n                piniaStore.bookingDetail = bookingState.bookingDetail\n                break\n              case 'booking/SET_SHARING_ORDERS':\n                piniaStore.sharingOrders = bookingState.sharingOrders\n                break\n              case 'booking/SET_USER_SHARING_ORDERS':\n                piniaStore.userSharingOrders = bookingState.userSharingOrders\n                break\n              case 'booking/SET_JOINED_SHARING_ORDERS':\n                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders\n                break\n              case 'booking/SET_SHARING_DETAIL':\n                piniaStore.sharingDetail = bookingState.sharingDetail\n                break\n              case 'booking/SET_LOADING':\n                piniaStore.loading = bookingState.loading\n                break\n              case 'booking/SET_PAGINATION':\n                piniaStore.pagination = bookingState.pagination\n                break\n              case 'booking/UPDATE_BOOKING_STATUS':\n                piniaStore.bookingList = bookingState.bookingList\n                break\n              case 'booking/BATCH_UPDATE':\n                // 批量更新时同步所有状态\n                piniaStore.bookingList = bookingState.bookingList\n                piniaStore.bookingDetail = bookingState.bookingDetail\n                piniaStore.sharingOrders = bookingState.sharingOrders\n                piniaStore.userSharingOrders = bookingState.userSharingOrders\n                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders\n                piniaStore.sharingDetail = bookingState.sharingDetail\n                piniaStore.loading = bookingState.loading\n                piniaStore.pagination = bookingState.pagination\n                console.log('[Vuex->Pinia] Booking批量同步完成')\n                break\n              default:\n                // 其他未知的booking mutations，进行全量同步\n                piniaStore.bookingList = bookingState.bookingList\n                piniaStore.bookingDetail = bookingState.bookingDetail\n                piniaStore.sharingOrders = bookingState.sharingOrders\n                piniaStore.userSharingOrders = bookingState.userSharingOrders\n                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders\n                piniaStore.sharingDetail = bookingState.sharingDetail\n                piniaStore.loading = bookingState.loading\n                piniaStore.pagination = bookingState.pagination\n                break\n            }\n          } finally {\n            syncInProgress = false\n          }\n        }\n      })\n    }\n  }\n}\n"], "names": ["uni", "store"], "mappings": ";;;AAGA,IAAI,iBAAiB;AAGd,SAAS,iBAAiB;AAC/B,SAAO,CAAC,EAAE,OAAO,iBAAiB;AAEhC,QAAI,WAAW,QAAQ,OAAO;AAC5BA,oBAAAA,MAAA,MAAA,OAAA,qCAAY,kCAAkC;AAG9C,iBAAW,OAAO,UAAUC,YAAK,MAAC,MAAM;AACxC,iBAAW,OAAO,gBAAgBA,YAAK,MAAC,MAAM;AAG9C,iBAAW,WAAW,CAAC,UAAU,UAAU;AACzC,YAAI;AAAgB;AAEpBD,4BAAA,MAAA,OAAA,qCAAY,4BAA4B,SAAS,MAAM,SAAS,OAAO;AAIvE,YAAI;AACF,2BAAiB;AACjBC,sBAAAA,MAAM,SAAS,cAAc,MAAM,OAAO;AAC1CA,sBAAAA,MAAM,SAAS,oBAAoB,MAAM,aAAa;AACtDD,wBAAAA,MAAA,MAAA,OAAA,qCAAY,2BAA2B,EAAE,SAAS,MAAM,SAAS,eAAe,MAAM,cAAa,CAAE;AAAA,QACtG,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,qCAAA,0BAA0B,KAAK;AAAA,QACvD,UAAkB;AACR,2BAAiB;AAAA,QAClB;AAAA,MACT,CAAO;AAGDC,kBAAAA,MAAM,UAAU,CAAC,UAAU,UAAU;AAEnC,YAAI,SAAS,SAAS,eAAe;AACnCD,wBAAY,MAAA,MAAA,OAAA,qCAAA,4BAA4B,SAAS,IAAI;AACrD,qBAAW,UAAU,MAAM;AAAA,QAC5B;AACD,YAAI,SAAS,SAAS,sBAAsB;AAC1CA,wBAAY,MAAA,MAAA,OAAA,qCAAA,4BAA4B,SAAS,IAAI;AACrD,qBAAW,gBAAgB,MAAM;AAAA,QAClC;AAAA,MAET,CAAO;AAAA,IACF;AAGD,QAAI,WAAW,QAAQ,QAAQ;AAC7BA,oBAAAA,MAAA,MAAA,OAAA,qCAAY,mCAAmC;AAG/C,YAAM,gBAAgBC,kBAAM,MAAM;AAClC,UAAI,eAAe;AACjB,mBAAW,OAAO,QAAQ,cAAc;AACxC,mBAAW,OAAO,WAAW,cAAc;AAC3C,mBAAW,OAAO,YAAY,cAAc;AAC5C,mBAAW,OAAO,aAAa,cAAc;AAC7C,mBAAW,OAAO,gBAAgB,cAAc;AAAA,MACjD;AAGD,iBAAW,WAAW,CAAC,UAAU,UAAU;AACzC,YAAI;AAAgB;AAEpBD,4BAAY,MAAA,OAAA,qCAAA,6BAA6B,SAAS,MAAM,SAAS,OAAO;AAIxE,YAAI;AACF,2BAAiB;AACjBC,sBAAAA,MAAM,OAAO,kBAAkB,MAAM,KAAK;AAC1CA,sBAAAA,MAAM,OAAO,sBAAsB,MAAM,QAAQ;AACjDA,sBAAAA,MAAM,OAAO,yBAAyB,MAAM,UAAU;AACtDA,sBAAAA,MAAM,OAAO,2BAA2B,MAAM,aAAa;AAC3DA,sBAAAA,MAAM,OAAO,uBAAuB,MAAM,SAAS;AACnDD,wBAAAA,wDAAY,yBAAyB;AAAA,QACtC,SAAQ,OAAO;AACdA,wBAAAA,0DAAc,2BAA2B,KAAK;AAAA,QACxD,UAAkB;AACR,2BAAiB;AAAA,QAClB;AAAA,MACT,CAAO;AAGDC,kBAAAA,MAAM,UAAU,CAAC,UAAU,UAAU;AACnC,YAAI,SAAS,KAAK,WAAW,OAAO,GAAG;AACrCD,wBAAA,MAAA,MAAA,OAAA,qCAAY,6BAA6B,SAAS,IAAI;AAEtD,gBAAM,YAAY,MAAM;AACxB,cAAI,SAAS,SAAS,kBAAkB;AACtC,uBAAW,QAAQ,UAAU;AAAA,UAC9B;AACD,cAAI,SAAS,SAAS,sBAAsB;AAC1C,uBAAW,WAAW,UAAU;AAAA,UACjC;AACD,cAAI,SAAS,SAAS,yBAAyB;AAC7C,uBAAW,aAAa,UAAU;AAAA,UACnC;AACD,cAAI,SAAS,SAAS,2BAA2B;AAC/C,uBAAW,gBAAgB,UAAU;AAAA,UACtC;AACD,cAAI,SAAS,SAAS,uBAAuB;AAC3C,uBAAW,YAAY,UAAU;AAAA,UAClC;AACD,cAAI,SAAS,SAAS,wBAAwB;AAC5C,uBAAW,QAAQ;AACnB,uBAAW,WAAW;AACtB,uBAAW,YAAY,EAAE,eAAe,GAAG,eAAe,EAAG;AAC7D,uBAAW,aAAa;AACxB,uBAAW,gBAAgB;AAAA,UAC5B;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACF;AAGD,QAAI,WAAW,QAAQ,SAAS;AAC9BA,oBAAAA,MAAA,MAAA,OAAA,sCAAY,oCAAoC;AAGhD,YAAM,iBAAiBC,kBAAM,MAAM;AACnC,UAAI,gBAAgB;AAClB,mBAAW,OAAO,YAAY,eAAe;AAC7C,mBAAW,OAAO,gBAAgB,eAAe;AACjD,mBAAW,OAAO,cAAc,eAAe;AAC/C,mBAAW,OAAO,aAAa,eAAe;AAC9C,mBAAW,OAAO,YAAY,eAAe;AAC7C,mBAAW,OAAO,gBAAgB,eAAe;AACjD,mBAAW,OAAO,UAAU,eAAe;AAC3C,mBAAW,OAAO,aAAa,eAAe;AAAA,MAC/C;AAGD,iBAAW,WAAW,CAAC,UAAU,UAAU;AACzC,YAAI;AAAgB;AAEpBD,sBAAY,MAAA,MAAA,OAAA,sCAAA,8BAA8B,SAAS,IAAI;AAGvD,YAAI;AACF,2BAAiB;AAGjB,gBAAM,eAAe;AAAA,YACnB,WAAW,MAAM;AAAA,YACjB,eAAe,MAAM;AAAA,YACrB,aAAa,MAAM;AAAA,YACnB,YAAY,MAAM;AAAA,YAClB,WAAW,MAAM;AAAA,YACjB,eAAe,MAAM;AAAA,YACrB,SAAS,MAAM;AAAA,YACf,YAAY,MAAM;AAAA,UACnB;AAGDC,4BAAM,OAAO,sBAAsB,YAAY;AAE/CD,8BAAA,MAAA,OAAA,sCAAY,+BAA+B,OAAO,KAAK,YAAY,CAAC;AAAA,QACrE,SAAQ,OAAO;AAEdA,wBAAAA,MAAA,MAAA,QAAA,sCAAa,gCAAgC,KAAK;AAClD,cAAI;AACFC,8BAAM,OAAO,wBAAwB,EAAE,MAAM,MAAM,WAAW,YAAY,MAAM,YAAY;AAC5FA,wBAAAA,MAAM,OAAO,4BAA4B,MAAM,aAAa;AAC5DA,wBAAAA,MAAM,OAAO,0BAA0B,MAAM,WAAW;AACxDA,wBAAAA,MAAM,OAAO,yBAAyB,MAAM,UAAU;AACtDA,wBAAAA,MAAM,OAAO,wBAAwB,MAAM,SAAS;AACpDA,wBAAAA,MAAM,OAAO,4BAA4B,MAAM,aAAa;AAC5DA,wBAAAA,MAAM,OAAO,qBAAqB,MAAM,OAAO;AAC/CD,0BAAAA,yDAAY,4BAA4B;AAAA,UACzC,SAAQ,eAAe;AACtBA,0BAAAA,MAAA,MAAA,SAAA,sCAAc,8BAA8B,aAAa;AAAA,UAC1D;AAAA,QACX,UAAkB;AACR,2BAAiB;AAAA,QAClB;AAAA,MACT,CAAO;AAGDC,kBAAAA,MAAM,UAAU,CAAC,UAAU,UAAU;AACnC,YAAI;AAAgB;AAEpB,YAAI,SAAS,KAAK,WAAW,QAAQ,GAAG;AACtCD,wBAAA,MAAA,MAAA,OAAA,sCAAY,8BAA8B,SAAS,IAAI;AAEvD,cAAI;AACF,6BAAiB;AACjB,kBAAM,aAAa,MAAM;AAGzB,oBAAQ,SAAS,MAAI;AAAA,cACnB,KAAK;AACH,2BAAW,YAAY,WAAW;AAClC,2BAAW,aAAa,WAAW;AACnC;AAAA,cACF,KAAK;AACH,2BAAW,YAAY,WAAW;AAClC;AAAA,cACF,KAAK;AACH,2BAAW,gBAAgB,WAAW;AACtC;AAAA,cACF,KAAK;AACH,2BAAW,cAAc,WAAW;AACpC;AAAA,cACF,KAAK;AACH,2BAAW,aAAa,WAAW;AACnC;AAAA,cACF,KAAK;AACH,2BAAW,YAAY,WAAW;AAClC;AAAA,cACF,KAAK;AACH,2BAAW,gBAAgB,WAAW;AACtC;AAAA,cACF,KAAK;AACH,2BAAW,UAAU,WAAW;AAChC;AAAA,cACF,KAAK;AAEH,2BAAW,YAAY,WAAW;AAClC,2BAAW,gBAAgB,WAAW;AACtC,2BAAW,cAAc,WAAW;AACpC,2BAAW,aAAa,WAAW;AACnC,2BAAW,YAAY,WAAW;AAClC,2BAAW,gBAAgB,WAAW;AACtC,2BAAW,UAAU,WAAW;AAChC,2BAAW,aAAa,WAAW;AACnCA,8BAAAA,yDAAY,2BAA2B;AACvC;AAAA,cACF;AAEE,2BAAW,YAAY,WAAW;AAClC,2BAAW,gBAAgB,WAAW;AACtC,2BAAW,cAAc,WAAW;AACpC,2BAAW,aAAa,WAAW;AACnC,2BAAW,YAAY,WAAW;AAClC,2BAAW,gBAAgB,WAAW;AACtC,2BAAW,UAAU,WAAW;AAChC,2BAAW,aAAa,WAAW;AACnC;AAAA,YACH;AAAA,UACb,UAAoB;AACR,6BAAiB;AAAA,UAClB;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACF;AAGD,QAAI,WAAW,QAAQ,WAAW;AAChCA,oBAAAA,MAAY,MAAA,OAAA,sCAAA,sCAAsC;AAGlD,YAAM,mBAAmBC,kBAAM,MAAM;AACrC,UAAI,kBAAkB;AACpB,mBAAW,OAAO,gBAAgB,iBAAiB,iBAAiB,CAAE;AACtE,mBAAW,OAAO,kBAAkB,iBAAiB,mBAAmB,CAAE;AAC1E,mBAAW,OAAO,mBAAmB,iBAAiB,oBAAoB,CAAE;AAC5E,mBAAW,OAAO,eAAe,iBAAiB,gBAAgB,CAAE;AACpE,mBAAW,OAAO,qBAAqB,iBAAiB;AACxD,mBAAW,OAAO,UAAU,iBAAiB,WAAW;AACxD,mBAAW,OAAO,aAAa,iBAAiB,cAAc,EAAE,SAAS,GAAG,UAAU,IAAI,OAAO,GAAG,YAAY,EAAG;AAAA,MACpH;AAGD,iBAAW,WAAW,CAAC,UAAU,UAAU;AACzC,YAAI;AAAgB;AAEpBD,sBAAA,MAAA,MAAA,OAAA,sCAAY,gCAAgC,SAAS,IAAI;AAGzD,YAAI;AACF,2BAAiB;AAEjB,gBAAM,iBAAiB;AAAA,YACrB,eAAe,MAAM;AAAA,YACrB,iBAAiB,MAAM;AAAA,YACvB,kBAAkB,MAAM;AAAA,YACxB,cAAc,MAAM;AAAA,YACpB,oBAAoB,MAAM;AAAA,YAC1B,SAAS,MAAM;AAAA,YACf,YAAY,MAAM;AAAA,UACnB;AAGDC,4BAAM,OAAO,wBAAwB,cAAc;AACnDD,8BAAY,MAAA,OAAA,sCAAA,iCAAiC,OAAO,KAAK,cAAc,CAAC;AAAA,QACzE,SAAQ,OAAO;AAEdA,wBAAAA,MAAA,MAAA,QAAA,sCAAa,uCAAuC,KAAK;AACzD,cAAI;AACFC,wBAAAA,MAAM,OAAO,8BAA8B,MAAM,aAAa;AAC9DA,wBAAAA,MAAM,OAAO,iCAAiC,MAAM,eAAe;AACnEA,wBAAAA,MAAM,OAAO,iCAAiC,MAAM,gBAAgB;AACpEA,wBAAAA,MAAM,OAAO,6BAA6B,MAAM,YAAY;AAC5DA,wBAAAA,MAAM,OAAO,oCAAoC,MAAM,kBAAkB;AACzEA,wBAAAA,MAAM,OAAO,uBAAuB,MAAM,OAAO;AACjDA,wBAAAA,MAAM,OAAO,0BAA0B,MAAM,UAAU;AACvDD,0BAAAA,MAAA,MAAA,OAAA,sCAAY,8BAA8B;AAAA,UAC3C,SAAQ,eAAe;AACtBA,0BAAAA,MAAc,MAAA,SAAA,sCAAA,gCAAgC,aAAa;AAAA,UAC5D;AAAA,QACX,UAAkB;AACR,2BAAiB;AAAA,QAClB;AAAA,MACT,CAAO;AAGDC,kBAAAA,MAAM,UAAU,CAAC,UAAU,UAAU;AACnC,YAAI;AAAgB;AAEpB,YAAI,SAAS,KAAK,WAAW,UAAU,GAAG;AACxCD,wBAAA,MAAA,MAAA,OAAA,sCAAY,gCAAgC,SAAS,IAAI;AAEzD,cAAI;AACF,6BAAiB;AACjB,kBAAM,eAAe,MAAM;AAG3B,oBAAQ,SAAS,MAAI;AAAA,cACnB,KAAK;AACH,2BAAW,gBAAgB,aAAa;AACxC;AAAA,cACF,KAAK;AACH,2BAAW,kBAAkB,aAAa;AAC1C;AAAA,cACF,KAAK;AACH,2BAAW,mBAAmB,aAAa;AAC3C;AAAA,cACF,KAAK;AACH,2BAAW,eAAe,aAAa;AACvC;AAAA,cACF,KAAK;AACH,2BAAW,qBAAqB,aAAa;AAC7C;AAAA,cACF,KAAK;AACH,2BAAW,UAAU,aAAa;AAClC;AAAA,cACF,KAAK;AACH,2BAAW,aAAa,aAAa;AACrC;AAAA,cACF,KAAK;AAEH,2BAAW,gBAAgB,aAAa;AACxC,2BAAW,kBAAkB,aAAa;AAC1C,2BAAW,mBAAmB,aAAa;AAC3C,2BAAW,eAAe,aAAa;AACvC,2BAAW,qBAAqB,aAAa;AAC7C,2BAAW,UAAU,aAAa;AAClC,2BAAW,aAAa,aAAa;AACrCA,8BAAAA,MAAA,MAAA,OAAA,sCAAY,6BAA6B;AACzC;AAAA,cACF;AAEE,2BAAW,gBAAgB,aAAa;AACxC,2BAAW,kBAAkB,aAAa;AAC1C,2BAAW,mBAAmB,aAAa;AAC3C,2BAAW,eAAe,aAAa;AACvC,2BAAW,qBAAqB,aAAa;AAC7C,2BAAW,UAAU,aAAa;AAClC,2BAAW,aAAa,aAAa;AACrC;AAAA,YACH;AAAA,UACb,UAAoB;AACR,6BAAiB;AAAA,UAClB;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACF;AAGD,QAAI,WAAW,QAAQ,WAAW;AAChCA,oBAAAA,MAAY,MAAA,OAAA,sCAAA,sCAAsC;AAGlD,YAAM,mBAAmBC,kBAAM,MAAM;AACrC,UAAI,kBAAkB;AACpB,mBAAW,OAAO,cAAc,iBAAiB,eAAe,CAAE;AAClE,mBAAW,OAAO,gBAAgB,iBAAiB;AACnD,mBAAW,OAAO,gBAAgB,iBAAiB,iBAAiB,CAAE;AACtE,mBAAW,OAAO,oBAAoB,iBAAiB,qBAAqB,CAAE;AAC9E,mBAAW,OAAO,sBAAsB,iBAAiB,uBAAuB,CAAE;AAClF,mBAAW,OAAO,gBAAgB,iBAAiB;AACnD,mBAAW,OAAO,UAAU,iBAAiB,WAAW;AACxD,mBAAW,OAAO,aAAa,iBAAiB,cAAc,EAAE,SAAS,GAAG,UAAU,IAAI,OAAO,GAAG,YAAY,GAAG,aAAa,EAAG;AAAA,MACpI;AAGD,iBAAW,WAAW,CAAC,UAAU,UAAU;AACzC,YAAI;AAAgB;AAEpBD,sBAAA,MAAA,MAAA,OAAA,sCAAY,gCAAgC,SAAS,IAAI;AAGzD,YAAI;AACF,2BAAiB;AAEjB,gBAAM,iBAAiB;AAAA,YACrB,aAAa,MAAM;AAAA,YACnB,eAAe,MAAM;AAAA,YACrB,eAAe,MAAM;AAAA,YACrB,mBAAmB,MAAM;AAAA,YACzB,qBAAqB,MAAM;AAAA,YAC3B,eAAe,MAAM;AAAA,YACrB,SAAS,MAAM;AAAA,YACf,YAAY,MAAM;AAAA,UACnB;AAGDC,4BAAM,OAAO,wBAAwB,cAAc;AACnDD,8BAAY,MAAA,OAAA,sCAAA,iCAAiC,OAAO,KAAK,cAAc,CAAC;AAAA,QACzE,SAAQ,OAAO;AAEdA,wBAAAA,MAAA,MAAA,QAAA,sCAAa,uCAAuC,KAAK;AACzD,cAAI;AACFC,8BAAM,OAAO,4BAA4B,EAAE,MAAM,MAAM,aAAa,YAAY,MAAM,YAAY;AAClGA,wBAAAA,MAAM,OAAO,8BAA8B,MAAM,aAAa;AAC9DA,wBAAAA,MAAM,OAAO,8BAA8B,MAAM,aAAa;AAC9DA,wBAAAA,MAAM,OAAO,mCAAmC,MAAM,iBAAiB;AACvEA,wBAAAA,MAAM,OAAO,qCAAqC,MAAM,mBAAmB;AAC3EA,wBAAAA,MAAM,OAAO,8BAA8B,MAAM,aAAa;AAC9DA,wBAAAA,MAAM,OAAO,uBAAuB,MAAM,OAAO;AACjDA,wBAAAA,MAAM,OAAO,0BAA0B,MAAM,UAAU;AACvDD,0BAAAA,MAAA,MAAA,OAAA,sCAAY,8BAA8B;AAAA,UAC3C,SAAQ,eAAe;AACtBA,0BAAAA,MAAc,MAAA,SAAA,sCAAA,gCAAgC,aAAa;AAAA,UAC5D;AAAA,QACX,UAAkB;AACR,2BAAiB;AAAA,QAClB;AAAA,MACT,CAAO;AAGDC,kBAAAA,MAAM,UAAU,CAAC,UAAU,UAAU;AACnC,YAAI;AAAgB;AAEpB,YAAI,SAAS,KAAK,WAAW,UAAU,GAAG;AACxCD,wBAAA,MAAA,MAAA,OAAA,sCAAY,gCAAgC,SAAS,IAAI;AAEzD,cAAI;AACF,6BAAiB;AACjB,kBAAM,eAAe,MAAM;AAG3B,oBAAQ,SAAS,MAAI;AAAA,cACnB,KAAK;AACH,2BAAW,cAAc,aAAa;AACtC,2BAAW,aAAa,aAAa;AACrC;AAAA,cACF,KAAK;AACH,2BAAW,cAAc,aAAa;AACtC;AAAA,cACF,KAAK;AACH,2BAAW,gBAAgB,aAAa;AACxC;AAAA,cACF,KAAK;AACH,2BAAW,gBAAgB,aAAa;AACxC;AAAA,cACF,KAAK;AACH,2BAAW,oBAAoB,aAAa;AAC5C;AAAA,cACF,KAAK;AACH,2BAAW,sBAAsB,aAAa;AAC9C;AAAA,cACF,KAAK;AACH,2BAAW,gBAAgB,aAAa;AACxC;AAAA,cACF,KAAK;AACH,2BAAW,UAAU,aAAa;AAClC;AAAA,cACF,KAAK;AACH,2BAAW,aAAa,aAAa;AACrC;AAAA,cACF,KAAK;AACH,2BAAW,cAAc,aAAa;AACtC;AAAA,cACF,KAAK;AAEH,2BAAW,cAAc,aAAa;AACtC,2BAAW,gBAAgB,aAAa;AACxC,2BAAW,gBAAgB,aAAa;AACxC,2BAAW,oBAAoB,aAAa;AAC5C,2BAAW,sBAAsB,aAAa;AAC9C,2BAAW,gBAAgB,aAAa;AACxC,2BAAW,UAAU,aAAa;AAClC,2BAAW,aAAa,aAAa;AACrCA,8BAAAA,MAAA,MAAA,OAAA,sCAAY,6BAA6B;AACzC;AAAA,cACF;AAEE,2BAAW,cAAc,aAAa;AACtC,2BAAW,gBAAgB,aAAa;AACxC,2BAAW,gBAAgB,aAAa;AACxC,2BAAW,oBAAoB,aAAa;AAC5C,2BAAW,sBAAsB,aAAa;AAC9C,2BAAW,gBAAgB,aAAa;AACxC,2BAAW,UAAU,aAAa;AAClC,2BAAW,aAAa,aAAa;AACrC;AAAA,YACH;AAAA,UACb,UAAoB;AACR,6BAAiB;AAAA,UAClB;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACF;AAAA,EACF;AACH;;"}