{"version": 3, "file": "migration-config.js", "sources": ["stores/migration-config.js"], "sourcesContent": ["// Vuex到Pinia迁移配置\nexport const migrationConfig = {\n  // 迁移阶段配置\n  phases: {\n    // 阶段1: 基础设施 - 已完成\n    phase1: {\n      name: '基础设施搭建',\n      status: 'completed',\n      modules: ['app'],\n      description: '设置Pinia实例，创建同步插件，建立并行运行环境'\n    },\n    \n    // 阶段2: 用户模块 - 进行中\n    phase2: {\n      name: '用户模块迁移',\n      status: 'in-progress',\n      modules: ['user'],\n      description: '迁移用户认证、登录状态、用户信息管理'\n    },\n    \n    // 阶段3: 场馆模块 - 进行中\n    phase3: {\n      name: '场馆模块迁移',\n      status: 'in-progress',\n      modules: ['venue'],\n      description: '迁移场馆列表、详情、时间段管理'\n    },\n    \n    // 阶段4: 预约模块 - 待开始\n    phase4: {\n      name: '预约模块迁移',\n      status: 'pending',\n      modules: ['booking'],\n      description: '迁移预约创建、列表、状态管理'\n    },\n    \n    // 阶段5: 拼场模块 - 已完成\n    phase5: {\n      name: '拼场模块迁移',\n      status: 'completed',\n      modules: ['sharing'],\n      description: '迁移拼场创建、申请、管理功能'\n    },\n\n    // 阶段6: 预订模块 - 进行中\n    phase6: {\n      name: '预订模块迁移',\n      status: 'in-progress',\n      modules: ['booking'],\n      description: '迁移预订管理、订单处理、拼场功能'\n    },\n\n    // 阶段7: 清理 - 待开始\n    phase7: {\n      name: '清理Vuex',\n      status: 'pending',\n      modules: [],\n      description: '移除Vuex依赖，清理同步代码'\n    }\n  },\n  \n  // 当前激活的模块 - 控制哪些模块使用Pinia\n  activeModules: ['app', 'user', 'venue', 'sharing', 'booking'],\n  \n  // 特性标志 - 控制是否启用Pinia功能\n  features: {\n    usePiniaForApp: true,\n    usePiniaForUser: true,\n    usePiniaForVenue: true,\n    usePiniaForBooking: true,\n    usePiniaForSharing: true,\n    enableSync: true, // 是否启用Vuex-Pinia同步\n    enableLogging: true // 是否启用迁移日志\n  },\n  \n  // 测试配置\n  testing: {\n    enableMigrationTests: true,\n    testModules: ['app', 'user', 'venue', 'sharing', 'booking'],\n    autoValidation: true // 自动验证状态同步\n  }\n}\n\n// 获取当前迁移状态\nexport function getMigrationStatus() {\n  const phases = migrationConfig.phases\n  const completed = Object.values(phases).filter(p => p.status === 'completed').length\n  const total = Object.keys(phases).length\n  \n  return {\n    progress: Math.round((completed / total) * 100),\n    currentPhase: Object.values(phases).find(p => p.status === 'in-progress')?.name || '未开始',\n    completedPhases: completed,\n    totalPhases: total\n  }\n}\n\n// 检查模块是否应该使用Pinia\nexport function shouldUsePinia(moduleName) {\n  return migrationConfig.activeModules.includes(moduleName)\n}\n\n// 检查特性是否启用\nexport function isFeatureEnabled(featureName) {\n  return migrationConfig.features[featureName] || false\n}\n\n// 更新迁移阶段状态\nexport function updatePhaseStatus(phaseName, status) {\n  if (migrationConfig.phases[phaseName]) {\n    migrationConfig.phases[phaseName].status = status\n    console.log(`[Migration] 阶段 ${phaseName} 状态更新为: ${status}`)\n  }\n}\n\n// 激活模块使用Pinia\nexport function activateModule(moduleName) {\n  if (!migrationConfig.activeModules.includes(moduleName)) {\n    migrationConfig.activeModules.push(moduleName)\n    console.log(`[Migration] 模块 ${moduleName} 已激活使用Pinia`)\n  }\n}\n\n// 记录迁移日志\nexport function logMigration(message, data = null) {\n  if (isFeatureEnabled('enableLogging')) {\n    console.log(`[Migration] ${message}`, data || '')\n  }\n}\n"], "names": ["uni"], "mappings": ";;AACY,MAAC,kBAAkB;AAAA;AAAA,EAE7B,QAAQ;AAAA;AAAA,IAEN,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,CAAC,KAAK;AAAA,MACf,aAAa;AAAA,IACd;AAAA;AAAA,IAGD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,CAAC,MAAM;AAAA,MAChB,aAAa;AAAA,IACd;AAAA;AAAA,IAGD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,CAAC,OAAO;AAAA,MACjB,aAAa;AAAA,IACd;AAAA;AAAA,IAGD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,CAAC,SAAS;AAAA,MACnB,aAAa;AAAA,IACd;AAAA;AAAA,IAGD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,CAAC,SAAS;AAAA,MACnB,aAAa;AAAA,IACd;AAAA;AAAA,IAGD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,CAAC,SAAS;AAAA,MACnB,aAAa;AAAA,IACd;AAAA;AAAA,IAGD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,CAAE;AAAA,MACX,aAAa;AAAA,IACd;AAAA,EACF;AAAA;AAAA,EAGD,eAAe,CAAC,OAAO,QAAQ,SAAS,WAAW,SAAS;AAAA;AAAA,EAG5D,UAAU;AAAA,IACR,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,YAAY;AAAA;AAAA,IACZ,eAAe;AAAA;AAAA,EAChB;AAAA;AAAA,EAGD,SAAS;AAAA,IACP,sBAAsB;AAAA,IACtB,aAAa,CAAC,OAAO,QAAQ,SAAS,WAAW,SAAS;AAAA,IAC1D,gBAAgB;AAAA;AAAA,EACjB;AACH;AAGO,SAAS,qBAAqB;;AACnC,QAAM,SAAS,gBAAgB;AAC/B,QAAM,YAAY,OAAO,OAAO,MAAM,EAAE,OAAO,OAAK,EAAE,WAAW,WAAW,EAAE;AAC9E,QAAM,QAAQ,OAAO,KAAK,MAAM,EAAE;AAElC,SAAO;AAAA,IACL,UAAU,KAAK,MAAO,YAAY,QAAS,GAAG;AAAA,IAC9C,gBAAc,YAAO,OAAO,MAAM,EAAE,KAAK,OAAK,EAAE,WAAW,aAAa,MAA1D,mBAA6D,SAAQ;AAAA,IACnF,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACd;AACH;AAQO,SAAS,iBAAiB,aAAa;AAC5C,SAAO,gBAAgB,SAAS,WAAW,KAAK;AAClD;AAmBO,SAAS,aAAa,SAAS,OAAO,MAAM;AACjD,MAAI,iBAAiB,eAAe,GAAG;AACrCA,0EAAY,eAAe,OAAO,IAAI,QAAQ,EAAE;AAAA,EACjD;AACH;;;;"}