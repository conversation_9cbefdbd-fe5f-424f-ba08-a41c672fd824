{"version": 3, "file": "sharing.js", "sources": ["store/modules/sharing.js"], "sourcesContent": ["import * as sharingApi from '@/api/sharing.js'\nimport { showSuccess, showError } from '@/utils/ui.js'\n\nconst state = {\n  sharingOrders: [],\n  mySharingOrders: [],\n  receivedRequests: [],\n  sentRequests: [],\n  sharingOrderDetail: null,\n  loading: false,\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: 0\n  }\n}\n\nconst mutations = {\n  SET_SHARING_ORDERS(state, orders) {\n    state.sharingOrders = orders\n  },\n  \n  SET_MY_SHARING_ORDERS(state, orders) {\n    state.mySharingOrders = orders\n  },\n  \n  SET_RECEIVED_REQUESTS(state, requests) {\n    state.receivedRequests = requests\n  },\n  \n  SET_SENT_REQUESTS(state, requests) {\n    state.sentRequests = requests\n  },\n  \n  SET_SHARING_ORDER_DETAIL(state, order) {\n    state.sharingOrderDetail = order\n  },\n  \n  SET_LOADING(state, loading) {\n    state.loading = loading\n  },\n  \n  SET_PAGINATION(state, pagination) {\n    state.pagination = { ...state.pagination, ...pagination }\n  },\n  \n  UPDATE_SHARING_ORDER_STATUS(state, { orderId, status }) {\n    const order = state.sharingOrders.find(o => o.id === orderId)\n    if (order) {\n      order.status = status\n    }\n    if (state.sharingOrderDetail && state.sharingOrderDetail.id === orderId) {\n      state.sharingOrderDetail.status = status\n    }\n  },\n  \n  UPDATE_SHARING_ORDER_PARTICIPANTS(state, { orderId, currentParticipants }) {\n    const order = state.sharingOrders.find(o => o.id === orderId)\n    if (order) {\n      order.currentParticipants = currentParticipants\n    }\n    if (state.sharingOrderDetail && state.sharingOrderDetail.id === orderId) {\n      state.sharingOrderDetail.currentParticipants = currentParticipants\n    }\n  },\n\n  // 批量更新mutation，用于优化同步性能\n  BATCH_UPDATE(state, updates) {\n    if (updates.sharingOrders !== undefined) {\n      state.sharingOrders = updates.sharingOrders\n    }\n    if (updates.mySharingOrders !== undefined) {\n      state.mySharingOrders = updates.mySharingOrders\n    }\n    if (updates.receivedRequests !== undefined) {\n      state.receivedRequests = updates.receivedRequests\n    }\n    if (updates.sentRequests !== undefined) {\n      state.sentRequests = updates.sentRequests\n    }\n    if (updates.sharingOrderDetail !== undefined) {\n      state.sharingOrderDetail = updates.sharingOrderDetail\n    }\n    if (updates.loading !== undefined) {\n      state.loading = updates.loading\n    }\n    if (updates.pagination !== undefined) {\n      state.pagination = { ...state.pagination, ...updates.pagination }\n    }\n  }\n}\n\nconst actions = {\n  // 获取可加入的拼场订单\n  async getSharingOrders({ commit }, params = {}) {\n    try {\n      commit('SET_LOADING', true)\n      \n      const response = await sharingApi.getJoinableSharingOrders(params)\n      console.log('Store: API返回的原始数据:', response)\n      \n      const orders = response.list || response.data || []\n      console.log('Store: 提取的订单数据:', orders)\n      \n      commit('SET_SHARING_ORDERS', orders)\n      if (response.pagination) {\n        commit('SET_PAGINATION', response.pagination)\n      }\n      return response\n    } catch (error) {\n      console.error('获取拼场订单失败:', error)\n      showError(error.message || '获取拼场订单失败')\n      commit('SET_SHARING_ORDERS', [])\n      return { data: [] }\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取可加入的拼场订单（别名方法）\n  async getJoinableSharingOrders({ commit }, params = {}) {\n    try {\n      commit('SET_LOADING', true)\n      \n      const response = await sharingApi.getJoinableSharingOrders(params)\n      console.log('Store: API返回的原始数据:', response)\n      \n      const orders = response.list || response.data || []\n      console.log('Store: 提取的订单数据:', orders)\n      \n      commit('SET_SHARING_ORDERS', orders)\n      if (response.pagination) {\n        commit('SET_PAGINATION', response.pagination)\n      }\n      return response\n    } catch (error) {\n      console.error('获取拼场订单失败:', error)\n      showError(error.message || '获取拼场订单失败')\n      commit('SET_SHARING_ORDERS', [])\n      return { data: [] }\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取所有拼场订单（包括所有状态）\n  async getAllSharingOrders({ commit }, params = {}) {\n    try {\n      commit('SET_LOADING', true)\n      \n      const response = await sharingApi.getAllSharingOrders(params)\n      console.log('Store: 获取所有拼场订单API返回的原始数据:', response)\n      \n      const orders = response.list || response.data || []\n      console.log('Store: 提取的所有拼场订单数据:', orders)\n      \n      commit('SET_SHARING_ORDERS', orders)\n      if (response.pagination) {\n        commit('SET_PAGINATION', response.pagination)\n      }\n      return response\n    } catch (error) {\n      console.error('获取所有拼场订单失败:', error)\n      showError(error.message || '获取所有拼场订单失败')\n      commit('SET_SHARING_ORDERS', [])\n      return { data: [] }\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 按场馆ID获取可加入的拼场订单\n  async getJoinableSharingOrdersByVenueId({ commit }, { venueId, params = {} }) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.getJoinableSharingOrdersByVenueId(venueId, params)\n      const orders = response.list || response.data || []\n      commit('SET_SHARING_ORDERS', orders)\n      return response\n    } catch (error) {\n      showError(error.message || '获取场馆拼场订单失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 按日期获取可加入的拼场订单\n  async getJoinableSharingOrdersByDate({ commit }, { date, params = {} }) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.getJoinableSharingOrdersByDate(date, params)\n      const orders = response.list || response.data || []\n      commit('SET_SHARING_ORDERS', orders)\n      return response\n    } catch (error) {\n      showError(error.message || '获取日期拼场订单失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 按场馆ID和日期获取可加入的拼场订单\n  async getJoinableSharingOrdersByVenueIdAndDate({ commit }, { venueId, date, params = {} }) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.getJoinableSharingOrdersByVenueIdAndDate(venueId, date, params)\n      const orders = response.list || response.data || []\n      commit('SET_SHARING_ORDERS', orders)\n      return response\n    } catch (error) {\n      showError(error.message || '获取拼场订单失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取我创建的拼场订单\n  async getMyCreatedSharingOrders({ commit }, params = {}) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.getMyCreatedSharingOrders(params)\n      const orders = response.list || response.data || []\n      commit('SET_MY_SHARING_ORDERS', orders)\n      return response\n    } catch (error) {\n      showError(error.message || '获取我的拼场订单失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取拼场订单详情\n  async getSharingOrderDetail({ commit }, orderId) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.getSharingOrderById(orderId)\n      const orderDetail = response.data || response\n      commit('SET_SHARING_ORDER_DETAIL', orderDetail)\n      return response\n    } catch (error) {\n      showError(error.message || '获取拼场订单详情失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 加入拼场订单\n  async joinSharingOrder({ commit }, payload) {\n    try {\n      // 支持两种调用方式：直接传orderId或传{orderId, data}对象\n      const orderId = typeof payload === 'object' ? payload.orderId : payload\n      const applicationData = typeof payload === 'object' ? payload.data : {}\n      \n      const response = await sharingApi.joinSharingOrder(orderId, applicationData)\n      showSuccess('申请提交成功')\n      // 更新参与人数\n      if (response.data && response.data.currentParticipants !== undefined) {\n        commit('UPDATE_SHARING_ORDER_PARTICIPANTS', {\n          orderId,\n          currentParticipants: response.data.currentParticipants\n        })\n      }\n      return response\n    } catch (error) {\n      showError(error.message || '申请提交失败')\n      throw error\n    }\n  },\n  \n  // 取消加入拼场订单\n  async cancelJoinSharingOrder({ commit }, orderId) {\n    try {\n      const response = await sharingApi.cancelJoinSharingOrder(orderId)\n      showSuccess('已取消加入拼场')\n      // 更新参与人数\n      if (response.data && response.data.currentParticipants !== undefined) {\n        commit('UPDATE_SHARING_ORDER_PARTICIPANTS', {\n          orderId,\n          currentParticipants: response.data.currentParticipants\n        })\n      }\n      return response\n    } catch (error) {\n      showError(error.message || '取消加入拼场失败')\n      throw error\n    }\n  },\n  \n  // 确认拼场订单\n  async confirmSharingOrder({ commit }, orderId) {\n    try {\n      const response = await sharingApi.confirmSharingOrder(orderId)\n      showSuccess('拼场订单已确认')\n      commit('UPDATE_SHARING_ORDER_STATUS', { orderId, status: 'CONFIRMED' })\n      return response\n    } catch (error) {\n      showError(error.message || '确认拼场订单失败')\n      throw error\n    }\n  },\n  \n  // 取消拼场订单\n  async cancelSharingOrder({ commit }, orderId) {\n    try {\n      const response = await sharingApi.cancelSharingOrder(orderId)\n      showSuccess('拼场订单已取消')\n      commit('UPDATE_SHARING_ORDER_STATUS', { orderId, status: 'CANCELLED' })\n      return response\n    } catch (error) {\n      showError(error.message || '取消拼场订单失败')\n      throw error\n    }\n  },\n  \n  // 获取我的拼场申请\n  async getMySharingRequests({ commit }, params = {}) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.getMySharedRequests(params)\n      commit('SET_SENT_REQUESTS', response.data || [])\n      return response.data || []\n    } catch (error) {\n      console.error('获取我的拼场申请失败:', error)\n      showError(error.message || '获取我的拼场申请失败')\n      commit('SET_SENT_REQUESTS', [])\n      return []\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取收到的拼场申请\n  async getReceivedSharingRequests({ commit }, params = {}) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.getReceivedSharedRequests(params)\n      commit('SET_RECEIVED_REQUESTS', response.data || [])\n      return response.data || []\n    } catch (error) {\n      console.error('获取收到的拼场申请失败:', error)\n      showError(error.message || '获取收到的拼场申请失败')\n      commit('SET_RECEIVED_REQUESTS', [])\n      return []\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 申请拼场\n  async applySharingOrder({ commit }, { orderId, data }) {\n    try {\n      const response = await sharingApi.applySharedBooking(orderId, data)\n      // 不在这里显示消息，让前端页面根据响应状态决定显示内容\n      return response\n    } catch (error) {\n      showError(error.message || '申请拼场失败')\n      throw error\n    }\n  },\n\n  // 申请加入拼场订单（需要支付）\n  async applyJoinSharingOrder({ commit }, orderId) {\n    try {\n      console.log('[SharingStore] 开始申请加入拼场订单:', orderId)\n      commit('SET_LOADING', true)\n\n      const response = await sharingApi.applyJoinSharingOrder(orderId)\n\n      if (response && response.success) {\n        console.log('[SharingStore] 申请加入拼场订单成功')\n        return response\n      } else {\n        throw new Error(response.message || '申请失败')\n      }\n    } catch (error) {\n      console.error('[SharingStore] 申请加入拼场订单失败:', error)\n      showError(error.message || '申请加入拼场订单失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 取消拼场申请\n  async cancelSharingRequest({ commit }, requestId) {\n    try {\n      const response = await sharingApi.cancelSharingRequest(requestId)\n      showSuccess('申请已取消')\n      return response\n    } catch (error) {\n      showError(error.message || '取消申请失败')\n      throw error\n    }\n  },\n  \n  // 处理拼场申请\n  async processSharingRequest({ commit }, { requestId, action, reason = '' }) {\n    try {\n      const data = {\n        action: action, // 直接传递action参数：'approve' 或 'reject'\n        responseMessage: reason || ''\n      }\n\n      const response = await sharingApi.handleSharedRequest(requestId, data)\n      showSuccess(action === 'approve' ? '已同意拼场申请' : '已拒绝拼场申请')\n      return response\n    } catch (error) {\n      // 检查是否是需要支付的错误\n      if (error.needPayment) {\n        // 保留完整的错误信息，包括needPayment和orderId\n        const enhancedError = new Error(error.message || '处理拼场申请失败')\n        enhancedError.needPayment = error.needPayment\n        enhancedError.orderId = error.orderId\n        enhancedError.orderStatus = error.orderStatus\n        throw enhancedError\n      } else {\n        showError(error.message || '处理拼场申请失败')\n        throw error\n      }\n    }\n  },\n  \n  // 移除拼场参与者\n  async removeSharingParticipant({ commit }, { orderId, participantId }) {\n    try {\n      const response = await sharingApi.removeSharingParticipant(orderId, participantId)\n      showSuccess('已移除参与者')\n      // 更新参与人数\n      if (response.data && response.data.currentParticipants !== undefined) {\n        commit('UPDATE_SHARING_ORDER_PARTICIPANTS', {\n          orderId,\n          currentParticipants: response.data.currentParticipants\n        })\n      }\n      return response\n    } catch (error) {\n      showError(error.message || '移除参与者失败')\n      throw error\n    }\n  },\n  \n  // 更新拼场设置\n  async updateSharingSettings({ commit }, { sharingId, settings }) {\n    try {\n      const response = await sharingApi.updateSharingSettings(sharingId, settings)\n      showSuccess('设置已更新')\n      return response\n    } catch (error) {\n      showError(error.message || '更新设置失败')\n      throw error\n    }\n  }\n}\n\nconst getters = {\n  sharingOrders: state => state.sharingOrders,\n  mySharingOrders: state => state.mySharingOrders,\n  receivedRequests: state => state.receivedRequests,\n  sentRequests: state => state.sentRequests,\n  sharingOrderDetail: state => state.sharingOrderDetail,\n  loading: state => state.loading,\n  pagination: state => state.pagination,\n  \n  // 可加入的拼场订单\n  availableSharingOrders: state => state.sharingOrders.filter(order => \n    order.status === 'OPEN' && order.currentParticipants < order.maxParticipants\n  ),\n  \n  // 进行中的拼场订单\n  activeSharingOrders: state => state.mySharingOrders.filter(order => \n    ['OPEN', 'CONFIRMED'].includes(order.status)\n  ),\n  \n  // 已完成的拼场订单\n  completedSharingOrders: state => state.mySharingOrders.filter(order => \n    order.status === 'COMPLETED'\n  ),\n  \n  // 已取消的拼场订单\n  cancelledSharingOrders: state => state.mySharingOrders.filter(order => \n    order.status === 'CANCELLED'\n  ),\n  \n  // 待处理的拼场申请\n  pendingRequests: state => state.receivedRequests.filter(request => \n    request.status === 'PENDING'\n  )\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n}"], "names": ["state", "sharingApi.getJoinableSharingOrders", "uni", "showError", "sharingApi.getAllSharingOrders", "sharingApi.getJoinableSharingOrdersByVenueId", "sharingApi.getJoinableSharingOrdersByDate", "sharingApi.getJoinableSharingOrdersByVenueIdAndDate", "sharingApi.getMyCreatedSharingOrders", "sharingApi.getSharingOrderById", "sharingApi.joinSharingOrder", "showSuccess", "sharingApi.cancelJoinSharingOrder", "sharingApi.confirmSharingOrder", "sharingApi.cancelSharingOrder", "sharingApi.getMySharedRequests", "sharingApi.getReceivedSharedRequests", "sharingApi.applySharedBooking", "sharingApi.applyJoinSharingOrder", "sharingApi.cancelSharingRequest", "sharingApi.handleSharedRequest", "sharingApi.removeSharingParticipant", "sharingApi.updateSharingSettings"], "mappings": ";;;;AAGA,MAAM,QAAQ;AAAA,EACZ,eAAe,CAAE;AAAA,EACjB,iBAAiB,CAAE;AAAA,EACnB,kBAAkB,CAAE;AAAA,EACpB,cAAc,CAAE;AAAA,EAChB,oBAAoB;AAAA,EACpB,SAAS;AAAA,EACT,YAAY;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACR;AACH;AAEA,MAAM,YAAY;AAAA,EAChB,mBAAmBA,QAAO,QAAQ;AAChC,IAAAA,OAAM,gBAAgB;AAAA,EACvB;AAAA,EAED,sBAAsBA,QAAO,QAAQ;AACnC,IAAAA,OAAM,kBAAkB;AAAA,EACzB;AAAA,EAED,sBAAsBA,QAAO,UAAU;AACrC,IAAAA,OAAM,mBAAmB;AAAA,EAC1B;AAAA,EAED,kBAAkBA,QAAO,UAAU;AACjC,IAAAA,OAAM,eAAe;AAAA,EACtB;AAAA,EAED,yBAAyBA,QAAO,OAAO;AACrC,IAAAA,OAAM,qBAAqB;AAAA,EAC5B;AAAA,EAED,YAAYA,QAAO,SAAS;AAC1B,IAAAA,OAAM,UAAU;AAAA,EACjB;AAAA,EAED,eAAeA,QAAO,YAAY;AAChC,IAAAA,OAAM,aAAa,EAAE,GAAGA,OAAM,YAAY,GAAG,WAAY;AAAA,EAC1D;AAAA,EAED,4BAA4BA,QAAO,EAAE,SAAS,OAAM,GAAI;AACtD,UAAM,QAAQA,OAAM,cAAc,KAAK,OAAK,EAAE,OAAO,OAAO;AAC5D,QAAI,OAAO;AACT,YAAM,SAAS;AAAA,IAChB;AACD,QAAIA,OAAM,sBAAsBA,OAAM,mBAAmB,OAAO,SAAS;AACvE,MAAAA,OAAM,mBAAmB,SAAS;AAAA,IACnC;AAAA,EACF;AAAA,EAED,kCAAkCA,QAAO,EAAE,SAAS,oBAAmB,GAAI;AACzE,UAAM,QAAQA,OAAM,cAAc,KAAK,OAAK,EAAE,OAAO,OAAO;AAC5D,QAAI,OAAO;AACT,YAAM,sBAAsB;AAAA,IAC7B;AACD,QAAIA,OAAM,sBAAsBA,OAAM,mBAAmB,OAAO,SAAS;AACvE,MAAAA,OAAM,mBAAmB,sBAAsB;AAAA,IAChD;AAAA,EACF;AAAA;AAAA,EAGD,aAAaA,QAAO,SAAS;AAC3B,QAAI,QAAQ,kBAAkB,QAAW;AACvC,MAAAA,OAAM,gBAAgB,QAAQ;AAAA,IAC/B;AACD,QAAI,QAAQ,oBAAoB,QAAW;AACzC,MAAAA,OAAM,kBAAkB,QAAQ;AAAA,IACjC;AACD,QAAI,QAAQ,qBAAqB,QAAW;AAC1C,MAAAA,OAAM,mBAAmB,QAAQ;AAAA,IAClC;AACD,QAAI,QAAQ,iBAAiB,QAAW;AACtC,MAAAA,OAAM,eAAe,QAAQ;AAAA,IAC9B;AACD,QAAI,QAAQ,uBAAuB,QAAW;AAC5C,MAAAA,OAAM,qBAAqB,QAAQ;AAAA,IACpC;AACD,QAAI,QAAQ,YAAY,QAAW;AACjC,MAAAA,OAAM,UAAU,QAAQ;AAAA,IACzB;AACD,QAAI,QAAQ,eAAe,QAAW;AACpC,MAAAA,OAAM,aAAa,EAAE,GAAGA,OAAM,YAAY,GAAG,QAAQ,WAAY;AAAA,IAClE;AAAA,EACF;AACH;AAEA,MAAM,UAAU;AAAA;AAAA,EAEd,MAAM,iBAAiB,EAAE,UAAU,SAAS,CAAA,GAAI;AAC9C,QAAI;AACF,aAAO,eAAe,IAAI;AAE1B,YAAM,WAAW,MAAMC,YAAmC,yBAAC,MAAM;AACjEC,oBAAAA,sDAAY,sBAAsB,QAAQ;AAE1C,YAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AACnDA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,mBAAmB,MAAM;AAErC,aAAO,sBAAsB,MAAM;AACnC,UAAI,SAAS,YAAY;AACvB,eAAO,kBAAkB,SAAS,UAAU;AAAA,MAC7C;AACD,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,wDAAc,aAAa,KAAK;AAChCC,yBAAU,MAAM,WAAW,UAAU;AACrC,aAAO,sBAAsB,EAAE;AAC/B,aAAO,EAAE,MAAM,GAAI;AAAA,IACzB,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,yBAAyB,EAAE,UAAU,SAAS,CAAA,GAAI;AACtD,QAAI;AACF,aAAO,eAAe,IAAI;AAE1B,YAAM,WAAW,MAAMF,YAAmC,yBAAC,MAAM;AACjEC,oBAAAA,sDAAY,sBAAsB,QAAQ;AAE1C,YAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AACnDA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,mBAAmB,MAAM;AAErC,aAAO,sBAAsB,MAAM;AACnC,UAAI,SAAS,YAAY;AACvB,eAAO,kBAAkB,SAAS,UAAU;AAAA,MAC7C;AACD,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,wDAAc,aAAa,KAAK;AAChCC,yBAAU,MAAM,WAAW,UAAU;AACrC,aAAO,sBAAsB,EAAE;AAC/B,aAAO,EAAE,MAAM,GAAI;AAAA,IACzB,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,oBAAoB,EAAE,UAAU,SAAS,CAAA,GAAI;AACjD,QAAI;AACF,aAAO,eAAe,IAAI;AAE1B,YAAM,WAAW,MAAMC,YAA8B,oBAAC,MAAM;AAC5DF,oBAAAA,MAAY,MAAA,OAAA,mCAAA,8BAA8B,QAAQ;AAElD,YAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AACnDA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,uBAAuB,MAAM;AAEzC,aAAO,sBAAsB,MAAM;AACnC,UAAI,SAAS,YAAY;AACvB,eAAO,kBAAkB,SAAS,UAAU;AAAA,MAC7C;AACD,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,mCAAA,eAAe,KAAK;AAClCC,yBAAU,MAAM,WAAW,YAAY;AACvC,aAAO,sBAAsB,EAAE;AAC/B,aAAO,EAAE,MAAM,GAAI;AAAA,IACzB,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,kCAAkC,EAAE,OAAQ,GAAE,EAAE,SAAS,SAAS,CAAA,KAAM;AAC5E,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAME,8CAA6C,SAAS,MAAM;AACnF,YAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AACnD,aAAO,sBAAsB,MAAM;AACnC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdF,yBAAU,MAAM,WAAW,YAAY;AACvC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,+BAA+B,EAAE,OAAQ,GAAE,EAAE,MAAM,SAAS,CAAA,KAAM;AACtE,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMG,2CAA0C,MAAM,MAAM;AAC7E,YAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AACnD,aAAO,sBAAsB,MAAM;AACnC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdH,yBAAU,MAAM,WAAW,YAAY;AACvC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,yCAAyC,EAAE,UAAU,EAAE,SAAS,MAAM,SAAS,CAAA,KAAM;AACzF,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMI,YAAAA,yCAAoD,SAAS,MAAM,MAAM;AAChG,YAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AACnD,aAAO,sBAAsB,MAAM;AACnC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdJ,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,0BAA0B,EAAE,UAAU,SAAS,CAAA,GAAI;AACvD,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMK,YAAoC,0BAAC,MAAM;AAClE,YAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AACnD,aAAO,yBAAyB,MAAM;AACtC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdL,yBAAU,MAAM,WAAW,YAAY;AACvC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,sBAAsB,EAAE,OAAQ,GAAE,SAAS;AAC/C,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMM,YAA8B,oBAAC,OAAO;AAC7D,YAAM,cAAc,SAAS,QAAQ;AACrC,aAAO,4BAA4B,WAAW;AAC9C,aAAO;AAAA,IACR,SAAQ,OAAO;AACdN,yBAAU,MAAM,WAAW,YAAY;AACvC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,iBAAiB,EAAE,OAAQ,GAAE,SAAS;AAC1C,QAAI;AAEF,YAAM,UAAU,OAAO,YAAY,WAAW,QAAQ,UAAU;AAChE,YAAM,kBAAkB,OAAO,YAAY,WAAW,QAAQ,OAAO,CAAE;AAEvE,YAAM,WAAW,MAAMO,6BAA4B,SAAS,eAAe;AAC3EC,eAAAA,YAAY,QAAQ;AAEpB,UAAI,SAAS,QAAQ,SAAS,KAAK,wBAAwB,QAAW;AACpE,eAAO,qCAAqC;AAAA,UAC1C;AAAA,UACA,qBAAqB,SAAS,KAAK;AAAA,QAC7C,CAAS;AAAA,MACF;AACD,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,uBAAuB,EAAE,OAAQ,GAAE,SAAS;AAChD,QAAI;AACF,YAAM,WAAW,MAAMS,YAAiC,uBAAC,OAAO;AAChED,eAAAA,YAAY,SAAS;AAErB,UAAI,SAAS,QAAQ,SAAS,KAAK,wBAAwB,QAAW;AACpE,eAAO,qCAAqC;AAAA,UAC1C;AAAA,UACA,qBAAqB,SAAS,KAAK;AAAA,QAC7C,CAAS;AAAA,MACF;AACD,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,oBAAoB,EAAE,OAAQ,GAAE,SAAS;AAC7C,QAAI;AACF,YAAM,WAAW,MAAMU,YAA8B,oBAAC,OAAO;AAC7DF,eAAAA,YAAY,SAAS;AACrB,aAAO,+BAA+B,EAAE,SAAS,QAAQ,YAAW,CAAE;AACtE,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,mBAAmB,EAAE,OAAQ,GAAE,SAAS;AAC5C,QAAI;AACF,YAAM,WAAW,MAAMW,YAA6B,mBAAC,OAAO;AAC5DH,eAAAA,YAAY,SAAS;AACrB,aAAO,+BAA+B,EAAE,SAAS,QAAQ,YAAW,CAAE;AACtE,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,qBAAqB,EAAE,UAAU,SAAS,CAAA,GAAI;AAClD,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMY,YAA8B,oBAAC,MAAM;AAC5D,aAAO,qBAAqB,SAAS,QAAQ,CAAA,CAAE;AAC/C,aAAO,SAAS,QAAQ,CAAE;AAAA,IAC3B,SAAQ,OAAO;AACdb,oBAAAA,MAAc,MAAA,SAAA,mCAAA,eAAe,KAAK;AAClCC,yBAAU,MAAM,WAAW,YAAY;AACvC,aAAO,qBAAqB,EAAE;AAC9B,aAAO,CAAE;AAAA,IACf,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,2BAA2B,EAAE,UAAU,SAAS,CAAA,GAAI;AACxD,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMa,YAAoC,0BAAC,MAAM;AAClE,aAAO,yBAAyB,SAAS,QAAQ,CAAA,CAAE;AACnD,aAAO,SAAS,QAAQ,CAAE;AAAA,IAC3B,SAAQ,OAAO;AACdd,oBAAAA,MAAc,MAAA,SAAA,mCAAA,gBAAgB,KAAK;AACnCC,yBAAU,MAAM,WAAW,aAAa;AACxC,aAAO,yBAAyB,EAAE;AAClC,aAAO,CAAE;AAAA,IACf,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,kBAAkB,EAAE,OAAM,GAAI,EAAE,SAAS,KAAI,GAAI;AACrD,QAAI;AACF,YAAM,WAAW,MAAMc,+BAA8B,SAAS,IAAI;AAElE,aAAO;AAAA,IACR,SAAQ,OAAO;AACdd,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,sBAAsB,EAAE,OAAQ,GAAE,SAAS;AAC/C,QAAI;AACFD,oBAAAA,MAAA,MAAA,OAAA,mCAAY,8BAA8B,OAAO;AACjD,aAAO,eAAe,IAAI;AAE1B,YAAM,WAAW,MAAMgB,YAAgC,sBAAC,OAAO;AAE/D,UAAI,YAAY,SAAS,SAAS;AAChChB,sBAAAA,MAAY,MAAA,OAAA,mCAAA,2BAA2B;AACvC,eAAO;AAAA,MACf,OAAa;AACL,cAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,MAC3C;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,SAAA,mCAAc,8BAA8B,KAAK;AACjDC,yBAAU,MAAM,WAAW,YAAY;AACvC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,qBAAqB,EAAE,OAAQ,GAAE,WAAW;AAChD,QAAI;AACF,YAAM,WAAW,MAAMgB,YAA+B,qBAAC,SAAS;AAChER,eAAAA,YAAY,OAAO;AACnB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,sBAAsB,EAAE,UAAU,EAAE,WAAW,QAAQ,SAAS,MAAM;AAC1E,QAAI;AACF,YAAM,OAAO;AAAA,QACX;AAAA;AAAA,QACA,iBAAiB,UAAU;AAAA,MAC5B;AAED,YAAM,WAAW,MAAMiB,gCAA+B,WAAW,IAAI;AACrET,eAAAA,YAAY,WAAW,YAAY,YAAY,SAAS;AACxD,aAAO;AAAA,IACR,SAAQ,OAAO;AAEd,UAAI,MAAM,aAAa;AAErB,cAAM,gBAAgB,IAAI,MAAM,MAAM,WAAW,UAAU;AAC3D,sBAAc,cAAc,MAAM;AAClC,sBAAc,UAAU,MAAM;AAC9B,sBAAc,cAAc,MAAM;AAClC,cAAM;AAAA,MACd,OAAa;AACLR,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,yBAAyB,EAAE,OAAM,GAAI,EAAE,SAAS,cAAa,GAAI;AACrE,QAAI;AACF,YAAM,WAAW,MAAMkB,qCAAoC,SAAS,aAAa;AACjFV,eAAAA,YAAY,QAAQ;AAEpB,UAAI,SAAS,QAAQ,SAAS,KAAK,wBAAwB,QAAW;AACpE,eAAO,qCAAqC;AAAA,UAC1C;AAAA,UACA,qBAAqB,SAAS,KAAK;AAAA,QAC7C,CAAS;AAAA,MACF;AACD,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,SAAS;AACpC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,sBAAsB,EAAE,OAAM,GAAI,EAAE,WAAW,SAAQ,GAAI;AAC/D,QAAI;AACF,YAAM,WAAW,MAAMmB,kCAAiC,WAAW,QAAQ;AAC3EX,eAAAA,YAAY,OAAO;AACnB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACP;AAAA,EACF;AACH;AAEA,MAAM,UAAU;AAAA,EACd,eAAe,CAAAH,WAASA,OAAM;AAAA,EAC9B,iBAAiB,CAAAA,WAASA,OAAM;AAAA,EAChC,kBAAkB,CAAAA,WAASA,OAAM;AAAA,EACjC,cAAc,CAAAA,WAASA,OAAM;AAAA,EAC7B,oBAAoB,CAAAA,WAASA,OAAM;AAAA,EACnC,SAAS,CAAAA,WAASA,OAAM;AAAA,EACxB,YAAY,CAAAA,WAASA,OAAM;AAAA;AAAA,EAG3B,wBAAwB,CAAAA,WAASA,OAAM,cAAc;AAAA,IAAO,WAC1D,MAAM,WAAW,UAAU,MAAM,sBAAsB,MAAM;AAAA,EAC9D;AAAA;AAAA,EAGD,qBAAqB,CAAAA,WAASA,OAAM,gBAAgB;AAAA,IAAO,WACzD,CAAC,QAAQ,WAAW,EAAE,SAAS,MAAM,MAAM;AAAA,EAC5C;AAAA;AAAA,EAGD,wBAAwB,CAAAA,WAASA,OAAM,gBAAgB;AAAA,IAAO,WAC5D,MAAM,WAAW;AAAA,EAClB;AAAA;AAAA,EAGD,wBAAwB,CAAAA,WAASA,OAAM,gBAAgB;AAAA,IAAO,WAC5D,MAAM,WAAW;AAAA,EAClB;AAAA;AAAA,EAGD,iBAAiB,CAAAA,WAASA,OAAM,iBAAiB;AAAA,IAAO,aACtD,QAAQ,WAAW;AAAA,EACpB;AACH;AAEA,MAAe,UAAA;AAAA,EACb,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;"}