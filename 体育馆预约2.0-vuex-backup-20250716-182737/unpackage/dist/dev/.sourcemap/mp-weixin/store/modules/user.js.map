{"version": 3, "file": "user.js", "sources": ["store/modules/user.js"], "sourcesContent": ["import * as authApi from '@/api/auth.js'\nimport * as userApi from '@/api/user.js'\nimport { setToken, removeToken, setUserInfo, removeUserInfo, getUserInfo, getToken } from '@/utils/auth.js'\nimport { showSuccess, showError } from '@/utils/ui.js'\nimport { clearAuthCache, updateAuthCache } from '@/utils/router-guard-new.js'\n\nconst state = {\n  token: getToken(),\n  userInfo: getUserInfo(),\n  userStats: {\n    totalBookings: 0,\n    totalSharings: 0\n  },\n  isLoggedIn: !!(getToken() && getUserInfo()),\n  loginChecking: false // 是否正在检查登录状态\n}\n\nconst mutations = {\n  SET_TOKEN(state, token) {\n    state.token = token\n    setToken(token)\n  },\n  \n  SET_USER_INFO(state, userInfo) {\n    state.userInfo = userInfo\n    setUserInfo(userInfo)\n  },\n  \n  SET_LOGIN_STATUS(state, status) {\n    console.log('[UserStore] 设置登录状态:', status)\n    state.isLoggedIn = status\n    // 同步更新路由守卫缓存\n    updateAuthCache(status)\n  },\n  \n  SET_LOGIN_CHECKING(state, checking) {\n    state.loginChecking = checking\n  },\n  \n  SET_USER_STATS(state, stats) {\n    state.userStats = stats\n  },\n  \n  CLEAR_USER_DATA(state) {\n    console.log('[UserStore] 清除用户数据')\n    state.token = ''\n    state.userInfo = null\n    state.userStats = {\n      totalBookings: 0,\n      totalSharings: 0\n    }\n    state.isLoggedIn = false\n    state.loginChecking = false\n    removeToken()\n    removeUserInfo()\n    // 清除路由守卫缓存\n    clearAuthCache()\n  }\n}\n\nconst actions = {\n  // 用户登录\n  async login({ commit }, loginData) {\n    try {\n      console.log('[UserStore] 开始登录')\n      const response = await authApi.login(loginData)\n      console.log('[UserStore] 登录响应:', response)\n      \n      if (!response) {\n        throw new Error('登录响应为空')\n      }\n      \n      const responseData = response.data || response\n      const token = responseData.accessToken || responseData.token\n      \n      if (!token) {\n        console.error('[UserStore] 响应数据:', responseData)\n        throw new Error('未获取到登录令牌')\n      }\n      \n      const user = {\n        id: responseData.id,\n        username: responseData.username,\n        email: responseData.email,\n        phone: responseData.phone,\n        nickname: responseData.nickname,\n        avatar: responseData.avatar,\n        roles: responseData.roles\n      }\n      \n      // 更新状态\n      commit('SET_TOKEN', token)\n      commit('SET_USER_INFO', user)\n      commit('SET_LOGIN_STATUS', true)\n      \n      console.log('[UserStore] 登录成功，用户信息:', user)\n      return response\n    } catch (error) {\n      console.error('[UserStore] 登录错误:', error)\n      commit('SET_LOGIN_STATUS', false)\n      throw error\n    }\n  },\n  \n  // 用户注册\n  async register({ commit }, registerData) {\n    try {\n      const response = await authApi.register(registerData)\n      showSuccess('注册成功')\n      return response\n    } catch (error) {\n      showError(error.message || '注册失败')\n      throw error\n    }\n  },\n  \n  // 获取短信验证码\n  async getSmsCode({ commit }, smsData) {\n    try {\n      const response = await authApi.getSmsCode(smsData.phone)\n      return response\n    } catch (error) {\n      throw error\n    }\n  },\n\n  // 短信验证码登录\n  async smsLogin({ commit }, loginData) {\n    try {\n      console.log('[UserStore] 开始短信登录')\n      const response = await authApi.smsLogin(loginData)\n      console.log('[UserStore] 短信登录响应:', response)\n      \n      if (!response) {\n        throw new Error('短信登录响应为空')\n      }\n      \n      const responseData = response.data || response\n      const token = responseData.accessToken || responseData.token\n      \n      if (!token) {\n        console.error('[UserStore] 响应数据:', responseData)\n        throw new Error('未获取到登录令牌')\n      }\n      \n      const user = {\n        id: responseData.id,\n        username: responseData.username,\n        email: responseData.email,\n        phone: responseData.phone,\n        nickname: responseData.nickname,\n        avatar: responseData.avatar,\n        roles: responseData.roles\n      }\n      \n      // 更新状态\n      commit('SET_TOKEN', token)\n      commit('SET_USER_INFO', user)\n      commit('SET_LOGIN_STATUS', true)\n      \n      console.log('[UserStore] 短信登录成功，用户信息:', user)\n      return response\n    } catch (error) {\n      console.error('[UserStore] 短信登录错误:', error)\n      commit('SET_LOGIN_STATUS', false)\n      throw error\n    }\n  },\n  \n  // 获取用户信息\n  async getUserInfo({ commit }) {\n    try {\n      console.log('[UserStore] 开始获取用户信息')\n      const response = await userApi.getUserInfo()\n      console.log('[UserStore] 获取用户信息响应:', response)\n      \n      if (response && response.data) {\n        console.log('[UserStore] 用户信息数据:', response.data)\n        commit('SET_USER_INFO', response.data)\n        commit('SET_LOGIN_STATUS', true)\n        return response\n      } else {\n        console.warn('[UserStore] 用户信息响应为空或无data字段')\n        return null\n      }\n    } catch (error) {\n      console.error('[UserStore] 获取用户信息失败:', error)\n      // 如果是认证错误，清除登录状态\n      if (error.code === 'LOGIN_EXPIRED' || error.message.includes('未授权')) {\n        commit('CLEAR_USER_DATA')\n      }\n      throw error\n    }\n  },\n  \n  // 获取用户统计信息\n  async getUserStats({ commit }) {\n    try {\n      const response = await userApi.getUserStats()\n      commit('SET_USER_STATS', response.data)\n      return response\n    } catch (error) {\n      console.error('[UserStore] 获取用户统计信息失败:', error)\n      commit('SET_USER_STATS', {\n        totalBookings: 0,\n        totalSharings: 0\n      })\n      throw error\n    }\n  },\n  \n  // 更新用户信息\n  async updateUserInfo({ commit }, userData) {\n    try {\n      console.log('[UserStore] 发送到后端的用户数据:', JSON.stringify(userData, null, 2))\n      const response = await userApi.updateUserInfo(userData)\n      console.log('[UserStore] 后端返回的响应:', response)\n      commit('SET_USER_INFO', response.data)\n      showSuccess('信息更新成功')\n      return response\n    } catch (error) {\n      console.error('[UserStore] 更新用户信息失败:', error)\n      showError(error.message || '更新失败')\n      throw error\n    }\n  },\n  \n  // 修改密码\n  async changeUserPassword({ commit }, passwordData) {\n    try {\n      const response = await userApi.changePassword(passwordData)\n      showSuccess('密码修改成功')\n      return response\n    } catch (error) {\n      showError(error.message || '密码修改失败')\n      throw error\n    }\n  },\n  \n  // 用户登出\n  async logout({ commit }) {\n    try {\n      console.log('[UserStore] 开始登出')\n      // 尝试调用后端API退出\n      await authApi.logout().catch(err => {\n        console.warn('[UserStore] 调用退出API失败，可能token已过期:', err)\n      })\n    } finally {\n      // 无论API调用成功与否，都清理前端数据\n      commit('CLEAR_USER_DATA')\n      showSuccess('已退出登录')\n      console.log('[UserStore] 登出完成')\n    }\n  },\n\n  // 检查登录状态（优化版本）\n  async checkLoginStatus({ commit, state }) {\n    // 避免重复检查\n    if (state.loginChecking) {\n      console.log('[UserStore] 正在检查登录状态，跳过重复检查')\n      return state.isLoggedIn\n    }\n    \n    try {\n      commit('SET_LOGIN_CHECKING', true)\n      \n      const token = getToken()\n      const userInfo = getUserInfo()\n      console.log('[UserStore] 检查登录状态 - token:', !!token, 'userInfo:', !!userInfo)\n\n      // 如果本地没有token或用户信息，直接返回未登录\n      if (!token || !userInfo) {\n        console.log('[UserStore] 本地无登录信息，清除状态')\n        commit('CLEAR_USER_DATA')\n        return false\n      }\n\n      // 验证token有效性\n      try {\n        const response = await userApi.getUserInfo()\n        console.log('[UserStore] token验证成功')\n        \n        if (response && response.data) {\n          // token有效，更新用户信息和登录状态\n          commit('SET_TOKEN', token)\n          commit('SET_USER_INFO', response.data)\n          commit('SET_LOGIN_STATUS', true)\n          return true\n        } else {\n          console.log('[UserStore] 用户信息响应异常，清除状态')\n          commit('CLEAR_USER_DATA')\n          return false\n        }\n      } catch (error) {\n        console.log('[UserStore] token验证失败:', error.message)\n        \n        // 检查是否是认证相关错误\n        if (error.code === 'LOGIN_EXPIRED' || \n            error.message.includes('登录已过期') || \n            error.message.includes('未授权') ||\n            error.message.includes('token') ||\n            error.status === 401) {\n          console.log('[UserStore] 认证失效，清除本地数据')\n          commit('CLEAR_USER_DATA')\n          return false\n        }\n        \n        // 网络错误等其他情况，暂时保持登录状态\n        console.log('[UserStore] 网络异常，保持本地登录状态')\n        commit('SET_TOKEN', token)\n        commit('SET_USER_INFO', userInfo)\n        commit('SET_LOGIN_STATUS', true)\n        return true\n      }\n    } catch (error) {\n      console.error('[UserStore] 检查登录状态异常:', error)\n      commit('CLEAR_USER_DATA')\n      return false\n    } finally {\n      commit('SET_LOGIN_CHECKING', false)\n    }\n  },\n  \n  // 强制更新登录状态\n  updateLoginStatus({ commit }, isLoggedIn) {\n    console.log('[UserStore] 强制更新登录状态:', isLoggedIn)\n    commit('SET_LOGIN_STATUS', isLoggedIn)\n  },\n  \n  // 初始化用户状态\n  initUserState({ commit, dispatch }) {\n    console.log('[UserStore] 初始化用户状态')\n    const token = getToken()\n    const userInfo = getUserInfo()\n    \n    if (token && userInfo) {\n      commit('SET_TOKEN', token)\n      commit('SET_USER_INFO', userInfo)\n      commit('SET_LOGIN_STATUS', true)\n      console.log('[UserStore] 从本地存储恢复登录状态')\n    } else {\n      commit('SET_LOGIN_STATUS', false)\n      console.log('[UserStore] 本地无登录信息')\n    }\n  }\n}\n\nconst getters = {\n  isLoggedIn: state => state.isLoggedIn,\n  userInfo: state => state.userInfo,\n  userStats: state => state.userStats,\n  token: state => state.token,\n  userId: state => state.userInfo?.id,\n  username: state => state.userInfo?.username,\n  nickname: state => state.userInfo?.nickname,\n  avatar: state => state.userInfo?.avatar,\n  phone: state => state.userInfo?.phone,\n  email: state => state.userInfo?.email,\n  loginChecking: state => state.loginChecking\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n}"], "names": ["getToken", "getUserInfo", "state", "setToken", "setUserInfo", "uni", "updateAuthCache", "removeToken", "removeUserInfo", "clearAuthCache", "authApi.login", "user", "authApi.register", "showSuccess", "showError", "authApi.getSmsCode", "authApi.smsLogin", "userApi.getUserInfo", "userApi.getUserStats", "userApi.updateUserInfo", "userApi.changePassword", "authApi.logout"], "mappings": ";;;;;;;AAMA,MAAM,QAAQ;AAAA,EACZ,OAAOA,WAAAA,SAAU;AAAA,EACjB,UAAUC,WAAAA,YAAa;AAAA,EACvB,WAAW;AAAA,IACT,eAAe;AAAA,IACf,eAAe;AAAA,EAChB;AAAA,EACD,YAAY,CAAC,EAAED,oBAAU,KAAIC,WAAW,YAAA;AAAA,EACxC,eAAe;AAAA;AACjB;AAEA,MAAM,YAAY;AAAA,EAChB,UAAUC,QAAO,OAAO;AACtB,IAAAA,OAAM,QAAQ;AACdC,eAAAA,SAAS,KAAK;AAAA,EACf;AAAA,EAED,cAAcD,QAAO,UAAU;AAC7B,IAAAA,OAAM,WAAW;AACjBE,eAAAA,YAAY,QAAQ;AAAA,EACrB;AAAA,EAED,iBAAiBF,QAAO,QAAQ;AAC9BG,kBAAAA,MAAA,MAAA,OAAA,+BAAY,uBAAuB,MAAM;AACzC,IAAAH,OAAM,aAAa;AAEnBI,yBAAAA,gBAAgB,MAAM;AAAA,EACvB;AAAA,EAED,mBAAmBJ,QAAO,UAAU;AAClC,IAAAA,OAAM,gBAAgB;AAAA,EACvB;AAAA,EAED,eAAeA,QAAO,OAAO;AAC3B,IAAAA,OAAM,YAAY;AAAA,EACnB;AAAA,EAED,gBAAgBA,QAAO;AACrBG,kBAAAA,MAAY,MAAA,OAAA,+BAAA,oBAAoB;AAChC,IAAAH,OAAM,QAAQ;AACd,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,YAAY;AAAA,MAChB,eAAe;AAAA,MACf,eAAe;AAAA,IAChB;AACD,IAAAA,OAAM,aAAa;AACnB,IAAAA,OAAM,gBAAgB;AACtBK,2BAAa;AACbC,8BAAgB;AAEhBC,wCAAgB;AAAA,EACjB;AACH;AAEA,MAAM,UAAU;AAAA;AAAA,EAEd,MAAM,MAAM,EAAE,OAAQ,GAAE,WAAW;AACjC,QAAI;AACFJ,oBAAAA,kDAAY,kBAAkB;AAC9B,YAAM,WAAW,MAAMK,SAAa,MAAC,SAAS;AAC9CL,oBAAAA,MAAY,MAAA,OAAA,+BAAA,qBAAqB,QAAQ;AAEzC,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,QAAQ;AAAA,MACzB;AAED,YAAM,eAAe,SAAS,QAAQ;AACtC,YAAM,QAAQ,aAAa,eAAe,aAAa;AAEvD,UAAI,CAAC,OAAO;AACVA,sBAAAA,MAAc,MAAA,SAAA,+BAAA,qBAAqB,YAAY;AAC/C,cAAM,IAAI,MAAM,UAAU;AAAA,MAC3B;AAED,YAAMM,QAAO;AAAA,QACX,IAAI,aAAa;AAAA,QACjB,UAAU,aAAa;AAAA,QACvB,OAAO,aAAa;AAAA,QACpB,OAAO,aAAa;AAAA,QACpB,UAAU,aAAa;AAAA,QACvB,QAAQ,aAAa;AAAA,QACrB,OAAO,aAAa;AAAA,MACrB;AAGD,aAAO,aAAa,KAAK;AACzB,aAAO,iBAAiBA,KAAI;AAC5B,aAAO,oBAAoB,IAAI;AAE/BN,oBAAAA,kDAAY,0BAA0BM,KAAI;AAC1C,aAAO;AAAA,IACR,SAAQ,OAAO;AACdN,oBAAAA,MAAc,MAAA,SAAA,+BAAA,qBAAqB,KAAK;AACxC,aAAO,oBAAoB,KAAK;AAChC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,SAAS,EAAE,OAAQ,GAAE,cAAc;AACvC,QAAI;AACF,YAAM,WAAW,MAAMO,SAAgB,SAAC,YAAY;AACpDC,eAAAA,YAAY,MAAM;AAClB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,MAAM;AACjC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,WAAW,EAAE,OAAQ,GAAE,SAAS;AACpC,QAAI;AACF,YAAM,WAAW,MAAMC,oBAAmB,QAAQ,KAAK;AACvD,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,SAAS,EAAE,OAAQ,GAAE,WAAW;AACpC,QAAI;AACFV,oBAAAA,MAAY,MAAA,OAAA,gCAAA,oBAAoB;AAChC,YAAM,WAAW,MAAMW,SAAgB,SAAC,SAAS;AACjDX,oBAAAA,mDAAY,uBAAuB,QAAQ;AAE3C,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,UAAU;AAAA,MAC3B;AAED,YAAM,eAAe,SAAS,QAAQ;AACtC,YAAM,QAAQ,aAAa,eAAe,aAAa;AAEvD,UAAI,CAAC,OAAO;AACVA,sBAAAA,MAAc,MAAA,SAAA,gCAAA,qBAAqB,YAAY;AAC/C,cAAM,IAAI,MAAM,UAAU;AAAA,MAC3B;AAED,YAAMM,QAAO;AAAA,QACX,IAAI,aAAa;AAAA,QACjB,UAAU,aAAa;AAAA,QACvB,OAAO,aAAa;AAAA,QACpB,OAAO,aAAa;AAAA,QACpB,UAAU,aAAa;AAAA,QACvB,QAAQ,aAAa;AAAA,QACrB,OAAO,aAAa;AAAA,MACrB;AAGD,aAAO,aAAa,KAAK;AACzB,aAAO,iBAAiBA,KAAI;AAC5B,aAAO,oBAAoB,IAAI;AAE/BN,oBAAAA,mDAAY,4BAA4BM,KAAI;AAC5C,aAAO;AAAA,IACR,SAAQ,OAAO;AACdN,oBAAAA,MAAc,MAAA,SAAA,gCAAA,uBAAuB,KAAK;AAC1C,aAAO,oBAAoB,KAAK;AAChC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,YAAY,EAAE,UAAU;AAC5B,QAAI;AACFA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,sBAAsB;AAClC,YAAM,WAAW,MAAMY,qBAAqB;AAC5CZ,oBAAAA,mDAAY,yBAAyB,QAAQ;AAE7C,UAAI,YAAY,SAAS,MAAM;AAC7BA,yEAAY,uBAAuB,SAAS,IAAI;AAChD,eAAO,iBAAiB,SAAS,IAAI;AACrC,eAAO,oBAAoB,IAAI;AAC/B,eAAO;AAAA,MACf,OAAa;AACLA,sBAAAA,MAAA,MAAA,QAAA,gCAAa,8BAA8B;AAC3C,eAAO;AAAA,MACR;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,qDAAc,yBAAyB,KAAK;AAE5C,UAAI,MAAM,SAAS,mBAAmB,MAAM,QAAQ,SAAS,KAAK,GAAG;AACnE,eAAO,iBAAiB;AAAA,MACzB;AACD,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,aAAa,EAAE,UAAU;AAC7B,QAAI;AACF,YAAM,WAAW,MAAMa,sBAAsB;AAC7C,aAAO,kBAAkB,SAAS,IAAI;AACtC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdb,oBAAAA,qDAAc,2BAA2B,KAAK;AAC9C,aAAO,kBAAkB;AAAA,QACvB,eAAe;AAAA,QACf,eAAe;AAAA,MACvB,CAAO;AACD,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,eAAe,EAAE,OAAQ,GAAE,UAAU;AACzC,QAAI;AACFA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,2BAA2B,KAAK,UAAU,UAAU,MAAM,CAAC,CAAC;AACxE,YAAM,WAAW,MAAMc,SAAsB,eAAC,QAAQ;AACtDd,oBAAAA,mDAAY,wBAAwB,QAAQ;AAC5C,aAAO,iBAAiB,SAAS,IAAI;AACrCQ,eAAAA,YAAY,QAAQ;AACpB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,oBAAAA,qDAAc,yBAAyB,KAAK;AAC5CS,yBAAU,MAAM,WAAW,MAAM;AACjC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,mBAAmB,EAAE,OAAQ,GAAE,cAAc;AACjD,QAAI;AACF,YAAM,WAAW,MAAMM,SAAsB,eAAC,YAAY;AAC1DP,eAAAA,YAAY,QAAQ;AACpB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,OAAO,EAAE,UAAU;AACvB,QAAI;AACFT,oBAAAA,mDAAY,kBAAkB;AAE9B,YAAMgB,SAAc,OAAA,EAAG,MAAM,SAAO;AAClChB,sBAAAA,MAAa,MAAA,QAAA,gCAAA,qCAAqC,GAAG;AAAA,MAC7D,CAAO;AAAA,IACP,UAAc;AAER,aAAO,iBAAiB;AACxBQ,eAAAA,YAAY,OAAO;AACnBR,oBAAAA,mDAAY,kBAAkB;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,iBAAiB,EAAE,QAAQ,OAAAH,UAAS;AAExC,QAAIA,OAAM,eAAe;AACvBG,oBAAAA,MAAY,MAAA,OAAA,gCAAA,6BAA6B;AACzC,aAAOH,OAAM;AAAA,IACd;AAED,QAAI;AACF,aAAO,sBAAsB,IAAI;AAEjC,YAAM,QAAQF,WAAAA,SAAU;AACxB,YAAM,WAAWC,WAAAA,YAAa;AAC9BI,0BAAY,MAAA,OAAA,gCAAA,+BAA+B,CAAC,CAAC,OAAO,aAAa,CAAC,CAAC,QAAQ;AAG3E,UAAI,CAAC,SAAS,CAAC,UAAU;AACvBA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,0BAA0B;AACtC,eAAO,iBAAiB;AACxB,eAAO;AAAA,MACR;AAGD,UAAI;AACF,cAAM,WAAW,MAAMY,qBAAqB;AAC5CZ,sBAAAA,MAAA,MAAA,OAAA,gCAAY,uBAAuB;AAEnC,YAAI,YAAY,SAAS,MAAM;AAE7B,iBAAO,aAAa,KAAK;AACzB,iBAAO,iBAAiB,SAAS,IAAI;AACrC,iBAAO,oBAAoB,IAAI;AAC/B,iBAAO;AAAA,QACjB,OAAe;AACLA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,2BAA2B;AACvC,iBAAO,iBAAiB;AACxB,iBAAO;AAAA,QACR;AAAA,MACF,SAAQ,OAAO;AACdA,yEAAY,0BAA0B,MAAM,OAAO;AAGnD,YAAI,MAAM,SAAS,mBACf,MAAM,QAAQ,SAAS,OAAO,KAC9B,MAAM,QAAQ,SAAS,KAAK,KAC5B,MAAM,QAAQ,SAAS,OAAO,KAC9B,MAAM,WAAW,KAAK;AACxBA,wBAAAA,mDAAY,yBAAyB;AACrC,iBAAO,iBAAiB;AACxB,iBAAO;AAAA,QACR;AAGDA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,2BAA2B;AACvC,eAAO,aAAa,KAAK;AACzB,eAAO,iBAAiB,QAAQ;AAChC,eAAO,oBAAoB,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,qDAAc,yBAAyB,KAAK;AAC5C,aAAO,iBAAiB;AACxB,aAAO;AAAA,IACb,UAAc;AACR,aAAO,sBAAsB,KAAK;AAAA,IACnC;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB,EAAE,OAAQ,GAAE,YAAY;AACxCA,kBAAAA,MAAY,MAAA,OAAA,gCAAA,yBAAyB,UAAU;AAC/C,WAAO,oBAAoB,UAAU;AAAA,EACtC;AAAA;AAAA,EAGD,cAAc,EAAE,QAAQ,YAAY;AAClCA,kBAAAA,MAAY,MAAA,OAAA,gCAAA,qBAAqB;AACjC,UAAM,QAAQL,WAAAA,SAAU;AACxB,UAAM,WAAWC,WAAAA,YAAa;AAE9B,QAAI,SAAS,UAAU;AACrB,aAAO,aAAa,KAAK;AACzB,aAAO,iBAAiB,QAAQ;AAChC,aAAO,oBAAoB,IAAI;AAC/BI,oBAAAA,MAAA,MAAA,OAAA,gCAAY,yBAAyB;AAAA,IAC3C,OAAW;AACL,aAAO,oBAAoB,KAAK;AAChCA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,qBAAqB;AAAA,IAClC;AAAA,EACF;AACH;AAEA,MAAM,UAAU;AAAA,EACd,YAAY,CAAAH,WAASA,OAAM;AAAA,EAC3B,UAAU,CAAAA,WAASA,OAAM;AAAA,EACzB,WAAW,CAAAA,WAASA,OAAM;AAAA,EAC1B,OAAO,CAAAA,WAASA,OAAM;AAAA,EACtB,QAAQ,CAAAA,WAAK;;AAAI,iBAAAA,OAAM,aAAN,mBAAgB;AAAA;AAAA,EACjC,UAAU,CAAAA,WAAK;;AAAI,iBAAAA,OAAM,aAAN,mBAAgB;AAAA;AAAA,EACnC,UAAU,CAAAA,WAAK;;AAAI,iBAAAA,OAAM,aAAN,mBAAgB;AAAA;AAAA,EACnC,QAAQ,CAAAA,WAAK;;AAAI,iBAAAA,OAAM,aAAN,mBAAgB;AAAA;AAAA,EACjC,OAAO,CAAAA,WAAK;;AAAI,iBAAAA,OAAM,aAAN,mBAAgB;AAAA;AAAA,EAChC,OAAO,CAAAA,WAAK;;AAAI,iBAAAA,OAAM,aAAN,mBAAgB;AAAA;AAAA,EAChC,eAAe,CAAAA,WAASA,OAAM;AAChC;AAEA,MAAe,OAAA;AAAA,EACb,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;"}