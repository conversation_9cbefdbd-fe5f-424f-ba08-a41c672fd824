{"version": 3, "file": "booking.js", "sources": ["store/modules/booking.js"], "sourcesContent": ["import * as bookingApi from '@/api/booking.js'\nimport * as userApi from '@/api/user.js'\nimport * as sharingApi from '@/api/sharing.js'\nimport { showSuccess, showError } from '@/utils/ui.js'\n\nconst state = {\n  bookingList: [],\n  sharingOrders: [],\n  userSharingOrders: [],\n  joinedSharingOrders: [],\n  bookingDetail: null,\n  sharingOrderDetail: null,\n  sharingDetail: null,\n  loading: false,\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: 0\n  }\n}\n\nconst mutations = {\n  SET_BOOKING_LIST(state, { list, pagination }) {\n    state.bookingList = list\n    if (pagination) {\n      state.pagination = { ...state.pagination, ...pagination }\n    }\n  },\n  \n  APPEND_BOOKING_LIST(state, list) {\n    state.bookingList = [...state.bookingList, ...list]\n  },\n  \n  SET_SHARING_ORDERS(state, orders) {\n    state.sharingOrders = orders\n  },\n  \n  SET_USER_SHARING_ORDERS(state, orders) {\n    state.userSharingOrders = orders\n  },\n  \n  SET_JOINED_SHARING_ORDERS(state, orders) {\n    state.joinedSharingOrders = orders\n  },\n  \n  SET_BOOKING_DETAIL(state, booking) {\n    // 只有当booking有效时才设置，避免undefined覆盖现有数据\n    if (booking && typeof booking === 'object') {\n      state.bookingDetail = booking\n    } else {\n      console.warn('⚠️ 收到无效的booking数据:', booking)\n      // 如果数据无效，设置一个默认的空对象，避免模板报错\n      state.bookingDetail = {\n        status: null,\n        orderNo: null,\n        venueName: null,\n        bookingType: null,\n        venueLocation: null,\n        bookingDate: null,\n        startTime: null,\n        endTime: null,\n        totalPrice: null,\n        createdAt: null,\n        venuePhone: null,\n        sharingOrder: null\n      }\n    }\n  },\n  \n  SET_SHARING_ORDER_DETAIL(state, order) {\n    state.sharingOrderDetail = order\n  },\n  \n  SET_SHARING_DETAIL(state, detail) {\n    state.sharingDetail = detail\n  },\n  \n  SET_LOADING(state, loading) {\n    state.loading = loading\n  },\n  \n  SET_PAGINATION(state, pagination) {\n    state.pagination = pagination\n  },\n  \n  UPDATE_BOOKING_STATUS(state, { bookingId, status }) {\n    const booking = state.bookingList.find(b => b.id === bookingId)\n    if (booking) {\n      booking.status = status\n    }\n    if (state.bookingDetail && state.bookingDetail.id === bookingId) {\n      state.bookingDetail.status = status\n    }\n  },\n  \n  UPDATE_SHARING_ORDER_PARTICIPANTS(state, { orderId, currentParticipants }) {\n    const order = state.sharingOrders.find(o => o.id === orderId)\n    if (order) {\n      order.currentParticipants = currentParticipants\n    }\n    if (state.sharingOrderDetail && state.sharingOrderDetail.id === orderId) {\n      state.sharingOrderDetail.currentParticipants = currentParticipants\n    }\n  },\n\n  // 批量更新mutation，用于优化同步性能\n  BATCH_UPDATE(state, updates) {\n    if (updates.bookingList !== undefined) {\n      state.bookingList = updates.bookingList\n    }\n    if (updates.bookingDetail !== undefined) {\n      state.bookingDetail = updates.bookingDetail\n    }\n    if (updates.sharingOrders !== undefined) {\n      state.sharingOrders = updates.sharingOrders\n    }\n    if (updates.userSharingOrders !== undefined) {\n      state.userSharingOrders = updates.userSharingOrders\n    }\n    if (updates.joinedSharingOrders !== undefined) {\n      state.joinedSharingOrders = updates.joinedSharingOrders\n    }\n    if (updates.sharingDetail !== undefined) {\n      state.sharingDetail = updates.sharingDetail\n    }\n    if (updates.loading !== undefined) {\n      state.loading = updates.loading\n    }\n    if (updates.pagination !== undefined) {\n      state.pagination = { ...state.pagination, ...updates.pagination }\n    }\n  }\n}\n\nconst actions = {\n  // 创建预约\n  async createBooking({ commit }, bookingData) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await bookingApi.createBooking(bookingData)\n      showSuccess('预约成功')\n      return response\n    } catch (error) {\n      showError(error.message || '预约失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n\n  // 创建拼场预约\n  async createSharedBooking({ commit }, bookingData) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await bookingApi.createSharedBooking(bookingData)\n      showSuccess('拼场预约成功')\n      return response\n    } catch (error) {\n      showError(error.message || '拼场预约失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取用户预约列表\n  async getUserBookings({ commit }, params = {}) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await userApi.getUserBookings(params)\n      \n      console.log('API响应原始数据:', response)\n      console.log('response.data:', response.data)\n      console.log('response.data类型:', typeof response.data)\n      \n      const { data, total, page, pageSize, totalPages } = response\n      \n      console.log('解构后的数据:')\n      console.log('data:', data)\n      console.log('data类型:', typeof data)\n      console.log('data是否为数组:', Array.isArray(data))\n      console.log('total:', total)\n      console.log('page:', page)\n      console.log('pageSize:', pageSize)\n      console.log('totalPages:', totalPages)\n      \n      const pagination = {\n        current: page,\n        pageSize: pageSize,\n        total: total,\n        totalPages: totalPages,\n        currentPage: page\n      }\n      \n      if (params.page === 1 || params.refresh) {\n        console.log('设置新的预约列表，数据长度:', (data || []).length)\n        commit('SET_BOOKING_LIST', { list: data || [], pagination: pagination })\n      } else {\n        console.log('追加预约列表，新增数据长度:', (data || []).length)\n        commit('APPEND_BOOKING_LIST', data || [])\n        commit('SET_PAGINATION', pagination)\n      }\n      \n      return response\n    } catch (error) {\n      console.error('获取用户预约列表失败:', error)\n      // 清空列表并重置分页\n      commit('SET_BOOKING_LIST', { list: [], pagination: { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 } })\n      showError(error.message || '获取预约列表失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取预约详情\n  async getBookingDetail({ commit }, bookingId) {\n    try {\n      commit('SET_LOADING', true)\n      console.log('🌐 发起API请求获取订单详情, ID:', bookingId)\n      console.log('🌐 ID类型:', typeof bookingId)\n      \n      if (!bookingId) {\n        throw new Error('订单ID不能为空')\n      }\n      \n      const response = await bookingApi.getBookingDetail(bookingId)\n      console.log('📡 完整API响应:', response)\n      console.log('📡 响应类型:', typeof response)\n      console.log('📡 响应是否为空:', !response)\n      \n      // 处理不同的响应数据结构\n      let bookingData = null\n      if (response && typeof response === 'object') {\n        // 如果response直接是数据对象\n        if (response.id || response.orderNo) {\n          bookingData = response\n          console.log('📡 使用response作为数据')\n        }\n        // 如果response有data属性\n        else if (response.data) {\n          bookingData = response.data\n          console.log('📡 使用response.data作为数据')\n        }\n        // 如果response有result属性\n        else if (response.result) {\n          bookingData = response.result\n          console.log('📡 使用response.result作为数据')\n        }\n        else {\n          console.warn('📡 响应数据结构未知:', Object.keys(response))\n          // 尝试直接使用response\n          bookingData = response\n        }\n      } else {\n        console.error('📡 响应数据无效:', response)\n        throw new Error('服务器返回的数据格式不正确')\n      }\n      \n      console.log('📡 处理后的订单数据:', bookingData)\n      console.log('📡 数据类型:', typeof bookingData)\n      console.log('📡 数据键:', bookingData ? Object.keys(bookingData) : 'null')\n      console.log('⏰ API返回的开始时间:', bookingData?.startTime)\n      console.log('⏰ API返回的结束时间:', bookingData?.endTime)\n      console.log('💰 API返回的总价格:', bookingData?.totalPrice)\n      console.log('🏷️ API返回的订单号(orderNo):', bookingData?.orderNo)\n      console.log('🏷️ API返回的订单号(orderNumber):', bookingData?.orderNumber)\n      console.log('🆔 API返回的ID:', bookingData?.id)\n      \n      if (!bookingData) {\n        throw new Error('未能获取到有效的订单数据')\n      }\n      \n      // 字段映射：如果后端返回的是orderNumber，映射为orderNo\n      if (bookingData.orderNumber && !bookingData.orderNo) {\n        bookingData.orderNo = bookingData.orderNumber\n        console.log('🔄 字段映射: orderNumber -> orderNo:', bookingData.orderNo)\n      }\n      \n      commit('SET_BOOKING_DETAIL', bookingData)\n      console.log('✅ 数据已存储到store')\n      return response\n    } catch (error) {\n      console.error('❌ API请求失败:', error)\n      console.error('❌ 错误类型:', error.constructor.name)\n      console.error('❌ 错误消息:', error.message)\n      console.error('❌ 错误堆栈:', error.stack)\n      \n      // 清空详情数据\n      commit('SET_BOOKING_DETAIL', null)\n      \n      showError(error.message || '获取预约详情失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 取消预约\n  async cancelBooking({ commit, dispatch }, bookingId) {\n    try {\n      const response = await bookingApi.cancelBooking(bookingId)\n      // 立即更新本地状态\n      commit('UPDATE_BOOKING_STATUS', { bookingId, status: 'CANCELLED' })\n      \n      // 延迟重新获取数据以确保服务器状态同步\n      setTimeout(() => {\n        dispatch('getUserBookings', { page: 1, pageSize: 10, refresh: true })\n      }, 1000)\n      \n      showSuccess('预约已取消')\n      return response\n    } catch (error) {\n      showError(error.message || '取消预约失败')\n      throw error\n    }\n  },\n  \n  // 申请拼场\n  async createSharingOrder({ commit }, { orderId, data }) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.applySharedBooking(orderId, data)\n      showSuccess('拼场申请已发送')\n      return response\n    } catch (error) {\n      showError(error.message || '申请拼场失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取可拼场的订单列表\n  async getSharingOrders({ commit, state }, params = {}) {\n    try {\n      commit('SET_LOADING', true)\n      console.log('开始获取拼场订单，参数:', params)\n      const response = await sharingApi.getJoinableSharingOrders(params)\n      console.log('拼场订单API响应:', response)\n      \n      if (response && (response.list || response.data)) {\n        // 处理两种可能的响应格式：直接返回数据 或 包装在data中\n        const responseData = response.list ? response : response.data\n        const { list, pagination } = responseData\n        console.log('拼场订单列表:', list, '分页信息:', pagination)\n        \n        if (list && Array.isArray(list)) {\n          if (params.page === 1 || params.refresh) {\n            commit('SET_SHARING_ORDERS', list)\n          } else {\n            // 加载更多时追加数据\n            const currentList = state.sharingOrders || []\n            commit('SET_SHARING_ORDERS', [...currentList, ...list])\n          }\n          \n          if (pagination) {\n            commit('SET_PAGINATION', pagination)\n          }\n          \n          console.log('成功设置拼场订单数据，数量:', list.length)\n        } else {\n          console.warn('拼场订单列表为空或格式不正确')\n          commit('SET_SHARING_ORDERS', [])\n        }\n      } else {\n        console.warn('拼场订单API响应数据为空')\n        commit('SET_SHARING_ORDERS', [])\n      }\n      \n      return response\n    } catch (error) {\n      console.error('获取拼场订单失败:', error)\n      showError(error.message || '获取拼场订单失败')\n      commit('SET_SHARING_ORDERS', [])\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n\n  // 创建拼场订单\n  async createSharingOrder({ commit }, sharingData) {\n    try {\n      const response = await sharingApi.createSharingOrder(sharingData)\n      showSuccess('拼场订单创建成功')\n      return response.data\n    } catch (error) {\n      showError(error.message || '创建拼场订单失败')\n      throw error\n    }\n  },\n\n  // 获取拼场订单详情\n  async getSharingOrderDetail({ commit }, orderId) {\n    try {\n      const response = await sharingApi.getSharingOrderById(orderId)\n      return response.data\n    } catch (error) {\n      showError(error.message || '获取拼场订单详情失败')\n      throw error\n    }\n  },\n\n  // 加入拼场订单\n  async joinSharingOrder({ commit }, orderId) {\n    try {\n      const response = await sharingApi.joinSharingOrder(orderId)\n      showSuccess('加入拼场成功')\n      return response.data\n    } catch (error) {\n      showError(error.message || '加入拼场失败')\n      throw error\n    }\n  },\n\n  // 获取我创建的拼场订单\n  async getMyCreatedSharingOrders({ commit }) {\n    try {\n      const response = await sharingApi.getMyCreatedSharingOrders()\n      return response.data\n    } catch (error) {\n      showError(error.message || '获取我创建的拼场订单失败')\n      throw error\n    }\n  },\n  \n  // 创建预约\n  async createBooking({ commit }, bookingData) {\n    try {\n      commit('SET_LOADING', true)\n      console.log('发起预约创建请求，数据:', bookingData)\n      const response = await bookingApi.createBooking(bookingData)\n      console.log('预约创建API响应:', response)\n      \n      // 返回响应数据，确保包含订单ID\n      return response.data || response\n    } catch (error) {\n      console.error('创建预约失败:', error)\n      showError(error.message || '创建预约失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 处理拼场申请\n  async handleSharingRequest({ commit }, { requestId, data }) {\n    try {\n      const response = await sharingApi.handleSharedRequest(requestId, data)\n      showSuccess(data.status === 'APPROVED' ? '已同意拼场申请' : '已拒绝拼场申请')\n      return response\n    } catch (error) {\n      showError(error.message || '处理拼场申请失败')\n      throw error\n    }\n  },\n  \n  // 获取我发出的拼场申请\n  async getUserSharingOrders({ commit }, params = {}) {\n    try {\n      const response = await sharingApi.getMySharedRequests(params)\n      commit('SET_USER_SHARING_ORDERS', response.data)\n      return response\n    } catch (error) {\n      showError(error.message || '获取拼场申请失败')\n      throw error\n    }\n  },\n\n  // 获取我收到的拼场申请\n  async getUserJoinedSharingOrders({ commit }, params = {}) {\n    try {\n      const response = await sharingApi.getReceivedSharedRequests(params)\n      commit('SET_JOINED_SHARING_ORDERS', response.data)\n      return response\n    } catch (error) {\n      showError(error.message || '获取拼场申请失败')\n      throw error\n    }\n  },\n  \n  // 获取拼场详情\n  async getSharingDetail({ commit }, sharingId) {\n    try {\n      commit('SET_LOADING', true)\n      const response = await sharingApi.getSharingOrderById(sharingId)\n      commit('SET_SHARING_DETAIL', response.data)\n      return response\n    } catch (error) {\n      showError(error.message || '获取拼场详情失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 移除拼场参与者\n  async removeSharingParticipant({ commit, dispatch, state }, { sharingId, participantId }) {\n    try {\n      commit('SET_LOADING', true)\n      // 调用API移除参与者\n      const response = await sharingApi.removeSharingParticipant(sharingId, participantId)\n      \n      // 重新获取拼场详情以确保数据同步\n      await dispatch('getSharingDetail', sharingId)\n      \n      return response\n    } catch (error) {\n      showError(error.message || '移除参与者失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 更新拼场设置\n  async updateSharingSettings({ commit, dispatch, state }, { sharingId, settings }) {\n    try {\n      commit('SET_LOADING', true)\n      // 调用API更新设置\n      const response = await sharingApi.updateSharingSettings(sharingId, settings)\n      \n      // 重新获取拼场详情以确保数据同步\n      await dispatch('getSharingDetail', sharingId)\n      \n      return response\n    } catch (error) {\n      showError(error.message || '更新设置失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  }\n}\n\nconst getters = {\n  bookingList: state => Array.isArray(state.bookingList) ? state.bookingList : [],\n  sharingOrders: state => Array.isArray(state.sharingOrders) ? state.sharingOrders : [],\n  userSharingOrders: state => Array.isArray(state.userSharingOrders) ? state.userSharingOrders : [],\n  joinedSharingOrders: state => Array.isArray(state.joinedSharingOrders) ? state.joinedSharingOrders : [],\n  bookingDetail: state => state.bookingDetail,\n  sharingOrderDetail: state => state.sharingOrderDetail,\n  sharingDetail: state => state.sharingDetail,\n  loading: state => state.loading,\n  pagination: state => state.pagination,\n  \n  // 根据状态筛选预约\n  activeBookings: state => (state.bookingList || []).filter(booking => \n    booking && ['PENDING', 'CONFIRMED'].includes(booking.status)\n  ),\n  completedBookings: state => (state.bookingList || []).filter(booking => \n    booking && booking.status === 'COMPLETED'\n  ),\n  cancelledBookings: state => (state.bookingList || []).filter(booking => \n    booking && booking.status === 'CANCELLED'\n  ),\n  \n  // 可加入的拼场订单\n  availableSharingOrders: state => (state.sharingOrders || []).filter(order => \n    order && order.status === 'OPEN' && order.currentParticipants < order.maxParticipants\n  )\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n}"], "names": ["state", "booking", "uni", "bookingApi.createBooking", "showSuccess", "showError", "bookingApi.createSharedBooking", "userApi.getUserBookings", "bookingApi.getBookingDetail", "bookingApi.cancelBooking", "sharingApi.applySharedBooking", "sharingApi.getJoinableSharingOrders", "sharingApi.createSharingOrder", "sharingApi.getSharingOrderById", "sharingApi.joinSharingOrder", "sharingApi.getMyCreatedSharingOrders", "sharingApi.handleSharedRequest", "sharingApi.getMySharedRequests", "sharingApi.getReceivedSharedRequests", "sharingApi.removeSharingParticipant", "sharingApi.updateSharingSettings"], "mappings": ";;;;;;AAKA,MAAM,QAAQ;AAAA,EACZ,aAAa,CAAE;AAAA,EACf,eAAe,CAAE;AAAA,EACjB,mBAAmB,CAAE;AAAA,EACrB,qBAAqB,CAAE;AAAA,EACvB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,YAAY;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACR;AACH;AAEA,MAAM,YAAY;AAAA,EAChB,iBAAiBA,QAAO,EAAE,MAAM,WAAU,GAAI;AAC5C,IAAAA,OAAM,cAAc;AACpB,QAAI,YAAY;AACd,MAAAA,OAAM,aAAa,EAAE,GAAGA,OAAM,YAAY,GAAG,WAAY;AAAA,IAC1D;AAAA,EACF;AAAA,EAED,oBAAoBA,QAAO,MAAM;AAC/B,IAAAA,OAAM,cAAc,CAAC,GAAGA,OAAM,aAAa,GAAG,IAAI;AAAA,EACnD;AAAA,EAED,mBAAmBA,QAAO,QAAQ;AAChC,IAAAA,OAAM,gBAAgB;AAAA,EACvB;AAAA,EAED,wBAAwBA,QAAO,QAAQ;AACrC,IAAAA,OAAM,oBAAoB;AAAA,EAC3B;AAAA,EAED,0BAA0BA,QAAO,QAAQ;AACvC,IAAAA,OAAM,sBAAsB;AAAA,EAC7B;AAAA,EAED,mBAAmBA,QAAOC,UAAS;AAEjC,QAAIA,YAAW,OAAOA,aAAY,UAAU;AAC1C,MAAAD,OAAM,gBAAgBC;AAAA,IAC5B,OAAW;AACLC,oBAAAA,MAAa,MAAA,QAAA,kCAAA,sBAAsBD,QAAO;AAE1C,MAAAD,OAAM,gBAAgB;AAAA,QACpB,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,cAAc;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EAED,yBAAyBA,QAAO,OAAO;AACrC,IAAAA,OAAM,qBAAqB;AAAA,EAC5B;AAAA,EAED,mBAAmBA,QAAO,QAAQ;AAChC,IAAAA,OAAM,gBAAgB;AAAA,EACvB;AAAA,EAED,YAAYA,QAAO,SAAS;AAC1B,IAAAA,OAAM,UAAU;AAAA,EACjB;AAAA,EAED,eAAeA,QAAO,YAAY;AAChC,IAAAA,OAAM,aAAa;AAAA,EACpB;AAAA,EAED,sBAAsBA,QAAO,EAAE,WAAW,OAAM,GAAI;AAClD,UAAMC,WAAUD,OAAM,YAAY,KAAK,OAAK,EAAE,OAAO,SAAS;AAC9D,QAAIC,UAAS;AACX,MAAAA,SAAQ,SAAS;AAAA,IAClB;AACD,QAAID,OAAM,iBAAiBA,OAAM,cAAc,OAAO,WAAW;AAC/D,MAAAA,OAAM,cAAc,SAAS;AAAA,IAC9B;AAAA,EACF;AAAA,EAED,kCAAkCA,QAAO,EAAE,SAAS,oBAAmB,GAAI;AACzE,UAAM,QAAQA,OAAM,cAAc,KAAK,OAAK,EAAE,OAAO,OAAO;AAC5D,QAAI,OAAO;AACT,YAAM,sBAAsB;AAAA,IAC7B;AACD,QAAIA,OAAM,sBAAsBA,OAAM,mBAAmB,OAAO,SAAS;AACvE,MAAAA,OAAM,mBAAmB,sBAAsB;AAAA,IAChD;AAAA,EACF;AAAA;AAAA,EAGD,aAAaA,QAAO,SAAS;AAC3B,QAAI,QAAQ,gBAAgB,QAAW;AACrC,MAAAA,OAAM,cAAc,QAAQ;AAAA,IAC7B;AACD,QAAI,QAAQ,kBAAkB,QAAW;AACvC,MAAAA,OAAM,gBAAgB,QAAQ;AAAA,IAC/B;AACD,QAAI,QAAQ,kBAAkB,QAAW;AACvC,MAAAA,OAAM,gBAAgB,QAAQ;AAAA,IAC/B;AACD,QAAI,QAAQ,sBAAsB,QAAW;AAC3C,MAAAA,OAAM,oBAAoB,QAAQ;AAAA,IACnC;AACD,QAAI,QAAQ,wBAAwB,QAAW;AAC7C,MAAAA,OAAM,sBAAsB,QAAQ;AAAA,IACrC;AACD,QAAI,QAAQ,kBAAkB,QAAW;AACvC,MAAAA,OAAM,gBAAgB,QAAQ;AAAA,IAC/B;AACD,QAAI,QAAQ,YAAY,QAAW;AACjC,MAAAA,OAAM,UAAU,QAAQ;AAAA,IACzB;AACD,QAAI,QAAQ,eAAe,QAAW;AACpC,MAAAA,OAAM,aAAa,EAAE,GAAGA,OAAM,YAAY,GAAG,QAAQ,WAAY;AAAA,IAClE;AAAA,EACF;AACH;AAEA,MAAM,UAAU;AAAA;AAAA,EAEd,MAAM,cAAc,EAAE,OAAQ,GAAE,aAAa;AAC3C,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMG,YAAwB,cAAC,WAAW;AAC3DC,eAAAA,YAAY,MAAM;AAClB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,MAAM;AACjC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,oBAAoB,EAAE,OAAQ,GAAE,aAAa;AACjD,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMC,YAA8B,oBAAC,WAAW;AACjEF,eAAAA,YAAY,QAAQ;AACpB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,gBAAgB,EAAE,UAAU,SAAS,CAAA,GAAI;AAC7C,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAME,SAAuB,gBAAC,MAAM;AAErDL,oBAAAA,MAAA,MAAA,OAAA,mCAAY,cAAc,QAAQ;AAClCA,0EAAY,kBAAkB,SAAS,IAAI;AAC3CA,oBAAY,MAAA,MAAA,OAAA,mCAAA,oBAAoB,OAAO,SAAS,IAAI;AAEpD,YAAM,EAAE,MAAM,OAAO,MAAM,UAAU,WAAU,IAAK;AAEpDA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,SAAS;AACrBA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,SAAS,IAAI;AACzBA,oBAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,OAAO,IAAI;AAClCA,0EAAY,cAAc,MAAM,QAAQ,IAAI,CAAC;AAC7CA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,UAAU,KAAK;AAC3BA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,SAAS,IAAI;AACzBA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,aAAa,QAAQ;AACjCA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,eAAe,UAAU;AAErC,YAAM,aAAa;AAAA,QACjB,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,MACd;AAED,UAAI,OAAO,SAAS,KAAK,OAAO,SAAS;AACvCA,4EAAY,mBAAmB,QAAQ,CAAE,GAAE,MAAM;AACjD,eAAO,oBAAoB,EAAE,MAAM,QAAQ,IAAI,YAAwB;AAAA,MAC/E,OAAa;AACLA,4EAAY,mBAAmB,QAAQ,CAAE,GAAE,MAAM;AACjD,eAAO,uBAAuB,QAAQ,EAAE;AACxC,eAAO,kBAAkB,UAAU;AAAA,MACpC;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,mCAAA,eAAe,KAAK;AAElC,aAAO,oBAAoB,EAAE,MAAM,CAAE,GAAE,YAAY,EAAE,SAAS,GAAG,UAAU,IAAI,OAAO,GAAG,YAAY,GAAG,aAAa,EAAC,GAAI;AAC1HG,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,iBAAiB,EAAE,OAAQ,GAAE,WAAW;AAC5C,QAAI;AACF,aAAO,eAAe,IAAI;AAC1BH,oBAAAA,MAAA,MAAA,OAAA,mCAAY,yBAAyB,SAAS;AAC9CA,oBAAY,MAAA,MAAA,OAAA,mCAAA,YAAY,OAAO,SAAS;AAExC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,UAAU;AAAA,MAC3B;AAED,YAAM,WAAW,MAAMM,YAA2B,iBAAC,SAAS;AAC5DN,oBAAAA,MAAA,MAAA,OAAA,mCAAY,eAAe,QAAQ;AACnCA,oBAAY,MAAA,MAAA,OAAA,mCAAA,YAAY,OAAO,QAAQ;AACvCA,oBAAA,MAAA,MAAA,OAAA,mCAAY,cAAc,CAAC,QAAQ;AAGnC,UAAI,cAAc;AAClB,UAAI,YAAY,OAAO,aAAa,UAAU;AAE5C,YAAI,SAAS,MAAM,SAAS,SAAS;AACnC,wBAAc;AACdA,wBAAAA,MAAA,MAAA,OAAA,mCAAY,mBAAmB;AAAA,QAChC,WAEQ,SAAS,MAAM;AACtB,wBAAc,SAAS;AACvBA,wBAAAA,sDAAY,wBAAwB;AAAA,QACrC,WAEQ,SAAS,QAAQ;AACxB,wBAAc,SAAS;AACvBA,wBAAAA,MAAA,MAAA,OAAA,mCAAY,0BAA0B;AAAA,QACvC,OACI;AACHA,8BAAA,MAAA,QAAA,mCAAa,gBAAgB,OAAO,KAAK,QAAQ,CAAC;AAElD,wBAAc;AAAA,QACf;AAAA,MACT,OAAa;AACLA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,cAAc,QAAQ;AACpC,cAAM,IAAI,MAAM,eAAe;AAAA,MAChC;AAEDA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,gBAAgB,WAAW;AACvCA,0EAAY,YAAY,OAAO,WAAW;AAC1CA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,cAAc,OAAO,KAAK,WAAW,IAAI,MAAM;AACtEA,oBAAY,MAAA,MAAA,OAAA,mCAAA,iBAAiB,2CAAa,SAAS;AACnDA,oBAAA,MAAA,MAAA,OAAA,mCAAY,iBAAiB,2CAAa,OAAO;AACjDA,oBAAY,MAAA,MAAA,OAAA,mCAAA,iBAAiB,2CAAa,UAAU;AACpDA,0EAAY,2BAA2B,2CAAa,OAAO;AAC3DA,oBAAA,MAAA,MAAA,OAAA,mCAAY,+BAA+B,2CAAa,WAAW;AACnEA,0EAAY,gBAAgB,2CAAa,EAAE;AAE3C,UAAI,CAAC,aAAa;AAChB,cAAM,IAAI,MAAM,cAAc;AAAA,MAC/B;AAGD,UAAI,YAAY,eAAe,CAAC,YAAY,SAAS;AACnD,oBAAY,UAAU,YAAY;AAClCA,sBAAA,MAAA,MAAA,OAAA,mCAAY,oCAAoC,YAAY,OAAO;AAAA,MACpE;AAED,aAAO,sBAAsB,WAAW;AACxCA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,eAAe;AAC3B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,wDAAc,cAAc,KAAK;AACjCA,4EAAc,WAAW,MAAM,YAAY,IAAI;AAC/CA,oBAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,MAAM,OAAO;AACtCA,oBAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,MAAM,KAAK;AAGpC,aAAO,sBAAsB,IAAI;AAEjCG,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,cAAc,EAAE,QAAQ,SAAQ,GAAI,WAAW;AACnD,QAAI;AACF,YAAM,WAAW,MAAMI,YAAwB,cAAC,SAAS;AAEzD,aAAO,yBAAyB,EAAE,WAAW,QAAQ,YAAW,CAAE;AAGlE,iBAAW,MAAM;AACf,iBAAS,mBAAmB,EAAE,MAAM,GAAG,UAAU,IAAI,SAAS,MAAM;AAAA,MACrE,GAAE,GAAI;AAEPL,eAAAA,YAAY,OAAO;AACnB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,mBAAmB,EAAE,OAAM,GAAI,EAAE,SAAS,KAAI,GAAI;AACtD,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMK,+BAA8B,SAAS,IAAI;AAClEN,eAAAA,YAAY,SAAS;AACrB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,iBAAiB,EAAE,QAAQ,OAAAL,OAAK,GAAI,SAAS,CAAA,GAAI;AACrD,QAAI;AACF,aAAO,eAAe,IAAI;AAC1BE,oBAAAA,MAAA,MAAA,OAAA,mCAAY,gBAAgB,MAAM;AAClC,YAAM,WAAW,MAAMS,YAAmC,yBAAC,MAAM;AACjET,oBAAAA,MAAA,MAAA,OAAA,mCAAY,cAAc,QAAQ;AAElC,UAAI,aAAa,SAAS,QAAQ,SAAS,OAAO;AAEhD,cAAM,eAAe,SAAS,OAAO,WAAW,SAAS;AACzD,cAAM,EAAE,MAAM,WAAU,IAAK;AAC7BA,4EAAY,WAAW,MAAM,SAAS,UAAU;AAEhD,YAAI,QAAQ,MAAM,QAAQ,IAAI,GAAG;AAC/B,cAAI,OAAO,SAAS,KAAK,OAAO,SAAS;AACvC,mBAAO,sBAAsB,IAAI;AAAA,UAC7C,OAAiB;AAEL,kBAAM,cAAcF,OAAM,iBAAiB,CAAE;AAC7C,mBAAO,sBAAsB,CAAC,GAAG,aAAa,GAAG,IAAI,CAAC;AAAA,UACvD;AAED,cAAI,YAAY;AACd,mBAAO,kBAAkB,UAAU;AAAA,UACpC;AAEDE,wBAAA,MAAA,MAAA,OAAA,mCAAY,kBAAkB,KAAK,MAAM;AAAA,QACnD,OAAe;AACLA,wBAAAA,MAAa,MAAA,QAAA,mCAAA,gBAAgB;AAC7B,iBAAO,sBAAsB,EAAE;AAAA,QAChC;AAAA,MACT,OAAa;AACLA,sBAAAA,MAAa,MAAA,QAAA,mCAAA,eAAe;AAC5B,eAAO,sBAAsB,EAAE;AAAA,MAChC;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,wDAAc,aAAa,KAAK;AAChCG,yBAAU,MAAM,WAAW,UAAU;AACrC,aAAO,sBAAsB,EAAE;AAC/B,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,mBAAmB,EAAE,OAAQ,GAAE,aAAa;AAChD,QAAI;AACF,YAAM,WAAW,MAAMO,YAA6B,mBAAC,WAAW;AAChER,eAAAA,YAAY,UAAU;AACtB,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,sBAAsB,EAAE,OAAQ,GAAE,SAAS;AAC/C,QAAI;AACF,YAAM,WAAW,MAAMQ,YAA8B,oBAAC,OAAO;AAC7D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,YAAY;AACvC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,iBAAiB,EAAE,OAAQ,GAAE,SAAS;AAC1C,QAAI;AACF,YAAM,WAAW,MAAMS,YAA2B,iBAAC,OAAO;AAC1DV,eAAAA,YAAY,QAAQ;AACpB,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,0BAA0B,EAAE,UAAU;AAC1C,QAAI;AACF,YAAM,WAAW,MAAMU,sCAAsC;AAC7D,aAAO,SAAS;AAAA,IACjB,SAAQ,OAAO;AACdV,yBAAU,MAAM,WAAW,cAAc;AACzC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,cAAc,EAAE,OAAQ,GAAE,aAAa;AAC3C,QAAI;AACF,aAAO,eAAe,IAAI;AAC1BH,oBAAAA,MAAY,MAAA,OAAA,mCAAA,gBAAgB,WAAW;AACvC,YAAM,WAAW,MAAMC,YAAwB,cAAC,WAAW;AAC3DD,oBAAAA,MAAA,MAAA,OAAA,mCAAY,cAAc,QAAQ;AAGlC,aAAO,SAAS,QAAQ;AAAA,IACzB,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,mCAAA,WAAW,KAAK;AAC9BG,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,qBAAqB,EAAE,OAAM,GAAI,EAAE,WAAW,KAAI,GAAI;AAC1D,QAAI;AACF,YAAM,WAAW,MAAMW,gCAA+B,WAAW,IAAI;AACrEZ,eAAAA,YAAY,KAAK,WAAW,aAAa,YAAY,SAAS;AAC9D,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,qBAAqB,EAAE,UAAU,SAAS,CAAA,GAAI;AAClD,QAAI;AACF,YAAM,WAAW,MAAMY,YAA8B,oBAAC,MAAM;AAC5D,aAAO,2BAA2B,SAAS,IAAI;AAC/C,aAAO;AAAA,IACR,SAAQ,OAAO;AACdZ,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,2BAA2B,EAAE,UAAU,SAAS,CAAA,GAAI;AACxD,QAAI;AACF,YAAM,WAAW,MAAMa,YAAoC,0BAAC,MAAM;AAClE,aAAO,6BAA6B,SAAS,IAAI;AACjD,aAAO;AAAA,IACR,SAAQ,OAAO;AACdb,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,iBAAiB,EAAE,OAAQ,GAAE,WAAW;AAC5C,QAAI;AACF,aAAO,eAAe,IAAI;AAC1B,YAAM,WAAW,MAAMQ,YAA8B,oBAAC,SAAS;AAC/D,aAAO,sBAAsB,SAAS,IAAI;AAC1C,aAAO;AAAA,IACR,SAAQ,OAAO;AACdR,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,yBAAyB,EAAE,QAAQ,UAAU,OAAAL,OAAK,GAAI,EAAE,WAAW,iBAAiB;AACxF,QAAI;AACF,aAAO,eAAe,IAAI;AAE1B,YAAM,WAAW,MAAMmB,qCAAoC,WAAW,aAAa;AAGnF,YAAM,SAAS,oBAAoB,SAAS;AAE5C,aAAO;AAAA,IACR,SAAQ,OAAO;AACdd,yBAAU,MAAM,WAAW,SAAS;AACpC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,sBAAsB,EAAE,QAAQ,UAAU,OAAAL,OAAK,GAAI,EAAE,WAAW,YAAY;AAChF,QAAI;AACF,aAAO,eAAe,IAAI;AAE1B,YAAM,WAAW,MAAMoB,kCAAiC,WAAW,QAAQ;AAG3E,YAAM,SAAS,oBAAoB,SAAS;AAE5C,aAAO;AAAA,IACR,SAAQ,OAAO;AACdf,yBAAU,MAAM,WAAW,QAAQ;AACnC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AACH;AAEA,MAAM,UAAU;AAAA,EACd,aAAa,CAAAL,WAAS,MAAM,QAAQA,OAAM,WAAW,IAAIA,OAAM,cAAc,CAAE;AAAA,EAC/E,eAAe,CAAAA,WAAS,MAAM,QAAQA,OAAM,aAAa,IAAIA,OAAM,gBAAgB,CAAE;AAAA,EACrF,mBAAmB,CAAAA,WAAS,MAAM,QAAQA,OAAM,iBAAiB,IAAIA,OAAM,oBAAoB,CAAE;AAAA,EACjG,qBAAqB,CAAAA,WAAS,MAAM,QAAQA,OAAM,mBAAmB,IAAIA,OAAM,sBAAsB,CAAE;AAAA,EACvG,eAAe,CAAAA,WAASA,OAAM;AAAA,EAC9B,oBAAoB,CAAAA,WAASA,OAAM;AAAA,EACnC,eAAe,CAAAA,WAASA,OAAM;AAAA,EAC9B,SAAS,CAAAA,WAASA,OAAM;AAAA,EACxB,YAAY,CAAAA,WAASA,OAAM;AAAA;AAAA,EAG3B,gBAAgB,CAAAA,YAAUA,OAAM,eAAe,CAAA,GAAI;AAAA,IAAO,CAAAC,aACxDA,YAAW,CAAC,WAAW,WAAW,EAAE,SAASA,SAAQ,MAAM;AAAA,EAC5D;AAAA,EACD,mBAAmB,CAAAD,YAAUA,OAAM,eAAe,CAAA,GAAI;AAAA,IAAO,CAAAC,aAC3DA,YAAWA,SAAQ,WAAW;AAAA,EAC/B;AAAA,EACD,mBAAmB,CAAAD,YAAUA,OAAM,eAAe,CAAA,GAAI;AAAA,IAAO,CAAAC,aAC3DA,YAAWA,SAAQ,WAAW;AAAA,EAC/B;AAAA;AAAA,EAGD,wBAAwB,CAAAD,YAAUA,OAAM,iBAAiB,CAAA,GAAI;AAAA,IAAO,WAClE,SAAS,MAAM,WAAW,UAAU,MAAM,sBAAsB,MAAM;AAAA,EACvE;AACH;AAEA,MAAe,UAAA;AAAA,EACb,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;"}