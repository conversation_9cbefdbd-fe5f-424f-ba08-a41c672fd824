{"version": 3, "file": "venue.js", "sources": ["store/modules/venue.js"], "sourcesContent": ["import * as venueApi from '@/api/venue.js'\nimport * as timeslotApi from '@/api/timeslot.js'\nimport { showError } from '@/utils/ui.js'\n\nconst state = {\n  venueList: [],\n  popularVenues: [],\n  venueDetail: null,\n  venueTypes: [],\n  timeSlots: [],\n  searchResults: [],\n  loading: false,\n  pagination: {\n    current: 1,\n    pageSize: 10,\n    total: 0\n  }\n}\n\nconst mutations = {\n  SET_VENUE_LIST(state, { list, pagination }) {\n    state.venueList = list\n    if (pagination) {\n      state.pagination = { ...state.pagination, ...pagination }\n    }\n  },\n  \n  APPEND_VENUE_LIST(state, list) {\n    state.venueList = [...state.venueList, ...list]\n  },\n  \n  SET_POPULAR_VENUES(state, venues) {\n    state.popularVenues = venues\n  },\n  \n  SET_VENUE_DETAIL(state, venue) {\n    state.venueDetail = venue\n  },\n  \n  SET_VENUE_TYPES(state, types) {\n    state.venueTypes = types\n  },\n  \n  SET_TIME_SLOTS(state, slots) {\n    state.timeSlots = slots\n  },\n  \n  SET_SEARCH_RESULTS(state, results) {\n    state.searchResults = results\n  },\n  \n  SET_LOADING(state, loading) {\n    state.loading = loading\n  },\n  \n  UPDATE_TIME_SLOT_STATUS(state, { slotId, status }) {\n    const slot = state.timeSlots.find(s => s.id === slotId)\n    if (slot) {\n      slot.status = status\n    }\n  },\n\n  // 批量更新mutation，用于优化同步性能\n  BATCH_UPDATE(state, updates) {\n    if (updates.venueList !== undefined) {\n      state.venueList = updates.venueList\n    }\n    if (updates.popularVenues !== undefined) {\n      state.popularVenues = updates.popularVenues\n    }\n    if (updates.venueDetail !== undefined) {\n      state.venueDetail = updates.venueDetail\n    }\n    if (updates.venueTypes !== undefined) {\n      state.venueTypes = updates.venueTypes\n    }\n    if (updates.timeSlots !== undefined) {\n      state.timeSlots = updates.timeSlots\n    }\n    if (updates.searchResults !== undefined) {\n      state.searchResults = updates.searchResults\n    }\n    if (updates.loading !== undefined) {\n      state.loading = updates.loading\n    }\n    if (updates.pagination !== undefined) {\n      state.pagination = { ...state.pagination, ...updates.pagination }\n    }\n  }\n}\n\nconst actions = {\n  // 获取场馆列表\n  async getVenueList({ commit, state }, params = {}) {\n    try {\n      console.log('开始获取场馆列表，参数:', params)\n      commit('SET_LOADING', true)\n      \n      // 添加超时处理\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时\n      })\n      \n      const apiPromise = venueApi.getVenueList(params)\n      const response = await Promise.race([apiPromise, timeoutPromise])\n      \n      console.log('场馆API响应:', response)\n      \n      // 处理响应数据\n      let list = []\n      let pagination = {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        totalPages: 1\n      }\n      \n      if (response && response.data) {\n        if (Array.isArray(response.data)) {\n          list = response.data\n          pagination = {\n            current: response.page || params.page || 1,\n            pageSize: response.pageSize || params.pageSize || 10,\n            total: response.total || response.data.length,\n            totalPages: response.totalPages || 1\n          }\n        } else {\n          console.warn('API响应数据格式异常，使用空数组:', response)\n        }\n      } else if (response && Array.isArray(response)) {\n        // 直接返回数组的情况\n        list = response\n        pagination.total = response.length\n      } else {\n        console.warn('API响应为空或格式错误，使用空数组:', response)\n      }\n      \n      console.log('解析的场馆列表:', list)\n      console.log('分页信息:', pagination)\n      \n      if (params.page === 1 || params.refresh) {\n        commit('SET_VENUE_LIST', { list, pagination })\n      } else {\n        commit('APPEND_VENUE_LIST', list)\n        commit('SET_VENUE_LIST', { list: state.venueList, pagination })\n      }\n      \n      return response\n    } catch (error) {\n      console.error('获取场馆列表失败:', error)\n      \n      // 如果是网络错误或超时，提供模拟数据\n      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('请求超时') || error.statusCode === undefined) {\n        console.log('使用模拟场馆数据')\n        const mockData = [\n          {\n            id: 1,\n            name: '示例篮球场',\n            image: 'https://via.placeholder.com/300x200/ff6b35/ffffff?text=篮球场',\n            location: '北京市朝阳区示例路123号',\n            rating: 4.5,\n            price: 120,\n            type: '篮球场',\n            status: 'available',\n            supportSharing: true\n          },\n          {\n            id: 2,\n            name: '示例羽毛球馆',\n            image: 'https://via.placeholder.com/300x200/4CAF50/ffffff?text=羽毛球馆',\n            location: '北京市海淀区示例街456号',\n            rating: 4.3,\n            price: 80,\n            type: '羽毛球',\n            status: 'available',\n            supportSharing: false\n          },\n          {\n            id: 3,\n            name: '示例网球场',\n            image: 'https://via.placeholder.com/300x200/2196F3/ffffff?text=网球场',\n            location: '北京市西城区示例大道789号',\n            rating: 4.7,\n            price: 200,\n            type: '网球',\n            status: 'maintenance',\n            supportSharing: true\n          }\n        ]\n        \n        const mockPagination = {\n          current: 1,\n          pageSize: 10,\n          total: mockData.length,\n          totalPages: 1\n        }\n        \n        commit('SET_VENUE_LIST', { list: mockData, pagination: mockPagination })\n        console.log('已设置模拟场馆数据:', mockData)\n        return { data: mockData }\n      }\n      \n      showError(error.message || '获取场馆列表失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取热门场馆\n  async getPopularVenues({ commit }) {\n    try {\n      console.log('开始获取热门场馆...')\n      const response = await venueApi.getPopularVenues()\n      console.log('热门场馆API响应:', response)\n      \n      // 处理两种可能的响应格式：直接返回数组 或 包装在data中\n      let venues = []\n      if (Array.isArray(response)) {\n        venues = response\n      } else if (response && Array.isArray(response.data)) {\n        venues = response.data\n      } else {\n        console.warn('热门场馆API响应格式异常:', response)\n      }\n      \n      commit('SET_POPULAR_VENUES', venues)\n      console.log('热门场馆数据已设置:', venues)\n      return response\n    } catch (error) {\n      console.error('获取热门场馆失败:', error)\n      commit('SET_POPULAR_VENUES', [])\n      showError(error.message || '获取热门场馆失败')\n      throw error\n    }\n  },\n  \n  // 获取场馆详情\n  async getVenueDetail({ commit }, venueId) {\n    try {\n      console.log('开始获取场馆详情，ID:', venueId)\n      commit('SET_LOADING', true)\n      \n      // 添加超时处理\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => reject(new Error('获取详情超时')), 10000) // 10秒超时\n      })\n      \n      const apiPromise = venueApi.getVenueDetail(venueId)\n      const response = await Promise.race([apiPromise, timeoutPromise])\n      console.log('场馆详情API响应:', response)\n      \n      // 处理响应数据\n      let venueDetail = null\n      if (response && response.data) {\n        venueDetail = response.data\n      } else if (response && !response.data) {\n        // 如果响应直接是数据对象\n        venueDetail = response\n      }\n      \n      if (venueDetail) {\n        // 数据转换：将后端数据格式转换为前端期望的格式\n        const transformedDetail = {\n          ...venueDetail,\n          // 转换营业时间格式\n          openingHours: venueDetail.openingHours || (venueDetail.openTime && venueDetail.closeTime ? \n            `${venueDetail.openTime.substring(0, 5)} - ${venueDetail.closeTime.substring(0, 5)}` : '08:00 - 22:00'),\n          // 转换设施格式\n          facilities: venueDetail.facilities ? \n            (Array.isArray(venueDetail.facilities) ? venueDetail.facilities : \n             venueDetail.facilities.split(',').map((facility, index) => ({\n               name: facility.trim(),\n               icon: ['🚪', '🚿', '🗄️', '💧', '🏀', '⚽', '🎾', '🏐'][index] || '🏢'\n             }))) : [],\n          // 确保价格字段存在\n          price: venueDetail.price || 0\n        }\n        \n        commit('SET_VENUE_DETAIL', transformedDetail)\n        console.log('场馆详情已设置:', transformedDetail)\n      } else {\n        console.error('场馆详情数据为空')\n        throw new Error('场馆详情数据为空')\n      }\n      \n      return response\n    } catch (error) {\n      console.error('获取场馆详情失败:', error)\n      \n      // 如果是网络错误、超时或后端服务未启动，提供模拟数据\n      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('获取详情超时') || error.statusCode === undefined) {\n        console.log('使用模拟数据')\n        const mockData = {\n          id: venueId,\n          name: '示例体育馆',\n          images: [\n            'https://via.placeholder.com/400x300/ff6b35/ffffff?text=场馆图片1',\n            'https://via.placeholder.com/400x300/4CAF50/ffffff?text=场馆图片2'\n          ],\n          rating: 4.5,\n          reviewCount: 128,\n          location: '北京市朝阳区示例路123号',\n          distance: 2.5,\n          price: 120,\n          tags: ['室内', '空调', '停车场', 'WiFi'],\n          description: '这是一个现代化的体育馆，设施齐全，环境优美。',\n          facilities: [\n            { name: '更衣室', icon: '🚪' },\n            { name: '淋浴间', icon: '🚿' },\n            { name: '储物柜', icon: '🗄️' },\n            { name: '饮水机', icon: '💧' }\n          ],\n          openTime: '06:00',\n          closeTime: '22:00',\n          openingHours: '06:00 - 22:00',\n          phone: '010-12345678'\n        }\n        commit('SET_VENUE_DETAIL', mockData)\n        console.log('已设置模拟场馆数据:', mockData)\n        return { data: mockData }\n      }\n      \n      showError(error.message || '获取场馆详情失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取场馆时间段\n  async getVenueTimeSlots({ commit }, { venueId, date }) {\n    try {\n      console.log('开始获取场馆时间段，venueId:', venueId, 'date:', date)\n      commit('SET_LOADING', true)\n      \n      // 添加超时处理\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => reject(new Error('获取时间段超时')), 8000) // 8秒超时\n      })\n      \n      // 添加时间戳参数强制刷新，避免缓存问题\n      const timestamp = Date.now()\n      const apiPromise = venueApi.getVenueTimeSlots(venueId, date, { _t: timestamp })\n      const response = await Promise.race([apiPromise, timeoutPromise])\n      \n      console.log('时间段API响应:', response)\n      \n      let timeSlots = []\n      if (response && response.data && response.data.slots && Array.isArray(response.data.slots)) {\n        timeSlots = response.data.slots\n      } else if (response && response.slots && Array.isArray(response.slots)) {\n        timeSlots = response.slots\n      } else if (response && response.data && Array.isArray(response.data)) {\n        timeSlots = response.data\n      } else if (response && Array.isArray(response)) {\n        timeSlots = response\n      } else {\n        console.warn('时间段API响应格式异常:', response)\n        timeSlots = []\n      }\n      \n      // 如果没有时间段，尝试生成时间段\n      if (timeSlots.length === 0) {\n        console.log('没有找到时间段，尝试生成时间段')\n        try {\n          const generateResponse = await timeslotApi.generateTimeSlots(venueId, date)\n          console.log('生成时间段响应:', generateResponse)\n          \n          // 重新获取时间段\n          const retryResponse = await venueApi.getVenueTimeSlots(venueId, date)\n          console.log('重新获取时间段响应:', retryResponse)\n          \n          if (retryResponse && retryResponse.data && Array.isArray(retryResponse.data)) {\n            timeSlots = retryResponse.data\n          } else if (retryResponse && Array.isArray(retryResponse)) {\n            timeSlots = retryResponse\n          }\n        } catch (generateError) {\n          console.error('生成时间段失败:', generateError)\n        }\n      }\n      \n      // 确保时间段数据格式正确并过滤过期时间段\n      const now = new Date()\n      const currentDate = now.toISOString().split('T')[0] // 获取当前日期 YYYY-MM-DD\n      const currentTime = now.getHours() * 60 + now.getMinutes() // 当前时间转换为分钟\n      \n      timeSlots = timeSlots.map(slot => {\n        let processedSlot = slot\n        \n        // 如果后端返回的是 available 字段，转换为 status 字段\n        if (slot.available !== undefined && slot.status === undefined) {\n          processedSlot = {\n            ...slot,\n            status: slot.available ? 'AVAILABLE' : 'OCCUPIED'\n          }\n        }\n        \n        // 检查时间段是否已过期（仅对当天的时间段进行检查）\n        if (date === currentDate && processedSlot.status === 'AVAILABLE') {\n          const slotStartTime = processedSlot.startTime.split(':').map(Number)\n          const slotStartMinutes = slotStartTime[0] * 60 + slotStartTime[1]\n          \n          // 如果时间段开始时间已过，将状态设置为已过期\n          if (slotStartMinutes <= currentTime) {\n            processedSlot = {\n              ...processedSlot,\n              status: 'EXPIRED',\n              originalStatus: slot.status || (slot.available ? 'AVAILABLE' : 'OCCUPIED')\n            }\n          }\n        }\n        \n        return processedSlot\n      })\n      \n      console.log('转换后的时间段数据:', timeSlots)\n      \n      commit('SET_TIME_SLOTS', timeSlots)\n      console.log('时间段已设置:', timeSlots)\n      return response\n    } catch (error) {\n      console.error('获取时间段失败:', error)\n      \n      // 如果是网络错误或超时，提供模拟数据\n      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('获取时间段超时') || error.statusCode === undefined) {\n        console.log('使用模拟时间段数据')\n        const mockTimeSlots = [\n          { id: 1, startTime: '09:00', endTime: '10:00', price: 120, status: 'AVAILABLE' },\n          { id: 2, startTime: '10:00', endTime: '11:00', price: 120, status: 'OCCUPIED' },\n          { id: 3, startTime: '11:00', endTime: '12:00', price: 120, status: 'AVAILABLE' },\n          { id: 4, startTime: '14:00', endTime: '15:00', price: 120, status: 'AVAILABLE' },\n          { id: 5, startTime: '15:00', endTime: '16:00', price: 120, status: 'AVAILABLE' },\n          { id: 6, startTime: '16:00', endTime: '17:00', price: 120, status: 'OCCUPIED' },\n          { id: 7, startTime: '17:00', endTime: '18:00', price: 120, status: 'MAINTENANCE' },\n          { id: 8, startTime: '19:00', endTime: '20:00', price: 150, status: 'AVAILABLE' },\n          { id: 9, startTime: '20:00', endTime: '21:00', price: 150, status: 'AVAILABLE' }\n        ]\n        commit('SET_TIME_SLOTS', mockTimeSlots)\n        console.log('已设置模拟时间段数据:', mockTimeSlots)\n        return { data: mockTimeSlots }\n      }\n      \n      showError(error.message || '获取时间段失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 获取场馆类型\n  async getVenueTypes({ commit }) {\n    try {\n      console.log('开始获取场馆类型...')\n      \n      // 添加超时处理\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => reject(new Error('请求超时')), 8000) // 8秒超时\n      })\n      \n      const apiPromise = venueApi.getVenueTypes()\n      const response = await Promise.race([apiPromise, timeoutPromise])\n      \n      console.log('场馆类型API响应:', response)\n      \n      let types = []\n      if (response && response.data && Array.isArray(response.data)) {\n        types = response.data\n      } else if (response && Array.isArray(response)) {\n        types = response\n      } else {\n        console.warn('场馆类型API响应格式异常:', response)\n      }\n      \n      commit('SET_VENUE_TYPES', types)\n      console.log('场馆类型已设置:', types)\n      return response\n    } catch (error) {\n      console.error('获取场馆类型失败:', error)\n      \n      // 如果是网络错误或超时，提供模拟数据\n      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('请求超时') || error.statusCode === undefined) {\n        console.log('使用模拟场馆类型数据')\n        const mockTypes = [\n          { id: 1, name: '篮球场' },\n          { id: 2, name: '羽毛球' },\n          { id: 3, name: '网球' },\n          { id: 4, name: '乒乓球' },\n          { id: 5, name: '足球场' }\n        ]\n        commit('SET_VENUE_TYPES', mockTypes)\n        console.log('已设置模拟场馆类型数据:', mockTypes)\n        return { data: mockTypes }\n      }\n      \n      showError(error.message || '获取场馆类型失败')\n      throw error\n    }\n  },\n  \n  // 搜索场馆\n  async searchVenues({ commit }, params) {\n    try {\n      console.log('开始搜索场馆，参数:', params)\n      commit('SET_LOADING', true)\n      \n      // 添加超时处理\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => reject(new Error('搜索超时')), 8000) // 8秒超时\n      })\n      \n      const apiPromise = venueApi.searchVenues(params)\n      const response = await Promise.race([apiPromise, timeoutPromise])\n      \n      console.log('搜索API响应:', response)\n      \n      let results = []\n      if (response && response.data && Array.isArray(response.data)) {\n        results = response.data\n      } else if (response && Array.isArray(response)) {\n        results = response\n      } else {\n        console.warn('搜索API响应格式异常:', response)\n      }\n      \n      commit('SET_SEARCH_RESULTS', results)\n      console.log('搜索结果已设置:', results)\n      return response\n    } catch (error) {\n      console.error('搜索场馆失败:', error)\n      \n      // 如果是网络错误或超时，返回空结果\n      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('搜索超时') || error.statusCode === undefined) {\n        console.log('搜索超时或网络错误，返回空结果')\n        commit('SET_SEARCH_RESULTS', [])\n        return { data: [] }\n      }\n      \n      showError(error.message || '搜索失败')\n      throw error\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  \n  // 更新时间段状态\n  updateTimeSlotStatus({ commit }, { slotId, status }) {\n    commit('UPDATE_TIME_SLOT_STATUS', { slotId, status })\n  },\n  \n  // getTimeSlots 别名，指向 getVenueTimeSlots\n  getTimeSlots({ dispatch }, params) {\n    return dispatch('getVenueTimeSlots', params)\n  }\n}\n\nconst getters = {\n  venueList: state => Array.isArray(state.venueList) ? state.venueList : [],\n  popularVenues: state => Array.isArray(state.popularVenues) ? state.popularVenues : [],\n  venueDetail: state => state.venueDetail,\n  venueTypes: state => Array.isArray(state.venueTypes) ? state.venueTypes : [],\n  timeSlots: state => Array.isArray(state.timeSlots) ? state.timeSlots : [],\n  searchResults: state => Array.isArray(state.searchResults) ? state.searchResults : [],\n  loading: state => state.loading,\n  pagination: state => state.pagination,\n  \n  // 根据状态筛选时间段\n  availableTimeSlots: state => {\n    const slots = Array.isArray(state.timeSlots) ? state.timeSlots : []\n    return slots.filter(slot => slot.status === 'AVAILABLE')\n  },\n  occupiedTimeSlots: state => {\n    const slots = Array.isArray(state.timeSlots) ? state.timeSlots : []\n    return slots.filter(slot => slot.status === 'OCCUPIED')\n  },\n  maintenanceTimeSlots: state => {\n    const slots = Array.isArray(state.timeSlots) ? state.timeSlots : []\n    return slots.filter(slot => slot.status === 'MAINTENANCE')\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n}"], "names": ["state", "venue", "uni", "venueApi.getVenueList", "showError", "venueApi.getPopularVenues", "venueApi.getVenueDetail", "venueApi.getVenueTimeSlots", "timeslotApi.generateTimeSlots", "venueApi.getVenueTypes", "venueApi.searchVenues"], "mappings": ";;;;;AAIA,MAAM,QAAQ;AAAA,EACZ,WAAW,CAAE;AAAA,EACb,eAAe,CAAE;AAAA,EACjB,aAAa;AAAA,EACb,YAAY,CAAE;AAAA,EACd,WAAW,CAAE;AAAA,EACb,eAAe,CAAE;AAAA,EACjB,SAAS;AAAA,EACT,YAAY;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACR;AACH;AAEA,MAAM,YAAY;AAAA,EAChB,eAAeA,QAAO,EAAE,MAAM,WAAU,GAAI;AAC1C,IAAAA,OAAM,YAAY;AAClB,QAAI,YAAY;AACd,MAAAA,OAAM,aAAa,EAAE,GAAGA,OAAM,YAAY,GAAG,WAAY;AAAA,IAC1D;AAAA,EACF;AAAA,EAED,kBAAkBA,QAAO,MAAM;AAC7B,IAAAA,OAAM,YAAY,CAAC,GAAGA,OAAM,WAAW,GAAG,IAAI;AAAA,EAC/C;AAAA,EAED,mBAAmBA,QAAO,QAAQ;AAChC,IAAAA,OAAM,gBAAgB;AAAA,EACvB;AAAA,EAED,iBAAiBA,QAAOC,QAAO;AAC7B,IAAAD,OAAM,cAAcC;AAAA,EACrB;AAAA,EAED,gBAAgBD,QAAO,OAAO;AAC5B,IAAAA,OAAM,aAAa;AAAA,EACpB;AAAA,EAED,eAAeA,QAAO,OAAO;AAC3B,IAAAA,OAAM,YAAY;AAAA,EACnB;AAAA,EAED,mBAAmBA,QAAO,SAAS;AACjC,IAAAA,OAAM,gBAAgB;AAAA,EACvB;AAAA,EAED,YAAYA,QAAO,SAAS;AAC1B,IAAAA,OAAM,UAAU;AAAA,EACjB;AAAA,EAED,wBAAwBA,QAAO,EAAE,QAAQ,OAAM,GAAI;AACjD,UAAM,OAAOA,OAAM,UAAU,KAAK,OAAK,EAAE,OAAO,MAAM;AACtD,QAAI,MAAM;AACR,WAAK,SAAS;AAAA,IACf;AAAA,EACF;AAAA;AAAA,EAGD,aAAaA,QAAO,SAAS;AAC3B,QAAI,QAAQ,cAAc,QAAW;AACnC,MAAAA,OAAM,YAAY,QAAQ;AAAA,IAC3B;AACD,QAAI,QAAQ,kBAAkB,QAAW;AACvC,MAAAA,OAAM,gBAAgB,QAAQ;AAAA,IAC/B;AACD,QAAI,QAAQ,gBAAgB,QAAW;AACrC,MAAAA,OAAM,cAAc,QAAQ;AAAA,IAC7B;AACD,QAAI,QAAQ,eAAe,QAAW;AACpC,MAAAA,OAAM,aAAa,QAAQ;AAAA,IAC5B;AACD,QAAI,QAAQ,cAAc,QAAW;AACnC,MAAAA,OAAM,YAAY,QAAQ;AAAA,IAC3B;AACD,QAAI,QAAQ,kBAAkB,QAAW;AACvC,MAAAA,OAAM,gBAAgB,QAAQ;AAAA,IAC/B;AACD,QAAI,QAAQ,YAAY,QAAW;AACjC,MAAAA,OAAM,UAAU,QAAQ;AAAA,IACzB;AACD,QAAI,QAAQ,eAAe,QAAW;AACpC,MAAAA,OAAM,aAAa,EAAE,GAAGA,OAAM,YAAY,GAAG,QAAQ,WAAY;AAAA,IAClE;AAAA,EACF;AACH;AAEA,MAAM,UAAU;AAAA;AAAA,EAEd,MAAM,aAAa,EAAE,QAAQ,OAAAA,OAAK,GAAI,SAAS,CAAA,GAAI;AACjD,QAAI;AACFE,oBAAAA,MAAA,MAAA,OAAA,gCAAY,gBAAgB,MAAM;AAClC,aAAO,eAAe,IAAI;AAG1B,YAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,mBAAW,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,GAAG,GAAK;AAAA,MACzD,CAAO;AAED,YAAM,aAAaC,UAAqB,aAAC,MAAM;AAC/C,YAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,YAAY,cAAc,CAAC;AAEhED,oBAAAA,MAAY,MAAA,OAAA,iCAAA,YAAY,QAAQ;AAGhC,UAAI,OAAO,CAAE;AACb,UAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,YAAY;AAAA,MACb;AAED,UAAI,YAAY,SAAS,MAAM;AAC7B,YAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AAChC,iBAAO,SAAS;AAChB,uBAAa;AAAA,YACX,SAAS,SAAS,QAAQ,OAAO,QAAQ;AAAA,YACzC,UAAU,SAAS,YAAY,OAAO,YAAY;AAAA,YAClD,OAAO,SAAS,SAAS,SAAS,KAAK;AAAA,YACvC,YAAY,SAAS,cAAc;AAAA,UACpC;AAAA,QACX,OAAe;AACLA,wBAAAA,MAAa,MAAA,QAAA,iCAAA,sBAAsB,QAAQ;AAAA,QAC5C;AAAA,MACF,WAAU,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAE9C,eAAO;AACP,mBAAW,QAAQ,SAAS;AAAA,MACpC,OAAa;AACLA,sBAAAA,MAAA,MAAA,QAAA,iCAAa,uBAAuB,QAAQ;AAAA,MAC7C;AAEDA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,YAAY,IAAI;AAC5BA,oBAAAA,oDAAY,SAAS,UAAU;AAE/B,UAAI,OAAO,SAAS,KAAK,OAAO,SAAS;AACvC,eAAO,kBAAkB,EAAE,MAAM,WAAU,CAAE;AAAA,MACrD,OAAa;AACL,eAAO,qBAAqB,IAAI;AAChC,eAAO,kBAAkB,EAAE,MAAMF,OAAM,WAAW,YAAY;AAAA,MAC/D;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdE,oBAAAA,sDAAc,aAAa,KAAK;AAGhC,UAAI,MAAM,QAAQ,SAAS,eAAe,KAAK,MAAM,QAAQ,SAAS,SAAS,KAAK,MAAM,QAAQ,SAAS,MAAM,KAAK,MAAM,eAAe,QAAW;AACpJA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,UAAU;AACtB,cAAM,WAAW;AAAA,UACf;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,gBAAgB;AAAA,UACjB;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,gBAAgB;AAAA,UACjB;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,gBAAgB;AAAA,UACjB;AAAA,QACF;AAED,cAAM,iBAAiB;AAAA,UACrB,SAAS;AAAA,UACT,UAAU;AAAA,UACV,OAAO,SAAS;AAAA,UAChB,YAAY;AAAA,QACb;AAED,eAAO,kBAAkB,EAAE,MAAM,UAAU,YAAY,gBAAgB;AACvEA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,QAAQ;AAClC,eAAO,EAAE,MAAM,SAAU;AAAA,MAC1B;AAEDE,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,iBAAiB,EAAE,UAAU;AACjC,QAAI;AACFF,oBAAAA,MAAA,MAAA,OAAA,iCAAY,aAAa;AACzB,YAAM,WAAW,MAAMG,2BAA2B;AAClDH,oBAAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,QAAQ;AAGlC,UAAI,SAAS,CAAE;AACf,UAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,iBAAS;AAAA,MACjB,WAAiB,YAAY,MAAM,QAAQ,SAAS,IAAI,GAAG;AACnD,iBAAS,SAAS;AAAA,MAC1B,OAAa;AACLA,sBAAAA,MAAa,MAAA,QAAA,iCAAA,kBAAkB,QAAQ;AAAA,MACxC;AAED,aAAO,sBAAsB,MAAM;AACnCA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,cAAc,MAAM;AAChC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,sDAAc,aAAa,KAAK;AAChC,aAAO,sBAAsB,EAAE;AAC/BE,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,eAAe,EAAE,OAAQ,GAAE,SAAS;AACxC,QAAI;AACFF,oBAAAA,MAAA,MAAA,OAAA,iCAAY,gBAAgB,OAAO;AACnC,aAAO,eAAe,IAAI;AAG1B,YAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,mBAAW,MAAM,OAAO,IAAI,MAAM,QAAQ,CAAC,GAAG,GAAK;AAAA,MAC3D,CAAO;AAED,YAAM,aAAaI,UAAuB,eAAC,OAAO;AAClD,YAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,YAAY,cAAc,CAAC;AAChEJ,oBAAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,QAAQ;AAGlC,UAAI,cAAc;AAClB,UAAI,YAAY,SAAS,MAAM;AAC7B,sBAAc,SAAS;AAAA,MACxB,WAAU,YAAY,CAAC,SAAS,MAAM;AAErC,sBAAc;AAAA,MACf;AAED,UAAI,aAAa;AAEf,cAAM,oBAAoB;AAAA,UACxB,GAAG;AAAA;AAAA,UAEH,cAAc,YAAY,iBAAiB,YAAY,YAAY,YAAY,YAC7E,GAAG,YAAY,SAAS,UAAU,GAAG,CAAC,CAAC,MAAM,YAAY,UAAU,UAAU,GAAG,CAAC,CAAC,KAAK;AAAA;AAAA,UAEzF,YAAY,YAAY,aACrB,MAAM,QAAQ,YAAY,UAAU,IAAI,YAAY,aACpD,YAAY,WAAW,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU,WAAW;AAAA,YAC1D,MAAM,SAAS,KAAM;AAAA,YACrB,MAAM,CAAC,MAAM,MAAM,OAAO,MAAM,MAAM,KAAK,MAAM,IAAI,EAAE,KAAK,KAAK;AAAA,UAClE,EAAC,IAAK,CAAE;AAAA;AAAA,UAEZ,OAAO,YAAY,SAAS;AAAA,QAC7B;AAED,eAAO,oBAAoB,iBAAiB;AAC5CA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,YAAY,iBAAiB;AAAA,MACjD,OAAa;AACLA,sBAAAA,MAAc,MAAA,SAAA,iCAAA,UAAU;AACxB,cAAM,IAAI,MAAM,UAAU;AAAA,MAC3B;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,sDAAc,aAAa,KAAK;AAGhC,UAAI,MAAM,QAAQ,SAAS,eAAe,KAAK,MAAM,QAAQ,SAAS,SAAS,KAAK,MAAM,QAAQ,SAAS,QAAQ,KAAK,MAAM,eAAe,QAAW;AACtJA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,QAAQ;AACpB,cAAM,WAAW;AAAA,UACf,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,UACD;AAAA,UACD,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,UAAU;AAAA,UACV,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,OAAO,MAAM;AAAA,UAChC,aAAa;AAAA,UACb,YAAY;AAAA,YACV,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,YAC3B,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,YAC3B,EAAE,MAAM,OAAO,MAAM,MAAO;AAAA,YAC5B,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,UAC5B;AAAA,UACD,UAAU;AAAA,UACV,WAAW;AAAA,UACX,cAAc;AAAA,UACd,OAAO;AAAA,QACR;AACD,eAAO,oBAAoB,QAAQ;AACnCA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,QAAQ;AAClC,eAAO,EAAE,MAAM,SAAU;AAAA,MAC1B;AAEDE,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,kBAAkB,EAAE,OAAM,GAAI,EAAE,SAAS,KAAI,GAAI;AACrD,QAAI;AACFF,oBAAY,MAAA,MAAA,OAAA,iCAAA,sBAAsB,SAAS,SAAS,IAAI;AACxD,aAAO,eAAe,IAAI;AAG1B,YAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,mBAAW,MAAM,OAAO,IAAI,MAAM,SAAS,CAAC,GAAG,GAAI;AAAA,MAC3D,CAAO;AAGD,YAAM,YAAY,KAAK,IAAK;AAC5B,YAAM,aAAaK,UAAAA,kBAA2B,SAAS,MAAM,EAAE,IAAI,WAAW;AAC9E,YAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,YAAY,cAAc,CAAC;AAEhEL,oBAAAA,MAAY,MAAA,OAAA,iCAAA,aAAa,QAAQ;AAEjC,UAAI,YAAY,CAAE;AAClB,UAAI,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,MAAM,QAAQ,SAAS,KAAK,KAAK,GAAG;AAC1F,oBAAY,SAAS,KAAK;AAAA,MAClC,WAAiB,YAAY,SAAS,SAAS,MAAM,QAAQ,SAAS,KAAK,GAAG;AACtE,oBAAY,SAAS;AAAA,MAC7B,WAAiB,YAAY,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,GAAG;AACpE,oBAAY,SAAS;AAAA,MACtB,WAAU,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAC9C,oBAAY;AAAA,MACpB,OAAa;AACLA,sBAAAA,MAAa,MAAA,QAAA,iCAAA,iBAAiB,QAAQ;AACtC,oBAAY,CAAE;AAAA,MACf;AAGD,UAAI,UAAU,WAAW,GAAG;AAC1BA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,iBAAiB;AAC7B,YAAI;AACF,gBAAM,mBAAmB,MAAMM,+BAA8B,SAAS,IAAI;AAC1EN,wBAAAA,MAAA,MAAA,OAAA,iCAAY,YAAY,gBAAgB;AAGxC,gBAAM,gBAAgB,MAAMK,4BAA2B,SAAS,IAAI;AACpEL,wBAAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,aAAa;AAEvC,cAAI,iBAAiB,cAAc,QAAQ,MAAM,QAAQ,cAAc,IAAI,GAAG;AAC5E,wBAAY,cAAc;AAAA,UAC3B,WAAU,iBAAiB,MAAM,QAAQ,aAAa,GAAG;AACxD,wBAAY;AAAA,UACb;AAAA,QACF,SAAQ,eAAe;AACtBA,wBAAAA,sDAAc,YAAY,aAAa;AAAA,QACxC;AAAA,MACF;AAGD,YAAM,MAAM,oBAAI,KAAM;AACtB,YAAM,cAAc,IAAI,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC;AAClD,YAAM,cAAc,IAAI,SAAU,IAAG,KAAK,IAAI,WAAY;AAE1D,kBAAY,UAAU,IAAI,UAAQ;AAChC,YAAI,gBAAgB;AAGpB,YAAI,KAAK,cAAc,UAAa,KAAK,WAAW,QAAW;AAC7D,0BAAgB;AAAA,YACd,GAAG;AAAA,YACH,QAAQ,KAAK,YAAY,cAAc;AAAA,UACxC;AAAA,QACF;AAGD,YAAI,SAAS,eAAe,cAAc,WAAW,aAAa;AAChE,gBAAM,gBAAgB,cAAc,UAAU,MAAM,GAAG,EAAE,IAAI,MAAM;AACnE,gBAAM,mBAAmB,cAAc,CAAC,IAAI,KAAK,cAAc,CAAC;AAGhE,cAAI,oBAAoB,aAAa;AACnC,4BAAgB;AAAA,cACd,GAAG;AAAA,cACH,QAAQ;AAAA,cACR,gBAAgB,KAAK,WAAW,KAAK,YAAY,cAAc;AAAA,YAChE;AAAA,UACF;AAAA,QACF;AAED,eAAO;AAAA,MACf,CAAO;AAEDA,oBAAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,SAAS;AAEnC,aAAO,kBAAkB,SAAS;AAClCA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,WAAW,SAAS;AAChC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,iCAAA,YAAY,KAAK;AAG/B,UAAI,MAAM,QAAQ,SAAS,eAAe,KAAK,MAAM,QAAQ,SAAS,SAAS,KAAK,MAAM,QAAQ,SAAS,SAAS,KAAK,MAAM,eAAe,QAAW;AACvJA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,WAAW;AACvB,cAAM,gBAAgB;AAAA,UACpB,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,YAAa;AAAA,UAChF,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,WAAY;AAAA,UAC/E,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,YAAa;AAAA,UAChF,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,YAAa;AAAA,UAChF,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,YAAa;AAAA,UAChF,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,WAAY;AAAA,UAC/E,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,cAAe;AAAA,UAClF,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,YAAa;AAAA,UAChF,EAAE,IAAI,GAAG,WAAW,SAAS,SAAS,SAAS,OAAO,KAAK,QAAQ,YAAa;AAAA,QACjF;AACD,eAAO,kBAAkB,aAAa;AACtCA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,eAAe,aAAa;AACxC,eAAO,EAAE,MAAM,cAAe;AAAA,MAC/B;AAEDE,yBAAU,MAAM,WAAW,SAAS;AACpC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,cAAc,EAAE,UAAU;AAC9B,QAAI;AACFF,oBAAAA,MAAA,MAAA,OAAA,iCAAY,aAAa;AAGzB,YAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,mBAAW,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,GAAG,GAAI;AAAA,MACxD,CAAO;AAED,YAAM,aAAaO,UAAAA,cAAwB;AAC3C,YAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,YAAY,cAAc,CAAC;AAEhEP,oBAAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,QAAQ;AAElC,UAAI,QAAQ,CAAE;AACd,UAAI,YAAY,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,GAAG;AAC7D,gBAAQ,SAAS;AAAA,MAClB,WAAU,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAC9C,gBAAQ;AAAA,MAChB,OAAa;AACLA,sBAAAA,MAAa,MAAA,QAAA,iCAAA,kBAAkB,QAAQ;AAAA,MACxC;AAED,aAAO,mBAAmB,KAAK;AAC/BA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,YAAY,KAAK;AAC7B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,sDAAc,aAAa,KAAK;AAGhC,UAAI,MAAM,QAAQ,SAAS,eAAe,KAAK,MAAM,QAAQ,SAAS,SAAS,KAAK,MAAM,QAAQ,SAAS,MAAM,KAAK,MAAM,eAAe,QAAW;AACpJA,sBAAAA,oDAAY,YAAY;AACxB,cAAM,YAAY;AAAA,UAChB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,UACtB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,UACtB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,UACrB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,UACtB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,QACvB;AACD,eAAO,mBAAmB,SAAS;AACnCA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,gBAAgB,SAAS;AACrC,eAAO,EAAE,MAAM,UAAW;AAAA,MAC3B;AAEDE,yBAAU,MAAM,WAAW,UAAU;AACrC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,aAAa,EAAE,OAAQ,GAAE,QAAQ;AACrC,QAAI;AACFF,oBAAAA,MAAY,MAAA,OAAA,iCAAA,cAAc,MAAM;AAChC,aAAO,eAAe,IAAI;AAG1B,YAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,mBAAW,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,GAAG,GAAI;AAAA,MACxD,CAAO;AAED,YAAM,aAAaQ,UAAqB,aAAC,MAAM;AAC/C,YAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,YAAY,cAAc,CAAC;AAEhER,oBAAAA,MAAY,MAAA,OAAA,iCAAA,YAAY,QAAQ;AAEhC,UAAI,UAAU,CAAE;AAChB,UAAI,YAAY,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,GAAG;AAC7D,kBAAU,SAAS;AAAA,MACpB,WAAU,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAC9C,kBAAU;AAAA,MAClB,OAAa;AACLA,sBAAAA,MAAa,MAAA,QAAA,iCAAA,gBAAgB,QAAQ;AAAA,MACtC;AAED,aAAO,sBAAsB,OAAO;AACpCA,oBAAAA,oDAAY,YAAY,OAAO;AAC/B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,iCAAA,WAAW,KAAK;AAG9B,UAAI,MAAM,QAAQ,SAAS,eAAe,KAAK,MAAM,QAAQ,SAAS,SAAS,KAAK,MAAM,QAAQ,SAAS,MAAM,KAAK,MAAM,eAAe,QAAW;AACpJA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,iBAAiB;AAC7B,eAAO,sBAAsB,EAAE;AAC/B,eAAO,EAAE,MAAM,GAAI;AAAA,MACpB;AAEDE,yBAAU,MAAM,WAAW,MAAM;AACjC,YAAM;AAAA,IACZ,UAAc;AACR,aAAO,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAGD,qBAAqB,EAAE,OAAM,GAAI,EAAE,QAAQ,OAAM,GAAI;AACnD,WAAO,2BAA2B,EAAE,QAAQ,OAAM,CAAE;AAAA,EACrD;AAAA;AAAA,EAGD,aAAa,EAAE,SAAU,GAAE,QAAQ;AACjC,WAAO,SAAS,qBAAqB,MAAM;AAAA,EAC5C;AACH;AAEA,MAAM,UAAU;AAAA,EACd,WAAW,CAAAJ,WAAS,MAAM,QAAQA,OAAM,SAAS,IAAIA,OAAM,YAAY,CAAE;AAAA,EACzE,eAAe,CAAAA,WAAS,MAAM,QAAQA,OAAM,aAAa,IAAIA,OAAM,gBAAgB,CAAE;AAAA,EACrF,aAAa,CAAAA,WAASA,OAAM;AAAA,EAC5B,YAAY,CAAAA,WAAS,MAAM,QAAQA,OAAM,UAAU,IAAIA,OAAM,aAAa,CAAE;AAAA,EAC5E,WAAW,CAAAA,WAAS,MAAM,QAAQA,OAAM,SAAS,IAAIA,OAAM,YAAY,CAAE;AAAA,EACzE,eAAe,CAAAA,WAAS,MAAM,QAAQA,OAAM,aAAa,IAAIA,OAAM,gBAAgB,CAAE;AAAA,EACrF,SAAS,CAAAA,WAASA,OAAM;AAAA,EACxB,YAAY,CAAAA,WAASA,OAAM;AAAA;AAAA,EAG3B,oBAAoB,CAAAA,WAAS;AAC3B,UAAM,QAAQ,MAAM,QAAQA,OAAM,SAAS,IAAIA,OAAM,YAAY,CAAE;AACnE,WAAO,MAAM,OAAO,UAAQ,KAAK,WAAW,WAAW;AAAA,EACxD;AAAA,EACD,mBAAmB,CAAAA,WAAS;AAC1B,UAAM,QAAQ,MAAM,QAAQA,OAAM,SAAS,IAAIA,OAAM,YAAY,CAAE;AACnE,WAAO,MAAM,OAAO,UAAQ,KAAK,WAAW,UAAU;AAAA,EACvD;AAAA,EACD,sBAAsB,CAAAA,WAAS;AAC7B,UAAM,QAAQ,MAAM,QAAQA,OAAM,SAAS,IAAIA,OAAM,YAAY,CAAE;AACnE,WAAO,MAAM,OAAO,UAAQ,KAAK,WAAW,aAAa;AAAA,EAC1D;AACH;AAEA,MAAe,QAAA;AAAA,EACb,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;"}