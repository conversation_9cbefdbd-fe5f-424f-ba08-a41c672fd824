{"version": 3, "file": "pinia-migration-test.js", "sources": ["pages/test/pinia-migration-test.vue", "pages/test/pinia-migration-test.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">Pinia迁移测试</text>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">Vuex状态</text>\n      <view class=\"state-display\">\n        <text>Loading: {{ vuexLoading }}</text>\n        <text>Network: {{ vuexNetworkStatus }}</text>\n      </view>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">Pinia状态</text>\n      <view class=\"state-display\">\n        <text>Loading: {{ piniaLoading }}</text>\n        <text>Network: {{ piniaNetworkStatus }}</text>\n      </view>\n    </view>\n    \n    <view class=\"test-actions\">\n      <button @click=\"testVuexUpdate\" class=\"test-btn\">测试Vuex更新</button>\n      <button @click=\"testPiniaUpdate\" class=\"test-btn\">测试Pinia更新</button>\n      <button @click=\"toggleLoading\" class=\"test-btn\">切换Loading</button>\n      <button @click=\"toggleNetwork\" class=\"test-btn\">切换Network</button>\n      <button @click=\"runValidation\" class=\"test-btn validation-btn\">运行验证</button>\n      <button @click=\"toggleContinuousValidation\" class=\"test-btn\"\n              :class=\"{ 'active': continuousValidation }\">\n        {{ continuousValidation ? '停止' : '开始' }}持续验证\n      </button>\n      <button @click=\"testPiniaDirectUpdate\" class=\"test-btn direct-btn\">\n        直接设置Pinia状态\n      </button>\n      <button @click=\"forceSync\" class=\"test-btn sync-btn\">\n        强制同步状态\n      </button>\n      <button @click=\"testVenueVuex\" class=\"test-btn venue-btn\">\n        测试Venue Vuex\n      </button>\n      <button @click=\"testVenuePinia\" class=\"test-btn venue-btn\">\n        测试Venue Pinia\n      </button>\n      <button @click=\"diagnoseVenueStore\" class=\"test-btn debug-btn\">\n        诊断Venue Store\n      </button>\n      <button @click=\"testVenueBasic\" class=\"test-btn success-btn\">\n        基础Venue测试\n      </button>\n      <button @click=\"testSharingVuex\" class=\"test-btn sharing-btn\">\n        测试Sharing Vuex\n      </button>\n      <button @click=\"testSharingPinia\" class=\"test-btn sharing-btn\">\n        测试Sharing Pinia\n      </button>\n      <button @click=\"testBookingVuex\" class=\"test-btn booking-btn\">\n        测试Booking Vuex\n      </button>\n      <button @click=\"testBookingPinia\" class=\"test-btn booking-btn\">\n        测试Booking Pinia\n      </button>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">User状态测试</text>\n      <view class=\"state-display\">\n        <text>Vuex登录状态: {{ vuexUserLoggedIn }}</text>\n        <text>Pinia登录状态: {{ piniaUserLoggedIn }}</text>\n        <text>Vuex用户名: {{ vuexUsername || '未登录' }}</text>\n        <text>Pinia用户名: {{ piniaUsername || '未登录' }}</text>\n      </view>\n    </view>\n\n    <view class=\"test-section\">\n      <text class=\"section-title\">Venue状态测试</text>\n      <view class=\"state-display\">\n        <text>Vuex场馆数量: {{ vuexVenueCount }}</text>\n        <text>Pinia场馆数量: {{ piniaVenueCount }}</text>\n        <text>Vuex加载状态: {{ vuexVenueLoading }}</text>\n        <text>Pinia加载状态: {{ piniaVenueLoading }}</text>\n        <text>Vuex搜索结果: {{ vuexSearchCount }}</text>\n        <text>Pinia搜索结果: {{ piniaSearchCount }}</text>\n      </view>\n    </view>\n\n    <view class=\"test-section\">\n      <text class=\"section-title\">Sharing状态测试</text>\n      <view class=\"state-display\">\n        <text>Vuex分享订单: {{ vuexSharingCount }}</text>\n        <text>Pinia分享订单: {{ piniaSharingCount }}</text>\n        <text>Vuex我的订单: {{ vuexMySharingCount }}</text>\n        <text>Pinia我的订单: {{ piniaMySharingCount }}</text>\n        <text>Vuex加载状态: {{ vuexSharingLoading }}</text>\n        <text>Pinia加载状态: {{ piniaSharingLoading }}</text>\n      </view>\n    </view>\n\n    <view class=\"test-section\">\n      <text class=\"section-title\">Booking状态测试</text>\n      <view class=\"state-display\">\n        <text>Vuex预订数量: {{ vuexBookingCount }}</text>\n        <text>Pinia预订数量: {{ piniaBookingCount }}</text>\n        <text>Vuex拼场订单: {{ vuexBookingSharingCount }}</text>\n        <text>Pinia拼场订单: {{ piniaBookingSharingCount }}</text>\n        <text>Vuex加载状态: {{ vuexBookingLoading }}</text>\n        <text>Pinia加载状态: {{ piniaBookingLoading }}</text>\n      </view>\n    </view>\n\n    <view class=\"sync-status\">\n      <text class=\"sync-title\">同步状态检查</text>\n      <text :class=\"['sync-result', syncStatus.loading ? 'success' : 'error']\">\n        Loading同步: {{ syncStatus.loading ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.network ? 'success' : 'error']\">\n        Network同步: {{ syncStatus.network ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.userLogin ? 'success' : 'error']\">\n        User登录同步: {{ syncStatus.userLogin ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.venueCount ? 'success' : 'error']\">\n        Venue数量同步: {{ syncStatus.venueCount ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.venueLoading ? 'success' : 'error']\">\n        Venue加载同步: {{ syncStatus.venueLoading ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.venueSearch ? 'success' : 'error']\">\n        Venue搜索同步: {{ syncStatus.venueSearch ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.sharingOrders ? 'success' : 'error']\">\n        Sharing订单同步: {{ syncStatus.sharingOrders ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.sharingLoading ? 'success' : 'error']\">\n        Sharing加载同步: {{ syncStatus.sharingLoading ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.bookingCount ? 'success' : 'error']\">\n        Booking数量同步: {{ syncStatus.bookingCount ? '✓' : '✗' }}\n      </text>\n      <text :class=\"['sync-result', syncStatus.bookingLoading ? 'success' : 'error']\">\n        Booking加载同步: {{ syncStatus.bookingLoading ? '✓' : '✗' }}\n      </text>\n    </view>\n\n    <view class=\"validation-results\" v-if=\"validationResults\">\n      <text class=\"section-title\">验证结果</text>\n      <view class=\"result-item\">\n        <text :class=\"['result-status', validationResults.overall ? 'success' : 'error']\">\n          总体状态: {{ validationResults.overall ? '通过' : '失败' }}\n        </text>\n      </view>\n      <view class=\"result-item\" v-if=\"validationResults.modules.app\">\n        <text :class=\"['result-status', validationResults.modules.app.valid ? 'success' : 'error']\">\n          App模块: {{ validationResults.modules.app.valid ? '✓' : '✗' }}\n        </text>\n      </view>\n      <view class=\"result-item\" v-if=\"validationResults.modules.user\">\n        <text :class=\"['result-status', validationResults.modules.user.valid ? 'success' : 'error']\">\n          User模块: {{ validationResults.modules.user.valid ? '✓' : '✗' }}\n        </text>\n      </view>\n      <view class=\"result-item\" v-if=\"validationResults.modules.venue\">\n        <text :class=\"['result-status', validationResults.modules.venue.valid ? 'success' : 'error']\">\n          Venue模块: {{ validationResults.modules.venue.valid ? '✓' : '✗' }}\n        </text>\n      </view>\n      <view class=\"result-item\" v-if=\"validationResults.modules.sharing\">\n        <text :class=\"['result-status', validationResults.modules.sharing.valid ? 'success' : 'error']\">\n          Sharing模块: {{ validationResults.modules.sharing.valid ? '✓' : '✗' }}\n        </text>\n      </view>\n      <view class=\"result-item\" v-if=\"validationResults.modules.booking\">\n        <text :class=\"['result-status', validationResults.modules.booking.valid ? 'success' : 'error']\">\n          Booking模块: {{ validationResults.modules.booking.valid ? '✓' : '✗' }}\n        </text>\n      </view>\n      <text class=\"timestamp\">验证时间: {{ validationResults.timestamp }}</text>\n    </view>\n\n    <view class=\"migration-status\" v-if=\"migrationStatus\">\n      <text class=\"section-title\">迁移进度</text>\n      <view class=\"progress-info\">\n        <text>当前阶段: {{ migrationStatus.currentPhase }}</text>\n        <text>完成进度: {{ migrationStatus.progress }}%</text>\n        <text>已完成: {{ migrationStatus.completedPhases }}/{{ migrationStatus.totalPhases }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\nimport { useAppStore } from '@/stores/app.js'\nimport { useUserStore } from '@/stores/user.js'\nimport { useVenueStore } from '@/stores/venue.js'\nimport { useSharingStore } from '@/stores/sharing.js'\nimport { useBookingStore } from '@/stores/booking.js'\nimport { validateMigration, startValidation, stopValidation } from '@/stores/migration-validator.js'\nimport { getMigrationStatus } from '@/stores/migration-config.js'\n\nexport default {\n  name: 'PiniaMigrationTest',\n\n  data() {\n    return {\n      appStore: null,\n      userStore: null,\n      venueStore: null,\n      sharingStore: null,\n      bookingStore: null,\n      validationResults: null,\n      continuousValidation: false,\n      validationInterval: null,\n      migrationStatus: null\n    }\n  },\n  \n  computed: {\n    ...mapState(['loading', 'networkStatus']),\n    ...mapState('user', ['isLoggedIn', 'userInfo']),\n    ...mapState('venue', {\n      vuexVenueList: 'venueList',\n      vuexVenueLoading: 'loading',\n      vuexSearchResults: 'searchResults'\n    }),\n    ...mapState('sharing', {\n      vuexSharingOrders: 'sharingOrders',\n      vuexMySharingOrders: 'mySharingOrders',\n      vuexSharingLoading: 'loading'\n    }),\n    ...mapState('booking', {\n      vuexBookingList: 'bookingList',\n      vuexBookingSharingOrders: 'sharingOrders',\n      vuexBookingLoading: 'loading'\n    }),\n\n    vuexLoading() {\n      return this.loading\n    },\n\n    vuexNetworkStatus() {\n      return this.networkStatus\n    },\n\n    vuexUserLoggedIn() {\n      return this.isLoggedIn\n    },\n\n    vuexUsername() {\n      return this.userInfo?.username\n    },\n\n    piniaLoading() {\n      return this.appStore?.loading || false\n    },\n\n    piniaNetworkStatus() {\n      return this.appStore?.networkStatus || true\n    },\n\n    piniaUserLoggedIn() {\n      return this.userStore?.isLoggedIn || false\n    },\n\n    piniaUsername() {\n      return this.userStore?.userInfo?.username\n    },\n\n    vuexVenueCount() {\n      return this.vuexVenueList?.length || 0\n    },\n\n    piniaVenueCount() {\n      return this.venueStore?.venueList?.length || 0\n    },\n\n    piniaVenueLoading() {\n      return this.venueStore?.loading || false\n    },\n\n    vuexSearchCount() {\n      return this.vuexSearchResults?.length || 0\n    },\n\n    piniaSearchCount() {\n      return this.venueStore?.searchResults?.length || 0\n    },\n\n    vuexSharingCount() {\n      return this.vuexSharingOrders?.length || 0\n    },\n\n    piniaSharingCount() {\n      return this.sharingStore?.sharingOrders?.length || 0\n    },\n\n    vuexMySharingCount() {\n      return this.vuexMySharingOrders?.length || 0\n    },\n\n    piniaMySharingCount() {\n      return this.sharingStore?.mySharingOrders?.length || 0\n    },\n\n    piniaSharingLoading() {\n      return this.sharingStore?.loading || false\n    },\n\n    vuexBookingCount() {\n      return this.vuexBookingList?.length || 0\n    },\n\n    piniaBookingCount() {\n      return this.bookingStore?.bookingList?.length || 0\n    },\n\n    vuexBookingSharingCount() {\n      return this.vuexBookingSharingOrders?.length || 0\n    },\n\n    piniaBookingSharingCount() {\n      return this.bookingStore?.sharingOrders?.length || 0\n    },\n\n    piniaBookingLoading() {\n      return this.bookingStore?.loading || false\n    },\n\n    syncStatus() {\n      return {\n        loading: this.vuexLoading === this.piniaLoading,\n        network: this.vuexNetworkStatus === this.piniaNetworkStatus,\n        userLogin: this.vuexUserLoggedIn === this.piniaUserLoggedIn,\n        venueCount: this.vuexVenueCount === this.piniaVenueCount,\n        venueLoading: this.vuexVenueLoading === this.piniaVenueLoading,\n        venueSearch: this.vuexSearchCount === this.piniaSearchCount,\n        sharingOrders: this.vuexSharingCount === this.piniaSharingCount,\n        sharingLoading: this.vuexSharingLoading === this.piniaSharingLoading,\n        bookingCount: this.vuexBookingCount === this.piniaBookingCount,\n        bookingLoading: this.vuexBookingLoading === this.piniaBookingLoading\n      }\n    }\n  },\n  \n  methods: {\n    ...mapActions(['setLoading', 'setNetworkStatus']),\n    \n    testVuexUpdate() {\n      console.log('测试Vuex更新')\n      this.setLoading(!this.vuexLoading)\n    },\n    \n    testPiniaUpdate() {\n      console.log('测试Pinia更新')\n      const newValue = !this.piniaLoading\n      console.log('Pinia设置loading为:', newValue)\n      this.appStore.setLoading(newValue)\n    },\n    \n    toggleLoading() {\n      const newValue = !this.vuexLoading\n      console.log('切换Loading状态到:', newValue)\n      this.setLoading(newValue)\n    },\n    \n    toggleNetwork() {\n      const newValue = !this.vuexNetworkStatus\n      console.log('切换Network状态到:', newValue)\n      this.setNetworkStatus(newValue)\n    },\n\n    // 运行验证\n    runValidation() {\n      console.log('运行迁移验证')\n      this.validationResults = validateMigration()\n\n      uni.showToast({\n        title: this.validationResults.overall ? '验证通过' : '验证失败',\n        icon: this.validationResults.overall ? 'success' : 'error'\n      })\n    },\n\n    // 切换持续验证\n    toggleContinuousValidation() {\n      if (this.continuousValidation) {\n        // 停止持续验证\n        if (this.validationInterval) {\n          stopValidation(this.validationInterval)\n          this.validationInterval = null\n        }\n        this.continuousValidation = false\n        console.log('持续验证已停止')\n      } else {\n        // 开始持续验证\n        this.validationInterval = startValidation(3000) // 每3秒验证一次\n        this.continuousValidation = true\n        console.log('持续验证已开始')\n      }\n    },\n\n    // 直接更新Pinia状态\n    testPiniaDirectUpdate() {\n      console.log('直接设置Pinia状态')\n      const newLoading = !this.piniaLoading\n      const newNetwork = !this.piniaNetworkStatus\n\n      // 直接设置状态属性\n      this.appStore.loading = newLoading\n      this.appStore.networkStatus = newNetwork\n\n      console.log('Pinia状态已直接设置:', { loading: newLoading, network: newNetwork })\n    },\n\n    // 强制同步状态\n    forceSync() {\n      console.log('强制同步状态')\n\n      // 从Vuex同步到Pinia\n      this.appStore.loading = this.vuexLoading\n      this.appStore.networkStatus = this.vuexNetworkStatus\n\n      console.log('状态已强制同步')\n\n      // 运行验证\n      setTimeout(() => {\n        this.runValidation()\n      }, 100)\n    },\n\n    // 诊断Venue Store\n    diagnoseVenueStore() {\n      console.log('=== Venue Store 诊断开始 ===')\n\n      try {\n        // 测试多种获取方式\n        const store1 = this.venueStore\n        const store2 = useVenueStore()\n\n        console.log('this.venueStore:', {\n          exists: !!store1,\n          type: typeof store1,\n          constructor: store1?.constructor?.name,\n          getVenueListType: typeof store1?.getVenueList,\n          getVenueListValue: store1?.getVenueList\n        })\n\n        console.log('useVenueStore():', {\n          exists: !!store2,\n          type: typeof store2,\n          constructor: store2?.constructor?.name,\n          getVenueListType: typeof store2?.getVenueList,\n          getVenueListValue: store2?.getVenueList\n        })\n\n        console.log('相等性检查:', store1 === store2)\n\n        // 检查store的内部结构\n        if (store2) {\n          console.log('Store内部结构:')\n          console.log('- $id:', store2.$id)\n          console.log('- state keys:', Object.keys(store2.$state || {}))\n          console.log('- 所有属性:', Object.getOwnPropertyNames(store2))\n          console.log('- 原型链:', Object.getPrototypeOf(store2))\n\n          // 尝试直接访问actions\n          const descriptor = Object.getOwnPropertyDescriptor(store2, 'getVenueList')\n          console.log('getVenueList属性描述符:', descriptor)\n        }\n\n        uni.showToast({\n          title: '诊断完成，查看控制台',\n          icon: 'success'\n        })\n\n      } catch (error) {\n        console.error('诊断失败:', error)\n        uni.showToast({\n          title: '诊断失败',\n          icon: 'error'\n        })\n      }\n\n      console.log('=== Venue Store 诊断结束 ===')\n    },\n\n    // 基础Venue测试（避开getVenueList问题）\n    testVenueBasic() {\n      console.log('=== 基础Venue测试开始 ===')\n\n      try {\n        const venueStore = useVenueStore()\n\n        if (!venueStore) {\n          throw new Error('VenueStore未初始化')\n        }\n\n        console.log('1. 测试状态读取:')\n        console.log('- venueList:', venueStore.venueList)\n        console.log('- loading:', venueStore.loading)\n        console.log('- searchResults:', venueStore.searchResults)\n\n        console.log('2. 测试状态设置:')\n        venueStore.setLoading(true)\n        console.log('- 设置loading为true，当前值:', venueStore.loading)\n\n        venueStore.setSearchResults(['测试场馆A', '测试场馆B'])\n        console.log('- 设置搜索结果，当前值:', venueStore.searchResults)\n\n        console.log('3. 测试状态清除:')\n        setTimeout(() => {\n          venueStore.setLoading(false)\n          venueStore.clearSearchResults()\n          console.log('- 清除后loading:', venueStore.loading)\n          console.log('- 清除后searchResults:', venueStore.searchResults)\n        }, 1000)\n\n        console.log('4. 测试getters:')\n        console.log('- totalVenues:', venueStore.totalVenues)\n        console.log('- isLoading:', venueStore.isLoading)\n\n        uni.showToast({\n          title: '基础测试完成',\n          icon: 'success'\n        })\n\n      } catch (error) {\n        console.error('基础测试失败:', error)\n        uni.showToast({\n          title: '基础测试失败',\n          icon: 'error'\n        })\n      }\n\n      console.log('=== 基础Venue测试结束 ===')\n    },\n\n    // 测试Sharing Vuex功能\n    async testSharingVuex() {\n      console.log('测试Sharing Vuex功能')\n      try {\n        // 通过Vuex获取分享订单列表\n        await this.$store.dispatch('sharing/getSharingOrders', { page: 1, pageSize: 5 })\n        console.log('Vuex分享订单列表获取成功')\n\n        // 设置loading状态测试\n        this.$store.commit('sharing/SET_LOADING', true)\n        setTimeout(() => {\n          this.$store.commit('sharing/SET_LOADING', false)\n        }, 1000)\n\n        uni.showToast({\n          title: 'Sharing Vuex测试完成',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('Sharing Vuex测试失败:', error)\n        uni.showToast({\n          title: 'Sharing Vuex测试失败',\n          icon: 'error'\n        })\n      }\n    },\n\n    // 测试Sharing Pinia功能\n    async testSharingPinia() {\n      console.log('测试Sharing Pinia功能')\n      try {\n        const sharingStore = useSharingStore()\n\n        if (!sharingStore) {\n          throw new Error('SharingStore未初始化')\n        }\n\n        console.log('1. 测试状态读取:')\n        console.log('- sharingOrders:', sharingStore.sharingOrders)\n        console.log('- mySharingOrders:', sharingStore.mySharingOrders)\n        console.log('- loading:', sharingStore.loading)\n\n        console.log('2. 测试API调用:')\n        try {\n          await sharingStore.getSharingOrdersList({ page: 1, pageSize: 5 })\n          console.log('Pinia分享订单列表获取成功')\n        } catch (apiError) {\n          console.warn('API调用失败，继续其他测试:', apiError)\n        }\n\n        console.log('3. 测试状态设置:')\n        sharingStore.setLoading(true)\n        console.log('- 设置loading为true，当前值:', sharingStore.loading)\n\n        setTimeout(() => {\n          sharingStore.setLoading(false)\n          console.log('- 设置loading为false，当前值:', sharingStore.loading)\n        }, 1000)\n\n        console.log('4. 测试getters:')\n        console.log('- totalSharingOrders:', sharingStore.totalSharingOrders)\n        console.log('- isLoading:', sharingStore.isLoading)\n\n        uni.showToast({\n          title: 'Sharing Pinia测试完成',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('Sharing Pinia测试失败:', error)\n        uni.showToast({\n          title: `Sharing Pinia测试失败: ${error.message}`,\n          icon: 'error'\n        })\n      }\n    },\n\n    // 测试Venue Vuex功能\n    async testVenueVuex() {\n      console.log('测试Venue Vuex功能')\n      try {\n        // 通过Vuex获取场馆列表\n        await this.$store.dispatch('venue/getVenueList', { page: 1, pageSize: 5 })\n        console.log('Vuex场馆列表获取成功')\n\n        // 设置loading状态测试\n        this.$store.commit('venue/SET_LOADING', true)\n        setTimeout(() => {\n          this.$store.commit('venue/SET_LOADING', false)\n        }, 1000)\n\n        uni.showToast({\n          title: 'Venue Vuex测试完成',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('Venue Vuex测试失败:', error)\n        uni.showToast({\n          title: 'Venue Vuex测试失败',\n          icon: 'error'\n        })\n      }\n    },\n\n    // 测试Venue Pinia功能\n    async testVenuePinia() {\n      console.log('测试Venue Pinia功能')\n      try {\n        // 重新获取store实例，确保最新状态\n        const freshVenueStore = useVenueStore()\n\n        if (!freshVenueStore) {\n          throw new Error('VenueStore未初始化')\n        }\n\n        console.log('Fresh VenueStore可用方法:', Object.getOwnPropertyNames(freshVenueStore))\n        console.log('Fresh getVenueList类型:', typeof freshVenueStore.getVenueList)\n\n        // 尝试调用getVenueList方法\n        if (typeof freshVenueStore.getVenueList === 'function') {\n          try {\n            await freshVenueStore.getVenueList({ page: 1, pageSize: 5 })\n            console.log('Pinia场馆列表获取成功')\n          } catch (error) {\n            console.warn('getVenueList调用失败:', error)\n          }\n        } else {\n          console.warn('getVenueList不是函数，跳过API调用测试')\n        }\n\n        // 测试基础状态设置\n        freshVenueStore.setLoading(true)\n        console.log('设置loading为true')\n\n        setTimeout(() => {\n          freshVenueStore.setLoading(false)\n          console.log('设置loading为false')\n        }, 1000)\n\n        // 测试搜索结果\n        freshVenueStore.setSearchResults(['测试场馆1', '测试场馆2'])\n        console.log('设置搜索结果')\n\n        setTimeout(() => {\n          freshVenueStore.clearSearchResults()\n          console.log('清除搜索结果')\n        }, 2000)\n\n        // 更新实例引用\n        this.venueStore = freshVenueStore\n\n        uni.showToast({\n          title: 'Venue Pinia测试完成',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('Venue Pinia测试失败:', error)\n        uni.showToast({\n          title: `Venue Pinia测试失败: ${error.message}`,\n          icon: 'error'\n        })\n      }\n    },\n\n    // 测试Booking Vuex功能\n    async testBookingVuex() {\n      console.log('测试Booking Vuex功能')\n      try {\n        // 通过Vuex获取用户预订列表\n        await this.$store.dispatch('booking/getUserBookings', { page: 1, pageSize: 5 })\n        console.log('Vuex用户预订列表获取成功')\n\n        // 设置loading状态测试\n        this.$store.commit('booking/SET_LOADING', true)\n        setTimeout(() => {\n          this.$store.commit('booking/SET_LOADING', false)\n        }, 1000)\n\n        uni.showToast({\n          title: 'Booking Vuex测试完成',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('Booking Vuex测试失败:', error)\n        uni.showToast({\n          title: 'Booking Vuex测试失败',\n          icon: 'error'\n        })\n      }\n    },\n\n    // 测试Booking Pinia功能\n    async testBookingPinia() {\n      console.log('测试Booking Pinia功能')\n      try {\n        const bookingStore = useBookingStore()\n\n        if (!bookingStore) {\n          throw new Error('BookingStore未初始化')\n        }\n\n        console.log('1. 测试状态读取:')\n        console.log('- bookingList:', bookingStore.bookingList)\n        console.log('- sharingOrders:', bookingStore.sharingOrders)\n        console.log('- loading:', bookingStore.loading)\n\n        console.log('2. 测试API调用:')\n        try {\n          await bookingStore.getUserBookings({ page: 1, pageSize: 5 })\n          console.log('Pinia用户预订列表获取成功')\n        } catch (apiError) {\n          console.warn('API调用失败，继续其他测试:', apiError)\n        }\n\n        console.log('3. 测试状态设置:')\n        bookingStore.setLoading(true)\n        console.log('- 设置loading为true，当前值:', bookingStore.loading)\n\n        setTimeout(() => {\n          bookingStore.setLoading(false)\n          console.log('- 设置loading为false，当前值:', bookingStore.loading)\n        }, 1000)\n\n        console.log('4. 测试getters:')\n        console.log('- totalBookings:', bookingStore.totalBookings)\n        console.log('- isLoading:', bookingStore.isLoading)\n\n        uni.showToast({\n          title: 'Booking Pinia测试完成',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('Booking Pinia测试失败:', error)\n        uni.showToast({\n          title: `Booking Pinia测试失败: ${error.message}`,\n          icon: 'error'\n        })\n      }\n    }\n  },\n\n  onLoad() {\n    console.log('初始化Pinia迁移测试页面')\n    try {\n      this.appStore = useAppStore()\n      this.userStore = useUserStore()\n      this.venueStore = useVenueStore()\n      this.sharingStore = useSharingStore()\n      this.bookingStore = useBookingStore()\n      this.migrationStatus = getMigrationStatus()\n\n      console.log('Stores初始化成功:', {\n        appStore: !!this.appStore,\n        userStore: !!this.userStore,\n        venueStore: !!this.venueStore,\n        venueStoreType: typeof this.venueStore,\n        venueStoreGetVenueListType: typeof this.venueStore?.getVenueList\n      })\n\n      // 强制重新绑定actions（如果需要）\n      if (this.venueStore && typeof this.venueStore.getVenueList !== 'function') {\n        console.warn('VenueStore actions未正确绑定，尝试重新初始化')\n        // 重新获取store实例\n        setTimeout(() => {\n          this.venueStore = useVenueStore()\n          console.log('重新初始化后的getVenueList类型:', typeof this.venueStore?.getVenueList)\n        }, 100)\n      }\n    } catch (error) {\n      console.error('Stores初始化失败:', error)\n    }\n\n    // 验证初始状态同步\n    setTimeout(() => {\n      console.log('初始状态检查:', {\n        vuex: {\n          loading: this.vuexLoading,\n          network: this.vuexNetworkStatus,\n          userLogin: this.vuexUserLoggedIn,\n          username: this.vuexUsername\n        },\n        pinia: {\n          loading: this.piniaLoading,\n          network: this.piniaNetworkStatus,\n          userLogin: this.piniaUserLoggedIn,\n          username: this.piniaUsername\n        }\n      })\n\n      // 自动运行一次验证\n      this.runValidation()\n    }, 100)\n  },\n\n  onUnload() {\n    // 清理持续验证\n    if (this.validationInterval) {\n      stopValidation(this.validationInterval)\n      this.validationInterval = null\n      this.continuousValidation = false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  padding: 40rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n  \n  .title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n  }\n}\n\n.test-section {\n  background: white;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  border-radius: 12rpx;\n  \n  .section-title {\n    font-size: 28rpx;\n    font-weight: bold;\n    color: #666;\n    margin-bottom: 20rpx;\n    display: block;\n  }\n  \n  .state-display {\n    text {\n      display: block;\n      font-size: 26rpx;\n      color: #333;\n      margin-bottom: 10rpx;\n    }\n  }\n}\n\n.test-actions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20rpx;\n  margin-bottom: 40rpx;\n  \n  .test-btn {\n    flex: 1;\n    min-width: 200rpx;\n    height: 80rpx;\n    background: #007aff;\n    color: white;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 26rpx;\n\n    &.validation-btn {\n      background: #52c41a;\n    }\n\n    &.active {\n      background: #ff4d4f;\n    }\n\n    &.direct-btn {\n      background: #fa8c16;\n    }\n\n    &.sync-btn {\n      background: #722ed1;\n    }\n\n    &.venue-btn {\n      background: #13c2c2;\n    }\n\n    &.debug-btn {\n      background: #fa8c16;\n    }\n\n    &.success-btn {\n      background: #52c41a;\n    }\n\n    &.sharing-btn {\n      background: #eb2f96;\n    }\n\n    &.booking-btn {\n      background: #fa8c16;\n    }\n  }\n}\n\n.sync-status {\n  background: white;\n  padding: 30rpx;\n  border-radius: 12rpx;\n  \n  .sync-title {\n    font-size: 28rpx;\n    font-weight: bold;\n    color: #666;\n    margin-bottom: 20rpx;\n    display: block;\n  }\n  \n  .sync-result {\n    display: block;\n    font-size: 26rpx;\n    margin-bottom: 10rpx;\n    \n    &.success {\n      color: #52c41a;\n    }\n    \n    &.error {\n      color: #ff4d4f;\n    }\n  }\n}\n\n.validation-results, .migration-status {\n  background: white;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  border-radius: 12rpx;\n\n  .section-title {\n    font-size: 28rpx;\n    font-weight: bold;\n    color: #666;\n    margin-bottom: 20rpx;\n    display: block;\n  }\n\n  .result-item {\n    margin-bottom: 10rpx;\n\n    .result-status {\n      font-size: 26rpx;\n\n      &.success {\n        color: #52c41a;\n      }\n\n      &.error {\n        color: #ff4d4f;\n      }\n    }\n  }\n\n  .timestamp {\n    font-size: 22rpx;\n    color: #999;\n    margin-top: 10rpx;\n    display: block;\n  }\n\n  .progress-info {\n    text {\n      display: block;\n      font-size: 26rpx;\n      color: #333;\n      margin-bottom: 8rpx;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/pinia-migration-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapState", "mapActions", "uni", "validateMigration", "stopValidation", "startValidation", "useVenueStore", "useSharingStore", "useBookingStore", "useAppStore", "useUserStore", "getMigrationStatus", "_a"], "mappings": ";;;;;;;;;AAwMA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,IACnB;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,GAAGA,uBAAS,CAAC,WAAW,eAAe,CAAC;AAAA,IACxC,GAAGA,cAAQ,SAAC,QAAQ,CAAC,cAAc,UAAU,CAAC;AAAA,IAC9C,GAAGA,cAAAA,SAAS,SAAS;AAAA,MACnB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACrB,CAAC;AAAA,IACD,GAAGA,cAAAA,SAAS,WAAW;AAAA,MACrB,mBAAmB;AAAA,MACnB,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,IACtB,CAAC;AAAA,IACD,GAAGA,cAAAA,SAAS,WAAW;AAAA,MACrB,iBAAiB;AAAA,MACjB,0BAA0B;AAAA,MAC1B,oBAAoB;AAAA,IACtB,CAAC;AAAA,IAED,cAAc;AACZ,aAAO,KAAK;AAAA,IACb;AAAA,IAED,oBAAoB;AAClB,aAAO,KAAK;AAAA,IACb;AAAA,IAED,mBAAmB;AACjB,aAAO,KAAK;AAAA,IACb;AAAA,IAED,eAAe;;AACb,cAAO,UAAK,aAAL,mBAAe;AAAA,IACvB;AAAA,IAED,eAAe;;AACb,eAAO,UAAK,aAAL,mBAAe,YAAW;AAAA,IAClC;AAAA,IAED,qBAAqB;;AACnB,eAAO,UAAK,aAAL,mBAAe,kBAAiB;AAAA,IACxC;AAAA,IAED,oBAAoB;;AAClB,eAAO,UAAK,cAAL,mBAAgB,eAAc;AAAA,IACtC;AAAA,IAED,gBAAgB;;AACd,cAAO,gBAAK,cAAL,mBAAgB,aAAhB,mBAA0B;AAAA,IAClC;AAAA,IAED,iBAAiB;;AACf,eAAO,UAAK,kBAAL,mBAAoB,WAAU;AAAA,IACtC;AAAA,IAED,kBAAkB;;AAChB,eAAO,gBAAK,eAAL,mBAAiB,cAAjB,mBAA4B,WAAU;AAAA,IAC9C;AAAA,IAED,oBAAoB;;AAClB,eAAO,UAAK,eAAL,mBAAiB,YAAW;AAAA,IACpC;AAAA,IAED,kBAAkB;;AAChB,eAAO,UAAK,sBAAL,mBAAwB,WAAU;AAAA,IAC1C;AAAA,IAED,mBAAmB;;AACjB,eAAO,gBAAK,eAAL,mBAAiB,kBAAjB,mBAAgC,WAAU;AAAA,IAClD;AAAA,IAED,mBAAmB;;AACjB,eAAO,UAAK,sBAAL,mBAAwB,WAAU;AAAA,IAC1C;AAAA,IAED,oBAAoB;;AAClB,eAAO,gBAAK,iBAAL,mBAAmB,kBAAnB,mBAAkC,WAAU;AAAA,IACpD;AAAA,IAED,qBAAqB;;AACnB,eAAO,UAAK,wBAAL,mBAA0B,WAAU;AAAA,IAC5C;AAAA,IAED,sBAAsB;;AACpB,eAAO,gBAAK,iBAAL,mBAAmB,oBAAnB,mBAAoC,WAAU;AAAA,IACtD;AAAA,IAED,sBAAsB;;AACpB,eAAO,UAAK,iBAAL,mBAAmB,YAAW;AAAA,IACtC;AAAA,IAED,mBAAmB;;AACjB,eAAO,UAAK,oBAAL,mBAAsB,WAAU;AAAA,IACxC;AAAA,IAED,oBAAoB;;AAClB,eAAO,gBAAK,iBAAL,mBAAmB,gBAAnB,mBAAgC,WAAU;AAAA,IAClD;AAAA,IAED,0BAA0B;;AACxB,eAAO,UAAK,6BAAL,mBAA+B,WAAU;AAAA,IACjD;AAAA,IAED,2BAA2B;;AACzB,eAAO,gBAAK,iBAAL,mBAAmB,kBAAnB,mBAAkC,WAAU;AAAA,IACpD;AAAA,IAED,sBAAsB;;AACpB,eAAO,UAAK,iBAAL,mBAAmB,YAAW;AAAA,IACtC;AAAA,IAED,aAAa;AACX,aAAO;AAAA,QACL,SAAS,KAAK,gBAAgB,KAAK;AAAA,QACnC,SAAS,KAAK,sBAAsB,KAAK;AAAA,QACzC,WAAW,KAAK,qBAAqB,KAAK;AAAA,QAC1C,YAAY,KAAK,mBAAmB,KAAK;AAAA,QACzC,cAAc,KAAK,qBAAqB,KAAK;AAAA,QAC7C,aAAa,KAAK,oBAAoB,KAAK;AAAA,QAC3C,eAAe,KAAK,qBAAqB,KAAK;AAAA,QAC9C,gBAAgB,KAAK,uBAAuB,KAAK;AAAA,QACjD,cAAc,KAAK,qBAAqB,KAAK;AAAA,QAC7C,gBAAgB,KAAK,uBAAuB,KAAK;AAAA,MACnD;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,GAAGC,yBAAW,CAAC,cAAc,kBAAkB,CAAC;AAAA,IAEhD,iBAAiB;AACfC,oBAAAA,iEAAY,UAAU;AACtB,WAAK,WAAW,CAAC,KAAK,WAAW;AAAA,IAClC;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,iEAAY,WAAW;AACvB,YAAM,WAAW,CAAC,KAAK;AACvBA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,oBAAoB,QAAQ;AACxC,WAAK,SAAS,WAAW,QAAQ;AAAA,IAClC;AAAA,IAED,gBAAgB;AACd,YAAM,WAAW,CAAC,KAAK;AACvBA,oBAAAA,MAAA,MAAA,OAAA,8CAAY,iBAAiB,QAAQ;AACrC,WAAK,WAAW,QAAQ;AAAA,IACzB;AAAA,IAED,gBAAgB;AACd,YAAM,WAAW,CAAC,KAAK;AACvBA,oBAAAA,MAAA,MAAA,OAAA,8CAAY,iBAAiB,QAAQ;AACrC,WAAK,iBAAiB,QAAQ;AAAA,IAC/B;AAAA;AAAA,IAGD,gBAAgB;AACdA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,QAAQ;AACpB,WAAK,oBAAoBC,4CAAkB;AAE3CD,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,kBAAkB,UAAU,SAAS;AAAA,QACjD,MAAM,KAAK,kBAAkB,UAAU,YAAY;AAAA,OACpD;AAAA,IACF;AAAA;AAAA,IAGD,6BAA6B;AAC3B,UAAI,KAAK,sBAAsB;AAE7B,YAAI,KAAK,oBAAoB;AAC3BE,oCAAc,eAAC,KAAK,kBAAkB;AACtC,eAAK,qBAAqB;AAAA,QAC5B;AACA,aAAK,uBAAuB;AAC5BF,sBAAAA,MAAA,MAAA,OAAA,8CAAY,SAAS;AAAA,aAChB;AAEL,aAAK,qBAAqBG,0BAAe,gBAAC,GAAI;AAC9C,aAAK,uBAAuB;AAC5BH,sBAAAA,MAAA,MAAA,OAAA,8CAAY,SAAS;AAAA,MACvB;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AACtBA,oBAAAA,MAAA,MAAA,OAAA,8CAAY,aAAa;AACzB,YAAM,aAAa,CAAC,KAAK;AACzB,YAAM,aAAa,CAAC,KAAK;AAGzB,WAAK,SAAS,UAAU;AACxB,WAAK,SAAS,gBAAgB;AAE9BA,0BAAA,MAAA,OAAA,8CAAY,iBAAiB,EAAE,SAAS,YAAY,SAAS,YAAY;AAAA,IAC1E;AAAA;AAAA,IAGD,YAAY;AACVA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,QAAQ;AAGpB,WAAK,SAAS,UAAU,KAAK;AAC7B,WAAK,SAAS,gBAAgB,KAAK;AAEnCA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,SAAS;AAGrB,iBAAW,MAAM;AACf,aAAK,cAAc;AAAA,MACpB,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,qBAAqB;;AACnBA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,0BAA0B;AAEtC,UAAI;AAEF,cAAM,SAAS,KAAK;AACpB,cAAM,SAASI,aAAAA,cAAc;AAE7BJ,sBAAAA,MAAA,MAAA,OAAA,8CAAY,oBAAoB;AAAA,UAC9B,QAAQ,CAAC,CAAC;AAAA,UACV,MAAM,OAAO;AAAA,UACb,cAAa,sCAAQ,gBAAR,mBAAqB;AAAA,UAClC,kBAAkB,QAAO,iCAAQ;AAAA,UACjC,mBAAmB,iCAAQ;AAAA,SAC5B;AAEDA,sBAAAA,MAAA,MAAA,OAAA,8CAAY,oBAAoB;AAAA,UAC9B,QAAQ,CAAC,CAAC;AAAA,UACV,MAAM,OAAO;AAAA,UACb,cAAa,sCAAQ,gBAAR,mBAAqB;AAAA,UAClC,kBAAkB,QAAO,iCAAQ;AAAA,UACjC,mBAAmB,iCAAQ;AAAA,SAC5B;AAEDA,sBAAY,MAAA,MAAA,OAAA,8CAAA,UAAU,WAAW,MAAM;AAGvC,YAAI,QAAQ;AACVA,wBAAAA,MAAY,MAAA,OAAA,8CAAA,YAAY;AACxBA,wBAAA,MAAA,MAAA,OAAA,8CAAY,UAAU,OAAO,GAAG;AAChCA,wBAAAA,MAAY,MAAA,OAAA,8CAAA,iBAAiB,OAAO,KAAK,OAAO,UAAU,CAAA,CAAE,CAAC;AAC7DA,8BAAA,MAAA,OAAA,8CAAY,WAAW,OAAO,oBAAoB,MAAM,CAAC;AACzDA,8BAAY,MAAA,OAAA,8CAAA,UAAU,OAAO,eAAe,MAAM,CAAC;AAGnD,gBAAM,aAAa,OAAO,yBAAyB,QAAQ,cAAc;AACzEA,wBAAAA,iEAAY,sBAAsB,UAAU;AAAA,QAC9C;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,8CAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAEAA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,0BAA0B;AAAA,IACvC;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,qBAAqB;AAEjC,UAAI;AACF,cAAM,aAAaI,aAAAA,cAAc;AAEjC,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM,gBAAgB;AAAA,QAClC;AAEAJ,sBAAAA,iEAAY,YAAY;AACxBA,uFAAY,gBAAgB,WAAW,SAAS;AAChDA,sBAAY,MAAA,MAAA,OAAA,8CAAA,cAAc,WAAW,OAAO;AAC5CA,sBAAA,MAAA,MAAA,OAAA,8CAAY,oBAAoB,WAAW,aAAa;AAExDA,sBAAAA,iEAAY,YAAY;AACxB,mBAAW,WAAW,IAAI;AAC1BA,sBAAY,MAAA,MAAA,OAAA,8CAAA,yBAAyB,WAAW,OAAO;AAEvD,mBAAW,iBAAiB,CAAC,SAAS,OAAO,CAAC;AAC9CA,sBAAY,MAAA,MAAA,OAAA,8CAAA,iBAAiB,WAAW,aAAa;AAErDA,sBAAAA,iEAAY,YAAY;AACxB,mBAAW,MAAM;AACf,qBAAW,WAAW,KAAK;AAC3B,qBAAW,mBAAmB;AAC9BA,wBAAA,MAAA,MAAA,OAAA,8CAAY,iBAAiB,WAAW,OAAO;AAC/CA,wBAAY,MAAA,MAAA,OAAA,8CAAA,uBAAuB,WAAW,aAAa;AAAA,QAC5D,GAAE,GAAI;AAEPA,sBAAAA,MAAY,MAAA,OAAA,8CAAA,eAAe;AAC3BA,sBAAY,MAAA,MAAA,OAAA,8CAAA,kBAAkB,WAAW,WAAW;AACpDA,uFAAY,gBAAgB,WAAW,SAAS;AAEhDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,8CAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAEAA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,qBAAqB;AAAA,IAClC;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtBA,oBAAAA,iEAAY,kBAAkB;AAC9B,UAAI;AAEF,cAAM,KAAK,OAAO,SAAS,4BAA4B,EAAE,MAAM,GAAG,UAAU,GAAG;AAC/EA,sBAAAA,MAAA,MAAA,OAAA,8CAAY,gBAAgB;AAG5B,aAAK,OAAO,OAAO,uBAAuB,IAAI;AAC9C,mBAAW,MAAM;AACf,eAAK,OAAO,OAAO,uBAAuB,KAAK;AAAA,QAChD,GAAE,GAAI;AAEPA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,8CAAA,qBAAqB,KAAK;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,mBAAmB;AACvBA,oBAAAA,iEAAY,mBAAmB;AAC/B,UAAI;AACF,cAAM,eAAeK,eAAAA,gBAAgB;AAErC,YAAI,CAAC,cAAc;AACjB,gBAAM,IAAI,MAAM,kBAAkB;AAAA,QACpC;AAEAL,sBAAAA,iEAAY,YAAY;AACxBA,sBAAA,MAAA,MAAA,OAAA,8CAAY,oBAAoB,aAAa,aAAa;AAC1DA,sBAAY,MAAA,MAAA,OAAA,8CAAA,sBAAsB,aAAa,eAAe;AAC9DA,sBAAY,MAAA,MAAA,OAAA,8CAAA,cAAc,aAAa,OAAO;AAE9CA,sBAAAA,iEAAY,aAAa;AACzB,YAAI;AACF,gBAAM,aAAa,qBAAqB,EAAE,MAAM,GAAG,UAAU,GAAG;AAChEA,wBAAAA,MAAY,MAAA,OAAA,8CAAA,iBAAiB;AAAA,QAC/B,SAAS,UAAU;AACjBA,wBAAAA,MAAA,MAAA,QAAA,8CAAa,mBAAmB,QAAQ;AAAA,QAC1C;AAEAA,sBAAAA,iEAAY,YAAY;AACxB,qBAAa,WAAW,IAAI;AAC5BA,sBAAA,MAAA,MAAA,OAAA,8CAAY,yBAAyB,aAAa,OAAO;AAEzD,mBAAW,MAAM;AACf,uBAAa,WAAW,KAAK;AAC7BA,wBAAY,MAAA,MAAA,OAAA,8CAAA,0BAA0B,aAAa,OAAO;AAAA,QAC3D,GAAE,GAAI;AAEPA,sBAAAA,MAAY,MAAA,OAAA,8CAAA,eAAe;AAC3BA,sBAAA,MAAA,MAAA,OAAA,8CAAY,yBAAyB,aAAa,kBAAkB;AACpEA,uFAAY,gBAAgB,aAAa,SAAS;AAElDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,8CAAA,sBAAsB,KAAK;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,sBAAsB,MAAM,OAAO;AAAA,UAC1C,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpBA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,gBAAgB;AAC5B,UAAI;AAEF,cAAM,KAAK,OAAO,SAAS,sBAAsB,EAAE,MAAM,GAAG,UAAU,GAAG;AACzEA,sBAAAA,MAAY,MAAA,OAAA,8CAAA,cAAc;AAG1B,aAAK,OAAO,OAAO,qBAAqB,IAAI;AAC5C,mBAAW,MAAM;AACf,eAAK,OAAO,OAAO,qBAAqB,KAAK;AAAA,QAC9C,GAAE,GAAI;AAEPA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,8CAAA,mBAAmB,KAAK;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrBA,oBAAAA,MAAY,MAAA,OAAA,8CAAA,iBAAiB;AAC7B,UAAI;AAEF,cAAM,kBAAkBI,aAAAA,cAAc;AAEtC,YAAI,CAAC,iBAAiB;AACpB,gBAAM,IAAI,MAAM,gBAAgB;AAAA,QAClC;AAEAJ,4BAAA,MAAA,OAAA,8CAAY,yBAAyB,OAAO,oBAAoB,eAAe,CAAC;AAChFA,sBAAY,MAAA,MAAA,OAAA,8CAAA,yBAAyB,OAAO,gBAAgB,YAAY;AAGxE,YAAI,OAAO,gBAAgB,iBAAiB,YAAY;AACtD,cAAI;AACF,kBAAM,gBAAgB,aAAa,EAAE,MAAM,GAAG,UAAU,GAAG;AAC3DA,0BAAAA,MAAY,MAAA,OAAA,8CAAA,eAAe;AAAA,UAC3B,SAAO,OAAO;AACdA,0BAAAA,MAAa,MAAA,QAAA,8CAAA,qBAAqB,KAAK;AAAA,UACzC;AAAA,eACK;AACLA,wBAAAA,MAAA,MAAA,QAAA,8CAAa,4BAA4B;AAAA,QAC3C;AAGA,wBAAgB,WAAW,IAAI;AAC/BA,sBAAAA,MAAA,MAAA,OAAA,8CAAY,gBAAgB;AAE5B,mBAAW,MAAM;AACf,0BAAgB,WAAW,KAAK;AAChCA,wBAAAA,MAAY,MAAA,OAAA,8CAAA,iBAAiB;AAAA,QAC9B,GAAE,GAAI;AAGP,wBAAgB,iBAAiB,CAAC,SAAS,OAAO,CAAC;AACnDA,sBAAAA,MAAA,MAAA,OAAA,8CAAY,QAAQ;AAEpB,mBAAW,MAAM;AACf,0BAAgB,mBAAmB;AACnCA,wBAAAA,MAAY,MAAA,OAAA,8CAAA,QAAQ;AAAA,QACrB,GAAE,GAAI;AAGP,aAAK,aAAa;AAElBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,8CAAA,oBAAoB,KAAK;AACvCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,oBAAoB,MAAM,OAAO;AAAA,UACxC,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtBA,oBAAAA,iEAAY,kBAAkB;AAC9B,UAAI;AAEF,cAAM,KAAK,OAAO,SAAS,2BAA2B,EAAE,MAAM,GAAG,UAAU,GAAG;AAC9EA,sBAAAA,MAAA,MAAA,OAAA,8CAAY,gBAAgB;AAG5B,aAAK,OAAO,OAAO,uBAAuB,IAAI;AAC9C,mBAAW,MAAM;AACf,eAAK,OAAO,OAAO,uBAAuB,KAAK;AAAA,QAChD,GAAE,GAAI;AAEPA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,8CAAA,qBAAqB,KAAK;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,mBAAmB;AACvBA,oBAAAA,iEAAY,mBAAmB;AAC/B,UAAI;AACF,cAAM,eAAeM,eAAAA,gBAAgB;AAErC,YAAI,CAAC,cAAc;AACjB,gBAAM,IAAI,MAAM,kBAAkB;AAAA,QACpC;AAEAN,sBAAAA,iEAAY,YAAY;AACxBA,sBAAY,MAAA,MAAA,OAAA,8CAAA,kBAAkB,aAAa,WAAW;AACtDA,sBAAA,MAAA,MAAA,OAAA,8CAAY,oBAAoB,aAAa,aAAa;AAC1DA,sBAAY,MAAA,MAAA,OAAA,8CAAA,cAAc,aAAa,OAAO;AAE9CA,sBAAAA,iEAAY,aAAa;AACzB,YAAI;AACF,gBAAM,aAAa,gBAAgB,EAAE,MAAM,GAAG,UAAU,GAAG;AAC3DA,wBAAAA,MAAY,MAAA,OAAA,8CAAA,iBAAiB;AAAA,QAC/B,SAAS,UAAU;AACjBA,wBAAAA,MAAA,MAAA,QAAA,8CAAa,mBAAmB,QAAQ;AAAA,QAC1C;AAEAA,sBAAAA,iEAAY,YAAY;AACxB,qBAAa,WAAW,IAAI;AAC5BA,sBAAA,MAAA,MAAA,OAAA,8CAAY,yBAAyB,aAAa,OAAO;AAEzD,mBAAW,MAAM;AACf,uBAAa,WAAW,KAAK;AAC7BA,wBAAY,MAAA,MAAA,OAAA,8CAAA,0BAA0B,aAAa,OAAO;AAAA,QAC3D,GAAE,GAAI;AAEPA,sBAAAA,MAAY,MAAA,OAAA,8CAAA,eAAe;AAC3BA,sBAAA,MAAA,MAAA,OAAA,8CAAY,oBAAoB,aAAa,aAAa;AAC1DA,uFAAY,gBAAgB,aAAa,SAAS;AAElDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,8CAAA,sBAAsB,KAAK;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,sBAAsB,MAAM,OAAO;AAAA,UAC1C,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;;AACPA,kBAAAA,MAAA,MAAA,OAAA,8CAAY,gBAAgB;AAC5B,QAAI;AACF,WAAK,WAAWO,uBAAY;AAC5B,WAAK,YAAYC,yBAAa;AAC9B,WAAK,aAAaJ,2BAAc;AAChC,WAAK,eAAeC,+BAAgB;AACpC,WAAK,eAAeC,+BAAgB;AACpC,WAAK,kBAAkBG,0CAAmB;AAE1CT,oBAAAA,MAAY,MAAA,OAAA,8CAAA,gBAAgB;AAAA,QAC1B,UAAU,CAAC,CAAC,KAAK;AAAA,QACjB,WAAW,CAAC,CAAC,KAAK;AAAA,QAClB,YAAY,CAAC,CAAC,KAAK;AAAA,QACnB,gBAAgB,OAAO,KAAK;AAAA,QAC5B,4BAA4B,SAAO,UAAK,eAAL,mBAAiB;AAAA,OACrD;AAGD,UAAI,KAAK,cAAc,OAAO,KAAK,WAAW,iBAAiB,YAAY;AACzEA,sBAAAA,MAAa,MAAA,QAAA,8CAAA,iCAAiC;AAE9C,mBAAW,MAAM;;AACf,eAAK,aAAaI,2BAAc;AAChCJ,yFAAY,0BAA0B,SAAOU,MAAA,KAAK,eAAL,gBAAAA,IAAiB,aAAY;AAAA,QAC3E,GAAE,GAAG;AAAA,MACR;AAAA,IACA,SAAO,OAAO;AACdV,oBAAAA,MAAc,MAAA,SAAA,8CAAA,gBAAgB,KAAK;AAAA,IACrC;AAGA,eAAW,MAAM;AACfA,oBAAAA,iEAAY,WAAW;AAAA,QACrB,MAAM;AAAA,UACJ,SAAS,KAAK;AAAA,UACd,SAAS,KAAK;AAAA,UACd,WAAW,KAAK;AAAA,UAChB,UAAU,KAAK;AAAA,QAChB;AAAA,QACD,OAAO;AAAA,UACL,SAAS,KAAK;AAAA,UACd,SAAS,KAAK;AAAA,UACd,WAAW,KAAK;AAAA,UAChB,UAAU,KAAK;AAAA,QACjB;AAAA,OACD;AAGD,WAAK,cAAc;AAAA,IACpB,GAAE,GAAG;AAAA,EACP;AAAA,EAED,WAAW;AAET,QAAI,KAAK,oBAAoB;AAC3BE,gCAAc,eAAC,KAAK,kBAAkB;AACtC,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACp0BA,GAAG,WAAW,eAAe;"}