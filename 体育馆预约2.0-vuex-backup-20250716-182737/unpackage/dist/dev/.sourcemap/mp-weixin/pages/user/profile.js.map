{"version": 3, "file": "profile.js", "sources": ["pages/user/profile.vue", "pages/user/profile.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 头部用户信息 -->\n    <view class=\"header\">\n      <view class=\"user-info\">\n        <view class=\"avatar-wrapper\" @click=\"changeAvatar\">\n          <image \n            :src=\"userInfo?.avatar || '/static/images/default-avatar.svg'\" \n            class=\"avatar\"\n            mode=\"aspectFill\"\n          />\n          <view class=\"avatar-edit\">\n            <text class=\"edit-icon\">📷</text>\n          </view>\n        </view>\n        \n        <view class=\"user-details\">\n          <text class=\"nickname\">{{ userInfo?.nickname || userInfo?.username || '未设置昵称' }}</text>\n          <text class=\"username\">用户名: {{ userInfo?.username || '未设置用户名' }}</text>\n          <text class=\"phone\">{{ formatPhone(userInfo?.phone || '') }}</text>\n        </view>\n        \n        <view class=\"edit-btn\" @click=\"editProfile\">\n          <text class=\"edit-text\">编辑</text>\n        </view>\n      </view>\n      \n      <!-- 统计信息 -->\n      <view class=\"stats\">\n        <view class=\"stat-item\" @click=\"navigateTo('/pages/booking/list')\">\n          <text class=\"stat-number\">{{ userStats.totalBookings || 0 }}</text>\n          <text class=\"stat-label\">总预约</text>\n        </view>\n        <view class=\"stat-item\" @click=\"navigateTo('/pages/sharing/list?tab=my')\">\n          <text class=\"stat-number\">{{ userStats.totalSharings || 0 }}</text>\n          <text class=\"stat-label\">拼场次数</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 功能菜单 -->\n    <view class=\"menu-section\">\n      <!-- 我的订单 -->\n      <view class=\"menu-group\">\n        <view class=\"group-title\">\n          <text class=\"title-text\">我的订单</text>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"navigateTo('/pages/booking/list')\">\n          <view class=\"item-left\">\n            <text class=\"item-icon\">📅</text>\n            <text class=\"item-text\">我的预约</text>\n          </view>\n          <view class=\"item-right\">\n            <text v-if=\"pendingBookings > 0\" class=\"badge\">{{ pendingBookings }}</text>\n            <text class=\"arrow\">></text>\n          </view>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"navigateTo('/pages/sharing/list?tab=my')\">\n          <view class=\"item-left\">\n            <text class=\"item-icon\">👥</text>\n            <text class=\"item-text\">我的拼场</text>\n          </view>\n          <view class=\"item-right\">\n            <text v-if=\"pendingSharings > 0\" class=\"badge\">{{ pendingSharings }}</text>\n            <text class=\"arrow\">></text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 拼场申请 -->\n      <view class=\"menu-group\">\n        <view class=\"group-title\">\n          <text class=\"title-text\">拼场申请</text>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"navigateTo('/pages/sharing/requests')\">\n          <view class=\"item-left\">\n            <text class=\"item-icon\">📝</text>\n            <text class=\"item-text\">我的申请</text>\n          </view>\n          <view class=\"item-right\">\n            <text v-if=\"pendingRequests > 0\" class=\"badge\">{{ pendingRequests }}</text>\n            <text class=\"arrow\">></text>\n          </view>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"navigateTo('/pages/sharing/received')\">\n          <view class=\"item-left\">\n            <text class=\"item-icon\">📬</text>\n            <text class=\"item-text\">收到的申请</text>\n          </view>\n          <view class=\"item-right\">\n            <text v-if=\"receivedRequests > 0\" class=\"badge\">{{ receivedRequests }}</text>\n            <text class=\"arrow\">></text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 设置 -->\n      <view class=\"menu-group\">\n        <view class=\"group-title\">\n          <text class=\"title-text\">设置</text>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"showLogoutConfirm\">\n          <view class=\"item-left\">\n            <text class=\"item-icon\">🚪</text>\n            <text class=\"item-text\">退出登录</text>\n          </view>\n          <view class=\"item-right\">\n            <text class=\"arrow\">></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 退出登录确认弹窗 -->\n    <uni-popup ref=\"logoutPopup\" type=\"dialog\">\n      <uni-popup-dialog \n        type=\"warn\"\n        title=\"确认退出\"\n        content=\"确定要退出登录吗？\"\n        :before-close=\"true\"\n        @close=\"handleLogoutCancel\"\n        @confirm=\"handleLogoutConfirm\"\n      ></uni-popup-dialog>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { mapState, mapGetters, mapActions } from 'vuex'\n\nexport default {\n  name: 'UserProfile',\n  \n  data() {\n    return {\n      pendingBookings: 0,\n      pendingSharings: 0,\n      pendingRequests: 0,\n      receivedRequests: 0\n    }\n  },\n  \n  computed: {\n    ...mapState('user', ['userInfo', 'userStats']),\n    ...mapGetters('user', ['isLoggedIn'])\n  },\n\n  onLoad() {\n    // 加载用户数据\n    this.loadUserData();\n  },\n  \n  onShow() {\n    // 页面显示时刷新用户数据\n    this.loadUserData();\n  },\n  \n  methods: {\n    ...mapActions('user', [\n      'getUserInfo',\n      'getUserStats',\n      'logout'\n    ]),\n    \n    ...mapActions('booking', [\n      'getUserBookings',\n      'getUserSharingOrders'\n    ]),\n    \n    ...mapActions('sharing', [\n      'getMySharingRequests',\n      'getReceivedSharingRequests'\n    ]),\n    \n    // 加载用户数据\n    async loadUserData() {\n        try {\n          console.log('[Profile] 开始加载用户数据')\n          // 获取用户信息\n          await this.getUserInfo()\n          console.log('[Profile] 用户信息:', this.userInfo)\n          \n          // 获取用户统计数据\n          try {\n            await this.getUserStats()\n          } catch (statsError) {\n            console.error('获取用户统计失败:', statsError)\n            // 统计数据获取失败时，设置默认值\n            this.userStats = {\n              totalBookings: 0,\n              pendingBookings: 0,\n              completedBookings: 0,\n              totalSpent: 0\n            }\n          }\n          \n          // 获取待处理的预约和拼场数量\n          await this.loadPendingCounts()\n          \n        } catch (error) {\n          console.error('加载用户数据失败:', error)\n          uni.showToast({\n            title: '加载用户信息失败',\n            icon: 'none'\n          })\n        }\n      },\n    \n    // 加载待处理数量\n    async loadPendingCounts() {\n      try {\n        // 获取待确认的预约\n        const bookingResult = await this.getUserBookings({\n          status: 'pending',\n          page: 1,\n          pageSize: 1\n        })\n        this.pendingBookings = bookingResult.total || 0\n        \n        // 获取待处理的拼场\n        const sharingResult = await this.getUserSharingOrders({\n          status: 'pending',\n          page: 1,\n          pageSize: 1\n        })\n        // 处理拼场数据，可能是数组格式\n        if (Array.isArray(sharingResult.data)) {\n          this.pendingSharings = sharingResult.data.length\n        } else {\n          this.pendingSharings = sharingResult.total || 0\n        }\n        \n        // 获取我发出的待处理申请\n        const myRequestsResult = await this.getMySharingRequests()\n        if (Array.isArray(myRequestsResult)) {\n          this.pendingRequests = myRequestsResult.filter(req => req.status === 'pending').length\n        } else {\n          this.pendingRequests = 0\n        }\n        \n        // 获取收到的待处理申请\n        const receivedRequestsResult = await this.getReceivedSharingRequests()\n        if (Array.isArray(receivedRequestsResult)) {\n          this.receivedRequests = receivedRequestsResult.filter(req => req.status === 'pending').length\n        } else {\n          this.receivedRequests = 0\n        }\n        \n      } catch (error) {\n        console.error('加载待处理数量失败:', error)\n        // 设置默认值\n        this.pendingBookings = 0\n        this.pendingSharings = 0\n        this.pendingRequests = 0\n        this.receivedRequests = 0\n      }\n    },\n    \n    // 格式化手机号\n    formatPhone(phone) {\n      if (!phone) return ''\n      return phone.replace(/(\\d{3})(\\d{4})(\\d{4})/, '$1****$3')\n    },\n    \n    // 更换头像\n    changeAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          const tempFilePath = res.tempFilePaths[0]\n          this.uploadAvatar(tempFilePath)\n        }\n      })\n    },\n    \n    // 上传头像\n    async uploadAvatar(filePath) {\n      try {\n        uni.showLoading({ title: '上传中...' })\n        \n        // 这里应该调用上传头像的API\n        // const result = await this.uploadUserAvatar(filePath)\n        \n        uni.hideLoading()\n        uni.showToast({\n          title: '头像更新成功',\n          icon: 'success'\n        })\n        \n        // 重新获取用户信息\n        await this.getUserInfo()\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('上传头像失败:', error)\n        uni.showToast({\n          title: '上传失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 编辑资料\n    editProfile() {\n      // 确保用户信息已加载\n      if (!this.userInfo) {\n        this.loadUserData()\n      }\n      uni.navigateTo({\n        url: '/pages/user/edit-profile'\n      })\n    },\n    \n    // 页面跳转\n    navigateTo(url) {\n      // 检查是否为tabbar页面\n      const tabbarPages = [\n        '/pages/index/index',\n        '/pages/venue/list', \n        '/pages/sharing/list',\n        '/pages/booking/list',\n        '/pages/user/profile'\n      ]\n      \n      // 提取页面路径（去掉查询参数）\n      const pagePath = url.split('?')[0]\n      \n      if (tabbarPages.includes(pagePath)) {\n        // 如果是tabbar页面，使用switchTab\n        uni.switchTab({ url: pagePath })\n      } else {\n        // 普通页面使用navigateTo\n        uni.navigateTo({ url })\n      }\n    },\n    \n    // 显示退出登录确认\n    showLogoutConfirm() {\n      this.$refs.logoutPopup.open()\n    },\n    \n    // 取消退出登录\n    handleLogoutCancel() {\n      this.$refs.logoutPopup.close()\n    },\n    \n    // 确认退出登录\n    async handleLogoutConfirm() {\n      try {\n        await this.logout()\n        \n        this.$refs.logoutPopup.close()\n        \n        uni.showToast({\n          title: '已退出登录',\n          icon: 'success'\n        })\n        \n        // 跳转到登录页\n        setTimeout(() => {\n          uni.reLaunch({\n            url: '/pages/user/login'\n          })\n        }, 1000)\n        \n      } catch (error) {\n        console.error('退出登录失败:', error)\n        uni.showToast({\n          title: '退出失败',\n          icon: 'error'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n// 头部\n.header {\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8f65 100%);\n  padding: 40rpx 40rpx 30rpx;\n  \n  // 用户信息\n  .user-info {\n    display: flex;\n    align-items: center;\n    margin-bottom: 40rpx;\n    \n    .avatar-wrapper {\n      position: relative;\n      margin-right: 24rpx;\n      \n      .avatar {\n        width: 120rpx;\n        height: 120rpx;\n        border-radius: 60rpx;\n        border: 4rpx solid rgba(255, 255, 255, 0.3);\n      }\n      \n      .avatar-edit {\n        position: absolute;\n        bottom: -8rpx;\n        right: -8rpx;\n        width: 40rpx;\n        height: 40rpx;\n        background-color: #ffffff;\n        border-radius: 20rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n        \n        .edit-icon {\n          font-size: 20rpx;\n        }\n      }\n    }\n    \n    .user-details {\n      flex: 1;\n      \n      .nickname {\n        display: block;\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #ffffff;\n        margin-bottom: 8rpx;\n      }\n      \n      .username {\n        display: block;\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.9);\n        margin-bottom: 8rpx;\n      }\n      \n      .phone {\n        display: block;\n        font-size: 26rpx;\n        color: rgba(255, 255, 255, 0.8);\n        margin-bottom: 12rpx;\n      }\n      \n\n    }\n    \n    .edit-btn {\n      padding: 12rpx 24rpx;\n      background-color: rgba(255, 255, 255, 0.2);\n      border-radius: 20rpx;\n      \n      .edit-text {\n        font-size: 24rpx;\n        color: #ffffff;\n      }\n    }\n  }\n  \n  // 统计信息\n  .stats {\n    display: flex;\n    background-color: rgba(255, 255, 255, 0.15);\n    border-radius: 16rpx;\n    padding: 20rpx 0;\n    \n    .stat-item {\n      flex: 1;\n      text-align: center;\n      \n      .stat-number {\n        display: block;\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #ffffff;\n        margin-bottom: 8rpx;\n      }\n      \n      .stat-label {\n        font-size: 22rpx;\n        color: rgba(255, 255, 255, 0.8);\n      }\n    }\n  }\n}\n\n// 菜单区域\n.menu-section {\n  padding: 20rpx;\n  \n  .menu-group {\n    background-color: #ffffff;\n    border-radius: 16rpx;\n    margin-bottom: 20rpx;\n    overflow: hidden;\n    \n    .group-title {\n      padding: 24rpx 32rpx 16rpx;\n      \n      .title-text {\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #333333;\n      }\n    }\n    \n    .menu-item {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 24rpx 32rpx;\n      border-bottom: 1rpx solid #f0f0f0;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .item-left {\n        display: flex;\n        align-items: center;\n        \n        .item-icon {\n          font-size: 32rpx;\n          margin-right: 20rpx;\n        }\n        \n        .item-text {\n          font-size: 28rpx;\n          color: #333333;\n        }\n      }\n      \n      .item-right {\n        display: flex;\n        align-items: center;\n        \n        .badge {\n          background-color: #ff4d4f;\n          color: #ffffff;\n          font-size: 20rpx;\n          padding: 4rpx 8rpx;\n          border-radius: 10rpx;\n          margin-right: 12rpx;\n          min-width: 32rpx;\n          text-align: center;\n        }\n        \n\n        \n        .arrow {\n          font-size: 24rpx;\n          color: #cccccc;\n        }\n      }\n    }\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/user/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapState", "mapGetters", "mapActions", "uni"], "mappings": ";;AAuIA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACpB;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,GAAGA,cAAQ,SAAC,QAAQ,CAAC,YAAY,WAAW,CAAC;AAAA,IAC7C,GAAGC,yBAAW,QAAQ,CAAC,YAAY,CAAC;AAAA,EACrC;AAAA,EAED,SAAS;AAEP,SAAK,aAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAEP,SAAK,aAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA,IACP,GAAGC,cAAAA,WAAW,QAAQ;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IAED,GAAGA,cAAAA,WAAW,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IAED,GAAGA,cAAAA,WAAW,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,IACF,CAAC;AAAA;AAAA,IAGD,MAAM,eAAe;AACjB,UAAI;AACFC,sBAAAA,MAAA,MAAA,OAAA,iCAAY,oBAAoB;AAEhC,cAAM,KAAK,YAAY;AACvBA,0EAAY,mBAAmB,KAAK,QAAQ;AAG5C,YAAI;AACF,gBAAM,KAAK,aAAa;AAAA,QAC1B,SAAS,YAAY;AACnBA,wBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,UAAU;AAErC,eAAK,YAAY;AAAA,YACf,eAAe;AAAA,YACf,iBAAiB;AAAA,YACjB,mBAAmB;AAAA,YACnB,YAAY;AAAA,UACd;AAAA,QACF;AAGA,cAAM,KAAK,kBAAkB;AAAA,MAE7B,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGH,MAAM,oBAAoB;AACxB,UAAI;AAEF,cAAM,gBAAgB,MAAM,KAAK,gBAAgB;AAAA,UAC/C,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD,aAAK,kBAAkB,cAAc,SAAS;AAG9C,cAAM,gBAAgB,MAAM,KAAK,qBAAqB;AAAA,UACpD,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAED,YAAI,MAAM,QAAQ,cAAc,IAAI,GAAG;AACrC,eAAK,kBAAkB,cAAc,KAAK;AAAA,eACrC;AACL,eAAK,kBAAkB,cAAc,SAAS;AAAA,QAChD;AAGA,cAAM,mBAAmB,MAAM,KAAK,qBAAqB;AACzD,YAAI,MAAM,QAAQ,gBAAgB,GAAG;AACnC,eAAK,kBAAkB,iBAAiB,OAAO,SAAO,IAAI,WAAW,SAAS,EAAE;AAAA,eAC3E;AACL,eAAK,kBAAkB;AAAA,QACzB;AAGA,cAAM,yBAAyB,MAAM,KAAK,2BAA2B;AACrE,YAAI,MAAM,QAAQ,sBAAsB,GAAG;AACzC,eAAK,mBAAmB,uBAAuB,OAAO,SAAO,IAAI,WAAW,SAAS,EAAE;AAAA,eAClF;AACL,eAAK,mBAAmB;AAAA,QAC1B;AAAA,MAEA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,iCAAA,cAAc,KAAK;AAEjC,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,MAAM,QAAQ,yBAAyB,UAAU;AAAA,IACzD;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,IAAI,cAAc,CAAC;AACxC,eAAK,aAAa,YAAY;AAAA,QAChC;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,aAAa,UAAU;AAC3B,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAKnCA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,cAAM,KAAK,YAAY;AAAA,MAEvB,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEZ,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,aAAa;AAAA,MACpB;AACAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,KAAK;AAEd,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,WAAW,IAAI,MAAM,GAAG,EAAE,CAAC;AAEjC,UAAI,YAAY,SAAS,QAAQ,GAAG;AAElCA,sBAAAA,MAAI,UAAU,EAAE,KAAK,SAAO,CAAG;AAAA,aAC1B;AAELA,4BAAI,WAAW,EAAE,KAAK;AAAA,MACxB;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,MAAM,YAAY,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,MAAM,YAAY,MAAM;AAAA,IAC9B;AAAA;AAAA,IAGD,MAAM,sBAAsB;AAC1B,UAAI;AACF,cAAM,KAAK,OAAO;AAElB,aAAK,MAAM,YAAY,MAAM;AAE7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,WACN;AAAA,QACF,GAAE,GAAI;AAAA,MAEP,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5XA,GAAG,WAAW,eAAe;"}