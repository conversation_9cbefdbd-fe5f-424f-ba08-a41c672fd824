{"version": 3, "file": "edit-profile.js", "sources": ["pages/user/edit-profile.vue", "pages/user/edit-profile.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 头像编辑 -->\n    <view class=\"avatar-section\">\n      <view class=\"avatar-wrapper\" @click=\"changeAvatar\">\n        <image \n          :src=\"formData.avatar || '/static/images/default-avatar.svg'\" \n          class=\"avatar\"\n          mode=\"aspectFill\"\n        />\n        <view class=\"avatar-edit\">\n          <text class=\"edit-icon\">📷</text>\n        </view>\n      </view>\n      <text class=\"avatar-tip\">点击更换头像</text>\n    </view>\n    \n    <!-- 个人信息表单 -->\n    <view class=\"form-section\">\n      <!-- 用户名 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">用户名</text>\n        <input \n          v-model=\"formData.username\" \n          class=\"item-input\" \n          placeholder=\"请输入用户名\"\n          maxlength=\"20\"\n        />\n      </view>\n      \n      <!-- 昵称 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">昵称</text>\n        <input \n          v-model=\"formData.nickname\" \n          class=\"item-input\" \n          placeholder=\"请输入昵称\"\n          maxlength=\"20\"\n        />\n      </view>\n      \n      <!-- 性别 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">性别</text>\n        <picker \n          mode=\"selector\" \n          :range=\"genderOptions\" \n          :value=\"genderIndex\"\n          @change=\"onGenderChange\"\n        >\n          <view class=\"picker-text\">\n            {{ formData.gender || '请选择性别' }}\n          </view>\n        </picker>\n      </view>\n      \n      <!-- 生日 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">生日</text>\n        <picker \n          mode=\"date\" \n          :value=\"formData.birthday\"\n          @change=\"onBirthdayChange\"\n        >\n          <view class=\"picker-text\">\n            {{ formData.birthday || '请选择生日' }}\n          </view>\n        </picker>\n      </view>\n      \n      <!-- 手机号 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">手机号</text>\n        <view class=\"phone-wrapper\">\n          <text class=\"phone-text\">{{ formatPhone(userInfo?.phone || '') }}</text>\n        </view>\n      </view>\n      \n      <!-- 邮箱 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">邮箱</text>\n        <input \n          v-model=\"formData.email\" \n          class=\"item-input\" \n          placeholder=\"请输入邮箱\"\n          type=\"email\"\n        />\n      </view>\n      \n      <!-- 修改密码 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">修改密码</text>\n        <button class=\"change-password-btn\" @click=\"showPasswordDialog\">修改密码</button>\n      </view>\n      \n      <!-- 个人简介 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">个人简介</text>\n        <textarea \n          v-model=\"formData.bio\" \n          class=\"item-textarea\" \n          placeholder=\"请输入个人简介\"\n          maxlength=\"200\"\n        />\n      </view>\n      \n      <!-- 运动偏好 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">运动偏好</text>\n        <view class=\"sports-tags\">\n          <view \n            class=\"sport-tag\"\n            :class=\"{ active: formData.sportsPreferences.includes(sport) }\"\n            v-for=\"sport in sportsOptions\"\n            :key=\"sport\"\n            @click=\"toggleSport(sport)\"\n          >\n            {{ sport }}\n          </view>\n        </view>\n      </view>\n      \n      <!-- 所在城市 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">所在城市</text>\n        <picker \n          mode=\"region\" \n          :value=\"regionValue\"\n          @change=\"onRegionChange\"\n        >\n          <view class=\"picker-text\">\n            {{ formData.city || '请选择城市' }}\n          </view>\n        </picker>\n      </view>\n    </view>\n    \n    <!-- 隐私设置 -->\n    <view class=\"privacy-section\">\n      <text class=\"section-title\">隐私设置</text>\n      \n      <view class=\"privacy-item\">\n        <text class=\"privacy-label\">允许他人查看我的预约记录</text>\n        <switch \n          :checked=\"formData.showBookingHistory\"\n          @change=\"onPrivacyChange('showBookingHistory', $event)\"\n        />\n      </view>\n      \n      <view class=\"privacy-item\">\n        <text class=\"privacy-label\">允许他人邀请我参与拼场</text>\n        <switch \n          :checked=\"formData.allowSharingInvite\"\n          @change=\"onPrivacyChange('allowSharingInvite', $event)\"\n        />\n      </view>\n      \n      <view class=\"privacy-item\">\n        <text class=\"privacy-label\">接收系统通知</text>\n        <switch \n          :checked=\"formData.receiveNotifications\"\n          @change=\"onPrivacyChange('receiveNotifications', $event)\"\n        />\n      </view>\n    </view>\n    \n    <!-- 底部操作 -->\n    <view class=\"bottom-actions\">\n      <button class=\"cancel-btn\" @click=\"goBack\">取消</button>\n      <button class=\"save-btn\" @click=\"saveProfile\">保存</button>\n    </view>\n    \n\n    <!-- 修改密码弹窗 -->\n    <uni-popup ref=\"passwordPopup\" type=\"center\">\n      <view class=\"password-dialog\">\n        <view class=\"dialog-header\">\n          <text class=\"dialog-title\">修改密码</text>\n          <text class=\"dialog-close\" @click=\"closePasswordDialog\">×</text>\n        </view>\n        \n        <view class=\"dialog-content\">\n          <view class=\"password-item\">\n            <text class=\"password-label\">当前密码</text>\n            <input \n              v-model=\"passwordForm.oldPassword\" \n              class=\"password-input\" \n              placeholder=\"请输入当前密码\"\n              type=\"password\"\n            />\n          </view>\n          \n          <view class=\"password-item\">\n            <text class=\"password-label\">新密码</text>\n            <input \n              v-model=\"passwordForm.newPassword\" \n              class=\"password-input\" \n              placeholder=\"请输入新密码\"\n              type=\"password\"\n            />\n          </view>\n          \n          <view class=\"password-item\">\n            <text class=\"password-label\">确认新密码</text>\n            <input \n              v-model=\"passwordForm.confirmPassword\" \n              class=\"password-input\" \n              placeholder=\"请再次输入新密码\"\n              type=\"password\"\n            />\n          </view>\n        </view>\n        \n        <view class=\"dialog-actions\">\n          <button class=\"cancel-btn\" @click=\"closePasswordDialog\">取消</button>\n          <button class=\"confirm-btn\" @click=\"changePassword\">确认</button>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n  name: 'EditProfile',\n  \n  data() {\n    return {\n      formData: {\n        avatar: '',\n        username: '',\n        nickname: '',\n        gender: '',\n        birthday: '',\n        email: '',\n        bio: '',\n        sportsPreferences: [],\n        city: '',\n        showBookingHistory: true,\n        allowSharingInvite: true,\n        receiveNotifications: true\n      },\n      \n      genderOptions: ['男', '女', '保密'],\n      sportsOptions: ['篮球', '足球', '羽毛球', '乒乓球', '网球', '游泳', '健身', '瑜伽'],\n      regionValue: [],\n      \n      // 密码修改表单\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      }\n    }\n  },\n  \n  computed: {\n    ...mapState('user', ['userInfo']),\n    \n    genderIndex() {\n      return this.genderOptions.indexOf(this.formData.gender)\n    }\n  },\n  \n  onLoad() {\n    this.initFormData()\n  },\n  \n  onShow() {\n    // 页面显示时重新初始化数据\n    this.initFormData()\n  },\n  \n  methods: {\n    ...mapActions('user', [\n      'updateUserInfo',\n      'uploadAvatar',\n      'changeUserPassword',\n      'getUserInfo'\n    ]),\n    \n    // 初始化表单数据\n    async initFormData() {\n      console.log('[EditProfile] 初始化表单数据，用户信息:', this.userInfo)\n      \n      // 如果用户信息为空，尝试重新获取\n      if (!this.userInfo) {\n        console.log('[EditProfile] 用户信息为空，尝试重新获取')\n        try {\n          const result = await this.getUserInfo()\n          console.log('[EditProfile] 重新获取用户信息结果:', result)\n          console.log('[EditProfile] 重新获取后的用户信息:', this.userInfo)\n        } catch (error) {\n          console.error('[EditProfile] 获取用户信息失败:', error)\n          uni.showToast({\n            title: '获取用户信息失败',\n            icon: 'error'\n          })\n          return\n        }\n      }\n      \n      if (this.userInfo) {\n        this.formData = {\n          avatar: this.userInfo.avatar || '',\n          username: this.userInfo.username || '',\n          nickname: this.userInfo.nickname || this.userInfo.username || '未设置昵称',\n          gender: this.userInfo.gender || '',\n          birthday: this.userInfo.birthday || '',\n          email: this.userInfo.email || '',\n          bio: this.userInfo.bio || '',\n          sportsPreferences: this.userInfo.sportsPreferences || [],\n          city: this.userInfo.city || '',\n          showBookingHistory: this.userInfo.showBookingHistory !== false,\n          allowSharingInvite: this.userInfo.allowSharingInvite !== false,\n          receiveNotifications: this.userInfo.receiveNotifications !== false\n        }\n        \n        // 设置城市选择器的值\n        if (this.userInfo.city) {\n          // 这里需要根据实际的城市数据结构来设置\n          this.regionValue = this.userInfo.city.split(' ')\n        }\n        console.log('[EditProfile] 表单数据已初始化:', this.formData)\n      } else {\n        console.log('[EditProfile] 用户信息为空，使用默认表单数据')\n        // 即使用户信息为空，也要初始化表单数据\n        this.formData = {\n          avatar: '',\n          username: '用户' + Date.now().toString().slice(-6), // 生成默认用户名\n          nickname: '未设置昵称',\n          gender: '',\n          birthday: '',\n          email: '',\n          bio: '',\n          sportsPreferences: [],\n          city: '',\n          showBookingHistory: true,\n          allowSharingInvite: true,\n          receiveNotifications: true\n        }\n        console.log('[EditProfile] 默认表单数据已设置:', this.formData)\n      }\n    },\n    \n    // 格式化手机号\n    formatPhone(phone) {\n      if (!phone) return ''\n      return phone.replace(/(\\d{3})(\\d{4})(\\d{4})/, '$1****$3')\n    },\n    \n    // 更换头像\n    changeAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          const tempFilePath = res.tempFilePaths[0]\n          this.uploadUserAvatar(tempFilePath)\n        }\n      })\n    },\n    \n    // 上传头像\n    async uploadUserAvatar(filePath) {\n      try {\n        uni.showLoading({ title: '上传中...' })\n        \n        const result = await this.uploadAvatar(filePath)\n        this.formData.avatar = result.url\n        \n        uni.hideLoading()\n        uni.showToast({\n          title: '头像更新成功',\n          icon: 'success'\n        })\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('上传头像失败:', error)\n        uni.showToast({\n          title: '上传失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 性别变化\n    onGenderChange(e) {\n      this.formData.gender = this.genderOptions[e.detail.value]\n    },\n    \n    // 生日变化\n    onBirthdayChange(e) {\n      this.formData.birthday = e.detail.value\n    },\n    \n    // 地区变化\n    onRegionChange(e) {\n      this.regionValue = e.detail.value\n      this.formData.city = e.detail.value.join(' ')\n    },\n    \n    // 切换运动偏好\n    toggleSport(sport) {\n      const index = this.formData.sportsPreferences.indexOf(sport)\n      if (index > -1) {\n        this.formData.sportsPreferences.splice(index, 1)\n      } else {\n        this.formData.sportsPreferences.push(sport)\n      }\n    },\n    \n    // 隐私设置变化\n    onPrivacyChange(key, e) {\n      this.formData[key] = e.detail.value\n    },\n    \n\n    \n    // 保存个人资料\n    async saveProfile() {\n      // 验证表单\n      if (!this.validateForm()) return\n      \n      let loadingShown = false\n      \n      try {\n        uni.showLoading({ title: '保存中...' })\n        loadingShown = true\n        \n        console.log('[EditProfile] 准备保存的表单数据:', JSON.stringify(this.formData, null, 2))\n        \n        // 过滤和清理数据，只发送必要的字段\n        const cleanData = {\n          username: this.formData.username?.trim() || '',\n          nickname: this.formData.nickname?.trim() || '',\n          gender: this.formData.gender || '',\n          birthday: this.formData.birthday || '',\n          email: this.formData.email?.trim() || '',\n          bio: this.formData.bio?.trim() || '',\n          city: this.formData.city?.trim() || '',\n          sportsPreferences: this.formData.sportsPreferences || [],\n          showBookingHistory: Boolean(this.formData.showBookingHistory),\n          allowSharingInvite: Boolean(this.formData.allowSharingInvite),\n          receiveNotifications: Boolean(this.formData.receiveNotifications)\n        }\n        \n        // 移除空字符串字段\n        Object.keys(cleanData).forEach(key => {\n          if (cleanData[key] === '' && key !== 'bio') {\n            delete cleanData[key]\n          }\n        })\n        \n        console.log('[EditProfile] 清理后的数据:', JSON.stringify(cleanData, null, 2))\n        \n        await this.updateUserInfo(cleanData)\n        \n        if (loadingShown) {\n          uni.hideLoading()\n          loadingShown = false\n        }\n        \n        uni.showToast({\n          title: '保存成功',\n          icon: 'success'\n        })\n        \n        // 返回上一页\n        setTimeout(() => {\n          uni.navigateBack()\n        }, 1500)\n        \n      } catch (error) {\n        if (loadingShown) {\n          uni.hideLoading()\n          loadingShown = false\n        }\n        console.error('保存失败:', error)\n        uni.showToast({\n          title: error.message || '保存失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 验证表单\n    validateForm() {\n      if (!this.formData.username.trim()) {\n        uni.showToast({\n          title: '请输入用户名',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      if (!this.formData.nickname.trim()) {\n        uni.showToast({\n          title: '请输入昵称',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      if (this.formData.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(this.formData.email)) {\n        uni.showToast({\n          title: '邮箱格式不正确',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 显示密码修改弹窗\n    showPasswordDialog() {\n      this.passwordForm = {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      }\n      this.$refs.passwordPopup.open()\n    },\n    \n    // 关闭密码修改弹窗\n    closePasswordDialog() {\n      this.$refs.passwordPopup.close()\n    },\n    \n    // 修改密码\n    async changePassword() {\n      // 验证表单\n      if (!this.passwordForm.oldPassword) {\n        uni.showToast({\n          title: '请输入当前密码',\n          icon: 'error'\n        })\n        return\n      }\n      \n      if (!this.passwordForm.newPassword) {\n        uni.showToast({\n          title: '请输入新密码',\n          icon: 'error'\n        })\n        return\n      }\n      \n      if (this.passwordForm.newPassword.length < 6) {\n        uni.showToast({\n          title: '新密码至少6位',\n          icon: 'error'\n        })\n        return\n      }\n      \n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\n        uni.showToast({\n          title: '两次密码输入不一致',\n          icon: 'error'\n        })\n        return\n      }\n      \n      try {\n        uni.showLoading({ title: '修改中...' })\n        \n        await this.changeUserPassword({\n          oldPassword: this.passwordForm.oldPassword,\n          newPassword: this.passwordForm.newPassword\n        })\n        \n        uni.hideLoading()\n        this.closePasswordDialog()\n        \n        uni.showToast({\n          title: '密码修改成功',\n          icon: 'success'\n        })\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('修改密码失败:', error)\n        uni.showToast({\n          title: error.message || '修改失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 返回\n    goBack() {\n      uni.navigateBack()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding-bottom: 120rpx;\n}\n\n// 头像区域\n.avatar-section {\n  background-color: #ffffff;\n  padding: 40rpx;\n  text-align: center;\n  margin-bottom: 20rpx;\n  \n  .avatar-wrapper {\n    position: relative;\n    display: inline-block;\n    margin-bottom: 16rpx;\n    \n    .avatar {\n      width: 160rpx;\n      height: 160rpx;\n      border-radius: 80rpx;\n      border: 4rpx solid #f0f0f0;\n    }\n    \n    .avatar-edit {\n      position: absolute;\n      bottom: 0;\n      right: 0;\n      width: 48rpx;\n      height: 48rpx;\n      background-color: #ff6b35;\n      border-radius: 24rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border: 4rpx solid #ffffff;\n      \n      .edit-icon {\n        font-size: 24rpx;\n        color: #ffffff;\n      }\n    }\n  }\n  \n  .avatar-tip {\n    font-size: 24rpx;\n    color: #999999;\n  }\n}\n\n// 表单区域\n.form-section {\n  background-color: #ffffff;\n  margin-bottom: 20rpx;\n  \n  .form-item {\n    display: flex;\n    align-items: center;\n    padding: 30rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .item-label {\n      width: 160rpx;\n      font-size: 28rpx;\n      color: #333333;\n      flex-shrink: 0;\n    }\n    \n    .item-input {\n      flex: 1;\n      font-size: 28rpx;\n      color: #333333;\n      text-align: right;\n    }\n    \n    .item-textarea {\n      flex: 1;\n      min-height: 120rpx;\n      font-size: 28rpx;\n      color: #333333;\n      text-align: right;\n    }\n    \n    .picker-text {\n      flex: 1;\n      font-size: 28rpx;\n      color: #333333;\n      text-align: right;\n    }\n    \n    .phone-wrapper {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      \n      .phone-text {\n        font-size: 28rpx;\n        color: #333333;\n      }\n      \n      .change-phone-btn {\n        padding: 8rpx 16rpx;\n        background-color: #ff6b35;\n        color: #ffffff;\n        font-size: 24rpx;\n        border-radius: 8rpx;\n        border: none;\n      }\n    }\n    \n    .change-password-btn {\n      flex: 1;\n      padding: 16rpx 24rpx;\n      background-color: #ff6b35;\n      color: #ffffff;\n      font-size: 28rpx;\n      border-radius: 8rpx;\n      border: none;\n      text-align: center;\n    }\n    \n    // 运动标签\n    .sports-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: flex-end;\n      gap: 12rpx;\n      \n      .sport-tag {\n        padding: 8rpx 16rpx;\n        background-color: #f5f5f5;\n        color: #666666;\n        border-radius: 20rpx;\n        font-size: 24rpx;\n        border: 2rpx solid transparent;\n        transition: all 0.3s;\n        \n        &.active {\n          background-color: #ff6b35;\n          color: #ffffff;\n          border-color: #ff6b35;\n        }\n      }\n    }\n  }\n}\n\n// 隐私设置\n.privacy-section {\n  background-color: #ffffff;\n  margin-bottom: 20rpx;\n  \n  .section-title {\n    display: block;\n    padding: 30rpx 30rpx 20rpx;\n    font-size: 28rpx;\n    font-weight: 600;\n    color: #333333;\n    border-bottom: 1rpx solid #f0f0f0;\n  }\n  \n  .privacy-item {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 30rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .privacy-label {\n      font-size: 28rpx;\n      color: #333333;\n    }\n  }\n}\n\n// 底部操作\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n  \n  .cancel-btn {\n    flex: 1;\n    height: 80rpx;\n    background-color: #f5f5f5;\n    color: #666666;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n    margin-right: 20rpx;\n  }\n  \n  .save-btn {\n    flex: 2;\n    height: 80rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n    font-weight: 600;\n  }\n}\n\n// 密码修改弹窗样式\n.password-dialog {\n  width: 600rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  \n  .dialog-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 32rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .dialog-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n    \n    .dialog-close {\n      font-size: 40rpx;\n      color: #999999;\n      width: 40rpx;\n      height: 40rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n  }\n  \n  .dialog-content {\n    padding: 32rpx;\n    \n    .password-item {\n      margin-bottom: 32rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .password-label {\n        display: block;\n        font-size: 28rpx;\n        color: #333333;\n        margin-bottom: 16rpx;\n      }\n      \n      .password-input {\n        width: 100%;\n        padding: 20rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 8rpx;\n        font-size: 28rpx;\n        background-color: #fafafa;\n        \n        &:focus {\n          border-color: #ff6b35;\n          background-color: #ffffff;\n        }\n      }\n    }\n  }\n  \n  .dialog-actions {\n    display: flex;\n    border-top: 1rpx solid #f0f0f0;\n    \n    .cancel-btn,\n    .confirm-btn {\n      flex: 1;\n      padding: 32rpx;\n      font-size: 28rpx;\n      border: none;\n      background-color: transparent;\n    }\n    \n    .cancel-btn {\n      color: #666666;\n      border-right: 1rpx solid #f0f0f0;\n    }\n    \n    .confirm-btn {\n      color: #ff6b35;\n      font-weight: 600;\n    }\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/user/edit-profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapState", "mapActions", "uni"], "mappings": ";;AAiOA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,mBAAmB,CAAE;AAAA,QACrB,MAAM;AAAA,QACN,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACvB;AAAA,MAED,eAAe,CAAC,KAAK,KAAK,IAAI;AAAA,MAC9B,eAAe,CAAC,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,IAAI;AAAA,MAChE,aAAa,CAAE;AAAA;AAAA,MAGf,cAAc;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,iBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,GAAGA,uBAAS,QAAQ,CAAC,UAAU,CAAC;AAAA,IAEhC,cAAc;AACZ,aAAO,KAAK,cAAc,QAAQ,KAAK,SAAS,MAAM;AAAA,IACxD;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,SAAS;AAEP,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,SAAS;AAAA,IACP,GAAGC,cAAAA,WAAW,QAAQ;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA;AAAA,IAGD,MAAM,eAAe;AACnBC,oBAAY,MAAA,MAAA,OAAA,sCAAA,+BAA+B,KAAK,QAAQ;AAGxD,UAAI,CAAC,KAAK,UAAU;AAClBA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,6BAA6B;AACzC,YAAI;AACF,gBAAM,SAAS,MAAM,KAAK,YAAY;AACtCA,wBAAAA,MAAA,MAAA,OAAA,sCAAY,6BAA6B,MAAM;AAC/CA,wBAAA,MAAA,MAAA,OAAA,sCAAY,6BAA6B,KAAK,QAAQ;AAAA,QACtD,SAAO,OAAO;AACdA,wBAAAA,2DAAc,2BAA2B,KAAK;AAC9CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,UAAU;AACjB,aAAK,WAAW;AAAA,UACd,QAAQ,KAAK,SAAS,UAAU;AAAA,UAChC,UAAU,KAAK,SAAS,YAAY;AAAA,UACpC,UAAU,KAAK,SAAS,YAAY,KAAK,SAAS,YAAY;AAAA,UAC9D,QAAQ,KAAK,SAAS,UAAU;AAAA,UAChC,UAAU,KAAK,SAAS,YAAY;AAAA,UACpC,OAAO,KAAK,SAAS,SAAS;AAAA,UAC9B,KAAK,KAAK,SAAS,OAAO;AAAA,UAC1B,mBAAmB,KAAK,SAAS,qBAAqB,CAAE;AAAA,UACxD,MAAM,KAAK,SAAS,QAAQ;AAAA,UAC5B,oBAAoB,KAAK,SAAS,uBAAuB;AAAA,UACzD,oBAAoB,KAAK,SAAS,uBAAuB;AAAA,UACzD,sBAAsB,KAAK,SAAS,yBAAyB;AAAA,QAC/D;AAGA,YAAI,KAAK,SAAS,MAAM;AAEtB,eAAK,cAAc,KAAK,SAAS,KAAK,MAAM,GAAG;AAAA,QACjD;AACAA,sBAAY,MAAA,MAAA,OAAA,sCAAA,2BAA2B,KAAK,QAAQ;AAAA,aAC/C;AACLA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,+BAA+B;AAE3C,aAAK,WAAW;AAAA,UACd,QAAQ;AAAA,UACR,UAAU,OAAO,KAAK,IAAG,EAAG,WAAW,MAAM,EAAE;AAAA;AAAA,UAC/C,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,UACL,mBAAmB,CAAE;AAAA,UACrB,MAAM;AAAA,UACN,oBAAoB;AAAA,UACpB,oBAAoB;AAAA,UACpB,sBAAsB;AAAA,QACxB;AACAA,sBAAY,MAAA,MAAA,OAAA,sCAAA,4BAA4B,KAAK,QAAQ;AAAA,MACvD;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,MAAM,QAAQ,yBAAyB,UAAU;AAAA,IACzD;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,IAAI,cAAc,CAAC;AACxC,eAAK,iBAAiB,YAAY;AAAA,QACpC;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,iBAAiB,UAAU;AAC/B,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,SAAS,MAAM,KAAK,aAAa,QAAQ;AAC/C,aAAK,SAAS,SAAS,OAAO;AAE9BA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,sCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,GAAG;AAChB,WAAK,SAAS,SAAS,KAAK,cAAc,EAAE,OAAO,KAAK;AAAA,IACzD;AAAA;AAAA,IAGD,iBAAiB,GAAG;AAClB,WAAK,SAAS,WAAW,EAAE,OAAO;AAAA,IACnC;AAAA;AAAA,IAGD,eAAe,GAAG;AAChB,WAAK,cAAc,EAAE,OAAO;AAC5B,WAAK,SAAS,OAAO,EAAE,OAAO,MAAM,KAAK,GAAG;AAAA,IAC7C;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,YAAM,QAAQ,KAAK,SAAS,kBAAkB,QAAQ,KAAK;AAC3D,UAAI,QAAQ,IAAI;AACd,aAAK,SAAS,kBAAkB,OAAO,OAAO,CAAC;AAAA,aAC1C;AACL,aAAK,SAAS,kBAAkB,KAAK,KAAK;AAAA,MAC5C;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB,KAAK,GAAG;AACtB,WAAK,SAAS,GAAG,IAAI,EAAE,OAAO;AAAA,IAC/B;AAAA;AAAA,IAKD,MAAM,cAAc;;AAElB,UAAI,CAAC,KAAK,aAAY;AAAI;AAE1B,UAAI,eAAe;AAEnB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AACnC,uBAAe;AAEfA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,4BAA4B,KAAK,UAAU,KAAK,UAAU,MAAM,CAAC,CAAC;AAG9E,cAAM,YAAY;AAAA,UAChB,YAAU,UAAK,SAAS,aAAd,mBAAwB,WAAU;AAAA,UAC5C,YAAU,UAAK,SAAS,aAAd,mBAAwB,WAAU;AAAA,UAC5C,QAAQ,KAAK,SAAS,UAAU;AAAA,UAChC,UAAU,KAAK,SAAS,YAAY;AAAA,UACpC,SAAO,UAAK,SAAS,UAAd,mBAAqB,WAAU;AAAA,UACtC,OAAK,UAAK,SAAS,QAAd,mBAAmB,WAAU;AAAA,UAClC,QAAM,UAAK,SAAS,SAAd,mBAAoB,WAAU;AAAA,UACpC,mBAAmB,KAAK,SAAS,qBAAqB,CAAE;AAAA,UACxD,oBAAoB,QAAQ,KAAK,SAAS,kBAAkB;AAAA,UAC5D,oBAAoB,QAAQ,KAAK,SAAS,kBAAkB;AAAA,UAC5D,sBAAsB,QAAQ,KAAK,SAAS,oBAAoB;AAAA,QAClE;AAGA,eAAO,KAAK,SAAS,EAAE,QAAQ,SAAO;AACpC,cAAI,UAAU,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC1C,mBAAO,UAAU,GAAG;AAAA,UACtB;AAAA,SACD;AAEDA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,yBAAyB,KAAK,UAAU,WAAW,MAAM,CAAC,CAAC;AAEvE,cAAM,KAAK,eAAe,SAAS;AAEnC,YAAI,cAAc;AAChBA,wBAAAA,MAAI,YAAY;AAChB,yBAAe;AAAA,QACjB;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,aAAa;AAAA,QAClB,GAAE,IAAI;AAAA,MAEP,SAAO,OAAO;AACd,YAAI,cAAc;AAChBA,wBAAAA,MAAI,YAAY;AAChB,yBAAe;AAAA,QACjB;AACAA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,CAAC,KAAK,SAAS,SAAS,KAAI,GAAI;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,SAAS,SAAS,KAAI,GAAI;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,SAAS,CAAC,6BAA6B,KAAK,KAAK,SAAS,KAAK,GAAG;AAClFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,eAAe;AAAA,QAClB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,iBAAiB;AAAA,MACnB;AACA,WAAK,MAAM,cAAc,KAAK;AAAA,IAC/B;AAAA;AAAA,IAGD,sBAAsB;AACpB,WAAK,MAAM,cAAc,MAAM;AAAA,IAChC;AAAA;AAAA,IAGD,MAAM,iBAAiB;AAErB,UAAI,CAAC,KAAK,aAAa,aAAa;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,aAAa,aAAa;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,KAAK,aAAa,YAAY,SAAS,GAAG;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,KAAK,aAAa,gBAAgB,KAAK,aAAa,iBAAiB;AACvEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,KAAK,mBAAmB;AAAA,UAC5B,aAAa,KAAK,aAAa;AAAA,UAC/B,aAAa,KAAK,aAAa;AAAA,SAChC;AAEDA,sBAAAA,MAAI,YAAY;AAChB,aAAK,oBAAoB;AAEzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,sCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IACnB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvlBA,GAAG,WAAW,eAAe;"}