{"version": 3, "file": "register.js", "sources": ["pages/user/register.vue", "pages/user/register.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 头部 -->\n    <view class=\"header\">\n      <text class=\"title\">注册账号</text>\n      <text class=\"subtitle\">加入我们，开启运动之旅</text>\n    </view>\n    \n    <!-- 注册表单 -->\n    <view class=\"register-form\">\n      <!-- 手机号 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">手机号 <text class=\"required\">*</text></text>\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">📱</text>\n          <input \n            v-model=\"formData.phone\" \n            class=\"input-field\" \n            type=\"number\"\n            placeholder=\"请输入手机号\"\n            maxlength=\"11\"\n          />\n        </view>\n      </view>\n      \n      <!-- 验证码 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">验证码 <text class=\"required\">*</text></text>\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">💬</text>\n          <input \n            v-model=\"formData.smsCode\" \n            class=\"input-field\" \n            type=\"number\"\n            placeholder=\"请输入验证码\"\n            maxlength=\"6\"\n          />\n          <button \n            class=\"sms-btn\" \n            :disabled=\"!canSendSms || smsCountdown > 0\"\n            @click=\"sendSmsCode\"\n          >\n            {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}\n          </button>\n        </view>\n      </view>\n      \n      <!-- 密码 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">密码 <text class=\"required\">*</text></text>\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">🔒</text>\n          <input \n            v-model=\"formData.password\" \n            class=\"input-field\" \n            :password=\"!showPassword\"\n            placeholder=\"请设置密码（6-20位）\"\n            maxlength=\"20\"\n          />\n          <text \n            class=\"password-toggle\" \n            @click=\"togglePassword\"\n          >\n            {{ showPassword ? '🙈' : '👁️' }}\n          </text>\n        </view>\n        <view class=\"password-tips\">\n          <text class=\"tip-item\" :class=\"{ valid: passwordValidation.length }\">• 长度6-20位</text>\n          <text class=\"tip-item\" :class=\"{ valid: passwordValidation.hasLetter }\">• 包含字母</text>\n          <text class=\"tip-item\" :class=\"{ valid: passwordValidation.hasNumber }\">• 包含数字</text>\n        </view>\n      </view>\n      \n      <!-- 确认密码 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">确认密码 <text class=\"required\">*</text></text>\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">🔒</text>\n          <input \n            v-model=\"formData.confirmPassword\" \n            class=\"input-field\" \n            :password=\"!showConfirmPassword\"\n            placeholder=\"请再次输入密码\"\n            maxlength=\"20\"\n          />\n          <text \n            class=\"password-toggle\" \n            @click=\"toggleConfirmPassword\"\n          >\n            {{ showConfirmPassword ? '🙈' : '👁️' }}\n          </text>\n        </view>\n        <view v-if=\"formData.confirmPassword && !passwordMatch\" class=\"error-tip\">\n          两次输入的密码不一致\n        </view>\n      </view>\n      \n      <!-- 昵称 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">昵称</text>\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">👤</text>\n          <input \n            v-model=\"formData.nickname\" \n            class=\"input-field\" \n            placeholder=\"请输入昵称（选填）\"\n            maxlength=\"20\"\n          />\n        </view>\n      </view>\n      \n      <!-- 邀请码 -->\n      <view class=\"form-item\">\n        <text class=\"item-label\">邀请码</text>\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">🎁</text>\n          <input \n            v-model=\"formData.inviteCode\" \n            class=\"input-field\" \n            placeholder=\"请输入邀请码（选填）\"\n            maxlength=\"10\"\n          />\n        </view>\n      </view>\n      \n      <!-- 协议同意 -->\n      <view class=\"agreement-section\">\n        <view class=\"agreement-item\" @click=\"toggleAgreement\">\n          <view class=\"checkbox\" :class=\"{ checked: agreedToTerms }\">\n            <text v-if=\"agreedToTerms\" class=\"check-icon\">✓</text>\n          </view>\n          <text class=\"agreement-text\">\n            我已阅读并同意\n            <text class=\"agreement-link\" @click.stop=\"showUserAgreement\">《用户协议》</text>\n            和\n            <text class=\"agreement-link\" @click.stop=\"showPrivacyPolicy\">《隐私政策》</text>\n          </text>\n        </view>\n      </view>\n      \n      <!-- 注册按钮 -->\n      <button \n        class=\"register-btn\" \n        :disabled=\"!canRegister\"\n        @click=\"handleRegister\"\n      >\n        注册\n      </button>\n    </view>\n    \n    <!-- 底部链接 -->\n    <view class=\"footer\">\n      <text class=\"footer-text\">已有账号？</text>\n      <text class=\"footer-link\" @click=\"navigateToLogin\">立即登录</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapActions, mapGetters } from 'vuex'\n\nexport default {\n  name: 'UserRegister',\n  \n  data() {\n    return {\n      showPassword: false,\n      showConfirmPassword: false,\n      smsCountdown: 0,\n      smsTimer: null,\n      agreedToTerms: false,\n      \n      formData: {\n        phone: '',\n        smsCode: '',\n        password: '',\n        confirmPassword: '',\n        nickname: '',\n        inviteCode: ''\n      }\n    }\n  },\n  \n  computed: {\n    ...mapGetters('user', ['loading']),\n    \n    // 是否可以发送短信\n    canSendSms() {\n      return this.formData.phone.length === 11 && /^1[3-9]\\d{9}$/.test(this.formData.phone)\n    },\n    \n    // 密码验证\n    passwordValidation() {\n      const password = this.formData.password\n      return {\n        length: password.length >= 6 && password.length <= 20,\n        hasLetter: /[a-zA-Z]/.test(password),\n        hasNumber: /\\d/.test(password)\n      }\n    },\n    \n    // 密码是否有效\n    isPasswordValid() {\n      return this.passwordValidation.length && \n             this.passwordValidation.hasLetter && \n             this.passwordValidation.hasNumber\n    },\n    \n    // 密码是否匹配\n    passwordMatch() {\n      return this.formData.password === this.formData.confirmPassword\n    },\n    \n    // 是否可以注册\n    canRegister() {\n      return this.canSendSms && \n             this.formData.smsCode.length === 6 &&\n             this.isPasswordValid &&\n             this.passwordMatch &&\n             this.agreedToTerms\n    }\n  },\n  \n  onUnload() {\n    // 清除定时器\n    if (this.smsTimer) {\n      clearInterval(this.smsTimer)\n    }\n  },\n  \n  methods: {\n    ...mapActions('user', [\n      'register',\n      'getSmsCode'\n    ]),\n    \n    // 切换密码显示\n    togglePassword() {\n      this.showPassword = !this.showPassword\n    },\n    \n    // 切换确认密码显示\n    toggleConfirmPassword() {\n      this.showConfirmPassword = !this.showConfirmPassword\n    },\n    \n    // 切换协议同意状态\n    toggleAgreement() {\n      this.agreedToTerms = !this.agreedToTerms\n    },\n    \n    // 发送短信验证码\n    async sendSmsCode() {\n      if (!this.canSendSms || this.smsCountdown > 0) return\n      \n      try {\n        uni.showLoading({ title: '发送中...' })\n        \n        await this.getSmsCode({\n          phone: this.formData.phone,\n          type: 'register'\n        })\n        \n        uni.hideLoading()\n        uni.showToast({\n          title: '验证码已发送',\n          icon: 'success'\n        })\n        \n        // 开始倒计时\n        this.startCountdown()\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('发送验证码失败:', error)\n        uni.showToast({\n          title: error.message || '发送失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 开始倒计时\n    startCountdown() {\n      this.smsCountdown = 60\n      this.smsTimer = setInterval(() => {\n        this.smsCountdown--\n        if (this.smsCountdown <= 0) {\n          clearInterval(this.smsTimer)\n          this.smsTimer = null\n        }\n      }, 1000)\n    },\n    \n    // 处理注册\n    async handleRegister() {\n      if (!this.canRegister) return\n      \n      // 验证表单\n      if (!this.validateForm()) return\n      \n      try {\n        await this.register({\n          username: this.formData.phone, // 使用手机号作为用户名\n          phone: this.formData.phone,\n          code: this.formData.smsCode, // 后端期望的字段名是code\n          password: this.formData.password,\n          nickname: this.formData.nickname || this.formData.phone, // 如果没有昵称，使用手机号\n          inviteCode: this.formData.inviteCode || undefined\n        })\n        \n        uni.showToast({\n          title: '注册成功',\n          icon: 'success'\n        })\n        \n        // 注册成功后跳转到登录页\n        setTimeout(() => {\n          uni.redirectTo({\n            url: '/pages/user/login'\n          })\n        }, 1500)\n        \n      } catch (error) {\n        console.error('注册失败:', error)\n        // request.js已经处理了错误提示，这里不需要重复显示\n      }\n    },\n    \n    // 验证表单\n    validateForm() {\n      // 验证手机号\n      if (!this.formData.phone) {\n        uni.showToast({\n          title: '请输入手机号',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      if (!/^1[3-9]\\d{9}$/.test(this.formData.phone)) {\n        uni.showToast({\n          title: '手机号格式不正确',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      // 验证验证码\n      if (!this.formData.smsCode) {\n        uni.showToast({\n          title: '请输入验证码',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      if (this.formData.smsCode.length !== 6) {\n        uni.showToast({\n          title: '验证码格式不正确',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      // 验证密码\n      if (!this.formData.password) {\n        uni.showToast({\n          title: '请设置密码',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      if (!this.isPasswordValid) {\n        uni.showToast({\n          title: '密码格式不符合要求',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      // 验证确认密码\n      if (!this.passwordMatch) {\n        uni.showToast({\n          title: '两次输入的密码不一致',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      // 验证协议同意\n      if (!this.agreedToTerms) {\n        uni.showToast({\n          title: '请同意用户协议和隐私政策',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 跳转到登录页\n    navigateToLogin() {\n      uni.navigateBack()\n    },\n    \n    // 显示用户协议\n    showUserAgreement() {\n      uni.navigateTo({\n        url: '/pages/user/agreement?type=user'\n      })\n    },\n    \n    // 显示隐私政策\n    showPrivacyPolicy() {\n      uni.navigateTo({\n        url: '/pages/user/agreement?type=privacy'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8f65 100%);\n  padding: 60rpx 60rpx 40rpx;\n}\n\n// 头部\n.header {\n  text-align: center;\n  margin-bottom: 60rpx;\n  \n  .title {\n    display: block;\n    font-size: 48rpx;\n    font-weight: 600;\n    color: #ffffff;\n    margin-bottom: 16rpx;\n  }\n  \n  .subtitle {\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.8);\n  }\n}\n\n// 注册表单\n.register-form {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n  \n  // 表单项\n  .form-item {\n    margin-bottom: 30rpx;\n    \n    .item-label {\n      display: block;\n      font-size: 28rpx;\n      color: #333333;\n      margin-bottom: 16rpx;\n      \n      .required {\n        color: #ff4d4f;\n      }\n    }\n    \n    .input-wrapper {\n      display: flex;\n      align-items: center;\n      background-color: #f8f8f8;\n      border-radius: 12rpx;\n      padding: 0 20rpx;\n      height: 88rpx;\n      \n      .input-icon {\n        font-size: 32rpx;\n        margin-right: 16rpx;\n        opacity: 0.6;\n      }\n      \n      .input-field {\n        flex: 1;\n        font-size: 28rpx;\n        color: #333333;\n        background: transparent;\n        border: none;\n      }\n      \n      .password-toggle {\n        font-size: 32rpx;\n        opacity: 0.6;\n        padding: 8rpx;\n      }\n      \n      .sms-btn {\n        padding: 12rpx 24rpx;\n        background-color: #ff6b35;\n        color: #ffffff;\n        border: none;\n        border-radius: 8rpx;\n        font-size: 24rpx;\n        \n        &[disabled] {\n          background-color: #cccccc;\n          color: #ffffff;\n        }\n      }\n    }\n    \n    // 密码提示\n    .password-tips {\n      display: flex;\n      flex-wrap: wrap;\n      margin-top: 12rpx;\n      \n      .tip-item {\n        font-size: 22rpx;\n        color: #999999;\n        margin-right: 20rpx;\n        margin-bottom: 8rpx;\n        \n        &.valid {\n          color: #52c41a;\n        }\n      }\n    }\n    \n    // 错误提示\n    .error-tip {\n      font-size: 22rpx;\n      color: #ff4d4f;\n      margin-top: 8rpx;\n    }\n  }\n  \n  // 协议区域\n  .agreement-section {\n    margin-bottom: 40rpx;\n    \n    .agreement-item {\n      display: flex;\n      align-items: flex-start;\n      \n      .checkbox {\n        width: 32rpx;\n        height: 32rpx;\n        border: 2rpx solid #cccccc;\n        border-radius: 6rpx;\n        margin-right: 16rpx;\n        margin-top: 4rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        &.checked {\n          background-color: #ff6b35;\n          border-color: #ff6b35;\n          \n          .check-icon {\n            color: #ffffff;\n            font-size: 20rpx;\n          }\n        }\n      }\n      \n      .agreement-text {\n        flex: 1;\n        font-size: 24rpx;\n        color: #666666;\n        line-height: 1.4;\n        \n        .agreement-link {\n          color: #ff6b35;\n          text-decoration: underline;\n        }\n      }\n    }\n  }\n  \n  // 注册按钮\n  .register-btn {\n    width: 100%;\n    height: 88rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 12rpx;\n    font-size: 32rpx;\n    font-weight: 600;\n    \n    &[disabled] {\n      background-color: #cccccc;\n      color: #ffffff;\n    }\n  }\n}\n\n// 底部链接\n.footer {\n  text-align: center;\n  \n  .footer-text {\n    font-size: 26rpx;\n    color: rgba(255, 255, 255, 0.8);\n  }\n  \n  .footer-link {\n    font-size: 26rpx;\n    color: #ffffff;\n    font-weight: 600;\n    margin-left: 8rpx;\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/user/register.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapGetters", "mapActions", "uni"], "mappings": ";;AAiKA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,eAAe;AAAA,MAEf,UAAU;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,GAAGA,yBAAW,QAAQ,CAAC,SAAS,CAAC;AAAA;AAAA,IAGjC,aAAa;AACX,aAAO,KAAK,SAAS,MAAM,WAAW,MAAM,gBAAgB,KAAK,KAAK,SAAS,KAAK;AAAA,IACrF;AAAA;AAAA,IAGD,qBAAqB;AACnB,YAAM,WAAW,KAAK,SAAS;AAC/B,aAAO;AAAA,QACL,QAAQ,SAAS,UAAU,KAAK,SAAS,UAAU;AAAA,QACnD,WAAW,WAAW,KAAK,QAAQ;AAAA,QACnC,WAAW,KAAK,KAAK,QAAQ;AAAA,MAC/B;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChB,aAAO,KAAK,mBAAmB,UACxB,KAAK,mBAAmB,aACxB,KAAK,mBAAmB;AAAA,IAChC;AAAA;AAAA,IAGD,gBAAgB;AACd,aAAO,KAAK,SAAS,aAAa,KAAK,SAAS;AAAA,IACjD;AAAA;AAAA,IAGD,cAAc;AACZ,aAAO,KAAK,cACL,KAAK,SAAS,QAAQ,WAAW,KACjC,KAAK,mBACL,KAAK,iBACL,KAAK;AAAA,IACd;AAAA,EACD;AAAA,EAED,WAAW;AAET,QAAI,KAAK,UAAU;AACjB,oBAAc,KAAK,QAAQ;AAAA,IAC7B;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,GAAGC,cAAAA,WAAW,QAAQ;AAAA,MACpB;AAAA,MACA;AAAA,IACF,CAAC;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,eAAe,CAAC,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,wBAAwB;AACtB,WAAK,sBAAsB,CAAC,KAAK;AAAA,IAClC;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,gBAAgB,CAAC,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI,CAAC,KAAK,cAAc,KAAK,eAAe;AAAG;AAE/C,UAAI;AACFC,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,KAAK,WAAW;AAAA,UACpB,OAAO,KAAK,SAAS;AAAA,UACrB,MAAM;AAAA,SACP;AAEDA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,aAAK,eAAe;AAAA,MAEpB,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,eAAe;AACpB,WAAK,WAAW,YAAY,MAAM;AAChC,aAAK;AACL,YAAI,KAAK,gBAAgB,GAAG;AAC1B,wBAAc,KAAK,QAAQ;AAC3B,eAAK,WAAW;AAAA,QAClB;AAAA,MACD,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrB,UAAI,CAAC,KAAK;AAAa;AAGvB,UAAI,CAAC,KAAK,aAAY;AAAI;AAE1B,UAAI;AACF,cAAM,KAAK,SAAS;AAAA,UAClB,UAAU,KAAK,SAAS;AAAA;AAAA,UACxB,OAAO,KAAK,SAAS;AAAA,UACrB,MAAM,KAAK,SAAS;AAAA;AAAA,UACpB,UAAU,KAAK,SAAS;AAAA,UACxB,UAAU,KAAK,SAAS,YAAY,KAAK,SAAS;AAAA;AAAA,UAClD,YAAY,KAAK,SAAS,cAAc;AAAA,SACzC;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,WACN;AAAA,QACF,GAAE,IAAI;AAAA,MAEP,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,SAAS,KAAK;AAAA,MAE9B;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AAEb,UAAI,CAAC,KAAK,SAAS,OAAO;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,gBAAgB,KAAK,KAAK,SAAS,KAAK,GAAG;AAC9CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAGA,UAAI,CAAC,KAAK,SAAS,SAAS;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,QAAQ,WAAW,GAAG;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAGA,UAAI,CAAC,KAAK,SAAS,UAAU;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,iBAAiB;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAGA,UAAI,CAAC,KAAK,eAAe;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAGA,UAAI,CAAC,KAAK,eAAe;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,oBAAoB;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACraA,GAAG,WAAW,eAAe;"}