{"version": 3, "file": "test-store.js", "sources": ["pages/test-store.vue", "pages/test-store.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">Store测试页面</text>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">Store连接状态</text>\n      <text class=\"test-result\">{{ $store ? '✅ Store已连接' : '❌ Store未连接' }}</text>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">Sharing模块状态</text>\n      <text class=\"test-result\">加载状态: {{ loading ? '加载中' : '已完成' }}</text>\n      <text class=\"test-result\">数据数量: {{ sharingOrders?.length || 0 }}</text>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">操作按钮</text>\n      <button class=\"test-btn\" @click=\"testGetData\">测试获取数据</button>\n      <button class=\"test-btn\" @click=\"testGenerateMock\">测试生成模拟数据</button>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">数据详情</text>\n      <view v-if=\"sharingOrders && sharingOrders.length > 0\">\n        <view v-for=\"(item, index) in sharingOrders\" :key=\"index\" class=\"data-item\">\n          <text class=\"data-text\">{{ index + 1 }}. {{ item.venueName }} - {{ item.teamName }}</text>\n          <text class=\"data-text\">状态: {{ item.status }}, 价格: ¥{{ item.totalPrice }}</text>\n        </view>\n      </view>\n      <text v-else class=\"no-data\">暂无数据</text>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">控制台日志</text>\n      <text class=\"log-text\">请查看控制台输出</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapState, mapActions, mapGetters } from 'vuex'\n\nexport default {\n  name: 'TestStore',\n  \n  computed: {\n    ...mapGetters('sharing', ['sharingOrders', 'loading'])\n  },\n  \n  onLoad() {\n    console.log('测试页面加载')\n    console.log('Store状态:', this.$store)\n    console.log('Sharing模块:', this.$store?.state?.sharing)\n    this.testGetData()\n  },\n  \n  methods: {\n    ...mapActions('sharing', ['getJoinableSharingOrders']),\n    \n    async testGetData() {\n      try {\n        console.log('开始测试获取数据')\n        console.log('getJoinableSharingOrders方法:', this.getJoinableSharingOrders)\n        \n        const result = await this.getJoinableSharingOrders({ page: 1, pageSize: 10 })\n        console.log('获取数据结果:', result)\n        console.log('Store中的数据:', this.sharingOrders)\n        \n        uni.showToast({\n          title: `获取到${this.sharingOrders?.length || 0}条数据`,\n          icon: 'none'\n        })\n      } catch (error) {\n        console.error('获取数据失败:', error)\n        uni.showToast({\n          title: '获取数据失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    testGenerateMock() {\n      console.log('测试生成模拟数据')\n      // 直接调用store的mutation来设置模拟数据\n      const mockData = [\n        {\n          id: 999,\n          venueName: '测试场馆',\n          venueLocation: '测试位置',\n          teamName: '测试队伍',\n          status: 'OPEN',\n          maxParticipants: 8,\n          currentParticipants: 3,\n          totalPrice: 500,\n          bookingDate: '2025-06-30',\n          startTime: '14:00',\n          endTime: '16:00',\n          creatorUsername: '测试用户',\n          description: '这是一条测试数据'\n        }\n      ]\n      \n      this.$store.commit('sharing/SET_SHARING_ORDERS', mockData)\n      console.log('设置模拟数据完成:', mockData)\n      \n      uni.showToast({\n        title: '模拟数据已设置',\n        icon: 'success'\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 20px;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n\n.test-section {\n  margin-bottom: 20px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10px;\n}\n\n.test-result {\n  display: block;\n  margin-bottom: 5px;\n  color: #666;\n}\n\n.test-btn {\n  margin: 5px;\n  padding: 10px 20px;\n  background: #007aff;\n  color: white;\n  border: none;\n  border-radius: 5px;\n}\n\n.data-item {\n  margin-bottom: 10px;\n  padding: 10px;\n  background: white;\n  border-radius: 5px;\n}\n\n.data-text {\n  display: block;\n  margin-bottom: 3px;\n  color: #333;\n  font-size: 14px;\n}\n\n.no-data {\n  color: #999;\n  font-style: italic;\n}\n\n.log-text {\n  color: #666;\n  font-size: 12px;\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test-store.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapGetters", "uni", "mapActions"], "mappings": ";;AA4CA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,UAAU;AAAA,IACR,GAAGA,cAAU,WAAC,WAAW,CAAC,iBAAiB,SAAS,CAAC;AAAA,EACtD;AAAA,EAED,SAAS;;AACPC,kBAAAA,MAAA,MAAA,OAAA,8BAAY,QAAQ;AACpBA,kBAAY,MAAA,MAAA,OAAA,8BAAA,YAAY,KAAK,MAAM;AACnCA,wBAAA,MAAA,OAAA,8BAAY,eAAc,gBAAK,WAAL,mBAAa,UAAb,mBAAoB,OAAO;AACrD,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA,IACP,GAAGC,yBAAW,WAAW,CAAC,0BAA0B,CAAC;AAAA,IAErD,MAAM,cAAc;;AAClB,UAAI;AACFD,sBAAAA,MAAY,MAAA,OAAA,8BAAA,UAAU;AACtBA,sBAAY,MAAA,MAAA,OAAA,8BAAA,+BAA+B,KAAK,wBAAwB;AAExE,cAAM,SAAS,MAAM,KAAK,yBAAyB,EAAE,MAAM,GAAG,UAAU,IAAI;AAC5EA,sBAAAA,MAAA,MAAA,OAAA,8BAAY,WAAW,MAAM;AAC7BA,sBAAY,MAAA,MAAA,OAAA,8BAAA,cAAc,KAAK,aAAa;AAE5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,QAAM,UAAK,kBAAL,mBAAoB,WAAU,CAAC;AAAA,UAC5C,MAAM;AAAA,SACP;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA,IAED,mBAAmB;AACjBA,oBAAAA,iDAAY,UAAU;AAEtB,YAAM,WAAW;AAAA,QACf;AAAA,UACE,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,iBAAiB;AAAA,UACjB,qBAAqB;AAAA,UACrB,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,WAAW;AAAA,UACX,SAAS;AAAA,UACT,iBAAiB;AAAA,UACjB,aAAa;AAAA,QACf;AAAA,MACF;AAEA,WAAK,OAAO,OAAO,8BAA8B,QAAQ;AACzDA,oBAAAA,MAAY,MAAA,OAAA,+BAAA,aAAa,QAAQ;AAEjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;AChHA,GAAG,WAAW,eAAe;"}