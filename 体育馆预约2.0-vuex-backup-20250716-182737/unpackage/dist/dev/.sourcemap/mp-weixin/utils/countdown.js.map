{"version": 3, "file": "countdown.js", "sources": ["utils/countdown.js"], "sourcesContent": ["/**\n * 拼场订单倒计时工具函数\n * 计算距离自动取消还有多长时间\n */\n\n/**\n * 计算拼场订单的自动取消时间\n * @param {string} bookingDate - 预约日期 (YYYY-MM-DD)\n * @param {string} startTime - 开始时间 (HH:mm:ss)\n * @returns {Date} 自动取消的时间点\n */\nexport function getAutoCancelTime(bookingDate, startTime) {\n  if (!bookingDate || !startTime) {\n    return null\n  }\n\n  try {\n    // 处理不同的时间格式\n    let timeStr = startTime\n    if (startTime.length === 5) {\n      timeStr = startTime + ':00' // HH:mm -> HH:mm:ss\n    }\n\n    // iOS兼容性处理：将 \"YYYY-MM-DD HH:mm:ss\" 转换为 \"YYYY/MM/DD HH:mm:ss\"\n    let dateTimeStr = `${bookingDate} ${timeStr}`\n    if (dateTimeStr.includes('-') && dateTimeStr.includes(' ')) {\n      // 将日期部分的 - 替换为 /，保持时间部分不变\n      const [datePart, timePart] = dateTimeStr.split(' ')\n      const formattedDate = datePart.replace(/-/g, '/')\n      dateTimeStr = `${formattedDate} ${timePart}`\n    }\n\n    // 创建预约时间\n    const bookingDateTime = new Date(dateTimeStr)\n\n    // 检查日期是否有效\n    if (isNaN(bookingDateTime.getTime())) {\n      console.warn('Invalid booking date/time:', bookingDate, startTime, 'formatted:', dateTimeStr)\n      return null\n    }\n\n    // 减去2小时得到自动取消时间\n    const cancelTime = new Date(bookingDateTime.getTime() - 2 * 60 * 60 * 1000)\n\n    return cancelTime\n  } catch (error) {\n    console.error('计算自动取消时间失败:', error)\n    return null\n  }\n}\n\n/**\n * 计算倒计时剩余时间\n * @param {Date} targetTime - 目标时间\n * @returns {Object} 倒计时对象\n */\nexport function calculateCountdown(targetTime) {\n  if (!targetTime || isNaN(targetTime.getTime())) {\n    return {\n      isExpired: true,\n      days: 0,\n      hours: 0,\n      minutes: 0,\n      seconds: 0,\n      totalSeconds: 0,\n      formatted: '已过期'\n    }\n  }\n  \n  const now = new Date()\n  const diff = targetTime.getTime() - now.getTime()\n  \n  // 如果已经过期\n  if (diff <= 0) {\n    return {\n      isExpired: true,\n      days: 0,\n      hours: 0,\n      minutes: 0,\n      seconds: 0,\n      totalSeconds: 0,\n      formatted: '已过期'\n    }\n  }\n  \n  // 计算各个时间单位\n  const totalSeconds = Math.floor(diff / 1000)\n  const days = Math.floor(totalSeconds / (24 * 60 * 60))\n  const hours = Math.floor((totalSeconds % (24 * 60 * 60)) / (60 * 60))\n  const minutes = Math.floor((totalSeconds % (60 * 60)) / 60)\n  const seconds = totalSeconds % 60\n  \n  // 格式化显示\n  let formatted = ''\n  if (days > 0) {\n    formatted = `${days}天${hours}小时${minutes}分钟`\n  } else if (hours > 0) {\n    formatted = `${hours}小时${minutes}分钟`\n  } else if (minutes > 0) {\n    formatted = `${minutes}分钟${seconds}秒`\n  } else {\n    formatted = `${seconds}秒`\n  }\n  \n  return {\n    isExpired: false,\n    days,\n    hours,\n    minutes,\n    seconds,\n    totalSeconds,\n    formatted\n  }\n}\n\n/**\n * 获取拼场订单的倒计时信息\n * @param {Object} sharingOrder - 拼场订单对象\n * @returns {Object} 倒计时信息\n */\nexport function getSharingOrderCountdown(sharingOrder) {\n  // 检查订单状态 - 支持多种拼场订单状态\n  const validStatuses = ['OPEN', 'SHARING', 'PENDING', 'CONFIRMED']\n  if (!sharingOrder || !validStatuses.includes(sharingOrder.status)) {\n    return {\n      showCountdown: false,\n      isExpired: true,\n      formatted: '不显示倒计时'\n    }\n  }\n\n  const cancelTime = getAutoCancelTime(sharingOrder.bookingDate, sharingOrder.startTime)\n\n  if (!cancelTime) {\n    return {\n      showCountdown: false,\n      isExpired: true,\n      formatted: '时间格式错误'\n    }\n  }\n\n  const countdown = calculateCountdown(cancelTime)\n\n  return {\n    showCountdown: true,\n    cancelTime,\n    ...countdown\n  }\n}\n\n/**\n * 判断是否应该显示倒计时\n * @param {Object} order - 订单对象\n * @returns {boolean} 是否显示倒计时\n */\nexport function shouldShowCountdown(order) {\n  // 只有拼场订单且状态为开放中才显示倒计时\n  if (!order) {\n    return false\n  }\n\n  // 检查是否为拼场订单\n  const isSharingOrder = order.bookingType === 'SHARED' ||\n                        order.status === 'OPEN' ||\n                        order.status === 'SHARING' ||\n                        order.maxParticipants > 0\n\n  if (!isSharingOrder) {\n    return false\n  }\n\n  // 检查状态是否为开放中（放宽条件）\n  const validStatuses = ['OPEN', 'SHARING', 'PENDING', 'CONFIRMED']\n\n  if (!validStatuses.includes(order.status)) {\n    return false\n  }\n\n  // 检查是否有必要的时间字段\n  const hasTimeFields = !!(order.bookingDate && order.startTime)\n\n  if (!hasTimeFields) {\n    return false\n  }\n\n  return true\n}\n\n/**\n * 格式化倒计时显示（简短版本）\n * @param {Object} countdown - 倒计时对象\n * @returns {string} 格式化的倒计时文本\n */\nexport function formatCountdownShort(countdown) {\n  if (!countdown || countdown.isExpired) {\n    return '已过期'\n  }\n  \n  const { days, hours, minutes } = countdown\n  \n  if (days > 0) {\n    return `${days}天${hours}时`\n  } else if (hours > 0) {\n    return `${hours}时${minutes}分`\n  } else {\n    return `${minutes}分`\n  }\n}\n\n/**\n * 获取倒计时的样式类名\n * @param {Object} countdown - 倒计时对象\n * @returns {string} CSS类名\n */\nexport function getCountdownClass(countdown) {\n  if (!countdown || countdown.isExpired) {\n    return 'countdown-expired'\n  }\n  \n  const { totalSeconds } = countdown\n  \n  // 小于30分钟：紧急状态（红色）\n  if (totalSeconds < 30 * 60) {\n    return 'countdown-urgent'\n  }\n  // 小于2小时：警告状态（橙色）\n  else if (totalSeconds < 2 * 60 * 60) {\n    return 'countdown-warning'\n  }\n  // 其他：正常状态（绿色）\n  else {\n    return 'countdown-normal'\n  }\n}\n\n/**\n * 创建倒计时定时器\n * @param {Function} callback - 回调函数\n * @param {number} interval - 更新间隔（毫秒），默认1000ms\n * @returns {number} 定时器ID\n */\nexport function createCountdownTimer(callback, interval = 1000) {\n  return setInterval(callback, interval)\n}\n\n/**\n * 清除倒计时定时器\n * @param {number} timerId - 定时器ID\n */\nexport function clearCountdownTimer(timerId) {\n  if (timerId) {\n    clearInterval(timerId)\n  }\n}\n"], "names": ["uni"], "mappings": ";;AAWO,SAAS,kBAAkB,aAAa,WAAW;AACxD,MAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,WAAO;AAAA,EACR;AAED,MAAI;AAEF,QAAI,UAAU;AACd,QAAI,UAAU,WAAW,GAAG;AAC1B,gBAAU,YAAY;AAAA,IACvB;AAGD,QAAI,cAAc,GAAG,WAAW,IAAI,OAAO;AAC3C,QAAI,YAAY,SAAS,GAAG,KAAK,YAAY,SAAS,GAAG,GAAG;AAE1D,YAAM,CAAC,UAAU,QAAQ,IAAI,YAAY,MAAM,GAAG;AAClD,YAAM,gBAAgB,SAAS,QAAQ,MAAM,GAAG;AAChD,oBAAc,GAAG,aAAa,IAAI,QAAQ;AAAA,IAC3C;AAGD,UAAM,kBAAkB,IAAI,KAAK,WAAW;AAG5C,QAAI,MAAM,gBAAgB,QAAO,CAAE,GAAG;AACpCA,0BAAA,MAAA,QAAA,4BAAa,8BAA8B,aAAa,WAAW,cAAc,WAAW;AAC5F,aAAO;AAAA,IACR;AAGD,UAAM,aAAa,IAAI,KAAK,gBAAgB,QAAS,IAAG,IAAI,KAAK,KAAK,GAAI;AAE1E,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,4BAAA,eAAe,KAAK;AAClC,WAAO;AAAA,EACR;AACH;AAOO,SAAS,mBAAmB,YAAY;AAC7C,MAAI,CAAC,cAAc,MAAM,WAAW,QAAS,CAAA,GAAG;AAC9C,WAAO;AAAA,MACL,WAAW;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,IACZ;AAAA,EACF;AAED,QAAM,MAAM,oBAAI,KAAM;AACtB,QAAM,OAAO,WAAW,QAAO,IAAK,IAAI,QAAS;AAGjD,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,MACL,WAAW;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,IACZ;AAAA,EACF;AAGD,QAAM,eAAe,KAAK,MAAM,OAAO,GAAI;AAC3C,QAAM,OAAO,KAAK,MAAM,gBAAgB,KAAK,KAAK,GAAG;AACrD,QAAM,QAAQ,KAAK,MAAO,gBAAgB,KAAK,KAAK,OAAQ,KAAK,GAAG;AACpE,QAAM,UAAU,KAAK,MAAO,gBAAgB,KAAK,MAAO,EAAE;AAC1D,QAAM,UAAU,eAAe;AAG/B,MAAI,YAAY;AAChB,MAAI,OAAO,GAAG;AACZ,gBAAY,GAAG,IAAI,IAAI,KAAK,KAAK,OAAO;AAAA,EAC5C,WAAa,QAAQ,GAAG;AACpB,gBAAY,GAAG,KAAK,KAAK,OAAO;AAAA,EACpC,WAAa,UAAU,GAAG;AACtB,gBAAY,GAAG,OAAO,KAAK,OAAO;AAAA,EACtC,OAAS;AACL,gBAAY,GAAG,OAAO;AAAA,EACvB;AAED,SAAO;AAAA,IACL,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACH;AAOO,SAAS,yBAAyB,cAAc;AAErD,QAAM,gBAAgB,CAAC,QAAQ,WAAW,WAAW,WAAW;AAChE,MAAI,CAAC,gBAAgB,CAAC,cAAc,SAAS,aAAa,MAAM,GAAG;AACjE,WAAO;AAAA,MACL,eAAe;AAAA,MACf,WAAW;AAAA,MACX,WAAW;AAAA,IACZ;AAAA,EACF;AAED,QAAM,aAAa,kBAAkB,aAAa,aAAa,aAAa,SAAS;AAErF,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,MACL,eAAe;AAAA,MACf,WAAW;AAAA,MACX,WAAW;AAAA,IACZ;AAAA,EACF;AAED,QAAM,YAAY,mBAAmB,UAAU;AAE/C,SAAO;AAAA,IACL,eAAe;AAAA,IACf;AAAA,IACA,GAAG;AAAA,EACJ;AACH;AAOO,SAAS,oBAAoB,OAAO;AAEzC,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACR;AAGD,QAAM,iBAAiB,MAAM,gBAAgB,YACvB,MAAM,WAAW,UACjB,MAAM,WAAW,aACjB,MAAM,kBAAkB;AAE9C,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACR;AAGD,QAAM,gBAAgB,CAAC,QAAQ,WAAW,WAAW,WAAW;AAEhE,MAAI,CAAC,cAAc,SAAS,MAAM,MAAM,GAAG;AACzC,WAAO;AAAA,EACR;AAGD,QAAM,gBAAgB,CAAC,EAAE,MAAM,eAAe,MAAM;AAEpD,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACR;AAED,SAAO;AACT;AAOO,SAAS,qBAAqB,WAAW;AAC9C,MAAI,CAAC,aAAa,UAAU,WAAW;AACrC,WAAO;AAAA,EACR;AAED,QAAM,EAAE,MAAM,OAAO,QAAS,IAAG;AAEjC,MAAI,OAAO,GAAG;AACZ,WAAO,GAAG,IAAI,IAAI,KAAK;AAAA,EAC3B,WAAa,QAAQ,GAAG;AACpB,WAAO,GAAG,KAAK,IAAI,OAAO;AAAA,EAC9B,OAAS;AACL,WAAO,GAAG,OAAO;AAAA,EAClB;AACH;AAOO,SAAS,kBAAkB,WAAW;AAC3C,MAAI,CAAC,aAAa,UAAU,WAAW;AACrC,WAAO;AAAA,EACR;AAED,QAAM,EAAE,aAAY,IAAK;AAGzB,MAAI,eAAe,KAAK,IAAI;AAC1B,WAAO;AAAA,EACR,WAEQ,eAAe,IAAI,KAAK,IAAI;AACnC,WAAO;AAAA,EACR,OAEI;AACH,WAAO;AAAA,EACR;AACH;AAQO,SAAS,qBAAqB,UAAU,WAAW,KAAM;AAC9D,SAAO,YAAY,UAAU,QAAQ;AACvC;AAMO,SAAS,oBAAoB,SAAS;AAC3C,MAAI,SAAS;AACX,kBAAc,OAAO;AAAA,EACtB;AACH;;;;;;;"}