{"version": 3, "file": "ui.js", "sources": ["utils/ui.js"], "sourcesContent": ["// 显示加载提示\nexport function showLoading(title = '加载中...') {\n  uni.showLoading({\n    title,\n    mask: true\n  })\n}\n\n// 隐藏加载提示\nexport function hideLoading() {\n  uni.hideLoading()\n}\n\n// 显示消息提示\nexport function showToast(options) {\n  if (typeof options === 'string') {\n    options = { title: options }\n  }\n  \n  const defaultOptions = {\n    title: '',\n    icon: 'none',\n    duration: 2000,\n    mask: false\n  }\n  \n  uni.showToast({\n    ...defaultOptions,\n    ...options\n  })\n}\n\n// 显示成功提示\nexport function showSuccess(title) {\n  showToast({\n    title,\n    icon: 'success'\n  })\n}\n\n// 显示错误提示\nexport function showError(title) {\n  showToast({\n    title,\n    icon: 'error'\n  })\n}\n\n// 显示确认对话框\nexport function showConfirm(options) {\n  if (typeof options === 'string') {\n    options = { content: options }\n  }\n  \n  const defaultOptions = {\n    title: '提示',\n    content: '',\n    showCancel: true,\n    confirmText: '确定',\n    cancelText: '取消'\n  }\n  \n  return new Promise((resolve) => {\n    uni.showModal({\n      ...defaultOptions,\n      ...options,\n      success: (res) => {\n        resolve(res.confirm)\n      }\n    })\n  })\n}\n\n// 显示操作菜单\nexport function showActionSheet(itemList) {\n  return new Promise((resolve, reject) => {\n    uni.showActionSheet({\n      itemList,\n      success: (res) => {\n        resolve(res.tapIndex)\n      },\n      fail: (error) => {\n        reject(error)\n      }\n    })\n  })\n}"], "names": ["uni"], "mappings": ";;AACO,SAAS,YAAY,QAAQ,UAAU;AAC5CA,gBAAAA,MAAI,YAAY;AAAA,IACd;AAAA,IACA,MAAM;AAAA,EACV,CAAG;AACH;AAGO,SAAS,cAAc;AAC5BA,gBAAAA,MAAI,YAAa;AACnB;AAGO,SAAS,UAAU,SAAS;AACjC,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU,EAAE,OAAO,QAAS;AAAA,EAC7B;AAED,QAAM,iBAAiB;AAAA,IACrB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AAEDA,gBAAAA,MAAI,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAG;AACH;AAGO,SAAS,YAAY,OAAO;AACjC,YAAU;AAAA,IACR;AAAA,IACA,MAAM;AAAA,EACV,CAAG;AACH;AAGO,SAAS,UAAU,OAAO;AAC/B,YAAU;AAAA,IACR;AAAA,IACA,MAAM;AAAA,EACV,CAAG;AACH;;;;;;"}