{"version": 3, "file": "navigation.js", "sources": ["utils/navigation.js"], "sourcesContent": ["// 导航工具函数\n\n// tabbar页面列表\nconst TABBAR_PAGES = [\n  '/pages/index/index',\n  '/pages/venue/list',\n  '/pages/sharing/list', \n  '/pages/booking/list',\n  '/pages/user/profile'\n]\n\n/**\n * 智能页面跳转\n * 自动判断是否为tabbar页面，使用正确的跳转方式\n * @param {string} url - 目标页面路径\n * @param {object} options - 跳转选项\n */\nexport function navigateTo(url, options = {}) {\n  // 提取页面路径（去除查询参数）\n  const pagePath = url.split('?')[0]\n  \n  // 检查是否为tabbar页面\n  const isTabbarPage = TABBAR_PAGES.includes(pagePath)\n  \n  if (isTabbarPage) {\n    // tabbar页面使用switchTab\n    uni.switchTab({\n      url,\n      ...options\n    })\n  } else {\n    // 普通页面使用navigateTo\n    uni.navigateTo({\n      url,\n      ...options\n    })\n  }\n}\n\n/**\n * 智能导航函数（别名）\n * @param {string} url - 目标页面路径\n * @param {object} options - 跳转选项\n */\nexport function smartNavigate(url, options = {}) {\n  return navigateTo(url, options)\n}\n\n/**\n * 返回上一页\n * @param {number} delta - 返回层数，默认为1\n */\nexport function navigateBack(delta = 1) {\n  uni.navigateBack({\n    delta\n  })\n}\n\n/**\n * 重定向到指定页面\n * @param {string} url - 目标页面路径\n * @param {object} options - 跳转选项\n */\nexport function redirectTo(url, options = {}) {\n  uni.redirectTo({\n    url,\n    ...options\n  })\n}\n\n/**\n * 重新启动到指定页面\n * @param {string} url - 目标页面路径\n * @param {object} options - 跳转选项\n */\nexport function reLaunch(url, options = {}) {\n  uni.reLaunch({\n    url,\n    ...options\n  })\n}\n\n/**\n * 切换到tabbar页面\n * @param {string} url - tabbar页面路径\n * @param {object} options - 跳转选项\n */\nexport function switchTab(url, options = {}) {\n  uni.switchTab({\n    url,\n    ...options\n  })\n}\n\n/**\n * 检查是否为tabbar页面\n * @param {string} url - 页面路径\n * @returns {boolean}\n */\nexport function isTabbarPage(url) {\n  const pagePath = url.split('?')[0]\n  return TABBAR_PAGES.includes(pagePath)\n}\n\nexport default {\n  navigateTo,\n  smartNavigate,\n  navigateBack,\n  redirectTo,\n  reLaunch,\n  switchTab,\n  isTabbarPage\n}"], "names": ["uni"], "mappings": ";;AAGA,MAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAQO,SAAS,WAAW,KAAK,UAAU,IAAI;AAE5C,QAAM,WAAW,IAAI,MAAM,GAAG,EAAE,CAAC;AAGjC,QAAM,eAAe,aAAa,SAAS,QAAQ;AAEnD,MAAI,cAAc;AAEhBA,kBAAAA,MAAI,UAAU;AAAA,MACZ;AAAA,MACA,GAAG;AAAA,IACT,CAAK;AAAA,EACL,OAAS;AAELA,kBAAAA,MAAI,WAAW;AAAA,MACb;AAAA,MACA,GAAG;AAAA,IACT,CAAK;AAAA,EACF;AACH;AAOO,SAAS,cAAc,KAAK,UAAU,IAAI;AAC/C,SAAO,WAAW,KAAK,OAAO;AAChC;;"}