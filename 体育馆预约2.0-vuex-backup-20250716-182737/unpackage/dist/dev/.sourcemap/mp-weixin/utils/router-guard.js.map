{"version": 3, "file": "router-guard.js", "sources": ["utils/router-guard.js"], "sourcesContent": ["import { showToast } from './ui.js'\nimport { getToken, getUserInfo } from './auth.js'\n\n// 游客页面（未登录可访问）\nexport const guestPages = [\n  '/pages/user/login',\n  '/pages/user/register',\n  '/pages/index/index' // 允许未登录访问首页\n]\n\n// 所有页面都需要登录，除了登录和注册页面\nconst authPages = [\n  '/pages/index/index',\n  '/pages/venue/list',\n  '/pages/venue/detail',\n  '/pages/user/profile',\n  '/pages/booking/create',\n  '/pages/booking/list',\n  '/pages/booking/detail',\n  '/pages/sharing/create',\n  '/pages/sharing/list',\n  '/pages/sharing/detail'\n]\n\n// 路由守卫\nexport function setupRouterGuard() {\n  // 页面跳转前拦截\n  uni.addInterceptor('navigateTo', {\n    invoke(params) {\n      return checkPermission(params.url)\n    }\n  })\n  \n  uni.addInterceptor('redirectTo', {\n    invoke(params) {\n      return checkPermission(params.url)\n    }\n  })\n  \n  uni.addInterceptor('reLaunch', {\n    invoke(params) {\n      return checkPermission(params.url)\n    }\n  })\n  \n  uni.addInterceptor('switchTab', {\n    invoke(params) {\n      return checkPermission(params.url)\n    }\n  })\n}\n\n// 检查页面访问权限\nfunction checkPermission(url) {\n  // 提取页面路径\n  const pagePath = url.split('?')[0]\n  \n  // 检查是否是游客页面（登录、注册页面和首页）\n  const isGuestPage = guestPages.some(page => pagePath.includes(page))\n  \n  // 如果是游客页面，直接允许访问\n  if (isGuestPage) {\n    return true\n  }\n  \n  // 如果不是游客页面，则需要检查登录状态\n  // 检查登录状态（需要同时有token和userInfo）\n  const token = getToken()\n  const userInfo = getUserInfo()\n  \n  if (!token || !userInfo) {\n    // 需要登录但未登录，跳转到登录页\n    console.log('未登录，需要跳转到登录页面')\n    \n    // 避免重复提示\n    const pages = getCurrentPages()\n    const isFromLoginPage = pages.length > 0 && pages[pages.length - 1].route.includes('/pages/user/login')\n    \n    if (!isFromLoginPage) {\n      showToast('请先登录')\n    }\n    \n    // 保存当前页面路径作为重定向URL\n    const redirectUrl = encodeURIComponent(url)\n    \n    // 使用 reLaunch 而不是 navigateTo，确保用户不能通过返回按钮绕过登录\n    uni.reLaunch({\n      url: `/pages/user/login?redirect=${redirectUrl}`\n    })\n    return false\n  }\n  \n  return true\n}\n\n// 检查当前页面是否需要登录\nexport function checkCurrentPageAuth() {\n  const pages = getCurrentPages()\n  if (pages.length === 0) return true // 默认需要登录\n  \n  const currentPage = pages[pages.length - 1]\n  const pagePath = `/${currentPage.route}`\n  \n  // 检查是否是游客页面（登录和注册页面）\n  const isGuestPage = guestPages.some(page => pagePath.includes(page))\n  \n  // 如果是游客页面，则不需要登录；否则需要登录\n  return !isGuestPage\n}\n\n// 获取登录后的重定向页面\nexport function getRedirectPage() {\n  const pages = getCurrentPages()\n  if (pages.length <= 1) return '/pages/index/index'\n  \n  const previousPage = pages[pages.length - 2]\n  return previousPage ? `/${previousPage.route}` : '/pages/index/index'\n}"], "names": [], "mappings": ";;AAIY,MAAC,aAAa;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA;AACF;;"}