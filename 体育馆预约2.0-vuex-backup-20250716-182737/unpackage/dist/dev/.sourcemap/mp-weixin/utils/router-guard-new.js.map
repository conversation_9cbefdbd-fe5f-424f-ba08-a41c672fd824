{"version": 3, "file": "router-guard-new.js", "sources": ["utils/router-guard-new.js"], "sourcesContent": ["import { getToken, getUserInfo } from './auth.js'\nimport store from '@/store'\n\n// 无需登录即可访问的页面\nconst PUBLIC_PAGES = [\n  '/pages/user/login',\n  '/pages/user/register'\n]\n\n// 需要登录但不强制检查的页面（支付相关页面）\nconst PAYMENT_PAGES = [\n  '/pages/payment/index',\n  '/pages/payment/success',\n  '/pages/payment/failed'\n]\n\n// 除了登录注册页面，所有页面都需要登录\n\n// 当前是否正在检查登录状态\nlet isCheckingAuth = false\n// 登录状态检查结果缓存\nlet authCheckCache = null\nlet authCheckTime = 0\nconst AUTH_CACHE_DURATION = 30000 // 30秒缓存\n\n/**\n * 设置新的路由守卫\n */\nexport function setupRouterGuard() {\n  console.log('[RouterGuard] 设置路由守卫')\n  \n  // 拦截所有页面跳转\n  const interceptMethods = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']\n  \n  interceptMethods.forEach(method => {\n    uni.addInterceptor(method, {\n      invoke(params) {\n        return checkPagePermission(params.url, method)\n      }\n    })\n  })\n}\n\n/**\n * 检查页面访问权限\n * @param {string} url 目标页面URL\n * @param {string} method 跳转方法\n * @returns {boolean} 是否允许跳转\n */\nfunction checkPagePermission(url, method) {\n  const pagePath = extractPagePath(url)\n  console.log(`[RouterGuard] 检查页面权限: ${pagePath}, 方法: ${method}`)\n\n  // 1. 公开页面（登录注册页面），无需检查\n  if (isPublicPage(pagePath)) {\n    console.log('[RouterGuard] 公开页面，允许访问')\n    return true\n  }\n\n  // 2. 支付相关页面，使用宽松的登录检查\n  if (isPaymentPage(pagePath)) {\n    console.log('[RouterGuard] 支付页面，使用宽松检查')\n    const hasBasicAuth = !!(getToken() && getUserInfo())\n    if (!hasBasicAuth) {\n      console.log('[RouterGuard] 支付页面需要基本认证，跳转到登录页')\n      handleLoginRequired(url)\n      return false\n    }\n    console.log('[RouterGuard] 支付页面基本认证通过，允许访问')\n    return true\n  }\n\n  // 3. 检查登录状态\n  const isLoggedIn = checkLoginStatus()\n  console.log(`[RouterGuard] 登录状态: ${isLoggedIn}`)\n\n  // 4. 其他所有页面都需要登录\n  if (!isLoggedIn) {\n    console.log('[RouterGuard] 需要登录，跳转到登录页')\n    handleLoginRequired(url)\n    return false\n  }\n\n  console.log('[RouterGuard] 已登录，允许访问')\n  return true\n}\n\n/**\n * 提取页面路径（去除查询参数）\n * @param {string} url 完整URL\n * @returns {string} 页面路径\n */\nfunction extractPagePath(url) {\n  return url.split('?')[0]\n}\n\n/**\n * 检查是否为公开页面\n * @param {string} pagePath 页面路径\n * @returns {boolean}\n */\nfunction isPublicPage(pagePath) {\n  return PUBLIC_PAGES.some(page => pagePath === page)\n}\n\n/**\n * 检查是否为支付页面\n * @param {string} pagePath 页面路径\n * @returns {boolean}\n */\nfunction isPaymentPage(pagePath) {\n  return PAYMENT_PAGES.some(page => pagePath === page)\n}\n\n// 移除了不再使用的访客页面和认证页面检查函数\n\n/**\n * 检查登录状态（带缓存）\n * @returns {boolean} 是否已登录\n */\nfunction checkLoginStatus() {\n  const now = Date.now()\n  \n  // 使用缓存结果（30秒内）\n  if (authCheckCache !== null && (now - authCheckTime) < AUTH_CACHE_DURATION) {\n    console.log('[RouterGuard] 使用缓存的登录状态:', authCheckCache)\n    return authCheckCache\n  }\n  \n  // 检查本地存储\n  const token = getToken()\n  const userInfo = getUserInfo()\n  const storeLoginStatus = store.getters['user/isLoggedIn']\n  \n  console.log('[RouterGuard] 本地检查 - token:', !!token, 'userInfo:', !!userInfo, 'store:', storeLoginStatus)\n  \n  // 综合判断登录状态\n  const isLoggedIn = !!(token && userInfo && storeLoginStatus)\n  \n  // 更新缓存\n  authCheckCache = isLoggedIn\n  authCheckTime = now\n  \n  return isLoggedIn\n}\n\n/**\n * 处理需要登录的情况\n * @param {string} originalUrl 原始目标URL\n */\nfunction handleLoginRequired(originalUrl) {\n  // 避免重复跳转到登录页\n  const pages = getCurrentPages()\n  const currentPage = pages[pages.length - 1]\n  \n  if (currentPage && currentPage.route.includes('pages/user/login')) {\n    console.log('[RouterGuard] 已在登录页，不重复跳转')\n    return\n  }\n  \n  // 显示提示\n  uni.showToast({\n    title: '请先登录',\n    icon: 'none',\n    duration: 1500\n  })\n  \n  // 保存重定向URL\n  const redirectUrl = encodeURIComponent(originalUrl)\n  \n  // 跳转到登录页\n  setTimeout(() => {\n    uni.reLaunch({\n      url: `/pages/user/login?redirect=${redirectUrl}`,\n      fail: (err) => {\n        console.error('[RouterGuard] 跳转登录页失败:', err)\n      }\n    })\n  }, 100)\n}\n\n/**\n * 清除登录状态缓存\n */\nexport function clearAuthCache() {\n  console.log('[RouterGuard] 清除登录状态缓存')\n  authCheckCache = null\n  authCheckTime = 0\n}\n\n/**\n * 更新登录状态缓存\n * @param {boolean} isLoggedIn 登录状态\n */\nexport function updateAuthCache(isLoggedIn) {\n  console.log('[RouterGuard] 更新登录状态缓存:', isLoggedIn)\n  authCheckCache = isLoggedIn\n  authCheckTime = Date.now()\n}\n\n/**\n * 检查当前页面是否需要登录\n * @returns {boolean}\n */\nexport function checkCurrentPageAuth() {\n  const pages = getCurrentPages()\n  if (pages.length === 0) return false\n  \n  const currentPage = pages[pages.length - 1]\n  const pagePath = `/${currentPage.route}`\n  \n  return isAuthRequiredPage(pagePath)\n}\n\nexport default {\n  setupRouterGuard,\n  clearAuthCache,\n  updateAuthCache,\n  checkCurrentPageAuth\n}"], "names": ["uni", "getToken", "getUserInfo", "store"], "mappings": ";;;;AAIA,MAAM,eAAe;AAAA,EACnB;AAAA,EACA;AACF;AAGA,MAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AACF;AAOA,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,MAAM,sBAAsB;AAKrB,SAAS,mBAAmB;AACjCA,gBAAAA,MAAY,MAAA,OAAA,mCAAA,sBAAsB;AAGlC,QAAM,mBAAmB,CAAC,cAAc,cAAc,YAAY,WAAW;AAE7E,mBAAiB,QAAQ,YAAU;AACjCA,kBAAG,MAAC,eAAe,QAAQ;AAAA,MACzB,OAAO,QAAQ;AACb,eAAO,oBAAoB,OAAO,KAAK,MAAM;AAAA,MAC9C;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAQA,SAAS,oBAAoB,KAAK,QAAQ;AACxC,QAAM,WAAW,gBAAgB,GAAG;AACpCA,sBAAY,MAAA,OAAA,mCAAA,yBAAyB,QAAQ,SAAS,MAAM,EAAE;AAG9D,MAAI,aAAa,QAAQ,GAAG;AAC1BA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,yBAAyB;AACrC,WAAO;AAAA,EACR;AAGD,MAAI,cAAc,QAAQ,GAAG;AAC3BA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,2BAA2B;AACvC,UAAM,eAAe,CAAC,EAAEC,oBAAU,KAAIC,WAAW,YAAA;AACjD,QAAI,CAAC,cAAc;AACjBF,oBAAAA,sDAAY,iCAAiC;AAC7C,0BAAoB,GAAG;AACvB,aAAO;AAAA,IACR;AACDA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,+BAA+B;AAC3C,WAAO;AAAA,EACR;AAGD,QAAM,aAAa,iBAAkB;AACrCA,gBAAY,MAAA,MAAA,OAAA,mCAAA,uBAAuB,UAAU,EAAE;AAG/C,MAAI,CAAC,YAAY;AACfA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,2BAA2B;AACvC,wBAAoB,GAAG;AACvB,WAAO;AAAA,EACR;AAEDA,gBAAAA,MAAY,MAAA,OAAA,mCAAA,wBAAwB;AACpC,SAAO;AACT;AAOA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,MAAM,GAAG,EAAE,CAAC;AACzB;AAOA,SAAS,aAAa,UAAU;AAC9B,SAAO,aAAa,KAAK,UAAQ,aAAa,IAAI;AACpD;AAOA,SAAS,cAAc,UAAU;AAC/B,SAAO,cAAc,KAAK,UAAQ,aAAa,IAAI;AACrD;AAQA,SAAS,mBAAmB;AAC1B,QAAM,MAAM,KAAK,IAAK;AAGtB,MAAI,mBAAmB,QAAS,MAAM,gBAAiB,qBAAqB;AAC1EA,kBAAAA,MAAA,MAAA,OAAA,oCAAY,4BAA4B,cAAc;AACtD,WAAO;AAAA,EACR;AAGD,QAAM,QAAQC,WAAAA,SAAU;AACxB,QAAM,WAAWC,WAAAA,YAAa;AAC9B,QAAM,mBAAmBC,YAAAA,MAAM,QAAQ,iBAAiB;AAExDH,gBAAAA,uDAAY,+BAA+B,CAAC,CAAC,OAAO,aAAa,CAAC,CAAC,UAAU,UAAU,gBAAgB;AAGvG,QAAM,aAAa,CAAC,EAAE,SAAS,YAAY;AAG3C,mBAAiB;AACjB,kBAAgB;AAEhB,SAAO;AACT;AAMA,SAAS,oBAAoB,aAAa;AAExC,QAAM,QAAQ,gBAAiB;AAC/B,QAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAE1C,MAAI,eAAe,YAAY,MAAM,SAAS,kBAAkB,GAAG;AACjEA,kBAAAA,MAAA,MAAA,OAAA,oCAAY,2BAA2B;AACvC;AAAA,EACD;AAGDA,gBAAAA,MAAI,UAAU;AAAA,IACZ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACd,CAAG;AAGD,QAAM,cAAc,mBAAmB,WAAW;AAGlD,aAAW,MAAM;AACfA,kBAAAA,MAAI,SAAS;AAAA,MACX,KAAK,8BAA8B,WAAW;AAAA,MAC9C,MAAM,CAAC,QAAQ;AACbA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,0BAA0B,GAAG;AAAA,MAC5C;AAAA,IACP,CAAK;AAAA,EACF,GAAE,GAAG;AACR;AAKO,SAAS,iBAAiB;AAC/BA,gBAAAA,MAAY,MAAA,OAAA,oCAAA,wBAAwB;AACpC,mBAAiB;AACjB,kBAAgB;AAClB;AAMO,SAAS,gBAAgB,YAAY;AAC1CA,gBAAAA,MAAY,MAAA,OAAA,oCAAA,2BAA2B,UAAU;AACjD,mBAAiB;AACjB,kBAAgB,KAAK,IAAK;AAC5B;;;;"}