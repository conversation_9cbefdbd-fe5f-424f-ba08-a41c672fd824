"use strict";
const common_vendor = require("../common/vendor.js");
const migrationConfig = {
  // 迁移阶段配置
  phases: {
    // 阶段1: 基础设施 - 已完成
    phase1: {
      name: "基础设施搭建",
      status: "completed",
      modules: ["app"],
      description: "设置Pinia实例，创建同步插件，建立并行运行环境"
    },
    // 阶段2: 用户模块 - 进行中
    phase2: {
      name: "用户模块迁移",
      status: "in-progress",
      modules: ["user"],
      description: "迁移用户认证、登录状态、用户信息管理"
    },
    // 阶段3: 场馆模块 - 进行中
    phase3: {
      name: "场馆模块迁移",
      status: "in-progress",
      modules: ["venue"],
      description: "迁移场馆列表、详情、时间段管理"
    },
    // 阶段4: 预约模块 - 待开始
    phase4: {
      name: "预约模块迁移",
      status: "pending",
      modules: ["booking"],
      description: "迁移预约创建、列表、状态管理"
    },
    // 阶段5: 拼场模块 - 已完成
    phase5: {
      name: "拼场模块迁移",
      status: "completed",
      modules: ["sharing"],
      description: "迁移拼场创建、申请、管理功能"
    },
    // 阶段6: 预订模块 - 进行中
    phase6: {
      name: "预订模块迁移",
      status: "in-progress",
      modules: ["booking"],
      description: "迁移预订管理、订单处理、拼场功能"
    },
    // 阶段7: 清理 - 待开始
    phase7: {
      name: "清理Vuex",
      status: "pending",
      modules: [],
      description: "移除Vuex依赖，清理同步代码"
    }
  },
  // 当前激活的模块 - 控制哪些模块使用Pinia
  activeModules: ["app", "user", "venue", "sharing", "booking"],
  // 特性标志 - 控制是否启用Pinia功能
  features: {
    usePiniaForApp: true,
    usePiniaForUser: true,
    usePiniaForVenue: true,
    usePiniaForBooking: true,
    usePiniaForSharing: true,
    enableSync: true,
    // 是否启用Vuex-Pinia同步
    enableLogging: true
    // 是否启用迁移日志
  },
  // 测试配置
  testing: {
    enableMigrationTests: true,
    testModules: ["app", "user", "venue", "sharing", "booking"],
    autoValidation: true
    // 自动验证状态同步
  }
};
function getMigrationStatus() {
  var _a;
  const phases = migrationConfig.phases;
  const completed = Object.values(phases).filter((p) => p.status === "completed").length;
  const total = Object.keys(phases).length;
  return {
    progress: Math.round(completed / total * 100),
    currentPhase: ((_a = Object.values(phases).find((p) => p.status === "in-progress")) == null ? void 0 : _a.name) || "未开始",
    completedPhases: completed,
    totalPhases: total
  };
}
function isFeatureEnabled(featureName) {
  return migrationConfig.features[featureName] || false;
}
function logMigration(message, data = null) {
  if (isFeatureEnabled("enableLogging")) {
    common_vendor.index.__f__("log", "at stores/migration-config.js:127", `[Migration] ${message}`, data || "");
  }
}
exports.getMigrationStatus = getMigrationStatus;
exports.logMigration = logMigration;
exports.migrationConfig = migrationConfig;
//# sourceMappingURL=../../.sourcemap/mp-weixin/stores/migration-config.js.map
