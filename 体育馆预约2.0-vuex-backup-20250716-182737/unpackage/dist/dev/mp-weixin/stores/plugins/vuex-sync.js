"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
let syncInProgress = false;
function vuexSyncPlugin() {
  return ({ store: piniaStore }) => {
    if (piniaStore.$id === "app") {
      common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:11", "[Pinia-Vuex Sync] 初始化app store同步");
      piniaStore.$state.loading = store_index.store.state.loading;
      piniaStore.$state.networkStatus = store_index.store.state.networkStatus;
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress)
          return;
        common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:21", "[Pinia->Vuex] App同步状态变化:", mutation.type, mutation.payload);
        try {
          syncInProgress = true;
          store_index.store.dispatch("setLoading", state.loading);
          store_index.store.dispatch("setNetworkStatus", state.networkStatus);
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:29", "[Pinia->Vuex] App状态已同步:", { loading: state.loading, networkStatus: state.networkStatus });
        } catch (error) {
          common_vendor.index.__f__("error", "at stores/plugins/vuex-sync.js:31", "[Pinia->Vuex] App同步失败:", error);
        } finally {
          syncInProgress = false;
        }
      });
      store_index.store.subscribe((mutation, state) => {
        if (mutation.type === "SET_LOADING") {
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:41", "[Vuex->Pinia] App同步状态变化:", mutation.type);
          piniaStore.loading = state.loading;
        }
        if (mutation.type === "SET_NETWORK_STATUS") {
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:45", "[Vuex->Pinia] App同步状态变化:", mutation.type);
          piniaStore.networkStatus = state.networkStatus;
        }
      });
    }
    if (piniaStore.$id === "user") {
      common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:54", "[Pinia-Vuex Sync] 初始化user store同步");
      const vuexUserState = store_index.store.state.user;
      if (vuexUserState) {
        piniaStore.$state.token = vuexUserState.token;
        piniaStore.$state.userInfo = vuexUserState.userInfo;
        piniaStore.$state.userStats = vuexUserState.userStats;
        piniaStore.$state.isLoggedIn = vuexUserState.isLoggedIn;
        piniaStore.$state.loginChecking = vuexUserState.loginChecking;
      }
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress)
          return;
        common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:70", "[Pinia->Vuex] User同步状态变化:", mutation.type, mutation.payload);
        try {
          syncInProgress = true;
          store_index.store.commit("user/SET_TOKEN", state.token);
          store_index.store.commit("user/SET_USER_INFO", state.userInfo);
          store_index.store.commit("user/SET_LOGIN_STATUS", state.isLoggedIn);
          store_index.store.commit("user/SET_LOGIN_CHECKING", state.loginChecking);
          store_index.store.commit("user/SET_USER_STATS", state.userStats);
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:81", "[Pinia->Vuex] User状态已同步");
        } catch (error) {
          common_vendor.index.__f__("error", "at stores/plugins/vuex-sync.js:83", "[Pinia->Vuex] User同步失败:", error);
        } finally {
          syncInProgress = false;
        }
      });
      store_index.store.subscribe((mutation, state) => {
        if (mutation.type.startsWith("user/")) {
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:92", "[Vuex->Pinia] User同步状态变化:", mutation.type);
          const userState = state.user;
          if (mutation.type === "user/SET_TOKEN") {
            piniaStore.token = userState.token;
          }
          if (mutation.type === "user/SET_USER_INFO") {
            piniaStore.userInfo = userState.userInfo;
          }
          if (mutation.type === "user/SET_LOGIN_STATUS") {
            piniaStore.isLoggedIn = userState.isLoggedIn;
          }
          if (mutation.type === "user/SET_LOGIN_CHECKING") {
            piniaStore.loginChecking = userState.loginChecking;
          }
          if (mutation.type === "user/SET_USER_STATS") {
            piniaStore.userStats = userState.userStats;
          }
          if (mutation.type === "user/CLEAR_USER_DATA") {
            piniaStore.token = "";
            piniaStore.userInfo = null;
            piniaStore.userStats = { totalBookings: 0, totalSharings: 0 };
            piniaStore.isLoggedIn = false;
            piniaStore.loginChecking = false;
          }
        }
      });
    }
    if (piniaStore.$id === "venue") {
      common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:123", "[Pinia-Vuex Sync] 初始化venue store同步");
      const vuexVenueState = store_index.store.state.venue;
      if (vuexVenueState) {
        piniaStore.$state.venueList = vuexVenueState.venueList;
        piniaStore.$state.popularVenues = vuexVenueState.popularVenues;
        piniaStore.$state.venueDetail = vuexVenueState.venueDetail;
        piniaStore.$state.venueTypes = vuexVenueState.venueTypes;
        piniaStore.$state.timeSlots = vuexVenueState.timeSlots;
        piniaStore.$state.searchResults = vuexVenueState.searchResults;
        piniaStore.$state.loading = vuexVenueState.loading;
        piniaStore.$state.pagination = vuexVenueState.pagination;
      }
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress)
          return;
        common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:142", "[Pinia->Vuex] Venue同步状态变化:", mutation.type);
        try {
          syncInProgress = true;
          const venueUpdates = {
            venueList: state.venueList,
            popularVenues: state.popularVenues,
            venueDetail: state.venueDetail,
            venueTypes: state.venueTypes,
            timeSlots: state.timeSlots,
            searchResults: state.searchResults,
            loading: state.loading,
            pagination: state.pagination
          };
          store_index.store.commit("venue/BATCH_UPDATE", venueUpdates);
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:163", "[Pinia->Vuex] Venue状态已批量同步:", Object.keys(venueUpdates));
        } catch (error) {
          common_vendor.index.__f__("warn", "at stores/plugins/vuex-sync.js:166", "[Pinia->Vuex] 批量同步失败，使用单独同步:", error);
          try {
            store_index.store.commit("venue/SET_VENUE_LIST", { list: state.venueList, pagination: state.pagination });
            store_index.store.commit("venue/SET_POPULAR_VENUES", state.popularVenues);
            store_index.store.commit("venue/SET_VENUE_DETAIL", state.venueDetail);
            store_index.store.commit("venue/SET_VENUE_TYPES", state.venueTypes);
            store_index.store.commit("venue/SET_TIME_SLOTS", state.timeSlots);
            store_index.store.commit("venue/SET_SEARCH_RESULTS", state.searchResults);
            store_index.store.commit("venue/SET_LOADING", state.loading);
            common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:175", "[Pinia->Vuex] Venue状态已单独同步");
          } catch (fallbackError) {
            common_vendor.index.__f__("error", "at stores/plugins/vuex-sync.js:177", "[Pinia->Vuex] Venue同步完全失败:", fallbackError);
          }
        } finally {
          syncInProgress = false;
        }
      });
      store_index.store.subscribe((mutation, state) => {
        if (syncInProgress)
          return;
        if (mutation.type.startsWith("venue/")) {
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:189", "[Vuex->Pinia] Venue同步状态变化:", mutation.type);
          try {
            syncInProgress = true;
            const venueState = state.venue;
            switch (mutation.type) {
              case "venue/SET_VENUE_LIST":
                piniaStore.venueList = venueState.venueList;
                piniaStore.pagination = venueState.pagination;
                break;
              case "venue/APPEND_VENUE_LIST":
                piniaStore.venueList = venueState.venueList;
                break;
              case "venue/SET_POPULAR_VENUES":
                piniaStore.popularVenues = venueState.popularVenues;
                break;
              case "venue/SET_VENUE_DETAIL":
                piniaStore.venueDetail = venueState.venueDetail;
                break;
              case "venue/SET_VENUE_TYPES":
                piniaStore.venueTypes = venueState.venueTypes;
                break;
              case "venue/SET_TIME_SLOTS":
                piniaStore.timeSlots = venueState.timeSlots;
                break;
              case "venue/SET_SEARCH_RESULTS":
                piniaStore.searchResults = venueState.searchResults;
                break;
              case "venue/SET_LOADING":
                piniaStore.loading = venueState.loading;
                break;
              case "venue/BATCH_UPDATE":
                piniaStore.venueList = venueState.venueList;
                piniaStore.popularVenues = venueState.popularVenues;
                piniaStore.venueDetail = venueState.venueDetail;
                piniaStore.venueTypes = venueState.venueTypes;
                piniaStore.timeSlots = venueState.timeSlots;
                piniaStore.searchResults = venueState.searchResults;
                piniaStore.loading = venueState.loading;
                piniaStore.pagination = venueState.pagination;
                common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:232", "[Vuex->Pinia] Venue批量同步完成");
                break;
              default:
                piniaStore.venueList = venueState.venueList;
                piniaStore.popularVenues = venueState.popularVenues;
                piniaStore.venueDetail = venueState.venueDetail;
                piniaStore.venueTypes = venueState.venueTypes;
                piniaStore.timeSlots = venueState.timeSlots;
                piniaStore.searchResults = venueState.searchResults;
                piniaStore.loading = venueState.loading;
                piniaStore.pagination = venueState.pagination;
                break;
            }
          } finally {
            syncInProgress = false;
          }
        }
      });
    }
    if (piniaStore.$id === "sharing") {
      common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:255", "[Pinia-Vuex Sync] 初始化sharing store同步");
      const vuexSharingState = store_index.store.state.sharing;
      if (vuexSharingState) {
        piniaStore.$state.sharingOrders = vuexSharingState.sharingOrders || [];
        piniaStore.$state.mySharingOrders = vuexSharingState.mySharingOrders || [];
        piniaStore.$state.receivedRequests = vuexSharingState.receivedRequests || [];
        piniaStore.$state.sentRequests = vuexSharingState.sentRequests || [];
        piniaStore.$state.sharingOrderDetail = vuexSharingState.sharingOrderDetail;
        piniaStore.$state.loading = vuexSharingState.loading || false;
        piniaStore.$state.pagination = vuexSharingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1 };
      }
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress)
          return;
        common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:273", "[Pinia->Vuex] Sharing同步状态变化:", mutation.type);
        try {
          syncInProgress = true;
          const sharingUpdates = {
            sharingOrders: state.sharingOrders,
            mySharingOrders: state.mySharingOrders,
            receivedRequests: state.receivedRequests,
            sentRequests: state.sentRequests,
            sharingOrderDetail: state.sharingOrderDetail,
            loading: state.loading,
            pagination: state.pagination
          };
          store_index.store.commit("sharing/BATCH_UPDATE", sharingUpdates);
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:291", "[Pinia->Vuex] Sharing状态已批量同步:", Object.keys(sharingUpdates));
        } catch (error) {
          common_vendor.index.__f__("warn", "at stores/plugins/vuex-sync.js:294", "[Pinia->Vuex] Sharing批量同步失败，使用单独同步:", error);
          try {
            store_index.store.commit("sharing/SET_SHARING_ORDERS", state.sharingOrders);
            store_index.store.commit("sharing/SET_MY_SHARING_ORDERS", state.mySharingOrders);
            store_index.store.commit("sharing/SET_RECEIVED_REQUESTS", state.receivedRequests);
            store_index.store.commit("sharing/SET_SENT_REQUESTS", state.sentRequests);
            store_index.store.commit("sharing/SET_SHARING_ORDER_DETAIL", state.sharingOrderDetail);
            store_index.store.commit("sharing/SET_LOADING", state.loading);
            store_index.store.commit("sharing/SET_PAGINATION", state.pagination);
            common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:303", "[Pinia->Vuex] Sharing状态已单独同步");
          } catch (fallbackError) {
            common_vendor.index.__f__("error", "at stores/plugins/vuex-sync.js:305", "[Pinia->Vuex] Sharing同步完全失败:", fallbackError);
          }
        } finally {
          syncInProgress = false;
        }
      });
      store_index.store.subscribe((mutation, state) => {
        if (syncInProgress)
          return;
        if (mutation.type.startsWith("sharing/")) {
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:317", "[Vuex->Pinia] Sharing同步状态变化:", mutation.type);
          try {
            syncInProgress = true;
            const sharingState = state.sharing;
            switch (mutation.type) {
              case "sharing/SET_SHARING_ORDERS":
                piniaStore.sharingOrders = sharingState.sharingOrders;
                break;
              case "sharing/SET_MY_SHARING_ORDERS":
                piniaStore.mySharingOrders = sharingState.mySharingOrders;
                break;
              case "sharing/SET_RECEIVED_REQUESTS":
                piniaStore.receivedRequests = sharingState.receivedRequests;
                break;
              case "sharing/SET_SENT_REQUESTS":
                piniaStore.sentRequests = sharingState.sentRequests;
                break;
              case "sharing/SET_SHARING_ORDER_DETAIL":
                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail;
                break;
              case "sharing/SET_LOADING":
                piniaStore.loading = sharingState.loading;
                break;
              case "sharing/SET_PAGINATION":
                piniaStore.pagination = sharingState.pagination;
                break;
              case "sharing/BATCH_UPDATE":
                piniaStore.sharingOrders = sharingState.sharingOrders;
                piniaStore.mySharingOrders = sharingState.mySharingOrders;
                piniaStore.receivedRequests = sharingState.receivedRequests;
                piniaStore.sentRequests = sharingState.sentRequests;
                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail;
                piniaStore.loading = sharingState.loading;
                piniaStore.pagination = sharingState.pagination;
                common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:355", "[Vuex->Pinia] Sharing批量同步完成");
                break;
              default:
                piniaStore.sharingOrders = sharingState.sharingOrders;
                piniaStore.mySharingOrders = sharingState.mySharingOrders;
                piniaStore.receivedRequests = sharingState.receivedRequests;
                piniaStore.sentRequests = sharingState.sentRequests;
                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail;
                piniaStore.loading = sharingState.loading;
                piniaStore.pagination = sharingState.pagination;
                break;
            }
          } finally {
            syncInProgress = false;
          }
        }
      });
    }
    if (piniaStore.$id === "booking") {
      common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:377", "[Pinia-Vuex Sync] 初始化booking store同步");
      const vuexBookingState = store_index.store.state.booking;
      if (vuexBookingState) {
        piniaStore.$state.bookingList = vuexBookingState.bookingList || [];
        piniaStore.$state.bookingDetail = vuexBookingState.bookingDetail;
        piniaStore.$state.sharingOrders = vuexBookingState.sharingOrders || [];
        piniaStore.$state.userSharingOrders = vuexBookingState.userSharingOrders || [];
        piniaStore.$state.joinedSharingOrders = vuexBookingState.joinedSharingOrders || [];
        piniaStore.$state.sharingDetail = vuexBookingState.sharingDetail;
        piniaStore.$state.loading = vuexBookingState.loading || false;
        piniaStore.$state.pagination = vuexBookingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 };
      }
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress)
          return;
        common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:396", "[Pinia->Vuex] Booking同步状态变化:", mutation.type);
        try {
          syncInProgress = true;
          const bookingUpdates = {
            bookingList: state.bookingList,
            bookingDetail: state.bookingDetail,
            sharingOrders: state.sharingOrders,
            userSharingOrders: state.userSharingOrders,
            joinedSharingOrders: state.joinedSharingOrders,
            sharingDetail: state.sharingDetail,
            loading: state.loading,
            pagination: state.pagination
          };
          store_index.store.commit("booking/BATCH_UPDATE", bookingUpdates);
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:415", "[Pinia->Vuex] Booking状态已批量同步:", Object.keys(bookingUpdates));
        } catch (error) {
          common_vendor.index.__f__("warn", "at stores/plugins/vuex-sync.js:418", "[Pinia->Vuex] Booking批量同步失败，使用单独同步:", error);
          try {
            store_index.store.commit("booking/SET_BOOKING_LIST", { list: state.bookingList, pagination: state.pagination });
            store_index.store.commit("booking/SET_BOOKING_DETAIL", state.bookingDetail);
            store_index.store.commit("booking/SET_SHARING_ORDERS", state.sharingOrders);
            store_index.store.commit("booking/SET_USER_SHARING_ORDERS", state.userSharingOrders);
            store_index.store.commit("booking/SET_JOINED_SHARING_ORDERS", state.joinedSharingOrders);
            store_index.store.commit("booking/SET_SHARING_DETAIL", state.sharingDetail);
            store_index.store.commit("booking/SET_LOADING", state.loading);
            store_index.store.commit("booking/SET_PAGINATION", state.pagination);
            common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:428", "[Pinia->Vuex] Booking状态已单独同步");
          } catch (fallbackError) {
            common_vendor.index.__f__("error", "at stores/plugins/vuex-sync.js:430", "[Pinia->Vuex] Booking同步完全失败:", fallbackError);
          }
        } finally {
          syncInProgress = false;
        }
      });
      store_index.store.subscribe((mutation, state) => {
        if (syncInProgress)
          return;
        if (mutation.type.startsWith("booking/")) {
          common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:442", "[Vuex->Pinia] Booking同步状态变化:", mutation.type);
          try {
            syncInProgress = true;
            const bookingState = state.booking;
            switch (mutation.type) {
              case "booking/SET_BOOKING_LIST":
                piniaStore.bookingList = bookingState.bookingList;
                piniaStore.pagination = bookingState.pagination;
                break;
              case "booking/APPEND_BOOKING_LIST":
                piniaStore.bookingList = bookingState.bookingList;
                break;
              case "booking/SET_BOOKING_DETAIL":
                piniaStore.bookingDetail = bookingState.bookingDetail;
                break;
              case "booking/SET_SHARING_ORDERS":
                piniaStore.sharingOrders = bookingState.sharingOrders;
                break;
              case "booking/SET_USER_SHARING_ORDERS":
                piniaStore.userSharingOrders = bookingState.userSharingOrders;
                break;
              case "booking/SET_JOINED_SHARING_ORDERS":
                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders;
                break;
              case "booking/SET_SHARING_DETAIL":
                piniaStore.sharingDetail = bookingState.sharingDetail;
                break;
              case "booking/SET_LOADING":
                piniaStore.loading = bookingState.loading;
                break;
              case "booking/SET_PAGINATION":
                piniaStore.pagination = bookingState.pagination;
                break;
              case "booking/UPDATE_BOOKING_STATUS":
                piniaStore.bookingList = bookingState.bookingList;
                break;
              case "booking/BATCH_UPDATE":
                piniaStore.bookingList = bookingState.bookingList;
                piniaStore.bookingDetail = bookingState.bookingDetail;
                piniaStore.sharingOrders = bookingState.sharingOrders;
                piniaStore.userSharingOrders = bookingState.userSharingOrders;
                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders;
                piniaStore.sharingDetail = bookingState.sharingDetail;
                piniaStore.loading = bookingState.loading;
                piniaStore.pagination = bookingState.pagination;
                common_vendor.index.__f__("log", "at stores/plugins/vuex-sync.js:491", "[Vuex->Pinia] Booking批量同步完成");
                break;
              default:
                piniaStore.bookingList = bookingState.bookingList;
                piniaStore.bookingDetail = bookingState.bookingDetail;
                piniaStore.sharingOrders = bookingState.sharingOrders;
                piniaStore.userSharingOrders = bookingState.userSharingOrders;
                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders;
                piniaStore.sharingDetail = bookingState.sharingDetail;
                piniaStore.loading = bookingState.loading;
                piniaStore.pagination = bookingState.pagination;
                break;
            }
          } finally {
            syncInProgress = false;
          }
        }
      });
    }
  };
}
exports.vuexSyncPlugin = vuexSyncPlugin;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/stores/plugins/vuex-sync.js.map
