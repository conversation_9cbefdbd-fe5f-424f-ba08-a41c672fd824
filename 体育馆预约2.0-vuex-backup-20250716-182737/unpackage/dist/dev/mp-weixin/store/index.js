"use strict";
const common_vendor = require("../common/vendor.js");
const store_modules_user = require("./modules/user.js");
const store_modules_venue = require("./modules/venue.js");
const store_modules_booking = require("./modules/booking.js");
const store_modules_sharing = require("./modules/sharing.js");
const pinia = common_vendor.createPinia();
const store = common_vendor.createStore({
  modules: {
    user: store_modules_user.user,
    venue: store_modules_venue.venue,
    booking: store_modules_booking.booking,
    sharing: store_modules_sharing.sharing
  },
  state: {
    // 全局状态
    loading: false,
    networkStatus: true
  },
  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    SET_NETWORK_STATUS(state, status) {
      state.networkStatus = status;
    }
  },
  actions: {
    setLoading({ commit }, loading) {
      commit("SET_LOADING", loading);
    },
    setNetworkStatus({ commit }, status) {
      commit("SET_NETWORK_STATUS", status);
    }
  },
  getters: {
    isLoading: (state) => state.loading,
    isOnline: (state) => state.networkStatus
  }
});
exports.pinia = pinia;
exports.store = store;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/index.js.map
