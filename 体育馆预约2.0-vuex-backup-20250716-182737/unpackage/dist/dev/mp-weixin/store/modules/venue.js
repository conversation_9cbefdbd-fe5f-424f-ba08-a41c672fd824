"use strict";
const common_vendor = require("../../common/vendor.js");
const api_venue = require("../../api/venue.js");
const api_timeslot = require("../../api/timeslot.js");
const utils_ui = require("../../utils/ui.js");
const state = {
  venueList: [],
  popularVenues: [],
  venueDetail: null,
  venueTypes: [],
  timeSlots: [],
  searchResults: [],
  loading: false,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  }
};
const mutations = {
  SET_VENUE_LIST(state2, { list, pagination }) {
    state2.venueList = list;
    if (pagination) {
      state2.pagination = { ...state2.pagination, ...pagination };
    }
  },
  APPEND_VENUE_LIST(state2, list) {
    state2.venueList = [...state2.venueList, ...list];
  },
  SET_POPULAR_VENUES(state2, venues) {
    state2.popularVenues = venues;
  },
  SET_VENUE_DETAIL(state2, venue2) {
    state2.venueDetail = venue2;
  },
  SET_VENUE_TYPES(state2, types) {
    state2.venueTypes = types;
  },
  SET_TIME_SLOTS(state2, slots) {
    state2.timeSlots = slots;
  },
  SET_SEARCH_RESULTS(state2, results) {
    state2.searchResults = results;
  },
  SET_LOADING(state2, loading) {
    state2.loading = loading;
  },
  UPDATE_TIME_SLOT_STATUS(state2, { slotId, status }) {
    const slot = state2.timeSlots.find((s) => s.id === slotId);
    if (slot) {
      slot.status = status;
    }
  },
  // 批量更新mutation，用于优化同步性能
  BATCH_UPDATE(state2, updates) {
    if (updates.venueList !== void 0) {
      state2.venueList = updates.venueList;
    }
    if (updates.popularVenues !== void 0) {
      state2.popularVenues = updates.popularVenues;
    }
    if (updates.venueDetail !== void 0) {
      state2.venueDetail = updates.venueDetail;
    }
    if (updates.venueTypes !== void 0) {
      state2.venueTypes = updates.venueTypes;
    }
    if (updates.timeSlots !== void 0) {
      state2.timeSlots = updates.timeSlots;
    }
    if (updates.searchResults !== void 0) {
      state2.searchResults = updates.searchResults;
    }
    if (updates.loading !== void 0) {
      state2.loading = updates.loading;
    }
    if (updates.pagination !== void 0) {
      state2.pagination = { ...state2.pagination, ...updates.pagination };
    }
  }
};
const actions = {
  // 获取场馆列表
  async getVenueList({ commit, state: state2 }, params = {}) {
    try {
      common_vendor.index.__f__("log", "at store/modules/venue.js:96", "开始获取场馆列表，参数:", params);
      commit("SET_LOADING", true);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("请求超时")), 1e4);
      });
      const apiPromise = api_venue.getVenueList(params);
      const response = await Promise.race([apiPromise, timeoutPromise]);
      common_vendor.index.__f__("log", "at store/modules/venue.js:107", "场馆API响应:", response);
      let list = [];
      let pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      };
      if (response && response.data) {
        if (Array.isArray(response.data)) {
          list = response.data;
          pagination = {
            current: response.page || params.page || 1,
            pageSize: response.pageSize || params.pageSize || 10,
            total: response.total || response.data.length,
            totalPages: response.totalPages || 1
          };
        } else {
          common_vendor.index.__f__("warn", "at store/modules/venue.js:128", "API响应数据格式异常，使用空数组:", response);
        }
      } else if (response && Array.isArray(response)) {
        list = response;
        pagination.total = response.length;
      } else {
        common_vendor.index.__f__("warn", "at store/modules/venue.js:135", "API响应为空或格式错误，使用空数组:", response);
      }
      common_vendor.index.__f__("log", "at store/modules/venue.js:138", "解析的场馆列表:", list);
      common_vendor.index.__f__("log", "at store/modules/venue.js:139", "分页信息:", pagination);
      if (params.page === 1 || params.refresh) {
        commit("SET_VENUE_LIST", { list, pagination });
      } else {
        commit("APPEND_VENUE_LIST", list);
        commit("SET_VENUE_LIST", { list: state2.venueList, pagination });
      }
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/venue.js:150", "获取场馆列表失败:", error);
      if (error.message.includes("Network Error") || error.message.includes("timeout") || error.message.includes("请求超时") || error.statusCode === void 0) {
        common_vendor.index.__f__("log", "at store/modules/venue.js:154", "使用模拟场馆数据");
        const mockData = [
          {
            id: 1,
            name: "示例篮球场",
            image: "https://via.placeholder.com/300x200/ff6b35/ffffff?text=篮球场",
            location: "北京市朝阳区示例路123号",
            rating: 4.5,
            price: 120,
            type: "篮球场",
            status: "available",
            supportSharing: true
          },
          {
            id: 2,
            name: "示例羽毛球馆",
            image: "https://via.placeholder.com/300x200/4CAF50/ffffff?text=羽毛球馆",
            location: "北京市海淀区示例街456号",
            rating: 4.3,
            price: 80,
            type: "羽毛球",
            status: "available",
            supportSharing: false
          },
          {
            id: 3,
            name: "示例网球场",
            image: "https://via.placeholder.com/300x200/2196F3/ffffff?text=网球场",
            location: "北京市西城区示例大道789号",
            rating: 4.7,
            price: 200,
            type: "网球",
            status: "maintenance",
            supportSharing: true
          }
        ];
        const mockPagination = {
          current: 1,
          pageSize: 10,
          total: mockData.length,
          totalPages: 1
        };
        commit("SET_VENUE_LIST", { list: mockData, pagination: mockPagination });
        common_vendor.index.__f__("log", "at store/modules/venue.js:199", "已设置模拟场馆数据:", mockData);
        return { data: mockData };
      }
      utils_ui.showError(error.message || "获取场馆列表失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取热门场馆
  async getPopularVenues({ commit }) {
    try {
      common_vendor.index.__f__("log", "at store/modules/venue.js:213", "开始获取热门场馆...");
      const response = await api_venue.getPopularVenues();
      common_vendor.index.__f__("log", "at store/modules/venue.js:215", "热门场馆API响应:", response);
      let venues = [];
      if (Array.isArray(response)) {
        venues = response;
      } else if (response && Array.isArray(response.data)) {
        venues = response.data;
      } else {
        common_vendor.index.__f__("warn", "at store/modules/venue.js:224", "热门场馆API响应格式异常:", response);
      }
      commit("SET_POPULAR_VENUES", venues);
      common_vendor.index.__f__("log", "at store/modules/venue.js:228", "热门场馆数据已设置:", venues);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/venue.js:231", "获取热门场馆失败:", error);
      commit("SET_POPULAR_VENUES", []);
      utils_ui.showError(error.message || "获取热门场馆失败");
      throw error;
    }
  },
  // 获取场馆详情
  async getVenueDetail({ commit }, venueId) {
    try {
      common_vendor.index.__f__("log", "at store/modules/venue.js:241", "开始获取场馆详情，ID:", venueId);
      commit("SET_LOADING", true);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("获取详情超时")), 1e4);
      });
      const apiPromise = api_venue.getVenueDetail(venueId);
      const response = await Promise.race([apiPromise, timeoutPromise]);
      common_vendor.index.__f__("log", "at store/modules/venue.js:251", "场馆详情API响应:", response);
      let venueDetail = null;
      if (response && response.data) {
        venueDetail = response.data;
      } else if (response && !response.data) {
        venueDetail = response;
      }
      if (venueDetail) {
        const transformedDetail = {
          ...venueDetail,
          // 转换营业时间格式
          openingHours: venueDetail.openingHours || (venueDetail.openTime && venueDetail.closeTime ? `${venueDetail.openTime.substring(0, 5)} - ${venueDetail.closeTime.substring(0, 5)}` : "08:00 - 22:00"),
          // 转换设施格式
          facilities: venueDetail.facilities ? Array.isArray(venueDetail.facilities) ? venueDetail.facilities : venueDetail.facilities.split(",").map((facility, index) => ({
            name: facility.trim(),
            icon: ["🚪", "🚿", "🗄️", "💧", "🏀", "⚽", "🎾", "🏐"][index] || "🏢"
          })) : [],
          // 确保价格字段存在
          price: venueDetail.price || 0
        };
        commit("SET_VENUE_DETAIL", transformedDetail);
        common_vendor.index.__f__("log", "at store/modules/venue.js:281", "场馆详情已设置:", transformedDetail);
      } else {
        common_vendor.index.__f__("error", "at store/modules/venue.js:283", "场馆详情数据为空");
        throw new Error("场馆详情数据为空");
      }
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/venue.js:289", "获取场馆详情失败:", error);
      if (error.message.includes("Network Error") || error.message.includes("timeout") || error.message.includes("获取详情超时") || error.statusCode === void 0) {
        common_vendor.index.__f__("log", "at store/modules/venue.js:293", "使用模拟数据");
        const mockData = {
          id: venueId,
          name: "示例体育馆",
          images: [
            "https://via.placeholder.com/400x300/ff6b35/ffffff?text=场馆图片1",
            "https://via.placeholder.com/400x300/4CAF50/ffffff?text=场馆图片2"
          ],
          rating: 4.5,
          reviewCount: 128,
          location: "北京市朝阳区示例路123号",
          distance: 2.5,
          price: 120,
          tags: ["室内", "空调", "停车场", "WiFi"],
          description: "这是一个现代化的体育馆，设施齐全，环境优美。",
          facilities: [
            { name: "更衣室", icon: "🚪" },
            { name: "淋浴间", icon: "🚿" },
            { name: "储物柜", icon: "🗄️" },
            { name: "饮水机", icon: "💧" }
          ],
          openTime: "06:00",
          closeTime: "22:00",
          openingHours: "06:00 - 22:00",
          phone: "010-12345678"
        };
        commit("SET_VENUE_DETAIL", mockData);
        common_vendor.index.__f__("log", "at store/modules/venue.js:320", "已设置模拟场馆数据:", mockData);
        return { data: mockData };
      }
      utils_ui.showError(error.message || "获取场馆详情失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取场馆时间段
  async getVenueTimeSlots({ commit }, { venueId, date }) {
    try {
      common_vendor.index.__f__("log", "at store/modules/venue.js:334", "开始获取场馆时间段，venueId:", venueId, "date:", date);
      commit("SET_LOADING", true);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("获取时间段超时")), 8e3);
      });
      const timestamp = Date.now();
      const apiPromise = api_venue.getVenueTimeSlots(venueId, date, { _t: timestamp });
      const response = await Promise.race([apiPromise, timeoutPromise]);
      common_vendor.index.__f__("log", "at store/modules/venue.js:347", "时间段API响应:", response);
      let timeSlots = [];
      if (response && response.data && response.data.slots && Array.isArray(response.data.slots)) {
        timeSlots = response.data.slots;
      } else if (response && response.slots && Array.isArray(response.slots)) {
        timeSlots = response.slots;
      } else if (response && response.data && Array.isArray(response.data)) {
        timeSlots = response.data;
      } else if (response && Array.isArray(response)) {
        timeSlots = response;
      } else {
        common_vendor.index.__f__("warn", "at store/modules/venue.js:359", "时间段API响应格式异常:", response);
        timeSlots = [];
      }
      if (timeSlots.length === 0) {
        common_vendor.index.__f__("log", "at store/modules/venue.js:365", "没有找到时间段，尝试生成时间段");
        try {
          const generateResponse = await api_timeslot.generateTimeSlots(venueId, date);
          common_vendor.index.__f__("log", "at store/modules/venue.js:368", "生成时间段响应:", generateResponse);
          const retryResponse = await api_venue.getVenueTimeSlots(venueId, date);
          common_vendor.index.__f__("log", "at store/modules/venue.js:372", "重新获取时间段响应:", retryResponse);
          if (retryResponse && retryResponse.data && Array.isArray(retryResponse.data)) {
            timeSlots = retryResponse.data;
          } else if (retryResponse && Array.isArray(retryResponse)) {
            timeSlots = retryResponse;
          }
        } catch (generateError) {
          common_vendor.index.__f__("error", "at store/modules/venue.js:380", "生成时间段失败:", generateError);
        }
      }
      const now = /* @__PURE__ */ new Date();
      const currentDate = now.toISOString().split("T")[0];
      const currentTime = now.getHours() * 60 + now.getMinutes();
      timeSlots = timeSlots.map((slot) => {
        let processedSlot = slot;
        if (slot.available !== void 0 && slot.status === void 0) {
          processedSlot = {
            ...slot,
            status: slot.available ? "AVAILABLE" : "OCCUPIED"
          };
        }
        if (date === currentDate && processedSlot.status === "AVAILABLE") {
          const slotStartTime = processedSlot.startTime.split(":").map(Number);
          const slotStartMinutes = slotStartTime[0] * 60 + slotStartTime[1];
          if (slotStartMinutes <= currentTime) {
            processedSlot = {
              ...processedSlot,
              status: "EXPIRED",
              originalStatus: slot.status || (slot.available ? "AVAILABLE" : "OCCUPIED")
            };
          }
        }
        return processedSlot;
      });
      common_vendor.index.__f__("log", "at store/modules/venue.js:418", "转换后的时间段数据:", timeSlots);
      commit("SET_TIME_SLOTS", timeSlots);
      common_vendor.index.__f__("log", "at store/modules/venue.js:421", "时间段已设置:", timeSlots);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/venue.js:424", "获取时间段失败:", error);
      if (error.message.includes("Network Error") || error.message.includes("timeout") || error.message.includes("获取时间段超时") || error.statusCode === void 0) {
        common_vendor.index.__f__("log", "at store/modules/venue.js:428", "使用模拟时间段数据");
        const mockTimeSlots = [
          { id: 1, startTime: "09:00", endTime: "10:00", price: 120, status: "AVAILABLE" },
          { id: 2, startTime: "10:00", endTime: "11:00", price: 120, status: "OCCUPIED" },
          { id: 3, startTime: "11:00", endTime: "12:00", price: 120, status: "AVAILABLE" },
          { id: 4, startTime: "14:00", endTime: "15:00", price: 120, status: "AVAILABLE" },
          { id: 5, startTime: "15:00", endTime: "16:00", price: 120, status: "AVAILABLE" },
          { id: 6, startTime: "16:00", endTime: "17:00", price: 120, status: "OCCUPIED" },
          { id: 7, startTime: "17:00", endTime: "18:00", price: 120, status: "MAINTENANCE" },
          { id: 8, startTime: "19:00", endTime: "20:00", price: 150, status: "AVAILABLE" },
          { id: 9, startTime: "20:00", endTime: "21:00", price: 150, status: "AVAILABLE" }
        ];
        commit("SET_TIME_SLOTS", mockTimeSlots);
        common_vendor.index.__f__("log", "at store/modules/venue.js:441", "已设置模拟时间段数据:", mockTimeSlots);
        return { data: mockTimeSlots };
      }
      utils_ui.showError(error.message || "获取时间段失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取场馆类型
  async getVenueTypes({ commit }) {
    try {
      common_vendor.index.__f__("log", "at store/modules/venue.js:455", "开始获取场馆类型...");
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("请求超时")), 8e3);
      });
      const apiPromise = api_venue.getVenueTypes();
      const response = await Promise.race([apiPromise, timeoutPromise]);
      common_vendor.index.__f__("log", "at store/modules/venue.js:465", "场馆类型API响应:", response);
      let types = [];
      if (response && response.data && Array.isArray(response.data)) {
        types = response.data;
      } else if (response && Array.isArray(response)) {
        types = response;
      } else {
        common_vendor.index.__f__("warn", "at store/modules/venue.js:473", "场馆类型API响应格式异常:", response);
      }
      commit("SET_VENUE_TYPES", types);
      common_vendor.index.__f__("log", "at store/modules/venue.js:477", "场馆类型已设置:", types);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/venue.js:480", "获取场馆类型失败:", error);
      if (error.message.includes("Network Error") || error.message.includes("timeout") || error.message.includes("请求超时") || error.statusCode === void 0) {
        common_vendor.index.__f__("log", "at store/modules/venue.js:484", "使用模拟场馆类型数据");
        const mockTypes = [
          { id: 1, name: "篮球场" },
          { id: 2, name: "羽毛球" },
          { id: 3, name: "网球" },
          { id: 4, name: "乒乓球" },
          { id: 5, name: "足球场" }
        ];
        commit("SET_VENUE_TYPES", mockTypes);
        common_vendor.index.__f__("log", "at store/modules/venue.js:493", "已设置模拟场馆类型数据:", mockTypes);
        return { data: mockTypes };
      }
      utils_ui.showError(error.message || "获取场馆类型失败");
      throw error;
    }
  },
  // 搜索场馆
  async searchVenues({ commit }, params) {
    try {
      common_vendor.index.__f__("log", "at store/modules/venue.js:505", "开始搜索场馆，参数:", params);
      commit("SET_LOADING", true);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("搜索超时")), 8e3);
      });
      const apiPromise = api_venue.searchVenues(params);
      const response = await Promise.race([apiPromise, timeoutPromise]);
      common_vendor.index.__f__("log", "at store/modules/venue.js:516", "搜索API响应:", response);
      let results = [];
      if (response && response.data && Array.isArray(response.data)) {
        results = response.data;
      } else if (response && Array.isArray(response)) {
        results = response;
      } else {
        common_vendor.index.__f__("warn", "at store/modules/venue.js:524", "搜索API响应格式异常:", response);
      }
      commit("SET_SEARCH_RESULTS", results);
      common_vendor.index.__f__("log", "at store/modules/venue.js:528", "搜索结果已设置:", results);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/venue.js:531", "搜索场馆失败:", error);
      if (error.message.includes("Network Error") || error.message.includes("timeout") || error.message.includes("搜索超时") || error.statusCode === void 0) {
        common_vendor.index.__f__("log", "at store/modules/venue.js:535", "搜索超时或网络错误，返回空结果");
        commit("SET_SEARCH_RESULTS", []);
        return { data: [] };
      }
      utils_ui.showError(error.message || "搜索失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 更新时间段状态
  updateTimeSlotStatus({ commit }, { slotId, status }) {
    commit("UPDATE_TIME_SLOT_STATUS", { slotId, status });
  },
  // getTimeSlots 别名，指向 getVenueTimeSlots
  getTimeSlots({ dispatch }, params) {
    return dispatch("getVenueTimeSlots", params);
  }
};
const getters = {
  venueList: (state2) => Array.isArray(state2.venueList) ? state2.venueList : [],
  popularVenues: (state2) => Array.isArray(state2.popularVenues) ? state2.popularVenues : [],
  venueDetail: (state2) => state2.venueDetail,
  venueTypes: (state2) => Array.isArray(state2.venueTypes) ? state2.venueTypes : [],
  timeSlots: (state2) => Array.isArray(state2.timeSlots) ? state2.timeSlots : [],
  searchResults: (state2) => Array.isArray(state2.searchResults) ? state2.searchResults : [],
  loading: (state2) => state2.loading,
  pagination: (state2) => state2.pagination,
  // 根据状态筛选时间段
  availableTimeSlots: (state2) => {
    const slots = Array.isArray(state2.timeSlots) ? state2.timeSlots : [];
    return slots.filter((slot) => slot.status === "AVAILABLE");
  },
  occupiedTimeSlots: (state2) => {
    const slots = Array.isArray(state2.timeSlots) ? state2.timeSlots : [];
    return slots.filter((slot) => slot.status === "OCCUPIED");
  },
  maintenanceTimeSlots: (state2) => {
    const slots = Array.isArray(state2.timeSlots) ? state2.timeSlots : [];
    return slots.filter((slot) => slot.status === "MAINTENANCE");
  }
};
const venue = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
exports.venue = venue;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/venue.js.map
