"use strict";
const common_vendor = require("../../common/vendor.js");
const api_sharing = require("../../api/sharing.js");
const utils_ui = require("../../utils/ui.js");
const state = {
  sharingOrders: [],
  mySharingOrders: [],
  receivedRequests: [],
  sentRequests: [],
  sharingOrderDetail: null,
  loading: false,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  }
};
const mutations = {
  SET_SHARING_ORDERS(state2, orders) {
    state2.sharingOrders = orders;
  },
  SET_MY_SHARING_ORDERS(state2, orders) {
    state2.mySharingOrders = orders;
  },
  SET_RECEIVED_REQUESTS(state2, requests) {
    state2.receivedRequests = requests;
  },
  SET_SENT_REQUESTS(state2, requests) {
    state2.sentRequests = requests;
  },
  SET_SHARING_ORDER_DETAIL(state2, order) {
    state2.sharingOrderDetail = order;
  },
  SET_LOADING(state2, loading) {
    state2.loading = loading;
  },
  SET_PAGINATION(state2, pagination) {
    state2.pagination = { ...state2.pagination, ...pagination };
  },
  UPDATE_SHARING_ORDER_STATUS(state2, { orderId, status }) {
    const order = state2.sharingOrders.find((o) => o.id === orderId);
    if (order) {
      order.status = status;
    }
    if (state2.sharingOrderDetail && state2.sharingOrderDetail.id === orderId) {
      state2.sharingOrderDetail.status = status;
    }
  },
  UPDATE_SHARING_ORDER_PARTICIPANTS(state2, { orderId, currentParticipants }) {
    const order = state2.sharingOrders.find((o) => o.id === orderId);
    if (order) {
      order.currentParticipants = currentParticipants;
    }
    if (state2.sharingOrderDetail && state2.sharingOrderDetail.id === orderId) {
      state2.sharingOrderDetail.currentParticipants = currentParticipants;
    }
  },
  // 批量更新mutation，用于优化同步性能
  BATCH_UPDATE(state2, updates) {
    if (updates.sharingOrders !== void 0) {
      state2.sharingOrders = updates.sharingOrders;
    }
    if (updates.mySharingOrders !== void 0) {
      state2.mySharingOrders = updates.mySharingOrders;
    }
    if (updates.receivedRequests !== void 0) {
      state2.receivedRequests = updates.receivedRequests;
    }
    if (updates.sentRequests !== void 0) {
      state2.sentRequests = updates.sentRequests;
    }
    if (updates.sharingOrderDetail !== void 0) {
      state2.sharingOrderDetail = updates.sharingOrderDetail;
    }
    if (updates.loading !== void 0) {
      state2.loading = updates.loading;
    }
    if (updates.pagination !== void 0) {
      state2.pagination = { ...state2.pagination, ...updates.pagination };
    }
  }
};
const actions = {
  // 获取可加入的拼场订单
  async getSharingOrders({ commit }, params = {}) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getJoinableSharingOrders(params);
      common_vendor.index.__f__("log", "at store/modules/sharing.js:100", "Store: API返回的原始数据:", response);
      const orders = response.list || response.data || [];
      common_vendor.index.__f__("log", "at store/modules/sharing.js:103", "Store: 提取的订单数据:", orders);
      commit("SET_SHARING_ORDERS", orders);
      if (response.pagination) {
        commit("SET_PAGINATION", response.pagination);
      }
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/sharing.js:111", "获取拼场订单失败:", error);
      utils_ui.showError(error.message || "获取拼场订单失败");
      commit("SET_SHARING_ORDERS", []);
      return { data: [] };
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取可加入的拼场订单（别名方法）
  async getJoinableSharingOrders({ commit }, params = {}) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getJoinableSharingOrders(params);
      common_vendor.index.__f__("log", "at store/modules/sharing.js:126", "Store: API返回的原始数据:", response);
      const orders = response.list || response.data || [];
      common_vendor.index.__f__("log", "at store/modules/sharing.js:129", "Store: 提取的订单数据:", orders);
      commit("SET_SHARING_ORDERS", orders);
      if (response.pagination) {
        commit("SET_PAGINATION", response.pagination);
      }
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/sharing.js:137", "获取拼场订单失败:", error);
      utils_ui.showError(error.message || "获取拼场订单失败");
      commit("SET_SHARING_ORDERS", []);
      return { data: [] };
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取所有拼场订单（包括所有状态）
  async getAllSharingOrders({ commit }, params = {}) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getAllSharingOrders(params);
      common_vendor.index.__f__("log", "at store/modules/sharing.js:152", "Store: 获取所有拼场订单API返回的原始数据:", response);
      const orders = response.list || response.data || [];
      common_vendor.index.__f__("log", "at store/modules/sharing.js:155", "Store: 提取的所有拼场订单数据:", orders);
      commit("SET_SHARING_ORDERS", orders);
      if (response.pagination) {
        commit("SET_PAGINATION", response.pagination);
      }
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/sharing.js:163", "获取所有拼场订单失败:", error);
      utils_ui.showError(error.message || "获取所有拼场订单失败");
      commit("SET_SHARING_ORDERS", []);
      return { data: [] };
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 按场馆ID获取可加入的拼场订单
  async getJoinableSharingOrdersByVenueId({ commit }, { venueId, params = {} }) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getJoinableSharingOrdersByVenueId(venueId, params);
      const orders = response.list || response.data || [];
      commit("SET_SHARING_ORDERS", orders);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "获取场馆拼场订单失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 按日期获取可加入的拼场订单
  async getJoinableSharingOrdersByDate({ commit }, { date, params = {} }) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getJoinableSharingOrdersByDate(date, params);
      const orders = response.list || response.data || [];
      commit("SET_SHARING_ORDERS", orders);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "获取日期拼场订单失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 按场馆ID和日期获取可加入的拼场订单
  async getJoinableSharingOrdersByVenueIdAndDate({ commit }, { venueId, date, params = {} }) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getJoinableSharingOrdersByVenueIdAndDate(venueId, date, params);
      const orders = response.list || response.data || [];
      commit("SET_SHARING_ORDERS", orders);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "获取拼场订单失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取我创建的拼场订单
  async getMyCreatedSharingOrders({ commit }, params = {}) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getMyCreatedSharingOrders(params);
      const orders = response.list || response.data || [];
      commit("SET_MY_SHARING_ORDERS", orders);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "获取我的拼场订单失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取拼场订单详情
  async getSharingOrderDetail({ commit }, orderId) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getSharingOrderById(orderId);
      const orderDetail = response.data || response;
      commit("SET_SHARING_ORDER_DETAIL", orderDetail);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "获取拼场订单详情失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 加入拼场订单
  async joinSharingOrder({ commit }, payload) {
    try {
      const orderId = typeof payload === "object" ? payload.orderId : payload;
      const applicationData = typeof payload === "object" ? payload.data : {};
      const response = await api_sharing.joinSharingOrder(orderId, applicationData);
      utils_ui.showSuccess("申请提交成功");
      if (response.data && response.data.currentParticipants !== void 0) {
        commit("UPDATE_SHARING_ORDER_PARTICIPANTS", {
          orderId,
          currentParticipants: response.data.currentParticipants
        });
      }
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "申请提交失败");
      throw error;
    }
  },
  // 取消加入拼场订单
  async cancelJoinSharingOrder({ commit }, orderId) {
    try {
      const response = await api_sharing.cancelJoinSharingOrder(orderId);
      utils_ui.showSuccess("已取消加入拼场");
      if (response.data && response.data.currentParticipants !== void 0) {
        commit("UPDATE_SHARING_ORDER_PARTICIPANTS", {
          orderId,
          currentParticipants: response.data.currentParticipants
        });
      }
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "取消加入拼场失败");
      throw error;
    }
  },
  // 确认拼场订单
  async confirmSharingOrder({ commit }, orderId) {
    try {
      const response = await api_sharing.confirmSharingOrder(orderId);
      utils_ui.showSuccess("拼场订单已确认");
      commit("UPDATE_SHARING_ORDER_STATUS", { orderId, status: "CONFIRMED" });
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "确认拼场订单失败");
      throw error;
    }
  },
  // 取消拼场订单
  async cancelSharingOrder({ commit }, orderId) {
    try {
      const response = await api_sharing.cancelSharingOrder(orderId);
      utils_ui.showSuccess("拼场订单已取消");
      commit("UPDATE_SHARING_ORDER_STATUS", { orderId, status: "CANCELLED" });
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "取消拼场订单失败");
      throw error;
    }
  },
  // 获取我的拼场申请
  async getMySharingRequests({ commit }, params = {}) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getMySharedRequests(params);
      commit("SET_SENT_REQUESTS", response.data || []);
      return response.data || [];
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/sharing.js:328", "获取我的拼场申请失败:", error);
      utils_ui.showError(error.message || "获取我的拼场申请失败");
      commit("SET_SENT_REQUESTS", []);
      return [];
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取收到的拼场申请
  async getReceivedSharingRequests({ commit }, params = {}) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getReceivedSharedRequests(params);
      commit("SET_RECEIVED_REQUESTS", response.data || []);
      return response.data || [];
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/sharing.js:345", "获取收到的拼场申请失败:", error);
      utils_ui.showError(error.message || "获取收到的拼场申请失败");
      commit("SET_RECEIVED_REQUESTS", []);
      return [];
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 申请拼场
  async applySharingOrder({ commit }, { orderId, data }) {
    try {
      const response = await api_sharing.applySharedBooking(orderId, data);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "申请拼场失败");
      throw error;
    }
  },
  // 申请加入拼场订单（需要支付）
  async applyJoinSharingOrder({ commit }, orderId) {
    try {
      common_vendor.index.__f__("log", "at store/modules/sharing.js:369", "[SharingStore] 开始申请加入拼场订单:", orderId);
      commit("SET_LOADING", true);
      const response = await api_sharing.applyJoinSharingOrder(orderId);
      if (response && response.success) {
        common_vendor.index.__f__("log", "at store/modules/sharing.js:375", "[SharingStore] 申请加入拼场订单成功");
        return response;
      } else {
        throw new Error(response.message || "申请失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/sharing.js:381", "[SharingStore] 申请加入拼场订单失败:", error);
      utils_ui.showError(error.message || "申请加入拼场订单失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 取消拼场申请
  async cancelSharingRequest({ commit }, requestId) {
    try {
      const response = await api_sharing.cancelSharingRequest(requestId);
      utils_ui.showSuccess("申请已取消");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "取消申请失败");
      throw error;
    }
  },
  // 处理拼场申请
  async processSharingRequest({ commit }, { requestId, action, reason = "" }) {
    try {
      const data = {
        action,
        // 直接传递action参数：'approve' 或 'reject'
        responseMessage: reason || ""
      };
      const response = await api_sharing.handleSharedRequest(requestId, data);
      utils_ui.showSuccess(action === "approve" ? "已同意拼场申请" : "已拒绝拼场申请");
      return response;
    } catch (error) {
      if (error.needPayment) {
        const enhancedError = new Error(error.message || "处理拼场申请失败");
        enhancedError.needPayment = error.needPayment;
        enhancedError.orderId = error.orderId;
        enhancedError.orderStatus = error.orderStatus;
        throw enhancedError;
      } else {
        utils_ui.showError(error.message || "处理拼场申请失败");
        throw error;
      }
    }
  },
  // 移除拼场参与者
  async removeSharingParticipant({ commit }, { orderId, participantId }) {
    try {
      const response = await api_sharing.removeSharingParticipant(orderId, participantId);
      utils_ui.showSuccess("已移除参与者");
      if (response.data && response.data.currentParticipants !== void 0) {
        commit("UPDATE_SHARING_ORDER_PARTICIPANTS", {
          orderId,
          currentParticipants: response.data.currentParticipants
        });
      }
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "移除参与者失败");
      throw error;
    }
  },
  // 更新拼场设置
  async updateSharingSettings({ commit }, { sharingId, settings }) {
    try {
      const response = await api_sharing.updateSharingSettings(sharingId, settings);
      utils_ui.showSuccess("设置已更新");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "更新设置失败");
      throw error;
    }
  }
};
const getters = {
  sharingOrders: (state2) => state2.sharingOrders,
  mySharingOrders: (state2) => state2.mySharingOrders,
  receivedRequests: (state2) => state2.receivedRequests,
  sentRequests: (state2) => state2.sentRequests,
  sharingOrderDetail: (state2) => state2.sharingOrderDetail,
  loading: (state2) => state2.loading,
  pagination: (state2) => state2.pagination,
  // 可加入的拼场订单
  availableSharingOrders: (state2) => state2.sharingOrders.filter(
    (order) => order.status === "OPEN" && order.currentParticipants < order.maxParticipants
  ),
  // 进行中的拼场订单
  activeSharingOrders: (state2) => state2.mySharingOrders.filter(
    (order) => ["OPEN", "CONFIRMED"].includes(order.status)
  ),
  // 已完成的拼场订单
  completedSharingOrders: (state2) => state2.mySharingOrders.filter(
    (order) => order.status === "COMPLETED"
  ),
  // 已取消的拼场订单
  cancelledSharingOrders: (state2) => state2.mySharingOrders.filter(
    (order) => order.status === "CANCELLED"
  ),
  // 待处理的拼场申请
  pendingRequests: (state2) => state2.receivedRequests.filter(
    (request) => request.status === "PENDING"
  )
};
const sharing = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
exports.sharing = sharing;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/sharing.js.map
