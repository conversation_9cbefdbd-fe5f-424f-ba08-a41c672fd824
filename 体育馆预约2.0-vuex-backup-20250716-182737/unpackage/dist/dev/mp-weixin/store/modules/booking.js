"use strict";
const common_vendor = require("../../common/vendor.js");
const api_booking = require("../../api/booking.js");
const api_user = require("../../api/user.js");
const api_sharing = require("../../api/sharing.js");
const utils_ui = require("../../utils/ui.js");
const state = {
  bookingList: [],
  sharingOrders: [],
  userSharingOrders: [],
  joinedSharingOrders: [],
  bookingDetail: null,
  sharingOrderDetail: null,
  sharingDetail: null,
  loading: false,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  }
};
const mutations = {
  SET_BOOKING_LIST(state2, { list, pagination }) {
    state2.bookingList = list;
    if (pagination) {
      state2.pagination = { ...state2.pagination, ...pagination };
    }
  },
  APPEND_BOOKING_LIST(state2, list) {
    state2.bookingList = [...state2.bookingList, ...list];
  },
  SET_SHARING_ORDERS(state2, orders) {
    state2.sharingOrders = orders;
  },
  SET_USER_SHARING_ORDERS(state2, orders) {
    state2.userSharingOrders = orders;
  },
  SET_JOINED_SHARING_ORDERS(state2, orders) {
    state2.joinedSharingOrders = orders;
  },
  SET_BOOKING_DETAIL(state2, booking2) {
    if (booking2 && typeof booking2 === "object") {
      state2.bookingDetail = booking2;
    } else {
      common_vendor.index.__f__("warn", "at store/modules/booking.js:51", "⚠️ 收到无效的booking数据:", booking2);
      state2.bookingDetail = {
        status: null,
        orderNo: null,
        venueName: null,
        bookingType: null,
        venueLocation: null,
        bookingDate: null,
        startTime: null,
        endTime: null,
        totalPrice: null,
        createdAt: null,
        venuePhone: null,
        sharingOrder: null
      };
    }
  },
  SET_SHARING_ORDER_DETAIL(state2, order) {
    state2.sharingOrderDetail = order;
  },
  SET_SHARING_DETAIL(state2, detail) {
    state2.sharingDetail = detail;
  },
  SET_LOADING(state2, loading) {
    state2.loading = loading;
  },
  SET_PAGINATION(state2, pagination) {
    state2.pagination = pagination;
  },
  UPDATE_BOOKING_STATUS(state2, { bookingId, status }) {
    const booking2 = state2.bookingList.find((b) => b.id === bookingId);
    if (booking2) {
      booking2.status = status;
    }
    if (state2.bookingDetail && state2.bookingDetail.id === bookingId) {
      state2.bookingDetail.status = status;
    }
  },
  UPDATE_SHARING_ORDER_PARTICIPANTS(state2, { orderId, currentParticipants }) {
    const order = state2.sharingOrders.find((o) => o.id === orderId);
    if (order) {
      order.currentParticipants = currentParticipants;
    }
    if (state2.sharingOrderDetail && state2.sharingOrderDetail.id === orderId) {
      state2.sharingOrderDetail.currentParticipants = currentParticipants;
    }
  },
  // 批量更新mutation，用于优化同步性能
  BATCH_UPDATE(state2, updates) {
    if (updates.bookingList !== void 0) {
      state2.bookingList = updates.bookingList;
    }
    if (updates.bookingDetail !== void 0) {
      state2.bookingDetail = updates.bookingDetail;
    }
    if (updates.sharingOrders !== void 0) {
      state2.sharingOrders = updates.sharingOrders;
    }
    if (updates.userSharingOrders !== void 0) {
      state2.userSharingOrders = updates.userSharingOrders;
    }
    if (updates.joinedSharingOrders !== void 0) {
      state2.joinedSharingOrders = updates.joinedSharingOrders;
    }
    if (updates.sharingDetail !== void 0) {
      state2.sharingDetail = updates.sharingDetail;
    }
    if (updates.loading !== void 0) {
      state2.loading = updates.loading;
    }
    if (updates.pagination !== void 0) {
      state2.pagination = { ...state2.pagination, ...updates.pagination };
    }
  }
};
const actions = {
  // 创建预约
  async createBooking({ commit }, bookingData) {
    try {
      commit("SET_LOADING", true);
      const response = await api_booking.createBooking(bookingData);
      utils_ui.showSuccess("预约成功");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "预约失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 创建拼场预约
  async createSharedBooking({ commit }, bookingData) {
    try {
      commit("SET_LOADING", true);
      const response = await api_booking.createSharedBooking(bookingData);
      utils_ui.showSuccess("拼场预约成功");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "拼场预约失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取用户预约列表
  async getUserBookings({ commit }, params = {}) {
    try {
      commit("SET_LOADING", true);
      const response = await api_user.getUserBookings(params);
      common_vendor.index.__f__("log", "at store/modules/booking.js:172", "API响应原始数据:", response);
      common_vendor.index.__f__("log", "at store/modules/booking.js:173", "response.data:", response.data);
      common_vendor.index.__f__("log", "at store/modules/booking.js:174", "response.data类型:", typeof response.data);
      const { data, total, page, pageSize, totalPages } = response;
      common_vendor.index.__f__("log", "at store/modules/booking.js:178", "解构后的数据:");
      common_vendor.index.__f__("log", "at store/modules/booking.js:179", "data:", data);
      common_vendor.index.__f__("log", "at store/modules/booking.js:180", "data类型:", typeof data);
      common_vendor.index.__f__("log", "at store/modules/booking.js:181", "data是否为数组:", Array.isArray(data));
      common_vendor.index.__f__("log", "at store/modules/booking.js:182", "total:", total);
      common_vendor.index.__f__("log", "at store/modules/booking.js:183", "page:", page);
      common_vendor.index.__f__("log", "at store/modules/booking.js:184", "pageSize:", pageSize);
      common_vendor.index.__f__("log", "at store/modules/booking.js:185", "totalPages:", totalPages);
      const pagination = {
        current: page,
        pageSize,
        total,
        totalPages,
        currentPage: page
      };
      if (params.page === 1 || params.refresh) {
        common_vendor.index.__f__("log", "at store/modules/booking.js:196", "设置新的预约列表，数据长度:", (data || []).length);
        commit("SET_BOOKING_LIST", { list: data || [], pagination });
      } else {
        common_vendor.index.__f__("log", "at store/modules/booking.js:199", "追加预约列表，新增数据长度:", (data || []).length);
        commit("APPEND_BOOKING_LIST", data || []);
        commit("SET_PAGINATION", pagination);
      }
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/booking.js:206", "获取用户预约列表失败:", error);
      commit("SET_BOOKING_LIST", { list: [], pagination: { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 } });
      utils_ui.showError(error.message || "获取预约列表失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取预约详情
  async getBookingDetail({ commit }, bookingId) {
    try {
      commit("SET_LOADING", true);
      common_vendor.index.__f__("log", "at store/modules/booking.js:220", "🌐 发起API请求获取订单详情, ID:", bookingId);
      common_vendor.index.__f__("log", "at store/modules/booking.js:221", "🌐 ID类型:", typeof bookingId);
      if (!bookingId) {
        throw new Error("订单ID不能为空");
      }
      const response = await api_booking.getBookingDetail(bookingId);
      common_vendor.index.__f__("log", "at store/modules/booking.js:228", "📡 完整API响应:", response);
      common_vendor.index.__f__("log", "at store/modules/booking.js:229", "📡 响应类型:", typeof response);
      common_vendor.index.__f__("log", "at store/modules/booking.js:230", "📡 响应是否为空:", !response);
      let bookingData = null;
      if (response && typeof response === "object") {
        if (response.id || response.orderNo) {
          bookingData = response;
          common_vendor.index.__f__("log", "at store/modules/booking.js:238", "📡 使用response作为数据");
        } else if (response.data) {
          bookingData = response.data;
          common_vendor.index.__f__("log", "at store/modules/booking.js:243", "📡 使用response.data作为数据");
        } else if (response.result) {
          bookingData = response.result;
          common_vendor.index.__f__("log", "at store/modules/booking.js:248", "📡 使用response.result作为数据");
        } else {
          common_vendor.index.__f__("warn", "at store/modules/booking.js:251", "📡 响应数据结构未知:", Object.keys(response));
          bookingData = response;
        }
      } else {
        common_vendor.index.__f__("error", "at store/modules/booking.js:256", "📡 响应数据无效:", response);
        throw new Error("服务器返回的数据格式不正确");
      }
      common_vendor.index.__f__("log", "at store/modules/booking.js:260", "📡 处理后的订单数据:", bookingData);
      common_vendor.index.__f__("log", "at store/modules/booking.js:261", "📡 数据类型:", typeof bookingData);
      common_vendor.index.__f__("log", "at store/modules/booking.js:262", "📡 数据键:", bookingData ? Object.keys(bookingData) : "null");
      common_vendor.index.__f__("log", "at store/modules/booking.js:263", "⏰ API返回的开始时间:", bookingData == null ? void 0 : bookingData.startTime);
      common_vendor.index.__f__("log", "at store/modules/booking.js:264", "⏰ API返回的结束时间:", bookingData == null ? void 0 : bookingData.endTime);
      common_vendor.index.__f__("log", "at store/modules/booking.js:265", "💰 API返回的总价格:", bookingData == null ? void 0 : bookingData.totalPrice);
      common_vendor.index.__f__("log", "at store/modules/booking.js:266", "🏷️ API返回的订单号(orderNo):", bookingData == null ? void 0 : bookingData.orderNo);
      common_vendor.index.__f__("log", "at store/modules/booking.js:267", "🏷️ API返回的订单号(orderNumber):", bookingData == null ? void 0 : bookingData.orderNumber);
      common_vendor.index.__f__("log", "at store/modules/booking.js:268", "🆔 API返回的ID:", bookingData == null ? void 0 : bookingData.id);
      if (!bookingData) {
        throw new Error("未能获取到有效的订单数据");
      }
      if (bookingData.orderNumber && !bookingData.orderNo) {
        bookingData.orderNo = bookingData.orderNumber;
        common_vendor.index.__f__("log", "at store/modules/booking.js:277", "🔄 字段映射: orderNumber -> orderNo:", bookingData.orderNo);
      }
      commit("SET_BOOKING_DETAIL", bookingData);
      common_vendor.index.__f__("log", "at store/modules/booking.js:281", "✅ 数据已存储到store");
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/booking.js:284", "❌ API请求失败:", error);
      common_vendor.index.__f__("error", "at store/modules/booking.js:285", "❌ 错误类型:", error.constructor.name);
      common_vendor.index.__f__("error", "at store/modules/booking.js:286", "❌ 错误消息:", error.message);
      common_vendor.index.__f__("error", "at store/modules/booking.js:287", "❌ 错误堆栈:", error.stack);
      commit("SET_BOOKING_DETAIL", null);
      utils_ui.showError(error.message || "获取预约详情失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 取消预约
  async cancelBooking({ commit, dispatch }, bookingId) {
    try {
      const response = await api_booking.cancelBooking(bookingId);
      commit("UPDATE_BOOKING_STATUS", { bookingId, status: "CANCELLED" });
      setTimeout(() => {
        dispatch("getUserBookings", { page: 1, pageSize: 10, refresh: true });
      }, 1e3);
      utils_ui.showSuccess("预约已取消");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "取消预约失败");
      throw error;
    }
  },
  // 申请拼场
  async createSharingOrder({ commit }, { orderId, data }) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.applySharedBooking(orderId, data);
      utils_ui.showSuccess("拼场申请已发送");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "申请拼场失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 获取可拼场的订单列表
  async getSharingOrders({ commit, state: state2 }, params = {}) {
    try {
      commit("SET_LOADING", true);
      common_vendor.index.__f__("log", "at store/modules/booking.js:338", "开始获取拼场订单，参数:", params);
      const response = await api_sharing.getJoinableSharingOrders(params);
      common_vendor.index.__f__("log", "at store/modules/booking.js:340", "拼场订单API响应:", response);
      if (response && (response.list || response.data)) {
        const responseData = response.list ? response : response.data;
        const { list, pagination } = responseData;
        common_vendor.index.__f__("log", "at store/modules/booking.js:346", "拼场订单列表:", list, "分页信息:", pagination);
        if (list && Array.isArray(list)) {
          if (params.page === 1 || params.refresh) {
            commit("SET_SHARING_ORDERS", list);
          } else {
            const currentList = state2.sharingOrders || [];
            commit("SET_SHARING_ORDERS", [...currentList, ...list]);
          }
          if (pagination) {
            commit("SET_PAGINATION", pagination);
          }
          common_vendor.index.__f__("log", "at store/modules/booking.js:361", "成功设置拼场订单数据，数量:", list.length);
        } else {
          common_vendor.index.__f__("warn", "at store/modules/booking.js:363", "拼场订单列表为空或格式不正确");
          commit("SET_SHARING_ORDERS", []);
        }
      } else {
        common_vendor.index.__f__("warn", "at store/modules/booking.js:367", "拼场订单API响应数据为空");
        commit("SET_SHARING_ORDERS", []);
      }
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/booking.js:373", "获取拼场订单失败:", error);
      utils_ui.showError(error.message || "获取拼场订单失败");
      commit("SET_SHARING_ORDERS", []);
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 创建拼场订单
  async createSharingOrder({ commit }, sharingData) {
    try {
      const response = await api_sharing.createSharingOrder(sharingData);
      utils_ui.showSuccess("拼场订单创建成功");
      return response.data;
    } catch (error) {
      utils_ui.showError(error.message || "创建拼场订单失败");
      throw error;
    }
  },
  // 获取拼场订单详情
  async getSharingOrderDetail({ commit }, orderId) {
    try {
      const response = await api_sharing.getSharingOrderById(orderId);
      return response.data;
    } catch (error) {
      utils_ui.showError(error.message || "获取拼场订单详情失败");
      throw error;
    }
  },
  // 加入拼场订单
  async joinSharingOrder({ commit }, orderId) {
    try {
      const response = await api_sharing.joinSharingOrder(orderId);
      utils_ui.showSuccess("加入拼场成功");
      return response.data;
    } catch (error) {
      utils_ui.showError(error.message || "加入拼场失败");
      throw error;
    }
  },
  // 获取我创建的拼场订单
  async getMyCreatedSharingOrders({ commit }) {
    try {
      const response = await api_sharing.getMyCreatedSharingOrders();
      return response.data;
    } catch (error) {
      utils_ui.showError(error.message || "获取我创建的拼场订单失败");
      throw error;
    }
  },
  // 创建预约
  async createBooking({ commit }, bookingData) {
    try {
      commit("SET_LOADING", true);
      common_vendor.index.__f__("log", "at store/modules/booking.js:432", "发起预约创建请求，数据:", bookingData);
      const response = await api_booking.createBooking(bookingData);
      common_vendor.index.__f__("log", "at store/modules/booking.js:434", "预约创建API响应:", response);
      return response.data || response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/booking.js:439", "创建预约失败:", error);
      utils_ui.showError(error.message || "创建预约失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 处理拼场申请
  async handleSharingRequest({ commit }, { requestId, data }) {
    try {
      const response = await api_sharing.handleSharedRequest(requestId, data);
      utils_ui.showSuccess(data.status === "APPROVED" ? "已同意拼场申请" : "已拒绝拼场申请");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "处理拼场申请失败");
      throw error;
    }
  },
  // 获取我发出的拼场申请
  async getUserSharingOrders({ commit }, params = {}) {
    try {
      const response = await api_sharing.getMySharedRequests(params);
      commit("SET_USER_SHARING_ORDERS", response.data);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "获取拼场申请失败");
      throw error;
    }
  },
  // 获取我收到的拼场申请
  async getUserJoinedSharingOrders({ commit }, params = {}) {
    try {
      const response = await api_sharing.getReceivedSharedRequests(params);
      commit("SET_JOINED_SHARING_ORDERS", response.data);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "获取拼场申请失败");
      throw error;
    }
  },
  // 获取拼场详情
  async getSharingDetail({ commit }, sharingId) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.getSharingOrderById(sharingId);
      commit("SET_SHARING_DETAIL", response.data);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "获取拼场详情失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 移除拼场参与者
  async removeSharingParticipant({ commit, dispatch, state: state2 }, { sharingId, participantId }) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.removeSharingParticipant(sharingId, participantId);
      await dispatch("getSharingDetail", sharingId);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "移除参与者失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  },
  // 更新拼场设置
  async updateSharingSettings({ commit, dispatch, state: state2 }, { sharingId, settings }) {
    try {
      commit("SET_LOADING", true);
      const response = await api_sharing.updateSharingSettings(sharingId, settings);
      await dispatch("getSharingDetail", sharingId);
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "更新设置失败");
      throw error;
    } finally {
      commit("SET_LOADING", false);
    }
  }
};
const getters = {
  bookingList: (state2) => Array.isArray(state2.bookingList) ? state2.bookingList : [],
  sharingOrders: (state2) => Array.isArray(state2.sharingOrders) ? state2.sharingOrders : [],
  userSharingOrders: (state2) => Array.isArray(state2.userSharingOrders) ? state2.userSharingOrders : [],
  joinedSharingOrders: (state2) => Array.isArray(state2.joinedSharingOrders) ? state2.joinedSharingOrders : [],
  bookingDetail: (state2) => state2.bookingDetail,
  sharingOrderDetail: (state2) => state2.sharingOrderDetail,
  sharingDetail: (state2) => state2.sharingDetail,
  loading: (state2) => state2.loading,
  pagination: (state2) => state2.pagination,
  // 根据状态筛选预约
  activeBookings: (state2) => (state2.bookingList || []).filter(
    (booking2) => booking2 && ["PENDING", "CONFIRMED"].includes(booking2.status)
  ),
  completedBookings: (state2) => (state2.bookingList || []).filter(
    (booking2) => booking2 && booking2.status === "COMPLETED"
  ),
  cancelledBookings: (state2) => (state2.bookingList || []).filter(
    (booking2) => booking2 && booking2.status === "CANCELLED"
  ),
  // 可加入的拼场订单
  availableSharingOrders: (state2) => (state2.sharingOrders || []).filter(
    (order) => order && order.status === "OPEN" && order.currentParticipants < order.maxParticipants
  )
};
const booking = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
exports.booking = booking;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/booking.js.map
