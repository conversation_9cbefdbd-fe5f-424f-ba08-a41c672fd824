"use strict";
const common_vendor = require("../../common/vendor.js");
const api_auth = require("../../api/auth.js");
const api_user = require("../../api/user.js");
const utils_auth = require("../../utils/auth.js");
const utils_ui = require("../../utils/ui.js");
const utils_routerGuardNew = require("../../utils/router-guard-new.js");
const state = {
  token: utils_auth.getToken(),
  userInfo: utils_auth.getUserInfo(),
  userStats: {
    totalBookings: 0,
    totalSharings: 0
  },
  isLoggedIn: !!(utils_auth.getToken() && utils_auth.getUserInfo()),
  loginChecking: false
  // 是否正在检查登录状态
};
const mutations = {
  SET_TOKEN(state2, token) {
    state2.token = token;
    utils_auth.setToken(token);
  },
  SET_USER_INFO(state2, userInfo) {
    state2.userInfo = userInfo;
    utils_auth.setUserInfo(userInfo);
  },
  SET_LOGIN_STATUS(state2, status) {
    common_vendor.index.__f__("log", "at store/modules/user.js:30", "[UserStore] 设置登录状态:", status);
    state2.isLoggedIn = status;
    utils_routerGuardNew.updateAuthCache(status);
  },
  SET_LOGIN_CHECKING(state2, checking) {
    state2.loginChecking = checking;
  },
  SET_USER_STATS(state2, stats) {
    state2.userStats = stats;
  },
  CLEAR_USER_DATA(state2) {
    common_vendor.index.__f__("log", "at store/modules/user.js:45", "[UserStore] 清除用户数据");
    state2.token = "";
    state2.userInfo = null;
    state2.userStats = {
      totalBookings: 0,
      totalSharings: 0
    };
    state2.isLoggedIn = false;
    state2.loginChecking = false;
    utils_auth.removeToken();
    utils_auth.removeUserInfo();
    utils_routerGuardNew.clearAuthCache();
  }
};
const actions = {
  // 用户登录
  async login({ commit }, loginData) {
    try {
      common_vendor.index.__f__("log", "at store/modules/user.js:65", "[UserStore] 开始登录");
      const response = await api_auth.login(loginData);
      common_vendor.index.__f__("log", "at store/modules/user.js:67", "[UserStore] 登录响应:", response);
      if (!response) {
        throw new Error("登录响应为空");
      }
      const responseData = response.data || response;
      const token = responseData.accessToken || responseData.token;
      if (!token) {
        common_vendor.index.__f__("error", "at store/modules/user.js:77", "[UserStore] 响应数据:", responseData);
        throw new Error("未获取到登录令牌");
      }
      const user2 = {
        id: responseData.id,
        username: responseData.username,
        email: responseData.email,
        phone: responseData.phone,
        nickname: responseData.nickname,
        avatar: responseData.avatar,
        roles: responseData.roles
      };
      commit("SET_TOKEN", token);
      commit("SET_USER_INFO", user2);
      commit("SET_LOGIN_STATUS", true);
      common_vendor.index.__f__("log", "at store/modules/user.js:96", "[UserStore] 登录成功，用户信息:", user2);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:99", "[UserStore] 登录错误:", error);
      commit("SET_LOGIN_STATUS", false);
      throw error;
    }
  },
  // 用户注册
  async register({ commit }, registerData) {
    try {
      const response = await api_auth.register(registerData);
      utils_ui.showSuccess("注册成功");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "注册失败");
      throw error;
    }
  },
  // 获取短信验证码
  async getSmsCode({ commit }, smsData) {
    try {
      const response = await api_auth.getSmsCode(smsData.phone);
      return response;
    } catch (error) {
      throw error;
    }
  },
  // 短信验证码登录
  async smsLogin({ commit }, loginData) {
    try {
      common_vendor.index.__f__("log", "at store/modules/user.js:130", "[UserStore] 开始短信登录");
      const response = await api_auth.smsLogin(loginData);
      common_vendor.index.__f__("log", "at store/modules/user.js:132", "[UserStore] 短信登录响应:", response);
      if (!response) {
        throw new Error("短信登录响应为空");
      }
      const responseData = response.data || response;
      const token = responseData.accessToken || responseData.token;
      if (!token) {
        common_vendor.index.__f__("error", "at store/modules/user.js:142", "[UserStore] 响应数据:", responseData);
        throw new Error("未获取到登录令牌");
      }
      const user2 = {
        id: responseData.id,
        username: responseData.username,
        email: responseData.email,
        phone: responseData.phone,
        nickname: responseData.nickname,
        avatar: responseData.avatar,
        roles: responseData.roles
      };
      commit("SET_TOKEN", token);
      commit("SET_USER_INFO", user2);
      commit("SET_LOGIN_STATUS", true);
      common_vendor.index.__f__("log", "at store/modules/user.js:161", "[UserStore] 短信登录成功，用户信息:", user2);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:164", "[UserStore] 短信登录错误:", error);
      commit("SET_LOGIN_STATUS", false);
      throw error;
    }
  },
  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      common_vendor.index.__f__("log", "at store/modules/user.js:173", "[UserStore] 开始获取用户信息");
      const response = await api_user.getUserInfo();
      common_vendor.index.__f__("log", "at store/modules/user.js:175", "[UserStore] 获取用户信息响应:", response);
      if (response && response.data) {
        common_vendor.index.__f__("log", "at store/modules/user.js:178", "[UserStore] 用户信息数据:", response.data);
        commit("SET_USER_INFO", response.data);
        commit("SET_LOGIN_STATUS", true);
        return response;
      } else {
        common_vendor.index.__f__("warn", "at store/modules/user.js:183", "[UserStore] 用户信息响应为空或无data字段");
        return null;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:187", "[UserStore] 获取用户信息失败:", error);
      if (error.code === "LOGIN_EXPIRED" || error.message.includes("未授权")) {
        commit("CLEAR_USER_DATA");
      }
      throw error;
    }
  },
  // 获取用户统计信息
  async getUserStats({ commit }) {
    try {
      const response = await api_user.getUserStats();
      commit("SET_USER_STATS", response.data);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:203", "[UserStore] 获取用户统计信息失败:", error);
      commit("SET_USER_STATS", {
        totalBookings: 0,
        totalSharings: 0
      });
      throw error;
    }
  },
  // 更新用户信息
  async updateUserInfo({ commit }, userData) {
    try {
      common_vendor.index.__f__("log", "at store/modules/user.js:215", "[UserStore] 发送到后端的用户数据:", JSON.stringify(userData, null, 2));
      const response = await api_user.updateUserInfo(userData);
      common_vendor.index.__f__("log", "at store/modules/user.js:217", "[UserStore] 后端返回的响应:", response);
      commit("SET_USER_INFO", response.data);
      utils_ui.showSuccess("信息更新成功");
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:222", "[UserStore] 更新用户信息失败:", error);
      utils_ui.showError(error.message || "更新失败");
      throw error;
    }
  },
  // 修改密码
  async changeUserPassword({ commit }, passwordData) {
    try {
      const response = await api_user.changePassword(passwordData);
      utils_ui.showSuccess("密码修改成功");
      return response;
    } catch (error) {
      utils_ui.showError(error.message || "密码修改失败");
      throw error;
    }
  },
  // 用户登出
  async logout({ commit }) {
    try {
      common_vendor.index.__f__("log", "at store/modules/user.js:243", "[UserStore] 开始登出");
      await api_auth.logout().catch((err) => {
        common_vendor.index.__f__("warn", "at store/modules/user.js:246", "[UserStore] 调用退出API失败，可能token已过期:", err);
      });
    } finally {
      commit("CLEAR_USER_DATA");
      utils_ui.showSuccess("已退出登录");
      common_vendor.index.__f__("log", "at store/modules/user.js:252", "[UserStore] 登出完成");
    }
  },
  // 检查登录状态（优化版本）
  async checkLoginStatus({ commit, state: state2 }) {
    if (state2.loginChecking) {
      common_vendor.index.__f__("log", "at store/modules/user.js:260", "[UserStore] 正在检查登录状态，跳过重复检查");
      return state2.isLoggedIn;
    }
    try {
      commit("SET_LOGIN_CHECKING", true);
      const token = utils_auth.getToken();
      const userInfo = utils_auth.getUserInfo();
      common_vendor.index.__f__("log", "at store/modules/user.js:269", "[UserStore] 检查登录状态 - token:", !!token, "userInfo:", !!userInfo);
      if (!token || !userInfo) {
        common_vendor.index.__f__("log", "at store/modules/user.js:273", "[UserStore] 本地无登录信息，清除状态");
        commit("CLEAR_USER_DATA");
        return false;
      }
      try {
        const response = await api_user.getUserInfo();
        common_vendor.index.__f__("log", "at store/modules/user.js:281", "[UserStore] token验证成功");
        if (response && response.data) {
          commit("SET_TOKEN", token);
          commit("SET_USER_INFO", response.data);
          commit("SET_LOGIN_STATUS", true);
          return true;
        } else {
          common_vendor.index.__f__("log", "at store/modules/user.js:290", "[UserStore] 用户信息响应异常，清除状态");
          commit("CLEAR_USER_DATA");
          return false;
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at store/modules/user.js:295", "[UserStore] token验证失败:", error.message);
        if (error.code === "LOGIN_EXPIRED" || error.message.includes("登录已过期") || error.message.includes("未授权") || error.message.includes("token") || error.status === 401) {
          common_vendor.index.__f__("log", "at store/modules/user.js:303", "[UserStore] 认证失效，清除本地数据");
          commit("CLEAR_USER_DATA");
          return false;
        }
        common_vendor.index.__f__("log", "at store/modules/user.js:309", "[UserStore] 网络异常，保持本地登录状态");
        commit("SET_TOKEN", token);
        commit("SET_USER_INFO", userInfo);
        commit("SET_LOGIN_STATUS", true);
        return true;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:316", "[UserStore] 检查登录状态异常:", error);
      commit("CLEAR_USER_DATA");
      return false;
    } finally {
      commit("SET_LOGIN_CHECKING", false);
    }
  },
  // 强制更新登录状态
  updateLoginStatus({ commit }, isLoggedIn) {
    common_vendor.index.__f__("log", "at store/modules/user.js:326", "[UserStore] 强制更新登录状态:", isLoggedIn);
    commit("SET_LOGIN_STATUS", isLoggedIn);
  },
  // 初始化用户状态
  initUserState({ commit, dispatch }) {
    common_vendor.index.__f__("log", "at store/modules/user.js:332", "[UserStore] 初始化用户状态");
    const token = utils_auth.getToken();
    const userInfo = utils_auth.getUserInfo();
    if (token && userInfo) {
      commit("SET_TOKEN", token);
      commit("SET_USER_INFO", userInfo);
      commit("SET_LOGIN_STATUS", true);
      common_vendor.index.__f__("log", "at store/modules/user.js:340", "[UserStore] 从本地存储恢复登录状态");
    } else {
      commit("SET_LOGIN_STATUS", false);
      common_vendor.index.__f__("log", "at store/modules/user.js:343", "[UserStore] 本地无登录信息");
    }
  }
};
const getters = {
  isLoggedIn: (state2) => state2.isLoggedIn,
  userInfo: (state2) => state2.userInfo,
  userStats: (state2) => state2.userStats,
  token: (state2) => state2.token,
  userId: (state2) => {
    var _a;
    return (_a = state2.userInfo) == null ? void 0 : _a.id;
  },
  username: (state2) => {
    var _a;
    return (_a = state2.userInfo) == null ? void 0 : _a.username;
  },
  nickname: (state2) => {
    var _a;
    return (_a = state2.userInfo) == null ? void 0 : _a.nickname;
  },
  avatar: (state2) => {
    var _a;
    return (_a = state2.userInfo) == null ? void 0 : _a.avatar;
  },
  phone: (state2) => {
    var _a;
    return (_a = state2.userInfo) == null ? void 0 : _a.phone;
  },
  email: (state2) => {
    var _a;
    return (_a = state2.userInfo) == null ? void 0 : _a.email;
  },
  loginChecking: (state2) => state2.loginChecking
};
const user = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
exports.user = user;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/user.js.map
