"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  name: "BookingDetailTest",
  data() {
    return {
      testResult: "等待测试...",
      bookingData: null
    };
  },
  computed: {
    ...common_vendor.mapState("booking", ["bookingDetail", "loading"])
  },
  methods: {
    ...common_vendor.mapActions("booking", ["getBookingDetail"]),
    async testBookingDetail() {
      try {
        this.testResult = "开始测试...";
        this.bookingData = null;
        common_vendor.index.__f__("log", "at pages/test/booking-detail-test.vue:56", "🧪 开始测试订单详情API");
        utils_request.clearCache("/bookings/238");
        common_vendor.index.__f__("log", "at pages/test/booking-detail-test.vue:60", "🗑️ 已清除缓存");
        const response = await this.getBookingDetail(238);
        common_vendor.index.__f__("log", "at pages/test/booking-detail-test.vue:64", "🧪 测试响应:", response);
        common_vendor.index.__f__("log", "at pages/test/booking-detail-test.vue:67", "🧪 Store中的数据:", this.bookingDetail);
        this.bookingData = this.bookingDetail;
        if (this.bookingDetail && this.bookingDetail.orderNo) {
          this.testResult = "✅ 测试成功！获取到有效的订单数据";
        } else {
          this.testResult = "❌ 测试失败：获取到的数据无效";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/booking-detail-test.vue:78", "🧪 测试失败:", error);
        this.testResult = `❌ 测试失败: ${error.message}`;
      }
    },
    clearBookingCache() {
      utils_request.clearCache("/bookings/238");
      this.testResult = "🗑️ 缓存已清除";
      common_vendor.index.__f__("log", "at pages/test/booking-detail-test.vue:86", "🗑️ 订单缓存已清除");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.testBookingDetail && $options.testBookingDetail(...args)),
    b: common_vendor.o((...args) => $options.clearBookingCache && $options.clearBookingCache(...args)),
    c: common_vendor.t($data.testResult),
    d: $data.bookingData
  }, $data.bookingData ? {
    e: common_vendor.t(JSON.stringify($data.bookingData, null, 2))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7c79bd5d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/booking-detail-test.js.map
