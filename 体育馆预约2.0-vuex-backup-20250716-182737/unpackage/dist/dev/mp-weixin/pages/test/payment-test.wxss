
.payment-test-container.data-v-61e08248 {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-61e08248 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title.data-v-61e08248 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.subtitle.data-v-61e08248 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.section-title.data-v-61e08248 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0 20rpx 0;
}
.order-card.data-v-61e08248 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.order-header.data-v-61e08248 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.order-no.data-v-61e08248 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.order-type.data-v-61e08248 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}
.type-exclusive.data-v-61e08248 {
  background-color: #007aff;
}
.type-shared.data-v-61e08248 {
  background-color: #ff9500;
}
.type-normal.data-v-61e08248 {
  background-color: #8e8e93;
}
.order-info.data-v-61e08248 {
  margin-bottom: 20rpx;
}
.venue-name.data-v-61e08248 {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.booking-time.data-v-61e08248 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.price.data-v-61e08248 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
}
.order-status.data-v-61e08248 {
  margin-bottom: 20rpx;
}
.status-text.data-v-61e08248 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}
.status-pending.data-v-61e08248 {
  background-color: #ff9500;
}
.status-paid.data-v-61e08248 {
  background-color: #34c759;
}
.status-confirmed.data-v-61e08248 {
  background-color: #007aff;
}
.status-sharing.data-v-61e08248 {
  background-color: #ff9500;
}
.order-actions.data-v-61e08248 {
  display: flex;
  gap: 20rpx;
}
.action-btn.data-v-61e08248 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pay-btn.data-v-61e08248 {
  background-color: #ff6b35;
  color: white;
}
.disabled-btn.data-v-61e08248 {
  background-color: #e0e0e0;
  color: #999;
}
.test-results.data-v-61e08248 {
  margin-top: 40rpx;
}
.result-item.data-v-61e08248 {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.result-order.data-v-61e08248 {
  font-size: 26rpx;
  color: #333;
}
.result-status.data-v-61e08248 {
  font-size: 26rpx;
  font-weight: bold;
}
.result-status.success.data-v-61e08248 {
  color: #34c759;
}
.result-status.error.data-v-61e08248 {
  color: #ff3b30;
}
.result-message.data-v-61e08248 {
  font-size: 24rpx;
  color: #666;
}
