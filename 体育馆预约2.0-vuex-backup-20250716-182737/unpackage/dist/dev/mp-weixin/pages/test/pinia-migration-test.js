"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_app = require("../../stores/app.js");
const stores_user = require("../../stores/user.js");
const stores_venue = require("../../stores/venue.js");
const stores_sharing = require("../../stores/sharing.js");
const stores_booking = require("../../stores/booking.js");
const stores_migrationValidator = require("../../stores/migration-validator.js");
const stores_migrationConfig = require("../../stores/migration-config.js");
const _sfc_main = {
  name: "PiniaMigrationTest",
  data() {
    return {
      appStore: null,
      userStore: null,
      venueStore: null,
      sharingStore: null,
      bookingStore: null,
      validationResults: null,
      continuousValidation: false,
      validationInterval: null,
      migrationStatus: null
    };
  },
  computed: {
    ...common_vendor.mapState(["loading", "networkStatus"]),
    ...common_vendor.mapState("user", ["isLoggedIn", "userInfo"]),
    ...common_vendor.mapState("venue", {
      vuexVenueList: "venueList",
      vuexVenueLoading: "loading",
      vuexSearchResults: "searchResults"
    }),
    ...common_vendor.mapState("sharing", {
      vuexSharingOrders: "sharingOrders",
      vuexMySharingOrders: "mySharingOrders",
      vuexSharingLoading: "loading"
    }),
    ...common_vendor.mapState("booking", {
      vuexBookingList: "bookingList",
      vuexBookingSharingOrders: "sharingOrders",
      vuexBookingLoading: "loading"
    }),
    vuexLoading() {
      return this.loading;
    },
    vuexNetworkStatus() {
      return this.networkStatus;
    },
    vuexUserLoggedIn() {
      return this.isLoggedIn;
    },
    vuexUsername() {
      var _a;
      return (_a = this.userInfo) == null ? void 0 : _a.username;
    },
    piniaLoading() {
      var _a;
      return ((_a = this.appStore) == null ? void 0 : _a.loading) || false;
    },
    piniaNetworkStatus() {
      var _a;
      return ((_a = this.appStore) == null ? void 0 : _a.networkStatus) || true;
    },
    piniaUserLoggedIn() {
      var _a;
      return ((_a = this.userStore) == null ? void 0 : _a.isLoggedIn) || false;
    },
    piniaUsername() {
      var _a, _b;
      return (_b = (_a = this.userStore) == null ? void 0 : _a.userInfo) == null ? void 0 : _b.username;
    },
    vuexVenueCount() {
      var _a;
      return ((_a = this.vuexVenueList) == null ? void 0 : _a.length) || 0;
    },
    piniaVenueCount() {
      var _a, _b;
      return ((_b = (_a = this.venueStore) == null ? void 0 : _a.venueList) == null ? void 0 : _b.length) || 0;
    },
    piniaVenueLoading() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.loading) || false;
    },
    vuexSearchCount() {
      var _a;
      return ((_a = this.vuexSearchResults) == null ? void 0 : _a.length) || 0;
    },
    piniaSearchCount() {
      var _a, _b;
      return ((_b = (_a = this.venueStore) == null ? void 0 : _a.searchResults) == null ? void 0 : _b.length) || 0;
    },
    vuexSharingCount() {
      var _a;
      return ((_a = this.vuexSharingOrders) == null ? void 0 : _a.length) || 0;
    },
    piniaSharingCount() {
      var _a, _b;
      return ((_b = (_a = this.sharingStore) == null ? void 0 : _a.sharingOrders) == null ? void 0 : _b.length) || 0;
    },
    vuexMySharingCount() {
      var _a;
      return ((_a = this.vuexMySharingOrders) == null ? void 0 : _a.length) || 0;
    },
    piniaMySharingCount() {
      var _a, _b;
      return ((_b = (_a = this.sharingStore) == null ? void 0 : _a.mySharingOrders) == null ? void 0 : _b.length) || 0;
    },
    piniaSharingLoading() {
      var _a;
      return ((_a = this.sharingStore) == null ? void 0 : _a.loading) || false;
    },
    vuexBookingCount() {
      var _a;
      return ((_a = this.vuexBookingList) == null ? void 0 : _a.length) || 0;
    },
    piniaBookingCount() {
      var _a, _b;
      return ((_b = (_a = this.bookingStore) == null ? void 0 : _a.bookingList) == null ? void 0 : _b.length) || 0;
    },
    vuexBookingSharingCount() {
      var _a;
      return ((_a = this.vuexBookingSharingOrders) == null ? void 0 : _a.length) || 0;
    },
    piniaBookingSharingCount() {
      var _a, _b;
      return ((_b = (_a = this.bookingStore) == null ? void 0 : _a.sharingOrders) == null ? void 0 : _b.length) || 0;
    },
    piniaBookingLoading() {
      var _a;
      return ((_a = this.bookingStore) == null ? void 0 : _a.loading) || false;
    },
    syncStatus() {
      return {
        loading: this.vuexLoading === this.piniaLoading,
        network: this.vuexNetworkStatus === this.piniaNetworkStatus,
        userLogin: this.vuexUserLoggedIn === this.piniaUserLoggedIn,
        venueCount: this.vuexVenueCount === this.piniaVenueCount,
        venueLoading: this.vuexVenueLoading === this.piniaVenueLoading,
        venueSearch: this.vuexSearchCount === this.piniaSearchCount,
        sharingOrders: this.vuexSharingCount === this.piniaSharingCount,
        sharingLoading: this.vuexSharingLoading === this.piniaSharingLoading,
        bookingCount: this.vuexBookingCount === this.piniaBookingCount,
        bookingLoading: this.vuexBookingLoading === this.piniaBookingLoading
      };
    }
  },
  methods: {
    ...common_vendor.mapActions(["setLoading", "setNetworkStatus"]),
    testVuexUpdate() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:349", "测试Vuex更新");
      this.setLoading(!this.vuexLoading);
    },
    testPiniaUpdate() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:354", "测试Pinia更新");
      const newValue = !this.piniaLoading;
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:356", "Pinia设置loading为:", newValue);
      this.appStore.setLoading(newValue);
    },
    toggleLoading() {
      const newValue = !this.vuexLoading;
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:362", "切换Loading状态到:", newValue);
      this.setLoading(newValue);
    },
    toggleNetwork() {
      const newValue = !this.vuexNetworkStatus;
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:368", "切换Network状态到:", newValue);
      this.setNetworkStatus(newValue);
    },
    // 运行验证
    runValidation() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:374", "运行迁移验证");
      this.validationResults = stores_migrationValidator.validateMigration();
      common_vendor.index.showToast({
        title: this.validationResults.overall ? "验证通过" : "验证失败",
        icon: this.validationResults.overall ? "success" : "error"
      });
    },
    // 切换持续验证
    toggleContinuousValidation() {
      if (this.continuousValidation) {
        if (this.validationInterval) {
          stores_migrationValidator.stopValidation(this.validationInterval);
          this.validationInterval = null;
        }
        this.continuousValidation = false;
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:392", "持续验证已停止");
      } else {
        this.validationInterval = stores_migrationValidator.startValidation(3e3);
        this.continuousValidation = true;
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:397", "持续验证已开始");
      }
    },
    // 直接更新Pinia状态
    testPiniaDirectUpdate() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:403", "直接设置Pinia状态");
      const newLoading = !this.piniaLoading;
      const newNetwork = !this.piniaNetworkStatus;
      this.appStore.loading = newLoading;
      this.appStore.networkStatus = newNetwork;
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:411", "Pinia状态已直接设置:", { loading: newLoading, network: newNetwork });
    },
    // 强制同步状态
    forceSync() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:416", "强制同步状态");
      this.appStore.loading = this.vuexLoading;
      this.appStore.networkStatus = this.vuexNetworkStatus;
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:422", "状态已强制同步");
      setTimeout(() => {
        this.runValidation();
      }, 100);
    },
    // 诊断Venue Store
    diagnoseVenueStore() {
      var _a, _b;
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:432", "=== Venue Store 诊断开始 ===");
      try {
        const store1 = this.venueStore;
        const store2 = stores_venue.useVenueStore();
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:439", "this.venueStore:", {
          exists: !!store1,
          type: typeof store1,
          constructor: (_a = store1 == null ? void 0 : store1.constructor) == null ? void 0 : _a.name,
          getVenueListType: typeof (store1 == null ? void 0 : store1.getVenueList),
          getVenueListValue: store1 == null ? void 0 : store1.getVenueList
        });
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:447", "useVenueStore():", {
          exists: !!store2,
          type: typeof store2,
          constructor: (_b = store2 == null ? void 0 : store2.constructor) == null ? void 0 : _b.name,
          getVenueListType: typeof (store2 == null ? void 0 : store2.getVenueList),
          getVenueListValue: store2 == null ? void 0 : store2.getVenueList
        });
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:455", "相等性检查:", store1 === store2);
        if (store2) {
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:459", "Store内部结构:");
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:460", "- $id:", store2.$id);
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:461", "- state keys:", Object.keys(store2.$state || {}));
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:462", "- 所有属性:", Object.getOwnPropertyNames(store2));
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:463", "- 原型链:", Object.getPrototypeOf(store2));
          const descriptor = Object.getOwnPropertyDescriptor(store2, "getVenueList");
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:467", "getVenueList属性描述符:", descriptor);
        }
        common_vendor.index.showToast({
          title: "诊断完成，查看控制台",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:476", "诊断失败:", error);
        common_vendor.index.showToast({
          title: "诊断失败",
          icon: "error"
        });
      }
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:483", "=== Venue Store 诊断结束 ===");
    },
    // 基础Venue测试（避开getVenueList问题）
    testVenueBasic() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:488", "=== 基础Venue测试开始 ===");
      try {
        const venueStore = stores_venue.useVenueStore();
        if (!venueStore) {
          throw new Error("VenueStore未初始化");
        }
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:497", "1. 测试状态读取:");
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:498", "- venueList:", venueStore.venueList);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:499", "- loading:", venueStore.loading);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:500", "- searchResults:", venueStore.searchResults);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:502", "2. 测试状态设置:");
        venueStore.setLoading(true);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:504", "- 设置loading为true，当前值:", venueStore.loading);
        venueStore.setSearchResults(["测试场馆A", "测试场馆B"]);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:507", "- 设置搜索结果，当前值:", venueStore.searchResults);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:509", "3. 测试状态清除:");
        setTimeout(() => {
          venueStore.setLoading(false);
          venueStore.clearSearchResults();
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:513", "- 清除后loading:", venueStore.loading);
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:514", "- 清除后searchResults:", venueStore.searchResults);
        }, 1e3);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:517", "4. 测试getters:");
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:518", "- totalVenues:", venueStore.totalVenues);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:519", "- isLoading:", venueStore.isLoading);
        common_vendor.index.showToast({
          title: "基础测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:527", "基础测试失败:", error);
        common_vendor.index.showToast({
          title: "基础测试失败",
          icon: "error"
        });
      }
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:534", "=== 基础Venue测试结束 ===");
    },
    // 测试Sharing Vuex功能
    async testSharingVuex() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:539", "测试Sharing Vuex功能");
      try {
        await this.$store.dispatch("sharing/getSharingOrders", { page: 1, pageSize: 5 });
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:543", "Vuex分享订单列表获取成功");
        this.$store.commit("sharing/SET_LOADING", true);
        setTimeout(() => {
          this.$store.commit("sharing/SET_LOADING", false);
        }, 1e3);
        common_vendor.index.showToast({
          title: "Sharing Vuex测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:556", "Sharing Vuex测试失败:", error);
        common_vendor.index.showToast({
          title: "Sharing Vuex测试失败",
          icon: "error"
        });
      }
    },
    // 测试Sharing Pinia功能
    async testSharingPinia() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:566", "测试Sharing Pinia功能");
      try {
        const sharingStore = stores_sharing.useSharingStore();
        if (!sharingStore) {
          throw new Error("SharingStore未初始化");
        }
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:574", "1. 测试状态读取:");
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:575", "- sharingOrders:", sharingStore.sharingOrders);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:576", "- mySharingOrders:", sharingStore.mySharingOrders);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:577", "- loading:", sharingStore.loading);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:579", "2. 测试API调用:");
        try {
          await sharingStore.getSharingOrdersList({ page: 1, pageSize: 5 });
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:582", "Pinia分享订单列表获取成功");
        } catch (apiError) {
          common_vendor.index.__f__("warn", "at pages/test/pinia-migration-test.vue:584", "API调用失败，继续其他测试:", apiError);
        }
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:587", "3. 测试状态设置:");
        sharingStore.setLoading(true);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:589", "- 设置loading为true，当前值:", sharingStore.loading);
        setTimeout(() => {
          sharingStore.setLoading(false);
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:593", "- 设置loading为false，当前值:", sharingStore.loading);
        }, 1e3);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:596", "4. 测试getters:");
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:597", "- totalSharingOrders:", sharingStore.totalSharingOrders);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:598", "- isLoading:", sharingStore.isLoading);
        common_vendor.index.showToast({
          title: "Sharing Pinia测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:605", "Sharing Pinia测试失败:", error);
        common_vendor.index.showToast({
          title: `Sharing Pinia测试失败: ${error.message}`,
          icon: "error"
        });
      }
    },
    // 测试Venue Vuex功能
    async testVenueVuex() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:615", "测试Venue Vuex功能");
      try {
        await this.$store.dispatch("venue/getVenueList", { page: 1, pageSize: 5 });
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:619", "Vuex场馆列表获取成功");
        this.$store.commit("venue/SET_LOADING", true);
        setTimeout(() => {
          this.$store.commit("venue/SET_LOADING", false);
        }, 1e3);
        common_vendor.index.showToast({
          title: "Venue Vuex测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:632", "Venue Vuex测试失败:", error);
        common_vendor.index.showToast({
          title: "Venue Vuex测试失败",
          icon: "error"
        });
      }
    },
    // 测试Venue Pinia功能
    async testVenuePinia() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:642", "测试Venue Pinia功能");
      try {
        const freshVenueStore = stores_venue.useVenueStore();
        if (!freshVenueStore) {
          throw new Error("VenueStore未初始化");
        }
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:651", "Fresh VenueStore可用方法:", Object.getOwnPropertyNames(freshVenueStore));
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:652", "Fresh getVenueList类型:", typeof freshVenueStore.getVenueList);
        if (typeof freshVenueStore.getVenueList === "function") {
          try {
            await freshVenueStore.getVenueList({ page: 1, pageSize: 5 });
            common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:658", "Pinia场馆列表获取成功");
          } catch (error) {
            common_vendor.index.__f__("warn", "at pages/test/pinia-migration-test.vue:660", "getVenueList调用失败:", error);
          }
        } else {
          common_vendor.index.__f__("warn", "at pages/test/pinia-migration-test.vue:663", "getVenueList不是函数，跳过API调用测试");
        }
        freshVenueStore.setLoading(true);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:668", "设置loading为true");
        setTimeout(() => {
          freshVenueStore.setLoading(false);
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:672", "设置loading为false");
        }, 1e3);
        freshVenueStore.setSearchResults(["测试场馆1", "测试场馆2"]);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:677", "设置搜索结果");
        setTimeout(() => {
          freshVenueStore.clearSearchResults();
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:681", "清除搜索结果");
        }, 2e3);
        this.venueStore = freshVenueStore;
        common_vendor.index.showToast({
          title: "Venue Pinia测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:692", "Venue Pinia测试失败:", error);
        common_vendor.index.showToast({
          title: `Venue Pinia测试失败: ${error.message}`,
          icon: "error"
        });
      }
    },
    // 测试Booking Vuex功能
    async testBookingVuex() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:702", "测试Booking Vuex功能");
      try {
        await this.$store.dispatch("booking/getUserBookings", { page: 1, pageSize: 5 });
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:706", "Vuex用户预订列表获取成功");
        this.$store.commit("booking/SET_LOADING", true);
        setTimeout(() => {
          this.$store.commit("booking/SET_LOADING", false);
        }, 1e3);
        common_vendor.index.showToast({
          title: "Booking Vuex测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:719", "Booking Vuex测试失败:", error);
        common_vendor.index.showToast({
          title: "Booking Vuex测试失败",
          icon: "error"
        });
      }
    },
    // 测试Booking Pinia功能
    async testBookingPinia() {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:729", "测试Booking Pinia功能");
      try {
        const bookingStore = stores_booking.useBookingStore();
        if (!bookingStore) {
          throw new Error("BookingStore未初始化");
        }
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:737", "1. 测试状态读取:");
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:738", "- bookingList:", bookingStore.bookingList);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:739", "- sharingOrders:", bookingStore.sharingOrders);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:740", "- loading:", bookingStore.loading);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:742", "2. 测试API调用:");
        try {
          await bookingStore.getUserBookings({ page: 1, pageSize: 5 });
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:745", "Pinia用户预订列表获取成功");
        } catch (apiError) {
          common_vendor.index.__f__("warn", "at pages/test/pinia-migration-test.vue:747", "API调用失败，继续其他测试:", apiError);
        }
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:750", "3. 测试状态设置:");
        bookingStore.setLoading(true);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:752", "- 设置loading为true，当前值:", bookingStore.loading);
        setTimeout(() => {
          bookingStore.setLoading(false);
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:756", "- 设置loading为false，当前值:", bookingStore.loading);
        }, 1e3);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:759", "4. 测试getters:");
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:760", "- totalBookings:", bookingStore.totalBookings);
        common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:761", "- isLoading:", bookingStore.isLoading);
        common_vendor.index.showToast({
          title: "Booking Pinia测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:768", "Booking Pinia测试失败:", error);
        common_vendor.index.showToast({
          title: `Booking Pinia测试失败: ${error.message}`,
          icon: "error"
        });
      }
    }
  },
  onLoad() {
    var _a;
    common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:778", "初始化Pinia迁移测试页面");
    try {
      this.appStore = stores_app.useAppStore();
      this.userStore = stores_user.useUserStore();
      this.venueStore = stores_venue.useVenueStore();
      this.sharingStore = stores_sharing.useSharingStore();
      this.bookingStore = stores_booking.useBookingStore();
      this.migrationStatus = stores_migrationConfig.getMigrationStatus();
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:787", "Stores初始化成功:", {
        appStore: !!this.appStore,
        userStore: !!this.userStore,
        venueStore: !!this.venueStore,
        venueStoreType: typeof this.venueStore,
        venueStoreGetVenueListType: typeof ((_a = this.venueStore) == null ? void 0 : _a.getVenueList)
      });
      if (this.venueStore && typeof this.venueStore.getVenueList !== "function") {
        common_vendor.index.__f__("warn", "at pages/test/pinia-migration-test.vue:797", "VenueStore actions未正确绑定，尝试重新初始化");
        setTimeout(() => {
          var _a2;
          this.venueStore = stores_venue.useVenueStore();
          common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:801", "重新初始化后的getVenueList类型:", typeof ((_a2 = this.venueStore) == null ? void 0 : _a2.getVenueList));
        }, 100);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at pages/test/pinia-migration-test.vue:805", "Stores初始化失败:", error);
    }
    setTimeout(() => {
      common_vendor.index.__f__("log", "at pages/test/pinia-migration-test.vue:810", "初始状态检查:", {
        vuex: {
          loading: this.vuexLoading,
          network: this.vuexNetworkStatus,
          userLogin: this.vuexUserLoggedIn,
          username: this.vuexUsername
        },
        pinia: {
          loading: this.piniaLoading,
          network: this.piniaNetworkStatus,
          userLogin: this.piniaUserLoggedIn,
          username: this.piniaUsername
        }
      });
      this.runValidation();
    }, 100);
  },
  onUnload() {
    if (this.validationInterval) {
      stores_migrationValidator.stopValidation(this.validationInterval);
      this.validationInterval = null;
      this.continuousValidation = false;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.vuexLoading),
    b: common_vendor.t($options.vuexNetworkStatus),
    c: common_vendor.t($options.piniaLoading),
    d: common_vendor.t($options.piniaNetworkStatus),
    e: common_vendor.o((...args) => $options.testVuexUpdate && $options.testVuexUpdate(...args)),
    f: common_vendor.o((...args) => $options.testPiniaUpdate && $options.testPiniaUpdate(...args)),
    g: common_vendor.o((...args) => $options.toggleLoading && $options.toggleLoading(...args)),
    h: common_vendor.o((...args) => $options.toggleNetwork && $options.toggleNetwork(...args)),
    i: common_vendor.o((...args) => $options.runValidation && $options.runValidation(...args)),
    j: common_vendor.t($data.continuousValidation ? "停止" : "开始"),
    k: common_vendor.o((...args) => $options.toggleContinuousValidation && $options.toggleContinuousValidation(...args)),
    l: $data.continuousValidation ? 1 : "",
    m: common_vendor.o((...args) => $options.testPiniaDirectUpdate && $options.testPiniaDirectUpdate(...args)),
    n: common_vendor.o((...args) => $options.forceSync && $options.forceSync(...args)),
    o: common_vendor.o((...args) => $options.testVenueVuex && $options.testVenueVuex(...args)),
    p: common_vendor.o((...args) => $options.testVenuePinia && $options.testVenuePinia(...args)),
    q: common_vendor.o((...args) => $options.diagnoseVenueStore && $options.diagnoseVenueStore(...args)),
    r: common_vendor.o((...args) => $options.testVenueBasic && $options.testVenueBasic(...args)),
    s: common_vendor.o((...args) => $options.testSharingVuex && $options.testSharingVuex(...args)),
    t: common_vendor.o((...args) => $options.testSharingPinia && $options.testSharingPinia(...args)),
    v: common_vendor.o((...args) => $options.testBookingVuex && $options.testBookingVuex(...args)),
    w: common_vendor.o((...args) => $options.testBookingPinia && $options.testBookingPinia(...args)),
    x: common_vendor.t($options.vuexUserLoggedIn),
    y: common_vendor.t($options.piniaUserLoggedIn),
    z: common_vendor.t($options.vuexUsername || "未登录"),
    A: common_vendor.t($options.piniaUsername || "未登录"),
    B: common_vendor.t($options.vuexVenueCount),
    C: common_vendor.t($options.piniaVenueCount),
    D: common_vendor.t(_ctx.vuexVenueLoading),
    E: common_vendor.t($options.piniaVenueLoading),
    F: common_vendor.t($options.vuexSearchCount),
    G: common_vendor.t($options.piniaSearchCount),
    H: common_vendor.t($options.vuexSharingCount),
    I: common_vendor.t($options.piniaSharingCount),
    J: common_vendor.t($options.vuexMySharingCount),
    K: common_vendor.t($options.piniaMySharingCount),
    L: common_vendor.t(_ctx.vuexSharingLoading),
    M: common_vendor.t($options.piniaSharingLoading),
    N: common_vendor.t($options.vuexBookingCount),
    O: common_vendor.t($options.piniaBookingCount),
    P: common_vendor.t($options.vuexBookingSharingCount),
    Q: common_vendor.t($options.piniaBookingSharingCount),
    R: common_vendor.t(_ctx.vuexBookingLoading),
    S: common_vendor.t($options.piniaBookingLoading),
    T: common_vendor.t($options.syncStatus.loading ? "✓" : "✗"),
    U: common_vendor.n($options.syncStatus.loading ? "success" : "error"),
    V: common_vendor.t($options.syncStatus.network ? "✓" : "✗"),
    W: common_vendor.n($options.syncStatus.network ? "success" : "error"),
    X: common_vendor.t($options.syncStatus.userLogin ? "✓" : "✗"),
    Y: common_vendor.n($options.syncStatus.userLogin ? "success" : "error"),
    Z: common_vendor.t($options.syncStatus.venueCount ? "✓" : "✗"),
    aa: common_vendor.n($options.syncStatus.venueCount ? "success" : "error"),
    ab: common_vendor.t($options.syncStatus.venueLoading ? "✓" : "✗"),
    ac: common_vendor.n($options.syncStatus.venueLoading ? "success" : "error"),
    ad: common_vendor.t($options.syncStatus.venueSearch ? "✓" : "✗"),
    ae: common_vendor.n($options.syncStatus.venueSearch ? "success" : "error"),
    af: common_vendor.t($options.syncStatus.sharingOrders ? "✓" : "✗"),
    ag: common_vendor.n($options.syncStatus.sharingOrders ? "success" : "error"),
    ah: common_vendor.t($options.syncStatus.sharingLoading ? "✓" : "✗"),
    ai: common_vendor.n($options.syncStatus.sharingLoading ? "success" : "error"),
    aj: common_vendor.t($options.syncStatus.bookingCount ? "✓" : "✗"),
    ak: common_vendor.n($options.syncStatus.bookingCount ? "success" : "error"),
    al: common_vendor.t($options.syncStatus.bookingLoading ? "✓" : "✗"),
    am: common_vendor.n($options.syncStatus.bookingLoading ? "success" : "error"),
    an: $data.validationResults
  }, $data.validationResults ? common_vendor.e({
    ao: common_vendor.t($data.validationResults.overall ? "通过" : "失败"),
    ap: common_vendor.n($data.validationResults.overall ? "success" : "error"),
    aq: $data.validationResults.modules.app
  }, $data.validationResults.modules.app ? {
    ar: common_vendor.t($data.validationResults.modules.app.valid ? "✓" : "✗"),
    as: common_vendor.n($data.validationResults.modules.app.valid ? "success" : "error")
  } : {}, {
    at: $data.validationResults.modules.user
  }, $data.validationResults.modules.user ? {
    av: common_vendor.t($data.validationResults.modules.user.valid ? "✓" : "✗"),
    aw: common_vendor.n($data.validationResults.modules.user.valid ? "success" : "error")
  } : {}, {
    ax: $data.validationResults.modules.venue
  }, $data.validationResults.modules.venue ? {
    ay: common_vendor.t($data.validationResults.modules.venue.valid ? "✓" : "✗"),
    az: common_vendor.n($data.validationResults.modules.venue.valid ? "success" : "error")
  } : {}, {
    aA: $data.validationResults.modules.sharing
  }, $data.validationResults.modules.sharing ? {
    aB: common_vendor.t($data.validationResults.modules.sharing.valid ? "✓" : "✗"),
    aC: common_vendor.n($data.validationResults.modules.sharing.valid ? "success" : "error")
  } : {}, {
    aD: $data.validationResults.modules.booking
  }, $data.validationResults.modules.booking ? {
    aE: common_vendor.t($data.validationResults.modules.booking.valid ? "✓" : "✗"),
    aF: common_vendor.n($data.validationResults.modules.booking.valid ? "success" : "error")
  } : {}, {
    aG: common_vendor.t($data.validationResults.timestamp)
  }) : {}, {
    aH: $data.migrationStatus
  }, $data.migrationStatus ? {
    aI: common_vendor.t($data.migrationStatus.currentPhase),
    aJ: common_vendor.t($data.migrationStatus.progress),
    aK: common_vendor.t($data.migrationStatus.completedPhases),
    aL: common_vendor.t($data.migrationStatus.totalPhases)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c473bfa4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/pinia-migration-test.js.map
