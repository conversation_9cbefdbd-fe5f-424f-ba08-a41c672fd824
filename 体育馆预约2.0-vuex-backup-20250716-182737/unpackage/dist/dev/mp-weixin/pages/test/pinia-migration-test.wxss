/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-c473bfa4 {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-c473bfa4 {
  text-align: center;
  margin-bottom: 40rpx;
}
.header .title.data-v-c473bfa4 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-c473bfa4 {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}
.test-section .section-title.data-v-c473bfa4 {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}
.test-section .state-display text.data-v-c473bfa4 {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.test-actions.data-v-c473bfa4 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
}
.test-actions .test-btn.data-v-c473bfa4 {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}
.test-actions .test-btn.validation-btn.data-v-c473bfa4 {
  background: #52c41a;
}
.test-actions .test-btn.active.data-v-c473bfa4 {
  background: #ff4d4f;
}
.test-actions .test-btn.direct-btn.data-v-c473bfa4 {
  background: #fa8c16;
}
.test-actions .test-btn.sync-btn.data-v-c473bfa4 {
  background: #722ed1;
}
.test-actions .test-btn.venue-btn.data-v-c473bfa4 {
  background: #13c2c2;
}
.test-actions .test-btn.debug-btn.data-v-c473bfa4 {
  background: #fa8c16;
}
.test-actions .test-btn.success-btn.data-v-c473bfa4 {
  background: #52c41a;
}
.test-actions .test-btn.sharing-btn.data-v-c473bfa4 {
  background: #eb2f96;
}
.test-actions .test-btn.booking-btn.data-v-c473bfa4 {
  background: #fa8c16;
}
.sync-status.data-v-c473bfa4 {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
}
.sync-status .sync-title.data-v-c473bfa4 {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}
.sync-status .sync-result.data-v-c473bfa4 {
  display: block;
  font-size: 26rpx;
  margin-bottom: 10rpx;
}
.sync-status .sync-result.success.data-v-c473bfa4 {
  color: #52c41a;
}
.sync-status .sync-result.error.data-v-c473bfa4 {
  color: #ff4d4f;
}
.validation-results.data-v-c473bfa4, .migration-status.data-v-c473bfa4 {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}
.validation-results .section-title.data-v-c473bfa4, .migration-status .section-title.data-v-c473bfa4 {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}
.validation-results .result-item.data-v-c473bfa4, .migration-status .result-item.data-v-c473bfa4 {
  margin-bottom: 10rpx;
}
.validation-results .result-item .result-status.data-v-c473bfa4, .migration-status .result-item .result-status.data-v-c473bfa4 {
  font-size: 26rpx;
}
.validation-results .result-item .result-status.success.data-v-c473bfa4, .migration-status .result-item .result-status.success.data-v-c473bfa4 {
  color: #52c41a;
}
.validation-results .result-item .result-status.error.data-v-c473bfa4, .migration-status .result-item .result-status.error.data-v-c473bfa4 {
  color: #ff4d4f;
}
.validation-results .timestamp.data-v-c473bfa4, .migration-status .timestamp.data-v-c473bfa4 {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}
.validation-results .progress-info text.data-v-c473bfa4, .migration-status .progress-info text.data-v-c473bfa4 {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
}