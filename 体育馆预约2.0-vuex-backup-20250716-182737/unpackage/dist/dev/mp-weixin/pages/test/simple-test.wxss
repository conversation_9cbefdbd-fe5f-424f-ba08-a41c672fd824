/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-6b4b7f06 {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-6b4b7f06 {
  text-align: center;
  margin-bottom: 40rpx;
}
.header .title.data-v-6b4b7f06 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.content .auth-section.data-v-6b4b7f06, .content .api-section.data-v-6b4b7f06, .content .result-section.data-v-6b4b7f06 {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}
.content .auth-section .section-title.data-v-6b4b7f06, .content .api-section .section-title.data-v-6b4b7f06, .content .result-section .section-title.data-v-6b4b7f06 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.content .auth-section .status.data-v-6b4b7f06, .content .api-section .status.data-v-6b4b7f06, .content .result-section .status.data-v-6b4b7f06 {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.content .auth-section .result.data-v-6b4b7f06, .content .api-section .result.data-v-6b4b7f06, .content .result-section .result.data-v-6b4b7f06 {
  display: block;
  font-size: 24rpx;
  color: #333;
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  white-space: pre-wrap;
  word-break: break-all;
}
.content .test-btn.data-v-6b4b7f06 {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}
.content .test-btn.data-v-6b4b7f06:active {
  background-color: #0056cc;
}