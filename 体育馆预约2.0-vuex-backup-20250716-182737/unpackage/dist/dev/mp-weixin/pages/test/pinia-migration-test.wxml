<view class="container data-v-c473bfa4"><view class="header data-v-c473bfa4"><text class="title data-v-c473bfa4">Pinia迁移测试</text></view><view class="test-section data-v-c473bfa4"><text class="section-title data-v-c473bfa4">Vuex状态</text><view class="state-display data-v-c473bfa4"><text class="data-v-c473bfa4">Loading: {{a}}</text><text class="data-v-c473bfa4">Network: {{b}}</text></view></view><view class="test-section data-v-c473bfa4"><text class="section-title data-v-c473bfa4">Pinia状态</text><view class="state-display data-v-c473bfa4"><text class="data-v-c473bfa4">Loading: {{c}}</text><text class="data-v-c473bfa4">Network: {{d}}</text></view></view><view class="test-actions data-v-c473bfa4"><button bindtap="{{e}}" class="test-btn data-v-c473bfa4">测试Vuex更新</button><button bindtap="{{f}}" class="test-btn data-v-c473bfa4">测试Pinia更新</button><button bindtap="{{g}}" class="test-btn data-v-c473bfa4">切换Loading</button><button bindtap="{{h}}" class="test-btn data-v-c473bfa4">切换Network</button><button bindtap="{{i}}" class="test-btn validation-btn data-v-c473bfa4">运行验证</button><button bindtap="{{k}}" class="{{['test-btn', 'data-v-c473bfa4', l && 'active']}}">{{j}}持续验证 </button><button bindtap="{{m}}" class="test-btn direct-btn data-v-c473bfa4"> 直接设置Pinia状态 </button><button bindtap="{{n}}" class="test-btn sync-btn data-v-c473bfa4"> 强制同步状态 </button><button bindtap="{{o}}" class="test-btn venue-btn data-v-c473bfa4"> 测试Venue Vuex </button><button bindtap="{{p}}" class="test-btn venue-btn data-v-c473bfa4"> 测试Venue Pinia </button><button bindtap="{{q}}" class="test-btn debug-btn data-v-c473bfa4"> 诊断Venue Store </button><button bindtap="{{r}}" class="test-btn success-btn data-v-c473bfa4"> 基础Venue测试 </button><button bindtap="{{s}}" class="test-btn sharing-btn data-v-c473bfa4"> 测试Sharing Vuex </button><button bindtap="{{t}}" class="test-btn sharing-btn data-v-c473bfa4"> 测试Sharing Pinia </button><button bindtap="{{v}}" class="test-btn booking-btn data-v-c473bfa4"> 测试Booking Vuex </button><button bindtap="{{w}}" class="test-btn booking-btn data-v-c473bfa4"> 测试Booking Pinia </button></view><view class="test-section data-v-c473bfa4"><text class="section-title data-v-c473bfa4">User状态测试</text><view class="state-display data-v-c473bfa4"><text class="data-v-c473bfa4">Vuex登录状态: {{x}}</text><text class="data-v-c473bfa4">Pinia登录状态: {{y}}</text><text class="data-v-c473bfa4">Vuex用户名: {{z}}</text><text class="data-v-c473bfa4">Pinia用户名: {{A}}</text></view></view><view class="test-section data-v-c473bfa4"><text class="section-title data-v-c473bfa4">Venue状态测试</text><view class="state-display data-v-c473bfa4"><text class="data-v-c473bfa4">Vuex场馆数量: {{B}}</text><text class="data-v-c473bfa4">Pinia场馆数量: {{C}}</text><text class="data-v-c473bfa4">Vuex加载状态: {{D}}</text><text class="data-v-c473bfa4">Pinia加载状态: {{E}}</text><text class="data-v-c473bfa4">Vuex搜索结果: {{F}}</text><text class="data-v-c473bfa4">Pinia搜索结果: {{G}}</text></view></view><view class="test-section data-v-c473bfa4"><text class="section-title data-v-c473bfa4">Sharing状态测试</text><view class="state-display data-v-c473bfa4"><text class="data-v-c473bfa4">Vuex分享订单: {{H}}</text><text class="data-v-c473bfa4">Pinia分享订单: {{I}}</text><text class="data-v-c473bfa4">Vuex我的订单: {{J}}</text><text class="data-v-c473bfa4">Pinia我的订单: {{K}}</text><text class="data-v-c473bfa4">Vuex加载状态: {{L}}</text><text class="data-v-c473bfa4">Pinia加载状态: {{M}}</text></view></view><view class="test-section data-v-c473bfa4"><text class="section-title data-v-c473bfa4">Booking状态测试</text><view class="state-display data-v-c473bfa4"><text class="data-v-c473bfa4">Vuex预订数量: {{N}}</text><text class="data-v-c473bfa4">Pinia预订数量: {{O}}</text><text class="data-v-c473bfa4">Vuex拼场订单: {{P}}</text><text class="data-v-c473bfa4">Pinia拼场订单: {{Q}}</text><text class="data-v-c473bfa4">Vuex加载状态: {{R}}</text><text class="data-v-c473bfa4">Pinia加载状态: {{S}}</text></view></view><view class="sync-status data-v-c473bfa4"><text class="sync-title data-v-c473bfa4">同步状态检查</text><text class="{{['data-v-c473bfa4', 'sync-result', U]}}"> Loading同步: {{T}}</text><text class="{{['data-v-c473bfa4', 'sync-result', W]}}"> Network同步: {{V}}</text><text class="{{['data-v-c473bfa4', 'sync-result', Y]}}"> User登录同步: {{X}}</text><text class="{{['data-v-c473bfa4', 'sync-result', aa]}}"> Venue数量同步: {{Z}}</text><text class="{{['data-v-c473bfa4', 'sync-result', ac]}}"> Venue加载同步: {{ab}}</text><text class="{{['data-v-c473bfa4', 'sync-result', ae]}}"> Venue搜索同步: {{ad}}</text><text class="{{['data-v-c473bfa4', 'sync-result', ag]}}"> Sharing订单同步: {{af}}</text><text class="{{['data-v-c473bfa4', 'sync-result', ai]}}"> Sharing加载同步: {{ah}}</text><text class="{{['data-v-c473bfa4', 'sync-result', ak]}}"> Booking数量同步: {{aj}}</text><text class="{{['data-v-c473bfa4', 'sync-result', am]}}"> Booking加载同步: {{al}}</text></view><view wx:if="{{an}}" class="validation-results data-v-c473bfa4"><text class="section-title data-v-c473bfa4">验证结果</text><view class="result-item data-v-c473bfa4"><text class="{{['data-v-c473bfa4', 'result-status', ap]}}"> 总体状态: {{ao}}</text></view><view wx:if="{{aq}}" class="result-item data-v-c473bfa4"><text class="{{['data-v-c473bfa4', 'result-status', as]}}"> App模块: {{ar}}</text></view><view wx:if="{{at}}" class="result-item data-v-c473bfa4"><text class="{{['data-v-c473bfa4', 'result-status', aw]}}"> User模块: {{av}}</text></view><view wx:if="{{ax}}" class="result-item data-v-c473bfa4"><text class="{{['data-v-c473bfa4', 'result-status', az]}}"> Venue模块: {{ay}}</text></view><view wx:if="{{aA}}" class="result-item data-v-c473bfa4"><text class="{{['data-v-c473bfa4', 'result-status', aC]}}"> Sharing模块: {{aB}}</text></view><view wx:if="{{aD}}" class="result-item data-v-c473bfa4"><text class="{{['data-v-c473bfa4', 'result-status', aF]}}"> Booking模块: {{aE}}</text></view><text class="timestamp data-v-c473bfa4">验证时间: {{aG}}</text></view><view wx:if="{{aH}}" class="migration-status data-v-c473bfa4"><text class="section-title data-v-c473bfa4">迁移进度</text><view class="progress-info data-v-c473bfa4"><text class="data-v-c473bfa4">当前阶段: {{aI}}</text><text class="data-v-c473bfa4">完成进度: {{aJ}}%</text><text class="data-v-c473bfa4">已完成: {{aK}}/{{aL}}</text></view></view></view>