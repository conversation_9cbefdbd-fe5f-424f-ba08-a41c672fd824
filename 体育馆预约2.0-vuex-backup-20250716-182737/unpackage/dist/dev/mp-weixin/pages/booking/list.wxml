<view class="container data-v-c1931fab"><view class="status-filter data-v-c1931fab"><view wx:for="{{a}}" wx:for-item="status" wx:key="b" class="{{['filter-item', 'data-v-c1931fab', status.c && 'active']}}" bindtap="{{status.d}}">{{status.a}}</view></view><view class="booking-list data-v-c1931fab"><view wx:for="{{b}}" wx:for-item="booking" wx:key="P" class="booking-card data-v-c1931fab" bindtap="{{booking.Q}}"><view class="card-header data-v-c1931fab"><view class="venue-info data-v-c1931fab"><view class="venue-name-row data-v-c1931fab"><text class="venue-name data-v-c1931fab">{{booking.a}}</text><view wx:if="{{booking.b}}" class="booking-type-tag data-v-c1931fab"><text class="{{['tag-text', 'data-v-c1931fab', booking.d]}}">{{booking.c}}</text></view><view wx:if="{{booking.e}}" class="virtual-order-tag data-v-c1931fab"><text class="virtual-tag-text data-v-c1931fab">拼场申请</text></view></view><text class="booking-date data-v-c1931fab">{{booking.f}}</text></view><view class="{{['booking-status', 'data-v-c1931fab', booking.h]}}">{{booking.g}}</view></view><view class="card-content data-v-c1931fab"><view class="time-info data-v-c1931fab"><text class="time-icon data-v-c1931fab">🕐</text><text class="time-text data-v-c1931fab">{{booking.i}}</text></view><view class="location-info data-v-c1931fab"><text class="location-icon data-v-c1931fab">📍</text><text class="location-text data-v-c1931fab">{{booking.j}}</text></view><view class="order-info data-v-c1931fab"><text class="order-icon data-v-c1931fab">📋</text><text class="order-text data-v-c1931fab">订单号：{{booking.k}}</text></view><view class="create-time-info data-v-c1931fab"><text class="time-icon data-v-c1931fab">📅</text><text class="create-time-text data-v-c1931fab">创建时间：{{booking.l}}</text></view><view class="price-info data-v-c1931fab"><text class="price-label data-v-c1931fab">费用：</text><text class="price-value data-v-c1931fab">¥{{booking.m}}</text></view><countdown-timer wx:if="{{booking.n}}" class="simple data-v-c1931fab" bindexpired="{{booking.o}}" u-i="{{booking.p}}" bind:__l="__l" u-p="{{booking.q}}"/></view><view class="card-actions data-v-c1931fab"><block wx:if="{{booking.r}}"><button class="action-btn pay-btn data-v-c1931fab" catchtap="{{booking.s}}">立即支付</button><button class="action-btn cancel-btn data-v-c1931fab" catchtap="{{booking.t}}">取消预约</button></block><block wx:elif="{{booking.v}}"><button class="action-btn info-btn data-v-c1931fab" catchtap="{{booking.w}}">查看详情</button><button class="action-btn cancel-btn data-v-c1931fab" catchtap="{{booking.x}}">取消预约</button></block><block wx:elif="{{booking.y}}"><button class="action-btn info-btn data-v-c1931fab" catchtap="{{booking.z}}">查看详情</button><button class="action-btn participants-btn data-v-c1931fab" catchtap="{{booking.A}}">查看参与者</button><button class="action-btn cancel-btn data-v-c1931fab" catchtap="{{booking.B}}">取消预约</button></block><block wx:elif="{{booking.C}}"><button class="action-btn info-btn data-v-c1931fab" catchtap="{{booking.D}}">查看详情</button><button class="action-btn participants-btn data-v-c1931fab" catchtap="{{booking.E}}">查看参与者</button></block><block wx:elif="{{booking.F}}"><button class="action-btn checkin-btn data-v-c1931fab" catchtap="{{booking.G}}">签到</button><button class="action-btn cancel-btn data-v-c1931fab" catchtap="{{booking.H}}">取消预约</button></block><block wx:elif="{{booking.I}}"><button class="action-btn complete-btn data-v-c1931fab" catchtap="{{booking.J}}">完成订单</button></block><block wx:elif="{{booking.K}}"><button class="action-btn review-btn data-v-c1931fab" catchtap="{{booking.L}}">评价场馆</button><button class="action-btn rebook-btn data-v-c1931fab" catchtap="{{booking.M}}">再次预约</button></block><block wx:elif="{{booking.N}}"><button class="action-btn rebook-btn data-v-c1931fab" catchtap="{{booking.O}}">再次预约</button></block></view></view></view><view wx:if="{{c}}" class="empty-state data-v-c1931fab"><text class="empty-icon data-v-c1931fab">📅</text><text class="empty-text data-v-c1931fab">暂无预约记录</text><button class="empty-btn data-v-c1931fab" bindtap="{{d}}">去预约场馆</button></view><view wx:if="{{e}}" class="load-more data-v-c1931fab" bindtap="{{g}}"><text class="data-v-c1931fab">{{f}}</text></view><uni-popup wx:if="{{k}}" class="r data-v-c1931fab" u-s="{{['d']}}" u-r="cancelPopup" u-i="c1931fab-1" bind:__l="__l" u-p="{{k}}"><view class="cancel-modal data-v-c1931fab"><view class="modal-header data-v-c1931fab"><text class="modal-title data-v-c1931fab">取消预约</text></view><view class="modal-content data-v-c1931fab"><text class="modal-text data-v-c1931fab">确定要取消这个预约吗？</text><text class="modal-note data-v-c1931fab">取消后可能产生手续费，具体以场馆规定为准</text></view><view class="modal-actions data-v-c1931fab"><button class="modal-btn cancel-btn data-v-c1931fab" bindtap="{{h}}">暂不取消</button><button class="modal-btn confirm-btn data-v-c1931fab" bindtap="{{i}}">确认取消</button></view></view></uni-popup></view>