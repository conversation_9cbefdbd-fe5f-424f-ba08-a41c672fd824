"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  name: "BookingDetail",
  data() {
    return {
      bookingId: ""
    };
  },
  computed: {
    ...common_vendor.mapGetters("booking", ["bookingDetail", "loading"])
  },
  onLoad(options) {
    this.bookingId = options.id;
    this.initData();
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  methods: {
    ...common_vendor.mapActions("booking", ["getBookingDetail", "cancelBooking"]),
    // 初始化数据
    async initData() {
      try {
        if (!this.bookingId) {
          throw new Error("订单ID无效，请重新进入页面");
        }
        utils_request.clearCache(`/bookings/${this.bookingId}`);
        await this.getBookingDetail(this.bookingId);
        await this.$nextTick();
        if (!this.bookingDetail) {
          throw new Error("未能获取到订单数据，请检查网络连接");
        }
        if (!this.bookingDetail.orderNo && !this.bookingDetail.id) {
          throw new Error("订单数据不完整，订单可能不存在或已被删除");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/booking/detail.vue:272", "初始化数据失败:", error);
        common_vendor.index.showModal({
          title: "加载失败",
          content: error.message || "无法获取订单详情，请检查订单号是否正确",
          showCancel: true,
          cancelText: "返回",
          confirmText: "重试",
          success: (res) => {
            if (res.confirm) {
              this.initData();
            } else {
              common_vendor.index.navigateBack();
            }
          }
        });
      }
    },
    // 刷新数据
    async refreshData() {
      try {
        await this.initData();
        common_vendor.index.stopPullDownRefresh();
      } catch (error) {
        common_vendor.index.stopPullDownRefresh();
        common_vendor.index.__f__("error", "at pages/booking/detail.vue:300", "刷新数据失败:", error);
      }
    },
    // 取消预约
    cancelBooking() {
      this.$refs.cancelPopup.open();
    },
    // 关闭取消弹窗
    closeCancelModal() {
      this.$refs.cancelPopup.close();
    },
    // 确认取消
    async confirmCancel() {
      try {
        common_vendor.index.showLoading({ title: "取消中..." });
        await this.cancelBooking(this.bookingId);
        common_vendor.index.hideLoading();
        this.closeCancelModal();
        common_vendor.index.showToast({
          title: "取消成功",
          icon: "success"
        });
        await this.refreshData();
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/booking/detail.vue:334", "取消预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消失败",
          icon: "error"
        });
      }
    },
    // 评价场馆
    reviewVenue() {
      common_vendor.index.navigateTo({
        url: `/pages/venue/review?venueId=${this.bookingDetail.venueId}&bookingId=${this.bookingId}`
      });
    },
    // 支付订单
    payBooking() {
      if (!this.bookingDetail || !this.bookingDetail.id) {
        common_vendor.index.showToast({
          title: "订单信息不完整",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/booking/detail.vue:361", "跳转到支付页面，订单ID:", this.bookingDetail.id);
      common_vendor.index.navigateTo({
        url: `/pages/payment/index?orderId=${this.bookingDetail.id}&type=booking`
      });
    },
    // 再次预约
    rebookVenue() {
      common_vendor.index.navigateTo({
        url: `/pages/venue/detail?id=${this.bookingDetail.venueId}`
      });
    },
    // 跳转到拼场详情
    navigateToSharingDetail() {
      if (this.bookingDetail.sharingOrder) {
        common_vendor.index.navigateTo({
          url: `/pages/sharing/detail?id=${this.bookingDetail.sharingOrder.id}`
        });
      }
    },
    // 拨打电话
    callVenue() {
      if (this.bookingDetail.venuePhone) {
        common_vendor.index.makePhoneCall({
          phoneNumber: this.bookingDetail.venuePhone
        });
      } else {
        common_vendor.index.showToast({
          title: "暂无联系方式",
          icon: "none"
        });
      }
    },
    // 打开地图
    openMap() {
      if (this.bookingDetail.venueLatitude && this.bookingDetail.venueLongitude) {
        common_vendor.index.openLocation({
          latitude: this.bookingDetail.venueLatitude,
          longitude: this.bookingDetail.venueLongitude,
          name: this.bookingDetail.venueName,
          address: this.bookingDetail.venueLocation
        });
      } else {
        common_vendor.index.showToast({
          title: "暂无位置信息",
          icon: "none"
        });
      }
    },
    // 格式化日期
    formatDate(date) {
      return utils_helpers.formatDate(date, "YYYY年MM月DD日 dddd");
    },
    // 格式化日期时间
    formatDateTime(datetime) {
      return utils_helpers.formatDateTime(datetime, "YYYY-MM-DD HH:mm");
    },
    // 格式化创建时间
    formatCreateTime(datetime) {
      return utils_helpers.formatTime(datetime, "YYYY-MM-DD HH:mm");
    },
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        "PENDING": "status-pending",
        "CONFIRMED": "status-confirmed",
        "COMPLETED": "status-completed",
        "CANCELLED": "status-cancelled"
      };
      return statusMap[status] || "status-pending";
    },
    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        "PENDING": "⏳",
        "CONFIRMED": "✅",
        "COMPLETED": "🎉",
        "CANCELLED": "❌"
      };
      return iconMap[status] || "⏳";
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "PENDING": "待确认",
        "CONFIRMED": "已确认",
        "COMPLETED": "已完成",
        "CANCELLED": "已取消"
      };
      return statusMap[status] || "待确认";
    },
    // 获取状态描述
    getStatusDesc(status) {
      const descMap = {
        "PENDING": "场馆正在确认您的预约",
        "CONFIRMED": "预约已确认，请按时到场",
        "COMPLETED": "预约已完成，感谢您的使用",
        "CANCELLED": "预约已取消"
      };
      return descMap[status] || "";
    },
    // 获取拼场状态文本
    getSharingStatusText(status) {
      const statusMap = {
        "RECRUITING": "招募中",
        "FULL": "已满员",
        "COMPLETED": "已完成",
        "CANCELLED": "已取消"
      };
      return statusMap[status] || "招募中";
    },
    // 获取预约类型文本
    getBookingTypeText(bookingType) {
      const typeMap = {
        "EXCLUSIVE": "包场",
        "SHARED": "拼场"
      };
      return typeMap[bookingType] || "--";
    },
    // 获取预约类型样式类
    getBookingTypeClass(bookingType) {
      const classMap = {
        "EXCLUSIVE": "booking-type-exclusive",
        "SHARED": "booking-type-shared"
      };
      return classMap[bookingType] || "";
    },
    // 检查是否是虚拟订单
    isVirtualOrder() {
      if (!this.bookingDetail)
        return false;
      const bookingId = typeof this.bookingDetail.id === "string" ? parseInt(this.bookingDetail.id) : this.bookingDetail.id;
      return bookingId < 0;
    },
    // 获取支付金额（兼容虚拟订单和普通订单）
    getPaymentAmount() {
      if (!this.bookingDetail)
        return "0.00";
      if (this.isVirtualOrder()) {
        const amount = this.bookingDetail.paymentAmount || 0;
        return amount.toFixed(2);
      } else {
        const amount = this.bookingDetail.totalPrice || 0;
        return amount.toFixed(2);
      }
    },
    // 格式化预约日期（兼容虚拟订单和普通订单）
    formatBookingDate() {
      if (!this.bookingDetail)
        return "--";
      if (this.isVirtualOrder()) {
        const bookingTime = this.bookingDetail.bookingTime;
        if (!bookingTime)
          return "--";
        try {
          let dateTime;
          if (typeof bookingTime === "string") {
            let isoTime = bookingTime;
            if (bookingTime.includes(" ") && !bookingTime.includes("T")) {
              isoTime = bookingTime.replace(" ", "T");
            }
            dateTime = new Date(isoTime);
          } else {
            dateTime = new Date(bookingTime);
          }
          if (isNaN(dateTime.getTime())) {
            common_vendor.index.__f__("error", "at pages/booking/detail.vue:546", "虚拟订单日期格式化错误 - 无效的时间:", bookingTime);
            return "--";
          }
          return dateTime.toLocaleDateString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit"
          }).replace(/\//g, "-");
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/booking/detail.vue:556", "虚拟订单日期格式化错误:", error);
          return "--";
        }
      } else {
        if (this.bookingDetail.bookingDate) {
          return this.formatDate(this.bookingDetail.bookingDate);
        }
        return "--";
      }
    },
    // 格式化预约时间（兼容虚拟订单和普通订单）
    formatBookingTime() {
      if (!this.bookingDetail)
        return "--";
      if (this.isVirtualOrder()) {
        const startTime = this.bookingDetail.bookingTime;
        const endTime = this.bookingDetail.endTime;
        if (!startTime)
          return "--";
        try {
          let startDateTime, endDateTime;
          if (typeof startTime === "string") {
            let isoTime = startTime;
            if (startTime.includes(" ") && !startTime.includes("T")) {
              isoTime = startTime.replace(" ", "T");
            }
            startDateTime = new Date(isoTime);
            common_vendor.index.__f__("log", "at pages/booking/detail.vue:590", "预约详情时间转换 - 原始:", startTime, "转换后:", isoTime, "解析结果:", startDateTime);
          } else {
            startDateTime = new Date(startTime);
          }
          if (endTime) {
            if (typeof endTime === "string") {
              let isoEndTime = endTime;
              if (endTime.includes(" ") && !endTime.includes("T")) {
                isoEndTime = endTime.replace(" ", "T");
              }
              endDateTime = new Date(isoEndTime);
              common_vendor.index.__f__("log", "at pages/booking/detail.vue:602", "预约详情结束时间转换 - 原始:", endTime, "转换后:", isoEndTime, "解析结果:", endDateTime);
            } else {
              endDateTime = new Date(endTime);
            }
          }
          if (isNaN(startDateTime.getTime())) {
            common_vendor.index.__f__("error", "at pages/booking/detail.vue:610", "虚拟订单时间格式化错误 - 无效的开始时间:", startTime);
            return "--";
          }
          const startTimeStr = startDateTime.toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false
          });
          let endTimeStr = "";
          if (endDateTime && !isNaN(endDateTime.getTime())) {
            endTimeStr = endDateTime.toLocaleTimeString("zh-CN", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false
            });
          }
          return endTimeStr ? `${startTimeStr} - ${endTimeStr}` : startTimeStr;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/booking/detail.vue:633", "虚拟订单时间格式化错误:", error);
          return "--";
        }
      } else {
        if (this.bookingDetail.startTime && this.bookingDetail.endTime) {
          return `${this.bookingDetail.startTime} - ${this.bookingDetail.endTime}`;
        }
        return "--";
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i;
  return common_vendor.e({
    a: _ctx.loading
  }, _ctx.loading ? {} : !_ctx.bookingDetail || !_ctx.bookingDetail.orderNo ? {
    c: common_vendor.o((...args) => $options.initData && $options.initData(...args))
  } : common_vendor.e({
    d: common_vendor.t($options.getStatusIcon((_a = _ctx.bookingDetail) == null ? void 0 : _a.status)),
    e: common_vendor.n($options.getStatusClass((_b = _ctx.bookingDetail) == null ? void 0 : _b.status)),
    f: common_vendor.t($options.getStatusText((_c = _ctx.bookingDetail) == null ? void 0 : _c.status)),
    g: common_vendor.t($options.getStatusDesc((_d = _ctx.bookingDetail) == null ? void 0 : _d.status)),
    h: common_vendor.t(((_e = _ctx.bookingDetail) == null ? void 0 : _e.orderNo) || "--"),
    i: common_vendor.t(((_f = _ctx.bookingDetail) == null ? void 0 : _f.venueName) || "--"),
    j: common_vendor.t($options.getBookingTypeText((_g = _ctx.bookingDetail) == null ? void 0 : _g.bookingType)),
    k: common_vendor.n($options.getBookingTypeClass((_h = _ctx.bookingDetail) == null ? void 0 : _h.bookingType)),
    l: $options.isVirtualOrder()
  }, $options.isVirtualOrder() ? {} : {}, {
    m: common_vendor.t(((_i = _ctx.bookingDetail) == null ? void 0 : _i.venueLocation) || "--"),
    n: common_vendor.t($options.formatBookingDate()),
    o: common_vendor.t($options.formatBookingTime()),
    p: $options.isVirtualOrder()
  }, $options.isVirtualOrder() ? {
    q: common_vendor.t($options.getPaymentAmount())
  } : _ctx.bookingDetail && _ctx.bookingDetail.isSharedBooking ? {
    s: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.totalOriginalPrice || 0),
    t: common_vendor.t((_ctx.bookingDetail && _ctx.bookingDetail.totalOriginalPrice) - (_ctx.bookingDetail && _ctx.bookingDetail.totalPrice) || 0),
    v: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.totalPrice || 0)
  } : {
    w: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.totalPrice || 0)
  }, {
    r: _ctx.bookingDetail && _ctx.bookingDetail.isSharedBooking,
    x: common_vendor.t($options.formatCreateTime(_ctx.bookingDetail && _ctx.bookingDetail.createdAt || _ctx.bookingDetail && _ctx.bookingDetail.createTime)),
    y: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.venuePhone || "暂无"),
    z: common_vendor.o((...args) => $options.callVenue && $options.callVenue(...args)),
    A: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.venueLocation || "暂无"),
    B: common_vendor.o((...args) => $options.openMap && $options.openMap(...args)),
    C: _ctx.bookingDetail && _ctx.bookingDetail.sharingOrder
  }, _ctx.bookingDetail && _ctx.bookingDetail.sharingOrder ? {
    D: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.sharingOrder && _ctx.bookingDetail.sharingOrder.teamName || ""),
    E: common_vendor.t($options.getSharingStatusText(_ctx.bookingDetail && _ctx.bookingDetail.sharingOrder && _ctx.bookingDetail.sharingOrder.status)),
    F: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.sharingOrder && _ctx.bookingDetail.sharingOrder.currentParticipants || 0),
    G: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.sharingOrder && _ctx.bookingDetail.sharingOrder.maxParticipants || 0),
    H: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.sharingOrder && _ctx.bookingDetail.sharingOrder.pricePerPerson || 0),
    I: common_vendor.t(_ctx.bookingDetail && _ctx.bookingDetail.sharingOrder && _ctx.bookingDetail.sharingOrder.description || "暂无说明"),
    J: common_vendor.o((...args) => $options.navigateToSharingDetail && $options.navigateToSharingDetail(...args))
  } : {}, {
    K: _ctx.bookingDetail && _ctx.bookingDetail.status === "PENDING"
  }, _ctx.bookingDetail && _ctx.bookingDetail.status === "PENDING" ? {
    L: common_vendor.o((...args) => $options.cancelBooking && $options.cancelBooking(...args))
  } : {}, {
    M: _ctx.bookingDetail && _ctx.bookingDetail.status === "PENDING"
  }, _ctx.bookingDetail && _ctx.bookingDetail.status === "PENDING" ? {
    N: common_vendor.o((...args) => $options.payBooking && $options.payBooking(...args))
  } : {}, {
    O: _ctx.bookingDetail && _ctx.bookingDetail.status === "COMPLETED"
  }, _ctx.bookingDetail && _ctx.bookingDetail.status === "COMPLETED" ? {
    P: common_vendor.o((...args) => $options.reviewVenue && $options.reviewVenue(...args))
  } : {}, {
    Q: common_vendor.o((...args) => $options.rebookVenue && $options.rebookVenue(...args)),
    R: common_vendor.o((...args) => $options.closeCancelModal && $options.closeCancelModal(...args)),
    S: common_vendor.o((...args) => $options.confirmCancel && $options.confirmCancel(...args)),
    T: common_vendor.sr("cancelPopup", "70644a13-0"),
    U: common_vendor.p({
      type: "center"
    })
  }), {
    b: !_ctx.bookingDetail || !_ctx.bookingDetail.orderNo
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-70644a13"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/booking/detail.js.map
