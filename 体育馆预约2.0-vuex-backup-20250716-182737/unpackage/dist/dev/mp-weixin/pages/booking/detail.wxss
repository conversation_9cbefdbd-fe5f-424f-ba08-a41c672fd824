/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-70644a13 {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.status-section.data-v-70644a13 {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}
.status-section .status-icon.data-v-70644a13 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  margin-right: 30rpx;
}
.status-section .status-icon.status-pending.data-v-70644a13 {
  background-color: #fff7e6;
}
.status-section .status-icon.status-confirmed.data-v-70644a13 {
  background-color: #e6f7ff;
}
.status-section .status-icon.status-completed.data-v-70644a13 {
  background-color: #f6ffed;
}
.status-section .status-icon.status-cancelled.data-v-70644a13 {
  background-color: #fff2f0;
}
.status-section .status-info.data-v-70644a13 {
  flex: 1;
}
.status-section .status-info .status-text.data-v-70644a13 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.status-section .status-info .status-desc.data-v-70644a13 {
  font-size: 24rpx;
  color: #666666;
}
.info-section.data-v-70644a13,
.contact-section.data-v-70644a13,
.sharing-section.data-v-70644a13 {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}
.info-section .section-title.data-v-70644a13,
.contact-section .section-title.data-v-70644a13,
.sharing-section .section-title.data-v-70644a13 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-section .info-item.data-v-70644a13 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}
.info-section .info-item.data-v-70644a13:last-child {
  border-bottom: none;
}
.info-section .info-item .info-label.data-v-70644a13 {
  font-size: 28rpx;
  color: #666666;
}
.info-section .info-item .info-value.data-v-70644a13 {
  font-size: 28rpx;
  color: #333333;
  text-align: right;
  max-width: 60%;
}
.info-section .info-item .info-value.price.data-v-70644a13 {
  color: #ff6b35;
  font-weight: 600;
}
.info-section .info-item .info-value.booking-type.data-v-70644a13 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.info-section .info-item .info-value.booking-type.booking-type-exclusive.data-v-70644a13 {
  background-color: #e6f7ff;
  color: #1890ff;
}
.info-section .info-item .info-value.booking-type.booking-type-shared.data-v-70644a13 {
  background-color: #fff7e6;
  color: #fa8c16;
}
.info-section .info-item .booking-type-container.data-v-70644a13 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.info-section .info-item .virtual-order-badge.data-v-70644a13 {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f9f0ff;
  color: #722ed1;
  border: 1rpx solid #722ed1;
}
.contact-section .contact-item.data-v-70644a13 {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}
.contact-section .contact-item.data-v-70644a13:last-child {
  border-bottom: none;
}
.contact-section .contact-item .contact-icon.data-v-70644a13 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
}
.contact-section .contact-item .contact-info.data-v-70644a13 {
  flex: 1;
}
.contact-section .contact-item .contact-info .contact-label.data-v-70644a13 {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 4rpx;
}
.contact-section .contact-item .contact-info .contact-value.data-v-70644a13 {
  font-size: 24rpx;
  color: #666666;
}
.contact-section .contact-item .contact-arrow.data-v-70644a13 {
  font-size: 24rpx;
  color: #cccccc;
}
.sharing-section .sharing-card.data-v-70644a13 {
  padding: 30rpx;
}
.sharing-section .sharing-card .sharing-header.data-v-70644a13 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.sharing-section .sharing-card .sharing-header .sharing-team.data-v-70644a13 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.sharing-section .sharing-card .sharing-header .sharing-status.data-v-70644a13 {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  background-color: #e6f7ff;
  color: #1890ff;
  border-radius: 16rpx;
}
.sharing-section .sharing-card .sharing-info.data-v-70644a13 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.sharing-section .sharing-card .sharing-info .sharing-participants.data-v-70644a13,
.sharing-section .sharing-card .sharing-info .sharing-price.data-v-70644a13 {
  font-size: 24rpx;
  color: #666666;
}
.sharing-section .sharing-card .sharing-info .sharing-price.data-v-70644a13 {
  color: #ff6b35;
  font-weight: 600;
}
.sharing-section .sharing-card .sharing-desc.data-v-70644a13 {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}
.actions-section.data-v-70644a13 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}
.actions-section .action-btn.data-v-70644a13 {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
  border: 1rpx solid;
}
.actions-section .action-btn.data-v-70644a13:last-child {
  margin-right: 0;
}
.actions-section .action-btn.cancel-btn.data-v-70644a13 {
  background-color: transparent;
  color: #ff4d4f;
  border-color: #ff4d4f;
}
.actions-section .action-btn.share-btn.data-v-70644a13 {
  background-color: #ff6b35;
  color: #ffffff;
  border-color: #ff6b35;
}
.actions-section .action-btn.review-btn.data-v-70644a13 {
  background-color: transparent;
  color: #1890ff;
  border-color: #1890ff;
}
.actions-section .action-btn.rebook-btn.data-v-70644a13 {
  background-color: #ff6b35;
  color: #ffffff;
  border-color: #ff6b35;
}
.cancel-modal.data-v-70644a13 {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}
.cancel-modal .modal-header.data-v-70644a13 {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}
.cancel-modal .modal-header .modal-title.data-v-70644a13 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.cancel-modal .modal-content.data-v-70644a13 {
  padding: 40rpx 30rpx;
  text-align: center;
}
.cancel-modal .modal-content .modal-text.data-v-70644a13 {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
}
.cancel-modal .modal-content .modal-note.data-v-70644a13 {
  font-size: 24rpx;
  color: #999999;
}
.cancel-modal .modal-actions.data-v-70644a13 {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}
.cancel-modal .modal-actions .modal-btn.data-v-70644a13 {
  flex: 1;
  height: 100rpx;
  border: none;
  font-size: 28rpx;
}
.cancel-modal .modal-actions .modal-btn.cancel-btn.data-v-70644a13 {
  background-color: #f5f5f5;
  color: #666666;
}
.cancel-modal .modal-actions .modal-btn.confirm-btn.data-v-70644a13 {
  background-color: #ff6b35;
  color: #ffffff;
}
.loading-state.data-v-70644a13 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-state .loading-text.data-v-70644a13 {
  font-size: 28rpx;
  color: #999999;
}
.empty-state.data-v-70644a13 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.empty-state .empty-text.data-v-70644a13 {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.empty-state .retry-btn.data-v-70644a13 {
  padding: 16rpx 32rpx;
  background-color: #007aff;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}