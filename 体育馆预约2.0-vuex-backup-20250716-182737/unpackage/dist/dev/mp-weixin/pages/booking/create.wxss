/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-d639e0f6 {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 600rpx;
}
.main-scroll-view.data-v-d639e0f6 {
  height: 100vh;
  box-sizing: border-box;
}
.venue-summary.data-v-d639e0f6 {
  display: flex;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.venue-summary .venue-image.data-v-d639e0f6 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}
.venue-summary .venue-info.data-v-d639e0f6 {
  flex: 1;
}
.venue-summary .venue-info .venue-name.data-v-d639e0f6 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.venue-summary .venue-info .venue-location.data-v-d639e0f6 {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
}
.venue-summary .venue-info .venue-price.data-v-d639e0f6 {
  display: block;
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}
.booking-form .form-section.data-v-d639e0f6 {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}
.booking-form .form-section .section-title.data-v-d639e0f6 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}
.booking-form .form-section .booking-type-display.data-v-d639e0f6 {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}
.booking-form .form-section .booking-type-display .booking-type-text.data-v-d639e0f6 {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff6b35;
}
.booking-form .form-section .time-info.data-v-d639e0f6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}
.booking-form .form-section .time-info .time-text.data-v-d639e0f6 {
  font-size: 28rpx;
  color: #333333;
}
.booking-form .form-section .time-info .change-time-btn.data-v-d639e0f6 {
  padding: 8rpx 16rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}
.booking-form .form-section .form-item.data-v-d639e0f6 {
  margin-bottom: 30rpx;
}
.booking-form .form-section .form-item.data-v-d639e0f6:last-child {
  margin-bottom: 0;
}
.booking-form .form-section .form-item .item-label.data-v-d639e0f6 {
  display: block;
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 12rpx;
}
.booking-form .form-section .form-item .item-label .required.data-v-d639e0f6 {
  color: #ff4d4f;
}
.booking-form .form-section .form-item .form-input.data-v-d639e0f6 {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
}
.booking-form .form-section .form-item .form-input.data-v-d639e0f6:focus {
  border-color: #1890ff;
  background-color: #f6ffed;
}
.booking-form .form-section .form-item .form-input.data-v-d639e0f6::-webkit-input-placeholder {
  color: #cccccc;
}
.booking-form .form-section .form-item .form-input.data-v-d639e0f6::placeholder {
  color: #cccccc;
}
.booking-form .form-section .form-item .form-picker.data-v-d639e0f6 {
  width: 100%;
}
.booking-form .form-section .form-item .form-picker .picker-text.data-v-d639e0f6 {
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  text-align: left;
}
.booking-form .form-section .form-item .form-picker .picker-text.data-v-d639e0f6::after {
  content: "";
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid #999999;
  border-top: 6rpx solid transparent;
  border-bottom: 6rpx solid transparent;
}
.booking-form .form-section .form-item .form-textarea.data-v-d639e0f6 {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  resize: none;
}
.booking-form .form-section .form-item .picker-text.data-v-d639e0f6 {
  padding: 20rpx;
  background-color: #f8f8f8;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
}
.cost-summary.data-v-d639e0f6 {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.cost-summary .summary-title.data-v-d639e0f6 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}
.cost-summary .cost-item.data-v-d639e0f6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666666;
}
.cost-summary .cost-total.data-v-d639e0f6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding: 20rpx 0;
  border-top: 1px solid #f5f5f5;
  font-size: 28rpx;
}
.cost-summary .cost-total .total-amount.data-v-d639e0f6 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.cost-summary .shared-price.data-v-d639e0f6 {
  color: #ff6b00 !important;
  font-weight: bold;
  font-size: 36rpx !important;
}
.cost-summary .info-tip.data-v-d639e0f6 {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
}
.bottom-actions.data-v-d639e0f6 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 999;
}
.bottom-actions .bottom-cost.data-v-d639e0f6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}
.bottom-actions .bottom-cost .cost-label.data-v-d639e0f6 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
}
.bottom-actions .bottom-cost .cost-value.data-v-d639e0f6 {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: 700;
}
.bottom-actions .action-buttons.data-v-d639e0f6 {
  display: flex;
}
.bottom-actions .action-buttons .cancel-btn.data-v-d639e0f6 {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666666;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}
.bottom-actions .action-buttons .confirm-btn.data-v-d639e0f6 {
  flex: 2;
  height: 80rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
}
.bottom-actions .action-buttons .confirm-btn[disabled].data-v-d639e0f6 {
  background-color: #cccccc;
  color: #ffffff;
}
.time-selector.data-v-d639e0f6 {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
}
.time-selector .selector-header.data-v-d639e0f6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.time-selector .selector-header .selector-title.data-v-d639e0f6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.time-selector .selector-header .close-btn.data-v-d639e0f6 {
  font-size: 32rpx;
  color: #999999;
  padding: 8rpx;
}
.time-selector .slots-container.data-v-d639e0f6 {
  padding: 30rpx;
}
.time-selector .slots-container .slots-title.data-v-d639e0f6 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.time-selector .slots-container .slots-grid.data-v-d639e0f6 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}
.time-selector .slots-container .slots-grid .slot-item.data-v-d639e0f6 {
  padding: 20rpx;
  border-radius: 8rpx;
  text-align: center;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s;
  position: relative;
}
.time-selector .slots-container .slots-grid .slot-item.available.data-v-d639e0f6 {
  background: #f6ffed;
  border-color: #b7eb8f;
}
.time-selector .slots-container .slots-grid .slot-item.available.data-v-d639e0f6:active {
  background: #d9f7be;
}
.time-selector .slots-container .slots-grid .slot-item.reserved.data-v-d639e0f6 {
  background: #fff2f0;
  border-color: #ffccc7;
  opacity: 0.6;
  cursor: not-allowed;
}
.time-selector .slots-container .slots-grid .slot-item.reserved .slot-time.data-v-d639e0f6 {
  color: #999999;
}
.time-selector .slots-container .slots-grid .slot-item.reserved .slot-status.data-v-d639e0f6 {
  color: #ff4d4f;
  font-weight: 500;
}
.time-selector .slots-container .slots-grid .slot-item.maintenance.data-v-d639e0f6 {
  background: #fff7e6;
  border-color: #ffd591;
  opacity: 0.6;
  cursor: not-allowed;
}
.time-selector .slots-container .slots-grid .slot-item.maintenance .slot-time.data-v-d639e0f6 {
  color: #999999;
}
.time-selector .slots-container .slots-grid .slot-item.maintenance .slot-status.data-v-d639e0f6 {
  color: #ff9500;
  font-weight: 500;
}
.time-selector .slots-container .slots-grid .slot-item.disabled.data-v-d639e0f6 {
  pointer-events: none;
  position: relative;
}
.time-selector .slots-container .slots-grid .slot-item.disabled.data-v-d639e0f6::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
}
.time-selector .slots-container .slots-grid .slot-item.time-restricted.data-v-d639e0f6 {
  background: #fff1f0;
  border-color: #ffccc7;
  opacity: 0.7;
  cursor: not-allowed;
}
.time-selector .slots-container .slots-grid .slot-item.time-restricted .slot-time.data-v-d639e0f6 {
  color: #999999;
}
.time-selector .slots-container .slots-grid .slot-item.time-restricted .slot-status.data-v-d639e0f6 {
  color: #ff4d4f;
  font-weight: 500;
}
.time-selector .slots-container .slots-grid .slot-item.selected.data-v-d639e0f6 {
  background: #e6f7ff;
  border-color: #91d5ff;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.2);
}
.time-selector .slots-container .slots-grid .slot-item .slot-time.data-v-d639e0f6 {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.time-selector .slots-container .slots-grid .slot-item .slot-status.data-v-d639e0f6 {
  display: block;
  font-size: 22rpx;
  color: #999999;
}
.time-selector .selector-actions.data-v-d639e0f6 {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.time-selector .selector-actions .selector-cancel.data-v-d639e0f6 {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666666;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}
.time-selector .selector-actions .selector-confirm.data-v-d639e0f6 {
  flex: 2;
  height: 80rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
}
.time-selector .selector-actions .selector-confirm[disabled].data-v-d639e0f6 {
  background-color: #cccccc;
  color: #ffffff;
}
.shared-info.data-v-d639e0f6 {
  background-color: #e6f7ff;
  border: 1rpx solid #91d5ff;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}
.shared-info .info-title.data-v-d639e0f6 {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 16rpx;
}
.shared-info .info-title.data-v-d639e0f6::before {
  content: "ℹ";
  margin-right: 8rpx;
  font-size: 28rpx;
}
.shared-info .info-list .info-item.data-v-d639e0f6 {
  display: flex;
  align-items: flex-start;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}
.shared-info .info-list .info-item.data-v-d639e0f6:last-child {
  margin-bottom: 0;
}
.shared-info .info-list .info-item.data-v-d639e0f6::before {
  content: "•";
  margin-right: 8rpx;
  color: #1890ff;
  font-weight: bold;
}
.time-restriction-tip.data-v-d639e0f6 {
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.time-restriction-tip .tip-content.data-v-d639e0f6 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #d46b08;
}
.time-restriction-tip .tip-content.data-v-d639e0f6::before {
  content: "⚠";
  margin-right: 8rpx;
  font-size: 26rpx;
  color: #fa8c16;
}
.sharing-notice.data-v-d639e0f6 {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 2rpx solid #40a9ff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
}
.sharing-notice .notice-header.data-v-d639e0f6 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.sharing-notice .notice-header .notice-icon.data-v-d639e0f6 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.sharing-notice .notice-header .notice-title.data-v-d639e0f6 {
  font-size: 30rpx;
  font-weight: 700;
  color: #1890ff;
  letter-spacing: 1rpx;
}
.sharing-notice .notice-content .notice-item.data-v-d639e0f6 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 12rpx 0;
}
.sharing-notice .notice-content .notice-item.data-v-d639e0f6:last-child {
  margin-bottom: 0;
}
.sharing-notice .notice-content .notice-item .item-icon.data-v-d639e0f6 {
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}
.sharing-notice .notice-content .notice-item .item-text.data-v-d639e0f6 {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.6;
  flex: 1;
}
.time-notice.data-v-d639e0f6 {
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 16rpx;
}
.time-notice .notice-text.data-v-d639e0f6 {
  display: block;
  font-size: 24rpx;
  color: #d46b08;
  line-height: 1.5;
}