/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-f1604f90 {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.search-section.data-v-f1604f90 {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
}
.search-section .search-bar.data-v-f1604f90 {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
}
.search-section .search-bar .search-input.data-v-f1604f90 {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  border: none;
  background: transparent;
}
.search-section .search-bar .search-icon.data-v-f1604f90 {
  font-size: 32rpx;
  color: #666666;
}
.filter-section.data-v-f1604f90 {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.filter-section .filter-scroll.data-v-f1604f90 {
  flex: 1;
  white-space: nowrap;
}
.filter-section .filter-scroll .filter-item.data-v-f1604f90 {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666666;
}
.filter-section .filter-scroll .filter-item.active.data-v-f1604f90 {
  background-color: #ff6b35;
  color: #ffffff;
}
.filter-section .filter-more.data-v-f1604f90 {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666666;
}
.venue-list.data-v-f1604f90 {
  padding: 20rpx 30rpx;
}
.venue-list .venue-card.data-v-f1604f90 {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.venue-list .venue-card .venue-image.data-v-f1604f90 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}
.venue-list .venue-card .venue-info.data-v-f1604f90 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.venue-list .venue-card .venue-info .venue-header.data-v-f1604f90 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}
.venue-list .venue-card .venue-info .venue-header .venue-name.data-v-f1604f90 {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 20rpx;
}
.venue-list .venue-card .venue-info .venue-header .venue-rating.data-v-f1604f90 {
  display: flex;
  align-items: center;
}
.venue-list .venue-card .venue-info .venue-header .venue-rating .rating-score.data-v-f1604f90 {
  font-size: 24rpx;
  color: #ff6b35;
  margin-right: 4rpx;
}
.venue-list .venue-card .venue-info .venue-header .venue-rating .rating-star.data-v-f1604f90 {
  font-size: 20rpx;
}
.venue-list .venue-card .venue-info .venue-location.data-v-f1604f90 {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 16rpx;
}
.venue-list .venue-card .venue-info .venue-tags.data-v-f1604f90 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.venue-list .venue-card .venue-info .venue-tags .venue-tag.data-v-f1604f90 {
  font-size: 20rpx;
  color: #999999;
  background-color: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}
.venue-list .venue-card .venue-info .venue-tags .sharing-tag.data-v-f1604f90 {
  background-color: #e8f5e8;
  color: #52c41a;
}
.venue-list .venue-card .venue-info .venue-footer.data-v-f1604f90 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.venue-list .venue-card .venue-info .venue-footer .venue-price.data-v-f1604f90 {
  display: flex;
  align-items: baseline;
}
.venue-list .venue-card .venue-info .venue-footer .venue-price .price-text.data-v-f1604f90 {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
}
.venue-list .venue-card .venue-info .venue-footer .venue-price .price-unit.data-v-f1604f90 {
  font-size: 24rpx;
  color: #999999;
  margin-left: 4rpx;
}
.venue-list .venue-card .venue-info .venue-footer .venue-status.data-v-f1604f90 {
  font-size: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
}
.venue-list .venue-card .venue-info .venue-footer .venue-status.status-available.data-v-f1604f90 {
  background-color: #e6f7ff;
  color: #1890ff;
}
.venue-list .venue-card .venue-info .venue-footer .venue-status.status-maintenance.data-v-f1604f90 {
  background-color: #fff7e6;
  color: #fa8c16;
}
.venue-list .venue-card .venue-info .venue-footer .venue-status.status-occupied.data-v-f1604f90 {
  background-color: #fff2f0;
  color: #ff4d4f;
}
.load-more.data-v-f1604f90 {
  text-align: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #666666;
}
.filter-modal.data-v-f1604f90 {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
}
.filter-modal .modal-header.data-v-f1604f90 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.filter-modal .modal-header .modal-title.data-v-f1604f90 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.filter-modal .modal-header .modal-close.data-v-f1604f90 {
  font-size: 32rpx;
  color: #999999;
}
.filter-modal .filter-content.data-v-f1604f90 {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.filter-modal .filter-content .filter-group.data-v-f1604f90 {
  margin-bottom: 40rpx;
}
.filter-modal .filter-content .filter-group .group-title.data-v-f1604f90 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}
.filter-modal .filter-content .filter-group .price-range.data-v-f1604f90 {
  display: flex;
  align-items: center;
}
.filter-modal .filter-content .filter-group .price-range .price-input.data-v-f1604f90 {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.filter-modal .filter-content .filter-group .price-range .price-separator.data-v-f1604f90 {
  margin: 0 20rpx;
  color: #999999;
}
.filter-modal .filter-content .filter-group .distance-options.data-v-f1604f90 {
  display: flex;
  flex-wrap: wrap;
}
.filter-modal .filter-content .filter-group .distance-options .distance-item.data-v-f1604f90 {
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666666;
}
.filter-modal .filter-content .filter-group .distance-options .distance-item.active.data-v-f1604f90 {
  background-color: #ff6b35;
  color: #ffffff;
}
.filter-modal .filter-content .filter-group .facility-options.data-v-f1604f90 {
  display: flex;
  flex-wrap: wrap;
}
.filter-modal .filter-content .filter-group .facility-options .facility-item.data-v-f1604f90 {
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666666;
}
.filter-modal .filter-content .filter-group .facility-options .facility-item.active.data-v-f1604f90 {
  background-color: #ff6b35;
  color: #ffffff;
}
.filter-modal .modal-footer.data-v-f1604f90 {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.filter-modal .modal-footer .reset-btn.data-v-f1604f90 {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666666;
  border: none;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}
.filter-modal .modal-footer .confirm-btn.data-v-f1604f90 {
  flex: 2;
  height: 80rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}