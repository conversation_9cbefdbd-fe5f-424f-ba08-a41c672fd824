/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-7c8355cf {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.image-section.data-v-7c8355cf {
  position: relative;
  height: 500rpx;
}
.image-section .venue-swiper.data-v-7c8355cf {
  height: 100%;
}
.image-section .venue-swiper .venue-image.data-v-7c8355cf {
  width: 100%;
  height: 100%;
}
.image-section .back-btn.data-v-7c8355cf {
  position: absolute;
  top: 60rpx;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  z-index: 10;
}
.info-section.data-v-7c8355cf {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.info-section .venue-header.data-v-7c8355cf {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.info-section .venue-header .venue-name.data-v-7c8355cf {
  flex: 1;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 20rpx;
}
.info-section .venue-header .venue-rating.data-v-7c8355cf {
  display: flex;
  align-items: center;
}
.info-section .venue-header .venue-rating .rating-score.data-v-7c8355cf {
  font-size: 28rpx;
  color: #ff6b35;
  margin-right: 8rpx;
}
.info-section .venue-header .venue-rating .rating-star.data-v-7c8355cf {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.info-section .venue-header .venue-rating .rating-count.data-v-7c8355cf {
  font-size: 24rpx;
  color: #999999;
}
.info-section .venue-location.data-v-7c8355cf {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.info-section .venue-location .location-icon.data-v-7c8355cf {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.info-section .venue-location .location-text.data-v-7c8355cf {
  flex: 1;
  font-size: 28rpx;
  color: #666666;
}
.info-section .venue-location .distance-text.data-v-7c8355cf {
  font-size: 24rpx;
  color: #999999;
}
.info-section .venue-price.data-v-7c8355cf {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.info-section .venue-price .price-label.data-v-7c8355cf {
  font-size: 28rpx;
  color: #333333;
}
.info-section .venue-price .price-value.data-v-7c8355cf {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff6b35;
  margin: 0 8rpx;
}
.info-section .venue-price .price-unit.data-v-7c8355cf {
  font-size: 24rpx;
  color: #999999;
}
.info-section .venue-tags.data-v-7c8355cf {
  display: flex;
  flex-wrap: wrap;
}
.info-section .venue-tags .venue-tag.data-v-7c8355cf {
  font-size: 22rpx;
  color: #666666;
  background-color: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.description-section.data-v-7c8355cf,
.facilities-section.data-v-7c8355cf,
.hours-section.data-v-7c8355cf {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.description-section .section-title.data-v-7c8355cf,
.facilities-section .section-title.data-v-7c8355cf,
.hours-section .section-title.data-v-7c8355cf {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.description-text.data-v-7c8355cf {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}
.facilities-grid.data-v-7c8355cf {
  display: flex;
  flex-wrap: wrap;
}
.facilities-grid .facility-item.data-v-7c8355cf {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.facilities-grid .facility-item .facility-icon.data-v-7c8355cf {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}
.facilities-grid .facility-item .facility-name.data-v-7c8355cf {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}
.hours-info .hours-text.data-v-7c8355cf {
  font-size: 28rpx;
  color: #666666;
}
.timeslot-section.data-v-7c8355cf {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.timeslot-section .section-title.data-v-7c8355cf {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
}
.timeslot-section .date-selector.data-v-7c8355cf {
  margin-bottom: 30rpx;
}
.timeslot-section .date-selector .date-scroll.data-v-7c8355cf {
  white-space: nowrap;
}
.timeslot-section .date-selector .date-scroll .date-item.data-v-7c8355cf {
  display: inline-block;
  text-align: center;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  min-width: 120rpx;
}
.timeslot-section .date-selector .date-scroll .date-item.active.data-v-7c8355cf {
  background-color: #ff6b35;
  color: #ffffff;
}
.timeslot-section .date-selector .date-scroll .date-item .date-day.data-v-7c8355cf {
  display: block;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}
.timeslot-section .date-selector .date-scroll .date-item .date-date.data-v-7c8355cf {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
}
.timeslot-section .timeslot-list .timeslot-item.data-v-7c8355cf {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  position: relative;
}
.timeslot-section .timeslot-list .timeslot-item.selected.data-v-7c8355cf {
  background-color: #fff7f0;
  border-color: #ff6b35;
}
.timeslot-section .timeslot-list .timeslot-item.occupied.data-v-7c8355cf {
  background-color: #f5f5f5;
  opacity: 0.6;
  cursor: not-allowed;
}
.timeslot-section .timeslot-list .timeslot-item.occupied .slot-status.data-v-7c8355cf {
  color: #999999;
  font-weight: 500;
}
.timeslot-section .timeslot-list .timeslot-item.occupied .slot-time.data-v-7c8355cf {
  color: #999999;
}
.timeslot-section .timeslot-list .timeslot-item.occupied .slot-price.data-v-7c8355cf {
  color: #cccccc;
}
.timeslot-section .timeslot-list .timeslot-item.maintenance.data-v-7c8355cf {
  background-color: #fff7e6;
  opacity: 0.8;
  cursor: not-allowed;
}
.timeslot-section .timeslot-list .timeslot-item.maintenance .slot-status.data-v-7c8355cf {
  color: #ff9500;
  font-weight: 500;
}
.timeslot-section .timeslot-list .timeslot-item.expired.data-v-7c8355cf {
  background-color: #f0f0f0;
  opacity: 0.5;
  cursor: not-allowed;
}
.timeslot-section .timeslot-list .timeslot-item.expired .slot-status.data-v-7c8355cf {
  color: #999999;
  font-weight: 500;
}
.timeslot-section .timeslot-list .timeslot-item.expired .slot-time.data-v-7c8355cf {
  color: #cccccc;
  text-decoration: line-through;
}
.timeslot-section .timeslot-list .timeslot-item.expired .slot-price.data-v-7c8355cf {
  color: #cccccc;
  text-decoration: line-through;
}
.timeslot-section .timeslot-list .timeslot-item.disabled.data-v-7c8355cf {
  pointer-events: none;
}
.timeslot-section .timeslot-list .timeslot-item.disabled.data-v-7c8355cf::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
}
.timeslot-section .timeslot-list .timeslot-item .slot-time.data-v-7c8355cf {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.timeslot-section .timeslot-list .timeslot-item .slot-price.data-v-7c8355cf {
  margin-right: 30rpx;
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}
.timeslot-section .timeslot-list .timeslot-item .slot-status.data-v-7c8355cf {
  font-size: 24rpx;
  color: #666666;
}
.timeslot-section .booking-type-section.data-v-7c8355cf {
  margin-bottom: 32rpx;
}
.timeslot-section .booking-type-section .booking-type-options.data-v-7c8355cf {
  margin-top: 24rpx;
}
.timeslot-section .booking-type-section .radio-item.data-v-7c8355cf {
  display: block;
  margin-bottom: 24rpx;
}
.timeslot-section .booking-type-section .radio-item .radio-wrapper.data-v-7c8355cf {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}
.timeslot-section .booking-type-section .radio-item .radio-wrapper.data-v-7c8355cf:active {
  background-color: #f0f0f0;
}
.timeslot-section .booking-type-section .radio-item .radio-circle.data-v-7c8355cf {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}
.timeslot-section .booking-type-section .radio-item .radio-circle.active.data-v-7c8355cf {
  border-color: #ff6b35;
  background-color: #ff6b35;
}
.timeslot-section .booking-type-section .radio-item .radio-circle .radio-dot.data-v-7c8355cf {
  width: 16rpx;
  height: 16rpx;
  background-color: white;
  border-radius: 50%;
}
.timeslot-section .booking-type-section .radio-item .radio-content.data-v-7c8355cf {
  flex: 1;
}
.timeslot-section .booking-type-section .radio-item .radio-content .radio-title.data-v-7c8355cf {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.timeslot-section .booking-type-section .radio-item .radio-content .radio-desc.data-v-7c8355cf {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.timeslot-section .booking-type-section .shared-form.data-v-7c8355cf {
  margin-top: 32rpx;
  padding: 32rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}
.timeslot-section .booking-type-section .shared-form .form-item.data-v-7c8355cf {
  margin-bottom: 32rpx;
}
.timeslot-section .booking-type-section .shared-form .form-item.data-v-7c8355cf:last-child {
  margin-bottom: 0;
}
.timeslot-section .booking-type-section .shared-form .form-item .item-label.data-v-7c8355cf {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.timeslot-section .booking-type-section .shared-form .form-item .form-input.data-v-7c8355cf {
  width: 100%;
  padding: 24rpx;
  background-color: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
}
.timeslot-section .booking-type-section .shared-form .form-item .form-input.data-v-7c8355cf:focus {
  border-color: #ff6b35;
}
.timeslot-section .booking-type-section .shared-form .form-item .picker-text.data-v-7c8355cf {
  padding: 24rpx;
  background-color: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.timeslot-section .booking-type-section .shared-form .form-item .picker-text.data-v-7c8355cf::after {
  content: ">";
  color: #999;
  font-size: 24rpx;
}
.timeslot-section .booking-type-section .shared-form .sharing-notice.data-v-7c8355cf {
  background-color: #fff3e0;
  border: 2rpx solid #ffcc80;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.timeslot-section .booking-type-section .shared-form .sharing-notice .notice-title.data-v-7c8355cf {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #e65100;
  margin-bottom: 16rpx;
}
.timeslot-section .booking-type-section .shared-form .sharing-notice .notice-text.data-v-7c8355cf {
  display: block;
  font-size: 26rpx;
  color: #bf360c;
  line-height: 1.5;
  margin-bottom: 8rpx;
}
.timeslot-section .booking-type-section .shared-form .sharing-notice .notice-text.data-v-7c8355cf:last-child {
  margin-bottom: 0;
}
.timeslot-section .booking-type-section .shared-form .time-notice.data-v-7c8355cf {
  background-color: #e3f2fd;
  border: 2rpx solid #90caf9;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
}
.timeslot-section .booking-type-section .shared-form .time-notice .notice-text.data-v-7c8355cf {
  font-size: 26rpx;
  color: #1565c0;
  line-height: 1.4;
}