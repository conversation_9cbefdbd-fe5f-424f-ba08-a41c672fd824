"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "VenueDetail",
  data() {
    return {
      venueId: "",
      selectedDate: "",
      selectedTimeSlots: [],
      // 改为数组以支持多时间段选择
      availableDates: [],
      bookingType: "EXCLUSIVE",
      // 预约类型：EXCLUSIVE(独享) 或 SHARED(拼场)
      teamName: "",
      // 拼场队伍名称
      contactInfo: "",
      // 拼场联系方式
      maxParticipants: 4
      // 最大参与人数
    };
  },
  computed: {
    ...common_vendor.mapGetters("venue", ["venueDetail", "timeSlots", "loading"]),
    // 过滤掉已过期的时间段
    filteredTimeSlots() {
      const slots = this.timeSlots.filter((slot) => slot.status !== "EXPIRED");
      return slots;
    }
  },
  onLoad(options) {
    this.venueId = options.id;
    this.initData();
  },
  onShow() {
    if (this.venueId && this.selectedDate) {
      setTimeout(() => {
        this.loadTimeSlots();
      }, 200);
    }
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  methods: {
    ...common_vendor.mapActions("venue", ["getVenueDetail", "getVenueTimeSlots"]),
    ...common_vendor.mapActions("booking", ["createBooking"]),
    // 初始化数据
    async initData() {
      try {
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:353", "开始初始化数据，场馆ID:", this.venueId);
        if (!this.venueId) {
          common_vendor.index.__f__("error", "at pages/venue/detail.vue:355", "场馆ID为空");
          common_vendor.index.showToast({
            title: "参数错误",
            icon: "error"
          });
          return;
        }
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:363", "调用getVenueDetail，参数:", this.venueId);
        await this.getVenueDetail(this.venueId);
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:365", "获取场馆详情成功:", this.venueDetail);
        this.initDates();
        if (this.selectedDate) {
          await this.loadTimeSlots();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:372", "初始化数据失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "error"
        });
      }
    },
    // 刷新数据
    async refreshData() {
      try {
        await this.initData();
        common_vendor.index.stopPullDownRefresh();
      } catch (error) {
        common_vendor.index.stopPullDownRefresh();
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:387", "刷新数据失败:", error);
      }
    },
    // 初始化可选日期
    initDates() {
      const dates = [];
      const today = /* @__PURE__ */ new Date();
      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dayNames = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        const day = i === 0 ? "今天" : i === 1 ? "明天" : dayNames[date.getDay()];
        dates.push({
          value: utils_helpers.formatDate(date, "YYYY-MM-DD"),
          day,
          date: utils_helpers.formatDate(date, "MM/DD")
        });
      }
      this.availableDates = dates;
      this.selectedDate = dates[0].value;
    },
    // 选择日期
    async selectDate(date) {
      this.selectedDate = date;
      this.selectedTimeSlots = [];
      await this.loadTimeSlots();
    },
    // 预约类型变化
    onBookingTypeChange(type) {
      this.bookingType = type;
      this.selectedTimeSlots = [];
    },
    // 加载时间段
    async loadTimeSlots() {
      try {
        await this.getVenueTimeSlots({
          venueId: this.venueId,
          date: this.selectedDate
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:437", "加载时间段失败:", error);
      }
    },
    // 选择时间段
    selectTimeSlot(slot) {
      common_vendor.index.__f__("log", "at pages/venue/detail.vue:443", "点击时间段:", slot);
      common_vendor.index.__f__("log", "at pages/venue/detail.vue:444", "时间段状态:", slot.status);
      if (slot.status === "OCCUPIED" || slot.status === "RESERVED") {
        common_vendor.index.showToast({
          title: "该时间段已被预约",
          icon: "none",
          duration: 2e3
        });
        return;
      } else if (slot.status === "MAINTENANCE") {
        common_vendor.index.showToast({
          title: "该时间段维护中",
          icon: "none",
          duration: 2e3
        });
        return;
      } else if (slot.status === "EXPIRED") {
        common_vendor.index.showToast({
          title: "该时间段已过期，无法预约",
          icon: "none",
          duration: 2e3
        });
        return;
      } else if (slot.status === "AVAILABLE") {
        if (this.bookingType === "SHARED") {
          if (!this.isTimeSlotValidForSharing(slot)) {
            common_vendor.index.showToast({
              title: "拼场预约请选择三个小时以后的时间段",
              icon: "none",
              duration: 3e3
            });
            return;
          }
        }
        const existingIndex = this.selectedTimeSlots.findIndex(
          (item) => item.id && item.id === slot.id || item.startTime === slot.startTime && item.endTime === slot.endTime
        );
        if (existingIndex !== -1) {
          this.selectedTimeSlots.splice(existingIndex, 1);
          common_vendor.index.__f__("log", "at pages/venue/detail.vue:489", "取消选择时间段:", slot);
          common_vendor.index.showToast({
            title: "已取消选择",
            icon: "success",
            duration: 1e3
          });
          return;
        }
        if (this.selectedTimeSlots.length > 0) {
          const hasConsecutive = this.selectedTimeSlots.some(
            (selectedSlot) => this.isConsecutiveTimeSlot(selectedSlot, slot)
          );
          if (!hasConsecutive) {
            common_vendor.index.showToast({
              title: "只能选择连续的时间段",
              icon: "none",
              duration: 2e3
            });
            return;
          }
        }
        this.selectedTimeSlots.push(slot);
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:518", "已选择时间段:", this.selectedTimeSlots);
        common_vendor.index.showToast({
          title: "已选择时间段",
          icon: "success",
          duration: 1e3
        });
      } else {
        common_vendor.index.showToast({
          title: "该时间段不可用",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 检查两个时间段是否连续
    isConsecutiveTimeSlot(slot1, slot2) {
      const slot1End = this.getMinutesFromTimeString(slot1.endTime);
      const slot2Start = this.getMinutesFromTimeString(slot2.startTime);
      const slot1Start = this.getMinutesFromTimeString(slot1.startTime);
      const slot2End = this.getMinutesFromTimeString(slot2.endTime);
      return slot1End === slot2Start || slot2End === slot1Start;
    },
    // 获取时间段样式类
    getSlotClass(slot) {
      const classes = ["timeslot-item"];
      if (slot.status === "OCCUPIED") {
        classes.push("occupied");
        classes.push("disabled");
      } else if (slot.status === "RESERVED") {
        classes.push("occupied");
        classes.push("disabled");
      } else if (slot.status === "MAINTENANCE") {
        classes.push("maintenance");
        classes.push("disabled");
      } else if (slot.status === "EXPIRED") {
        classes.push("expired");
        classes.push("disabled");
      }
      const isSelected = this.selectedTimeSlots.some(
        (selectedSlot) => slot.id && selectedSlot.id === slot.id || slot.startTime === selectedSlot.startTime && slot.endTime === selectedSlot.endTime
      );
      if (isSelected) {
        classes.push("selected");
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:572", "添加选中样式:", slot);
      }
      return classes.join(" ");
    },
    // 获取第一个时间段（按开始时间排序）
    getFirstTimeSlot() {
      if (this.selectedTimeSlots.length === 0)
        return null;
      return this.selectedTimeSlots.reduce((earliest, current) => {
        const earliestTime = this.getMinutesFromTimeString(earliest.startTime);
        const currentTime = this.getMinutesFromTimeString(current.startTime);
        return currentTime < earliestTime ? current : earliest;
      }, this.selectedTimeSlots[0]);
    },
    // 获取最后一个时间段（按结束时间排序）
    getLastTimeSlot() {
      if (this.selectedTimeSlots.length === 0)
        return null;
      return this.selectedTimeSlots.reduce((latest, current) => {
        const latestTime = this.getMinutesFromTimeString(latest.endTime);
        const currentTime = this.getMinutesFromTimeString(current.endTime);
        return currentTime > latestTime ? current : latest;
      }, this.selectedTimeSlots[0]);
    },
    // 将时间字符串转换为分钟数
    getMinutesFromTimeString(timeStr) {
      const [hours, minutes] = timeStr.split(":").map(Number);
      return hours * 60 + minutes;
    },
    // 检查时间段是否满足拼场预约的时间限制（需要提前3小时）
    isTimeSlotValidForSharing(slot) {
      const now = /* @__PURE__ */ new Date();
      const selectedDateTime = /* @__PURE__ */ new Date(`${this.selectedDate} ${slot.startTime}`);
      const timeDiff = selectedDateTime.getTime() - now.getTime();
      const hoursDiff = timeDiff / (1e3 * 60 * 60);
      return hoursDiff >= 3;
    },
    // 计算总价格
    getTotalPrice() {
      if (this.selectedTimeSlots.length === 0)
        return 0;
      return this.selectedTimeSlots.reduce((total, slot) => {
        return total + (slot.price || 0);
      }, 0);
    },
    // 获取时间段状态文本
    getSlotStatusText(status) {
      const statusMap = {
        "AVAILABLE": "可预约",
        "OCCUPIED": "已预约",
        "RESERVED": "已预约",
        "MAINTENANCE": "维护中",
        "EXPIRED": "已过期"
      };
      return statusMap[status] || "可预约";
    },
    // 获取预约按钮文本
    getBookButtonText() {
      if (this.selectedTimeSlots.length === 0) {
        return "请选择时间段";
      }
      return `预约 ${this.selectedTimeSlots.length} 个时间段`;
    },
    // 预约场馆
    bookVenue() {
      if (this.selectedTimeSlots.length === 0) {
        common_vendor.index.showToast({
          title: "请选择时间段",
          icon: "none"
        });
        return;
      }
      const selectedSlotsData = JSON.stringify(this.selectedTimeSlots);
      common_vendor.index.navigateTo({
        url: `/pages/booking/create?venueId=${this.venueDetail.id}&date=${this.selectedDate}&bookingType=${this.bookingType}&selectedSlots=${encodeURIComponent(selectedSlotsData)}`
      });
    },
    // 关闭预约弹窗
    closeBookingModal() {
      this.$refs.bookingPopup.close();
    },
    // 确认预约
    async confirmBooking() {
      try {
        if (this.selectedTimeSlots.length === 0) {
          common_vendor.index.showToast({
            title: "请选择时间段",
            icon: "none"
          });
          return;
        }
        common_vendor.index.showLoading({ title: "预约中..." });
        const sortedSlots = [...this.selectedTimeSlots].sort((a, b) => {
          const aTime = this.getMinutesFromTimeString(a.startTime);
          const bTime = this.getMinutesFromTimeString(b.startTime);
          return aTime - bTime;
        });
        const startTime = sortedSlots[0].startTime;
        const endTime = sortedSlots[sortedSlots.length - 1].endTime;
        const slotIds = this.selectedTimeSlots.map((slot) => slot.id);
        const bookingData = {
          venueId: this.venueId,
          date: this.selectedDate,
          startTime,
          endTime,
          slotIds,
          // 传递所有时间段ID
          remark: this.selectedTimeSlots.length > 1 ? `连续预约${this.selectedTimeSlots.length}个时间段` : "",
          bookingType: this.bookingType
        };
        await this.createBooking(bookingData);
        await this.getVenueTimeSlots({
          venueId: this.venueId,
          date: this.selectedDate
        });
        common_vendor.index.hideLoading();
        this.closeBookingModal();
        common_vendor.index.showToast({
          title: "预约成功",
          icon: "success"
        });
        this.selectedTimeSlots = [];
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/booking/list"
          });
        }, 1500);
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:743", "预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "预约失败",
          icon: "error"
        });
      }
    },
    // 联系场馆
    contactVenue() {
      if (this.venueDetail.phone) {
        common_vendor.index.makePhoneCall({
          phoneNumber: this.venueDetail.phone
        });
      } else {
        common_vendor.index.showToast({
          title: "暂无联系方式",
          icon: "none"
        });
      }
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 格式化选中日期
    formatSelectedDate() {
      const selectedDateObj = this.availableDates.find((d) => d.value === this.selectedDate);
      return selectedDateObj ? `${selectedDateObj.day} ${selectedDateObj.date}` : this.selectedDate;
    },
    // 计算预约时长
    getBookingDuration() {
      if (this.selectedTimeSlots.length === 0) {
        return "0小时";
      }
      if (this.selectedTimeSlots.length === 1) {
        const slot = this.selectedTimeSlots[0];
        const startMinutes = this.getMinutesFromTimeString(slot.startTime);
        const endMinutes = this.getMinutesFromTimeString(slot.endTime);
        const durationMinutes = endMinutes - startMinutes;
        const hours = Math.floor(durationMinutes / 60);
        const minutes = durationMinutes % 60;
        if (minutes === 0) {
          return `${hours}小时`;
        } else {
          return `${hours}小时${minutes}分钟`;
        }
      } else {
        const firstSlot = this.getFirstTimeSlot();
        const lastSlot = this.getLastTimeSlot();
        if (firstSlot && lastSlot) {
          const startMinutes = this.getMinutesFromTimeString(firstSlot.startTime);
          const endMinutes = this.getMinutesFromTimeString(lastSlot.endTime);
          const durationMinutes = endMinutes - startMinutes;
          const hours = Math.floor(durationMinutes / 60);
          const minutes = durationMinutes % 60;
          if (minutes === 0) {
            return `${hours}小时`;
          } else {
            return `${hours}小时${minutes}分钟`;
          }
        }
        return "0小时";
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d;
  return common_vendor.e({
    a: _ctx.loading
  }, _ctx.loading ? {} : _ctx.venueDetail ? common_vendor.e({
    c: common_vendor.f(_ctx.venueDetail.images, (image, index, i0) => {
      return {
        a: image,
        b: index
      };
    }),
    d: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    e: common_vendor.t(_ctx.venueDetail.name),
    f: common_vendor.t(_ctx.venueDetail.rating),
    g: common_vendor.t(_ctx.venueDetail.reviewCount),
    h: common_vendor.t(_ctx.venueDetail.location),
    i: common_vendor.t(_ctx.venueDetail.distance),
    j: common_vendor.t(_ctx.venueDetail.price),
    k: common_vendor.f(_ctx.venueDetail.tags, (tag, k0, i0) => {
      return {
        a: common_vendor.t(tag),
        b: tag
      };
    }),
    l: common_vendor.t(_ctx.venueDetail.description),
    m: common_vendor.f(_ctx.venueDetail.facilities, (facility, k0, i0) => {
      return {
        a: common_vendor.t(facility.icon),
        b: common_vendor.t(facility.name),
        c: facility.name
      };
    }),
    n: common_vendor.t(_ctx.venueDetail.openingHours),
    o: _ctx.venueDetail.supportSharing
  }, _ctx.venueDetail.supportSharing ? common_vendor.e({
    p: $data.bookingType === "EXCLUSIVE"
  }, $data.bookingType === "EXCLUSIVE" ? {} : {}, {
    q: $data.bookingType === "EXCLUSIVE" ? 1 : "",
    r: common_vendor.o(($event) => $options.onBookingTypeChange("EXCLUSIVE")),
    s: $data.bookingType === "SHARED"
  }, $data.bookingType === "SHARED" ? {} : {}, {
    t: $data.bookingType === "SHARED" ? 1 : "",
    v: common_vendor.o(($event) => $options.onBookingTypeChange("SHARED")),
    w: $data.bookingType === "SHARED"
  }, $data.bookingType === "SHARED" ? {} : {}) : {}, {
    x: common_vendor.f($data.availableDates, (date, index, i0) => {
      return {
        a: common_vendor.t(date.day),
        b: common_vendor.t(date.date),
        c: index,
        d: $data.selectedDate === date.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectDate(date.value), index)
      };
    }),
    y: common_vendor.f($options.filteredTimeSlots, (slot, k0, i0) => {
      return {
        a: common_vendor.t(slot.startTime),
        b: common_vendor.t(slot.endTime),
        c: common_vendor.t(slot.price),
        d: common_vendor.t($options.getSlotStatusText(slot.status)),
        e: slot.id,
        f: common_vendor.n($options.getSlotClass(slot)),
        g: common_vendor.o(($event) => $options.selectTimeSlot(slot), slot.id)
      };
    }),
    z: common_vendor.o((...args) => $options.contactVenue && $options.contactVenue(...args)),
    A: common_vendor.t($options.getBookButtonText()),
    B: $data.selectedTimeSlots.length === 0,
    C: common_vendor.o((...args) => $options.bookVenue && $options.bookVenue(...args)),
    D: common_vendor.t(_ctx.venueDetail.name),
    E: common_vendor.t(_ctx.venueDetail.location),
    F: common_vendor.t($options.formatSelectedDate()),
    G: $data.selectedTimeSlots.length === 1
  }, $data.selectedTimeSlots.length === 1 ? {
    H: common_vendor.t((_a = $data.selectedTimeSlots[0]) == null ? void 0 : _a.startTime),
    I: common_vendor.t((_b = $data.selectedTimeSlots[0]) == null ? void 0 : _b.endTime)
  } : $data.selectedTimeSlots.length > 1 ? {
    K: common_vendor.t((_c = $options.getFirstTimeSlot()) == null ? void 0 : _c.startTime),
    L: common_vendor.t((_d = $options.getLastTimeSlot()) == null ? void 0 : _d.endTime)
  } : {}, {
    J: $data.selectedTimeSlots.length > 1,
    M: common_vendor.t($options.getBookingDuration()),
    N: common_vendor.t($data.bookingType === "EXCLUSIVE" ? "独享预约" : "拼场预约"),
    O: $data.bookingType === "SHARED"
  }, $data.bookingType === "SHARED" ? {
    P: common_vendor.t($data.teamName),
    Q: common_vendor.t($data.contactInfo),
    R: common_vendor.t($data.maxParticipants)
  } : {}, {
    S: common_vendor.f($data.selectedTimeSlots, (slot, index, i0) => {
      return {
        a: common_vendor.t(slot.startTime),
        b: common_vendor.t(slot.endTime),
        c: common_vendor.t(slot.price),
        d: index
      };
    }),
    T: common_vendor.t($options.getTotalPrice()),
    U: _ctx.venueDetail.phone
  }, _ctx.venueDetail.phone ? {
    V: common_vendor.t(_ctx.venueDetail.phone)
  } : {}, {
    W: common_vendor.o((...args) => $options.closeBookingModal && $options.closeBookingModal(...args)),
    X: common_vendor.o((...args) => $options.confirmBooking && $options.confirmBooking(...args)),
    Y: common_vendor.sr("bookingPopup", "7c8355cf-0"),
    Z: common_vendor.p({
      type: "center"
    })
  }) : {
    aa: common_vendor.o((...args) => $options.initData && $options.initData(...args))
  }, {
    b: _ctx.venueDetail
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7c8355cf"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/venue/detail.js.map
