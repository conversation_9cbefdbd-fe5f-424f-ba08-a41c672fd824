<view class="container data-v-5560101a"><view class="navbar data-v-5560101a"><view class="nav-left data-v-5560101a" bindtap="{{a}}"><text class="nav-icon data-v-5560101a">‹</text></view><text class="nav-title data-v-5560101a">创建拼场</text><view class="nav-right data-v-5560101a"></view></view><view class="form-container data-v-5560101a"><view class="form-section data-v-5560101a"><view class="section-title data-v-5560101a">选择预约</view><view class="booking-selector data-v-5560101a" bindtap="{{g}}"><view wx:if="{{b}}" class="booking-info data-v-5560101a"><view class="booking-venue data-v-5560101a"><text class="venue-name data-v-5560101a">{{c}}</text><text class="booking-status data-v-5560101a">{{d}}</text></view><view class="booking-time data-v-5560101a"><text class="time-text data-v-5560101a">{{e}}</text></view><view class="booking-price data-v-5560101a"><text class="price-text data-v-5560101a">总费用：¥{{f}}</text></view></view><view wx:else class="booking-placeholder data-v-5560101a"><text class="placeholder-icon data-v-5560101a">+</text><text class="placeholder-text data-v-5560101a">点击选择已确认的预约</text></view><text class="selector-arrow data-v-5560101a">></text></view></view><view class="form-section data-v-5560101a"><view class="section-title data-v-5560101a">队伍信息</view><view class="form-card data-v-5560101a"><view class="form-item data-v-5560101a"><text class="form-label data-v-5560101a">队伍名称</text><input class="form-input data-v-5560101a" placeholder="请输入队伍名称" maxlength="20" value="{{h}}" bindinput="{{i}}"/></view><view class="form-item data-v-5560101a"><text class="form-label data-v-5560101a">拼场模式</text><view class="mode-display data-v-5560101a"><text class="mode-text data-v-5560101a">两支球队对战</text><text class="mode-desc data-v-5560101a">固定2支球队参与</text></view></view><view class="form-item data-v-5560101a"><text class="form-label data-v-5560101a">每队费用</text><view class="price-input-wrapper data-v-5560101a"><text class="price-symbol data-v-5560101a">¥</text><input class="price-input data-v-5560101a" type="digit" placeholder="0" value="{{j}}" bindinput="{{k}}"/></view></view><view class="form-item description-item data-v-5560101a"><text class="form-label data-v-5560101a">活动描述</text><block wx:if="{{r0}}"><textarea class="form-textarea data-v-5560101a" placeholder="请描述活动内容、要求等（选填）" maxlength="200" value="{{l}}" bindinput="{{m}}"/></block><view class="char-count data-v-5560101a"><text class="count-text data-v-5560101a">{{n}}/200</text></view></view></view></view><view class="form-section data-v-5560101a"><view class="section-title data-v-5560101a">联系方式</view><view class="form-card data-v-5560101a"><view class="form-item data-v-5560101a"><text class="form-label data-v-5560101a">联系电话</text><input class="form-input data-v-5560101a" type="number" placeholder="请输入联系电话" maxlength="11" value="{{o}}" bindinput="{{p}}"/></view><view class="form-item data-v-5560101a"><text class="form-label data-v-5560101a">微信号</text><input class="form-input data-v-5560101a" placeholder="请输入微信号（选填）" maxlength="30" value="{{q}}" bindinput="{{r}}"/></view></view></view><view class="form-section data-v-5560101a"><view class="section-title data-v-5560101a">拼场设置</view><view class="form-card data-v-5560101a"><view class="form-item switch-item data-v-5560101a"><view class="switch-info data-v-5560101a"><text class="switch-label data-v-5560101a">自动通过申请</text><text class="switch-desc data-v-5560101a">开启后，其他用户申请加入时将自动通过</text></view><switch class="data-v-5560101a" checked="{{s}}" bindchange="{{t}}" color="#ff6b35"/></view><view class="form-item switch-item data-v-5560101a"><view class="switch-info data-v-5560101a"><text class="switch-label data-v-5560101a">允许中途退出</text><text class="switch-desc data-v-5560101a">开启后，参与者可以在活动开始前退出</text></view><switch class="data-v-5560101a" checked="{{v}}" bindchange="{{w}}" color="#ff6b35"/></view></view></view></view><view class="bottom-actions data-v-5560101a"><view class="price-summary data-v-5560101a"><text class="summary-label data-v-5560101a">预计总费用</text><text class="summary-price data-v-5560101a">¥{{x}}</text></view><button class="{{['create-btn', 'data-v-5560101a', y && 'disabled']}}" bindtap="{{z}}"> 创建拼场 </button></view><uni-popup wx:if="{{I}}" class="r data-v-5560101a" u-s="{{['d']}}" u-r="bookingPopup" u-i="5560101a-0" bind:__l="__l" u-p="{{I}}"><view class="booking-modal data-v-5560101a"><view class="modal-header data-v-5560101a"><text class="modal-title data-v-5560101a">选择预约</text><text class="modal-close data-v-5560101a" bindtap="{{A}}">×</text></view><view class="booking-list data-v-5560101a"><view wx:if="{{B}}" class="loading-state data-v-5560101a"><text class="data-v-5560101a">加载中...</text></view><view wx:elif="{{C}}" class="empty-state data-v-5560101a"><text class="empty-icon data-v-5560101a">📅</text><text class="empty-text data-v-5560101a">暂无可用的预约</text><text class="empty-desc data-v-5560101a">请先预约场馆并确认后再创建拼场</text></view><view wx:else class="data-v-5560101a"><view wx:for="{{D}}" wx:for-item="booking" wx:key="f" class="{{['booking-item', 'data-v-5560101a', booking.g && 'selected']}}" bindtap="{{booking.h}}"><view class="booking-content data-v-5560101a"><view class="booking-header data-v-5560101a"><text class="venue-name data-v-5560101a">{{booking.a}}</text><text class="booking-status data-v-5560101a">{{booking.b}}</text></view><view class="booking-details data-v-5560101a"><text class="time-info data-v-5560101a">{{booking.c}}</text><text class="price-info data-v-5560101a">¥{{booking.d}}</text></view></view><view wx:if="{{booking.e}}" class="selected-icon data-v-5560101a">✓</view></view></view></view><view class="modal-actions data-v-5560101a"><button class="modal-btn cancel-btn data-v-5560101a" bindtap="{{E}}"> 取消 </button><button class="{{['modal-btn', 'confirm-btn', 'data-v-5560101a', F && 'disabled']}}" bindtap="{{G}}"> 确定 </button></view></view></uni-popup></view>