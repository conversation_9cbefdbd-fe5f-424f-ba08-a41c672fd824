<view class="container data-v-f108858d"><view class="navbar data-v-f108858d"><view class="nav-left data-v-f108858d" bindtap="{{a}}"><text class="nav-icon data-v-f108858d">‹</text></view><text class="nav-title data-v-f108858d">我的申请</text><view class="nav-right data-v-f108858d"></view></view><view class="filter-tabs data-v-f108858d"><view wx:for="{{b}}" wx:for-item="tab" wx:key="d" class="{{['filter-tab', 'data-v-f108858d', tab.e && 'active']}}" bindtap="{{tab.f}}"><text class="tab-text data-v-f108858d">{{tab.a}}</text><text wx:if="{{tab.b}}" class="tab-count data-v-f108858d">{{tab.c}}</text></view></view><view wx:if="{{c}}" class="loading-state data-v-f108858d"><text class="data-v-f108858d">加载中...</text></view><view wx:elif="{{d}}" class="error-state data-v-f108858d"><text class="error-icon data-v-f108858d">⚠️</text><text class="error-text data-v-f108858d">{{e}}</text><button class="retry-btn data-v-f108858d" bindtap="{{f}}"> 重新加载 </button></view><view wx:else class="content data-v-f108858d"><view wx:if="{{g}}" class="requests-list data-v-f108858d"><view wx:for="{{h}}" wx:for-item="request" wx:key="v" class="request-item data-v-f108858d" bindtap="{{request.w}}"><view class="sharing-info data-v-f108858d"><view class="sharing-header data-v-f108858d"><text class="venue-name data-v-f108858d">{{request.a}}</text><view class="{{['status-badge', 'data-v-f108858d', request.c]}}"><text class="status-text data-v-f108858d">{{request.b}}</text></view></view><view class="sharing-details data-v-f108858d"><text class="team-name data-v-f108858d">{{request.d}}</text><text class="activity-time data-v-f108858d">{{request.e}}</text><text class="price data-v-f108858d">支付金额 ¥{{request.f}}</text></view><view class="participants-progress data-v-f108858d"><view class="progress-info data-v-f108858d"><text class="progress-text data-v-f108858d">{{request.g}}/{{request.h}}人 </text><text class="progress-percent data-v-f108858d">{{request.i}}% </text></view><view class="progress-bar data-v-f108858d"><view class="progress-fill data-v-f108858d" style="{{'width:' + request.j}}"></view></view></view></view><view class="request-info data-v-f108858d"><view class="request-meta data-v-f108858d"><text class="request-time data-v-f108858d">申请时间：{{request.k}}</text><text wx:if="{{request.l}}" class="process-time data-v-f108858d"> 处理时间：{{request.m}}</text></view><view class="request-actions data-v-f108858d"><view wx:if="{{request.n}}" class="pending-actions data-v-f108858d"><button class="action-btn cancel-btn data-v-f108858d" catchtap="{{request.o}}"> 取消申请 </button></view><view wx:elif="{{request.p}}" class="approved-actions data-v-f108858d"><text class="approved-text data-v-f108858d">申请已通过</text><button class="action-btn join-btn data-v-f108858d" catchtap="{{request.q}}"> 查看详情 </button></view><view wx:elif="{{request.r}}" class="rejected-actions data-v-f108858d"><text class="rejected-text data-v-f108858d">申请被拒绝</text><text wx:if="{{request.s}}" class="reject-reason data-v-f108858d"> 原因：{{request.t}}</text></view></view></view></view></view><view wx:else class="empty-state data-v-f108858d"><text class="empty-icon data-v-f108858d">📝</text><text class="empty-title data-v-f108858d">{{i}}</text><text class="empty-desc data-v-f108858d">{{j}}</text><button class="browse-btn data-v-f108858d" bindtap="{{k}}"> 去看看拼场 </button></view></view><uni-popup wx:if="{{p}}" class="r data-v-f108858d" u-s="{{['d']}}" u-r="cancelPopup" u-i="f108858d-0" bind:__l="__l" u-p="{{p}}"><uni-popup-dialog wx:if="{{n}}" class="data-v-f108858d" bindconfirm="{{l}}" bindclose="{{m}}" u-i="f108858d-1,f108858d-0" bind:__l="__l" u-p="{{n}}"></uni-popup-dialog></uni-popup></view>