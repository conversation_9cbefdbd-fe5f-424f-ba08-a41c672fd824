"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "ReceivedRequests",
  data() {
    return {
      currentFilter: "all",
      error: "",
      approveTarget: null,
      rejectTarget: null,
      rejectReason: "",
      requests: [],
      filterTabs: [
        { label: "全部", value: "all", count: 0 },
        { label: "待处理", value: "pending", count: 0 },
        { label: "已同意", value: "approved", count: 0 },
        { label: "已拒绝", value: "rejected", count: 0 }
      ]
    };
  },
  computed: {
    ...common_vendor.mapState("sharing", ["loading"]),
    ...common_vendor.mapState("user", ["userInfo"]),
    // 过滤后的申请列表
    filteredRequests() {
      if (this.currentFilter === "all") {
        return this.requests;
      }
      const statusMap = {
        "pending": "PENDING",
        "approved": "APPROVED",
        "rejected": "REJECTED"
      };
      return this.requests.filter(
        (request) => request.status === statusMap[this.currentFilter]
      );
    }
  },
  onLoad() {
    this.loadRequests();
  },
  onShow() {
    this.loadRequests();
  },
  onPullDownRefresh() {
    this.loadRequests().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  methods: {
    ...common_vendor.mapActions("sharing", [
      "getReceivedSharingRequests",
      "processSharingRequest"
    ]),
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载申请列表
    async loadRequests() {
      try {
        this.error = "";
        common_vendor.index.__f__("log", "at pages/sharing/received.vue:275", "收到申请页面：开始加载申请列表");
        const requests = await this.getReceivedSharingRequests();
        this.requests = requests || [];
        this.updateFilterCounts();
        common_vendor.index.__f__("log", "at pages/sharing/received.vue:284", "收到申请页面：加载申请列表成功:", this.requests);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/received.vue:287", "收到申请页面：加载申请列表失败:", error);
        this.error = error.message || "加载失败，请重试";
        this.requests = [];
      }
    },
    // 更新筛选标签计数
    updateFilterCounts() {
      const counts = {
        all: this.requests.length,
        pending: this.requests.filter((r) => r.status === "PENDING").length,
        approved: this.requests.filter((r) => r.status === "APPROVED").length,
        rejected: this.requests.filter((r) => r.status === "REJECTED").length
      };
      this.filterTabs.forEach((tab) => {
        tab.count = counts[tab.value] || 0;
      });
    },
    // 切换筛选
    switchFilter(filter) {
      this.currentFilter = filter;
    },
    // 显示同意确认弹窗
    showApproveConfirm(request) {
      this.approveTarget = request;
      this.$refs.approvePopup.open();
    },
    // 显示拒绝对话框
    showRejectDialog(request) {
      this.rejectTarget = request;
      this.rejectReason = "";
      this.$refs.rejectPopup.open();
    },
    // 关闭拒绝对话框
    closeRejectDialog() {
      this.rejectTarget = null;
      this.rejectReason = "";
      this.$refs.rejectPopup.close();
    },
    // 确认同意申请
    async confirmApprove() {
      if (!this.approveTarget)
        return;
      try {
        common_vendor.index.showLoading({ title: "处理中..." });
        await this.processSharingRequest({
          requestId: this.approveTarget.id,
          action: "approve"
        });
        const request = this.requests.find((r) => r.id === this.approveTarget.id);
        if (request) {
          request.status = "APPROVED";
          request.processedAt = (/* @__PURE__ */ new Date()).toISOString();
        }
        this.updateFilterCounts();
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "已同意申请",
          icon: "success"
        });
        this.approveTarget = null;
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/sharing/received.vue:367", "收到申请页面：同意申请失败:", error);
        common_vendor.index.showToast({
          title: error.message || "操作失败",
          icon: "error"
        });
      }
    },
    // 确认拒绝申请
    async confirmReject() {
      if (!this.rejectTarget)
        return;
      try {
        common_vendor.index.showLoading({ title: "处理中..." });
        await this.processSharingRequest({
          requestId: this.rejectTarget.id,
          action: "reject",
          reason: this.rejectReason
        });
        const request = this.requests.find((r) => r.id === this.rejectTarget.id);
        if (request) {
          request.status = "REJECTED";
          request.processedAt = (/* @__PURE__ */ new Date()).toISOString();
          request.rejectReason = this.rejectReason;
        }
        this.updateFilterCounts();
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "已拒绝申请",
          icon: "success"
        });
        this.closeRejectDialog();
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/sharing/received.vue:410", "收到申请页面：拒绝申请失败:", error);
        common_vendor.index.showToast({
          title: error.message || "操作失败",
          icon: "error"
        });
      }
    },
    // 跳转到创建拼场
    goToCreateSharing() {
      common_vendor.index.navigateTo({
        url: "/pages/sharing/create"
      });
    },
    // 获取头像文字
    getAvatarText(name) {
      if (!name)
        return "?";
      return name.charAt(name.length - 1);
    },
    // 获取进度百分比
    getProgressPercent(current, max) {
      if (!max || max === 0)
        return 0;
      return Math.round(current / max * 100);
    },
    // 格式化活动时间
    formatActivityTime(request) {
      if (!request)
        return "--";
      const date = this.formatDate(request.bookingDate);
      const timeSlot = this.formatTimeSlot(request.startTime, request.endTime);
      return `${date} ${timeSlot}`;
    },
    // 格式化日期
    formatDate(date) {
      if (!date)
        return "--";
      return utils_helpers.formatDate(date, "MM-DD");
    },
    // 格式化日期时间
    formatDateTime(datetime) {
      if (!datetime)
        return "--";
      return utils_helpers.formatDateTime(datetime, "MM-DD HH:mm");
    },
    // 格式化时间段
    formatTimeSlot(startTime, endTime) {
      if (!startTime && !endTime) {
        return "时间未指定";
      }
      if (startTime && !endTime) {
        return startTime;
      }
      if (!startTime && endTime) {
        return endTime;
      }
      return `${startTime}-${endTime}`;
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "PENDING": "待处理",
        "APPROVED": "已同意",
        "APPROVED_PENDING_PAYMENT": "已批准待支付",
        "PAID": "拼场成功",
        "REJECTED": "已拒绝",
        "TIMEOUT_CANCELLED": "超时取消"
      };
      return statusMap[status] || "未知状态";
    },
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        "PENDING": "status-pending",
        "APPROVED": "status-approved",
        "REJECTED": "status-rejected"
      };
      return classMap[status] || "status-unknown";
    },
    // 获取空状态标题
    getEmptyTitle() {
      const titleMap = {
        "all": "暂无申请记录",
        "pending": "暂无待处理申请",
        "approved": "暂无已同意申请",
        "rejected": "暂无已拒绝申请"
      };
      return titleMap[this.currentFilter] || "暂无申请记录";
    },
    // 获取空状态描述
    getEmptyDesc() {
      const descMap = {
        "all": "还没有人申请您的拼场",
        "pending": "暂时没有需要处理的申请",
        "approved": "暂时没有同意的申请",
        "rejected": "暂时没有拒绝的申请"
      };
      return descMap[this.currentFilter] || "还没有人申请您的拼场";
    }
  }
};
if (!Array) {
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_popup_dialog2 + _easycom_uni_popup2)();
}
const _easycom_uni_popup_dialog = () => "../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_popup_dialog + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b;
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.f($data.filterTabs, (tab, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.label),
        b: tab.count > 0
      }, tab.count > 0 ? {
        c: common_vendor.t(tab.count)
      } : {}, {
        d: tab.value,
        e: $data.currentFilter === tab.value ? 1 : "",
        f: common_vendor.o(($event) => $options.switchFilter(tab.value), tab.value)
      });
    }),
    c: _ctx.loading
  }, _ctx.loading ? {} : $data.error ? {
    e: common_vendor.t($data.error),
    f: common_vendor.o((...args) => $options.loadRequests && $options.loadRequests(...args))
  } : common_vendor.e({
    g: $options.filteredRequests.length > 0
  }, $options.filteredRequests.length > 0 ? {
    h: common_vendor.f($options.filteredRequests, (request, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(request.venueName),
        b: common_vendor.t($options.getStatusText(request.status)),
        c: common_vendor.n($options.getStatusClass(request.status)),
        d: common_vendor.t(request.teamName),
        e: common_vendor.t($options.formatActivityTime(request)),
        f: common_vendor.t(request.pricePerPerson),
        g: common_vendor.t(request.currentParticipants),
        h: common_vendor.t(request.maxParticipants),
        i: common_vendor.t($options.getProgressPercent(request.currentParticipants, request.maxParticipants)),
        j: $options.getProgressPercent(request.currentParticipants, request.maxParticipants) + "%",
        k: request.applicantAvatar
      }, request.applicantAvatar ? {
        l: request.applicantAvatar
      } : {
        m: common_vendor.t($options.getAvatarText(request.applicantName))
      }, {
        n: common_vendor.t(request.applicantName),
        o: common_vendor.t($options.formatDateTime(request.createdAt)),
        p: request.message
      }, request.message ? {
        q: common_vendor.t(request.message)
      } : {}, {
        r: request.status === "PENDING"
      }, request.status === "PENDING" ? {
        s: common_vendor.o(($event) => $options.showRejectDialog(request), request.id),
        t: common_vendor.o(($event) => $options.showApproveConfirm(request), request.id)
      } : request.status === "APPROVED" ? {
        w: common_vendor.t($options.formatDateTime(request.processedAt))
      } : request.status === "REJECTED" ? common_vendor.e({
        y: common_vendor.t($options.formatDateTime(request.processedAt)),
        z: request.rejectReason
      }, request.rejectReason ? {
        A: common_vendor.t(request.rejectReason)
      } : {}) : {}, {
        v: request.status === "APPROVED",
        x: request.status === "REJECTED",
        B: request.id
      });
    })
  } : {
    i: common_vendor.t($options.getEmptyTitle()),
    j: common_vendor.t($options.getEmptyDesc()),
    k: common_vendor.o((...args) => $options.goToCreateSharing && $options.goToCreateSharing(...args))
  }), {
    d: $data.error,
    l: common_vendor.o($options.confirmApprove),
    m: common_vendor.o(() => {
      $data.approveTarget = null;
    }),
    n: common_vendor.p({
      type: "info",
      title: "同意申请",
      content: `确定同意 ${(_a = $data.approveTarget) == null ? void 0 : _a.applicantName} 的申请吗？`
    }),
    o: common_vendor.sr("approvePopup", "95722986-0"),
    p: common_vendor.p({
      type: "dialog"
    }),
    q: common_vendor.o((...args) => $options.closeRejectDialog && $options.closeRejectDialog(...args)),
    r: common_vendor.t((_b = $data.rejectTarget) == null ? void 0 : _b.applicantName),
    s: $data.rejectReason,
    t: common_vendor.o(($event) => $data.rejectReason = $event.detail.value),
    v: common_vendor.t($data.rejectReason.length),
    w: common_vendor.o((...args) => $options.closeRejectDialog && $options.closeRejectDialog(...args)),
    x: common_vendor.o((...args) => $options.confirmReject && $options.confirmReject(...args)),
    y: common_vendor.sr("rejectPopup", "95722986-2"),
    z: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-95722986"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/sharing/received.js.map
