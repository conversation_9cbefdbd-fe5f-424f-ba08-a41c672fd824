<view class="container data-v-72a58808"><view class="mode-switch data-v-72a58808"><view class="{{['mode-item', 'data-v-72a58808', a && 'active']}}" bindtap="{{b}}"> 可参与 </view><view class="{{['mode-item', 'data-v-72a58808', c && 'active']}}" bindtap="{{d}}"> 全部 </view></view><view class="filter-section data-v-72a58808"><scroll-view class="filter-scroll data-v-72a58808" scroll-x><view class="{{['filter-item', 'data-v-72a58808', e && 'active']}}" bindtap="{{f}}"> 全部 </view><view wx:for="{{g}}" wx:for-item="status" wx:key="b" class="{{['filter-item', 'data-v-72a58808', status.c && 'active']}}" bindtap="{{status.d}}">{{status.a}}</view></scroll-view><view class="filter-more data-v-72a58808" bindtap="{{h}}"><text class="data-v-72a58808">筛选</text></view></view><view wx:if="{{true}}" class="debug-info data-v-72a58808" style="background:#f0f0f0;padding:10px;margin:10px;border-radius:5px"><view class="data-v-72a58808" style="margin-bottom:5px"><text class="data-v-72a58808" style="font-size:12px;color:#666">调试信息：</text></view><view class="data-v-72a58808" style="margin-bottom:3px"><text class="data-v-72a58808" style="font-size:12px;color:#666">原始数据数量: {{i}}</text></view><view class="data-v-72a58808" style="margin-bottom:3px"><text class="data-v-72a58808" style="font-size:12px;color:#666">筛选后数量: {{j}}</text></view><view class="data-v-72a58808" style="margin-bottom:3px"><text class="data-v-72a58808" style="font-size:12px;color:#666">加载状态: {{k}}</text></view><view class="data-v-72a58808" style="margin-bottom:3px"><text class="data-v-72a58808" style="font-size:12px;color:#666">选中状态: {{l}}</text></view><view class="data-v-72a58808" style="margin-bottom:3px"><text class="data-v-72a58808" style="font-size:12px;color:#666">Store状态: {{m}}</text></view><view wx:if="{{n}}" class="data-v-72a58808" style="margin-bottom:3px"><text class="data-v-72a58808" style="font-size:12px;color:#666">第一条数据: {{o}}</text></view></view><view class="sharing-list data-v-72a58808"><view wx:for="{{p}}" wx:for-item="sharing" wx:key="A" class="{{['sharing-card', 'data-v-72a58808', sharing.B && 'full-card']}}" bindtap="{{sharing.C}}"><view class="card-header data-v-72a58808"><view class="venue-info data-v-72a58808"><text class="venue-name data-v-72a58808">{{sharing.a}}</text><text class="venue-location data-v-72a58808">📍 {{sharing.b}}</text></view><view wx:if="{{sharing.c}}" class="my-sharing-badge data-v-72a58808"><text class="badge-text data-v-72a58808">我的</text></view><view wx:if="{{sharing.d}}" class="full-badge data-v-72a58808"><text class="badge-text data-v-72a58808">已满</text></view><view class="{{['sharing-status', 'data-v-72a58808', sharing.f]}}">{{sharing.e}}</view></view><view class="card-content data-v-72a58808"><view class="time-info data-v-72a58808"><text class="time-icon data-v-72a58808">🕐</text><text class="time-text data-v-72a58808">{{sharing.g}}</text></view><view class="team-info data-v-72a58808"><text class="team-icon data-v-72a58808">👥</text><text class="team-name data-v-72a58808">{{sharing.h}}</text></view><view class="participants-info data-v-72a58808"><text class="participants-text data-v-72a58808">参与球队：{{sharing.i}}/{{sharing.j}}支</text><view class="progress-bar data-v-72a58808"><view class="progress-fill data-v-72a58808" style="{{'width:' + sharing.k}}"></view></view></view><countdown-timer wx:if="{{sharing.l}}" class="simple data-v-72a58808" bindexpired="{{sharing.m}}" u-i="{{sharing.n}}" bind:__l="__l" u-p="{{sharing.o}}"/><view class="price-info data-v-72a58808"><text class="price-label data-v-72a58808">费用：</text><text class="price-value data-v-72a58808">¥{{sharing.p}}</text><text class="price-note data-v-72a58808">（每队费用）</text></view><view class="creator-info data-v-72a58808"><text class="creator-label data-v-72a58808">发起人：</text><text class="creator-value data-v-72a58808">{{sharing.q}}</text></view><view class="create-info data-v-72a58808"><text class="create-label data-v-72a58808">创建时间：</text><text class="create-value data-v-72a58808">{{sharing.r}}</text></view><view wx:if="{{sharing.s}}" class="description data-v-72a58808"><text class="data-v-72a58808">{{sharing.t}}</text></view></view><view class="card-actions data-v-72a58808"><view class="organizer-info data-v-72a58808"><text class="organizer-name data-v-72a58808">{{sharing.v}}</text></view><button wx:if="{{sharing.w}}" class="join-btn data-v-72a58808" catchtap="{{sharing.x}}"> 申请拼场 </button><view wx:else class="{{['join-disabled', 'data-v-72a58808', sharing.z && 'applied']}}">{{sharing.y}}</view></view></view></view><view wx:if="{{q}}" class="empty-state data-v-72a58808"><text class="empty-icon data-v-72a58808">🏀</text><text class="empty-text data-v-72a58808">暂无拼场订单</text></view><view wx:if="{{r}}" class="loading-state data-v-72a58808"><text class="data-v-72a58808">加载中...</text></view><view wx:if="{{s}}" class="load-more data-v-72a58808" bindtap="{{v}}"><text class="data-v-72a58808">{{t}}</text></view><uni-popup wx:if="{{G}}" class="r data-v-72a58808" u-s="{{['d']}}" u-r="filterPopup" u-i="72a58808-1" bind:__l="__l" u-p="{{G}}"><view class="filter-modal data-v-72a58808"><view class="modal-header data-v-72a58808"><text class="modal-title data-v-72a58808">筛选条件</text><text class="modal-close data-v-72a58808" bindtap="{{w}}">✕</text></view><view class="filter-content data-v-72a58808"><view class="filter-group data-v-72a58808"><text class="group-title data-v-72a58808">活动日期</text><view class="date-options data-v-72a58808"><view wx:for="{{x}}" wx:for-item="date" wx:key="b" class="{{['date-item', 'data-v-72a58808', date.c && 'active']}}" bindtap="{{date.d}}">{{date.a}}</view></view></view><view class="filter-group data-v-72a58808"><text class="group-title data-v-72a58808">价格范围</text><view class="price-range data-v-72a58808"><input type="number" placeholder="最低价格" class="price-input data-v-72a58808" value="{{y}}" bindinput="{{z}}"/><text class="price-separator data-v-72a58808">-</text><input type="number" placeholder="最高价格" class="price-input data-v-72a58808" value="{{A}}" bindinput="{{B}}"/></view></view><view class="filter-group data-v-72a58808"><text class="group-title data-v-72a58808">参与人数</text><view class="participants-options data-v-72a58808"><view wx:for="{{C}}" wx:for-item="participants" wx:key="b" class="{{['participants-item', 'data-v-72a58808', participants.c && 'active']}}" bindtap="{{participants.d}}">{{participants.a}}</view></view></view></view><view class="modal-footer data-v-72a58808"><button class="reset-btn data-v-72a58808" bindtap="{{D}}">重置</button><button class="confirm-btn data-v-72a58808" bindtap="{{E}}">确定</button></view></view></uni-popup><uni-popup wx:if="{{T}}" class="r data-v-72a58808" u-s="{{['d']}}" u-r="joinPopup" u-i="72a58808-2" bind:__l="__l" u-p="{{T}}"><view class="apply-modal data-v-72a58808"><view class="modal-header data-v-72a58808"><text class="modal-title data-v-72a58808">申请加入拼场</text><text class="close-btn data-v-72a58808" bindtap="{{H}}">✕</text></view><view class="modal-content data-v-72a58808"><view class="form-item data-v-72a58808"><text class="form-label data-v-72a58808">队伍名称</text><input class="form-input data-v-72a58808" placeholder="请输入队伍名称（可选）" maxlength="20" value="{{I}}" bindinput="{{J}}"/></view><view class="form-item data-v-72a58808"><text class="form-label data-v-72a58808">联系方式 <text class="required data-v-72a58808">*</text></text><input class="form-input data-v-72a58808" placeholder="请输入手机号或微信号" maxlength="50" value="{{K}}" bindinput="{{L}}"/></view><view class="form-item data-v-72a58808"><text class="form-label data-v-72a58808">申请说明</text><text class="form-hint data-v-72a58808">您将代表一支球队申请加入此拼场</text></view><view class="form-item data-v-72a58808"><text class="form-label data-v-72a58808">申请留言</text><block wx:if="{{r0}}"><textarea class="form-textarea data-v-72a58808" placeholder="请输入申请留言（可选）" maxlength="200" value="{{M}}" bindinput="{{N}}"></textarea></block><text class="char-count data-v-72a58808">{{O}}/200</text></view></view><view class="modal-actions data-v-72a58808"><button class="modal-btn cancel-btn data-v-72a58808" bindtap="{{P}}"> 取消 </button><button class="modal-btn confirm-btn data-v-72a58808" disabled="{{Q}}" bindtap="{{R}}"> 提交申请 </button></view></view></uni-popup><view class="floating-btn data-v-72a58808" bindtap="{{U}}"><text class="floating-btn-text data-v-72a58808">我的拼场</text></view></view>