<view class="container data-v-d012c4ce"><view class="navbar data-v-d012c4ce"><view class="nav-left data-v-d012c4ce" bindtap="{{a}}"><text class="nav-icon data-v-d012c4ce">‹</text></view><text class="nav-title data-v-d012c4ce">管理拼场</text><view class="nav-right data-v-d012c4ce"></view></view><view wx:if="{{b}}" class="loading-state data-v-d012c4ce"><text class="data-v-d012c4ce">加载中...</text></view><view wx:elif="{{c}}" class="error-state data-v-d012c4ce"><text class="error-icon data-v-d012c4ce">⚠️</text><text class="error-text data-v-d012c4ce">{{d}}</text><button class="retry-btn data-v-d012c4ce" bindtap="{{e}}"> 重新加载 </button></view><view wx:elif="{{f}}" class="content data-v-d012c4ce"><view class="info-section data-v-d012c4ce"><view class="venue-header data-v-d012c4ce"><text class="venue-name data-v-d012c4ce">{{g}}</text><view class="{{['status-badge', 'data-v-d012c4ce', i]}}"><text class="status-text data-v-d012c4ce">{{h}}</text></view></view><view class="team-info data-v-d012c4ce"><view class="team-header data-v-d012c4ce"><text class="team-name data-v-d012c4ce">{{j}}</text><text class="creator-label data-v-d012c4ce">队长</text></view><view class="participants-progress data-v-d012c4ce"><view class="progress-info data-v-d012c4ce"><text class="progress-text data-v-d012c4ce"> 参与人数：{{k}}/{{l}}人 </text><text class="progress-percent data-v-d012c4ce">{{m}}% </text></view><view class="progress-bar data-v-d012c4ce"><view class="progress-fill data-v-d012c4ce" style="{{'width:' + n}}"></view></view></view></view><view class="activity-info data-v-d012c4ce"><view class="info-row data-v-d012c4ce"><text class="info-label data-v-d012c4ce">活动时间</text><text class="info-value data-v-d012c4ce">{{o}}</text></view><view class="info-row data-v-d012c4ce"><text class="info-label data-v-d012c4ce">每队费用</text><text class="info-value price data-v-d012c4ce">¥{{p}}</text></view><view class="info-row data-v-d012c4ce"><text class="info-label data-v-d012c4ce">订单号</text><text class="info-value order-no data-v-d012c4ce">{{q}}</text></view><view class="info-row data-v-d012c4ce"><text class="info-label data-v-d012c4ce">创建时间</text><text class="info-value data-v-d012c4ce">{{r}}</text></view></view><view wx:if="{{s}}" class="description data-v-d012c4ce"><text class="description-label data-v-d012c4ce">活动描述</text><text class="description-text data-v-d012c4ce">{{t}}</text></view></view><view class="participants-section data-v-d012c4ce"><view class="section-title data-v-d012c4ce"><text class="title-text data-v-d012c4ce">队伍管理</text><text class="count-text data-v-d012c4ce">({{v}}支)</text></view><view class="participants-list data-v-d012c4ce"><view wx:for="{{w}}" wx:for-item="participant" wx:key="f" class="participant-item data-v-d012c4ce"><view class="participant-info data-v-d012c4ce"><image class="participant-avatar data-v-d012c4ce" src="{{participant.a}}" mode="aspectFill"/><view class="participant-details data-v-d012c4ce"><text class="participant-name data-v-d012c4ce">{{participant.b}}</text><text class="participant-role data-v-d012c4ce">{{participant.c}}</text></view></view><view wx:if="{{participant.d}}" class="remove-btn data-v-d012c4ce" bindtap="{{participant.e}}"><text class="remove-text data-v-d012c4ce">移除</text></view></view></view><view wx:if="{{x}}" class="empty-participants data-v-d012c4ce"><text class="empty-icon data-v-d012c4ce">👥</text><text class="empty-text data-v-d012c4ce">暂无参与者</text></view></view><view class="settings-section data-v-d012c4ce"><view class="section-title data-v-d012c4ce"><text class="title-text data-v-d012c4ce">拼场设置</text></view><view class="settings-list data-v-d012c4ce"><view class="setting-item data-v-d012c4ce"><view class="setting-info data-v-d012c4ce"><text class="setting-label data-v-d012c4ce">自动通过申请</text><text class="setting-desc data-v-d012c4ce">{{y}}</text></view><switch class="data-v-d012c4ce" checked="{{z}}" bindchange="{{A}}" disabled="{{B}}" color="#ff6b35"/></view><view class="setting-item data-v-d012c4ce"><view class="setting-info data-v-d012c4ce"><text class="setting-label data-v-d012c4ce">允许中途退出</text><text class="setting-desc data-v-d012c4ce">开启后，参与者可以在活动开始前退出</text></view><switch class="data-v-d012c4ce" checked="{{C}}" bindchange="{{D}}" disabled="{{E}}" color="#ff6b35"/></view></view></view><view wx:if="{{F}}" class="requests-section data-v-d012c4ce"><view class="section-title data-v-d012c4ce"><text class="title-text data-v-d012c4ce">拼场申请</text><text class="count-text data-v-d012c4ce">({{G}}条待处理)</text></view><view class="requests-list data-v-d012c4ce"><view wx:for="{{H}}" wx:for-item="request" wx:key="i" class="request-item data-v-d012c4ce"><view class="request-info data-v-d012c4ce"><image class="request-avatar data-v-d012c4ce" src="{{request.a}}" mode="aspectFill"/><view class="request-details data-v-d012c4ce"><text class="request-name data-v-d012c4ce">{{request.b}}</text><text class="request-time data-v-d012c4ce">{{request.c}}</text></view></view><view class="request-actions data-v-d012c4ce"><view wx:if="{{request.d}}" class="action-buttons data-v-d012c4ce"><button class="action-btn reject-btn data-v-d012c4ce" bindtap="{{request.e}}"> 拒绝 </button><button class="action-btn approve-btn data-v-d012c4ce" bindtap="{{request.f}}"> 同意 </button></view><view wx:else class="request-status data-v-d012c4ce"><text class="{{['status-text', 'data-v-d012c4ce', request.h]}}">{{request.g}}</text></view></view></view></view></view></view><view wx:if="{{I}}" class="bottom-actions data-v-d012c4ce"><button wx:if="{{J}}" class="action-btn confirm-btn data-v-d012c4ce" bindtap="{{K}}"> 确认拼场 </button><button wx:if="{{L}}" class="action-btn cancel-btn data-v-d012c4ce" bindtap="{{M}}"> 取消拼场 </button></view><uni-popup wx:if="{{R}}" class="r data-v-d012c4ce" u-s="{{['d']}}" u-r="removePopup" u-i="d012c4ce-0" bind:__l="__l" u-p="{{R}}"><uni-popup-dialog wx:if="{{P}}" class="data-v-d012c4ce" bindconfirm="{{N}}" bindclose="{{O}}" u-i="d012c4ce-1,d012c4ce-0" bind:__l="__l" u-p="{{P}}"></uni-popup-dialog></uni-popup><uni-popup wx:if="{{W}}" class="r data-v-d012c4ce" u-s="{{['d']}}" u-r="cancelPopup" u-i="d012c4ce-2" bind:__l="__l" u-p="{{W}}"><uni-popup-dialog wx:if="{{U}}" class="data-v-d012c4ce" bindconfirm="{{S}}" bindclose="{{T}}" u-i="d012c4ce-3,d012c4ce-2" bind:__l="__l" u-p="{{U}}"></uni-popup-dialog></uni-popup></view>