"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "TestStore",
  computed: {
    ...common_vendor.mapGetters("sharing", ["sharingOrders", "loading"])
  },
  onLoad() {
    var _a, _b;
    common_vendor.index.__f__("log", "at pages/test-store.vue:53", "测试页面加载");
    common_vendor.index.__f__("log", "at pages/test-store.vue:54", "Store状态:", this.$store);
    common_vendor.index.__f__("log", "at pages/test-store.vue:55", "Sharing模块:", (_b = (_a = this.$store) == null ? void 0 : _a.state) == null ? void 0 : _b.sharing);
    this.testGetData();
  },
  methods: {
    ...common_vendor.mapActions("sharing", ["getJoinableSharingOrders"]),
    async testGetData() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/test-store.vue:64", "开始测试获取数据");
        common_vendor.index.__f__("log", "at pages/test-store.vue:65", "getJoinableSharingOrders方法:", this.getJoinableSharingOrders);
        const result = await this.getJoinableSharingOrders({ page: 1, pageSize: 10 });
        common_vendor.index.__f__("log", "at pages/test-store.vue:68", "获取数据结果:", result);
        common_vendor.index.__f__("log", "at pages/test-store.vue:69", "Store中的数据:", this.sharingOrders);
        common_vendor.index.showToast({
          title: `获取到${((_a = this.sharingOrders) == null ? void 0 : _a.length) || 0}条数据`,
          icon: "none"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/test-store.vue:76", "获取数据失败:", error);
        common_vendor.index.showToast({
          title: "获取数据失败",
          icon: "none"
        });
      }
    },
    testGenerateMock() {
      common_vendor.index.__f__("log", "at pages/test-store.vue:85", "测试生成模拟数据");
      const mockData = [
        {
          id: 999,
          venueName: "测试场馆",
          venueLocation: "测试位置",
          teamName: "测试队伍",
          status: "OPEN",
          maxParticipants: 8,
          currentParticipants: 3,
          totalPrice: 500,
          bookingDate: "2025-06-30",
          startTime: "14:00",
          endTime: "16:00",
          creatorUsername: "测试用户",
          description: "这是一条测试数据"
        }
      ];
      this.$store.commit("sharing/SET_SHARING_ORDERS", mockData);
      common_vendor.index.__f__("log", "at pages/test-store.vue:106", "设置模拟数据完成:", mockData);
      common_vendor.index.showToast({
        title: "模拟数据已设置",
        icon: "success"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: common_vendor.t(_ctx.$store ? "✅ Store已连接" : "❌ Store未连接"),
    b: common_vendor.t(_ctx.loading ? "加载中" : "已完成"),
    c: common_vendor.t(((_a = _ctx.sharingOrders) == null ? void 0 : _a.length) || 0),
    d: common_vendor.o((...args) => $options.testGetData && $options.testGetData(...args)),
    e: common_vendor.o((...args) => $options.testGenerateMock && $options.testGenerateMock(...args)),
    f: _ctx.sharingOrders && _ctx.sharingOrders.length > 0
  }, _ctx.sharingOrders && _ctx.sharingOrders.length > 0 ? {
    g: common_vendor.f(_ctx.sharingOrders, (item, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(item.venueName),
        c: common_vendor.t(item.teamName),
        d: common_vendor.t(item.status),
        e: common_vendor.t(item.totalPrice),
        f: index
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-47eddcbe"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/test-store.js.map
