"use strict";
const common_vendor = require("../common/vendor.js");
const utils_auth = require("./auth.js");
const store_index = require("../store/index.js");
const PUBLIC_PAGES = [
  "/pages/user/login",
  "/pages/user/register"
];
const PAYMENT_PAGES = [
  "/pages/payment/index",
  "/pages/payment/success",
  "/pages/payment/failed"
];
let authCheckCache = null;
let authCheckTime = 0;
const AUTH_CACHE_DURATION = 3e4;
function setupRouterGuard() {
  common_vendor.index.__f__("log", "at utils/router-guard-new.js:30", "[RouterGuard] 设置路由守卫");
  const interceptMethods = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
  interceptMethods.forEach((method) => {
    common_vendor.index.addInterceptor(method, {
      invoke(params) {
        return checkPagePermission(params.url, method);
      }
    });
  });
}
function checkPagePermission(url, method) {
  const pagePath = extractPagePath(url);
  common_vendor.index.__f__("log", "at utils/router-guard-new.js:52", `[RouterGuard] 检查页面权限: ${pagePath}, 方法: ${method}`);
  if (isPublicPage(pagePath)) {
    common_vendor.index.__f__("log", "at utils/router-guard-new.js:56", "[RouterGuard] 公开页面，允许访问");
    return true;
  }
  if (isPaymentPage(pagePath)) {
    common_vendor.index.__f__("log", "at utils/router-guard-new.js:62", "[RouterGuard] 支付页面，使用宽松检查");
    const hasBasicAuth = !!(utils_auth.getToken() && utils_auth.getUserInfo());
    if (!hasBasicAuth) {
      common_vendor.index.__f__("log", "at utils/router-guard-new.js:65", "[RouterGuard] 支付页面需要基本认证，跳转到登录页");
      handleLoginRequired(url);
      return false;
    }
    common_vendor.index.__f__("log", "at utils/router-guard-new.js:69", "[RouterGuard] 支付页面基本认证通过，允许访问");
    return true;
  }
  const isLoggedIn = checkLoginStatus();
  common_vendor.index.__f__("log", "at utils/router-guard-new.js:75", `[RouterGuard] 登录状态: ${isLoggedIn}`);
  if (!isLoggedIn) {
    common_vendor.index.__f__("log", "at utils/router-guard-new.js:79", "[RouterGuard] 需要登录，跳转到登录页");
    handleLoginRequired(url);
    return false;
  }
  common_vendor.index.__f__("log", "at utils/router-guard-new.js:84", "[RouterGuard] 已登录，允许访问");
  return true;
}
function extractPagePath(url) {
  return url.split("?")[0];
}
function isPublicPage(pagePath) {
  return PUBLIC_PAGES.some((page) => pagePath === page);
}
function isPaymentPage(pagePath) {
  return PAYMENT_PAGES.some((page) => pagePath === page);
}
function checkLoginStatus() {
  const now = Date.now();
  if (authCheckCache !== null && now - authCheckTime < AUTH_CACHE_DURATION) {
    common_vendor.index.__f__("log", "at utils/router-guard-new.js:126", "[RouterGuard] 使用缓存的登录状态:", authCheckCache);
    return authCheckCache;
  }
  const token = utils_auth.getToken();
  const userInfo = utils_auth.getUserInfo();
  const storeLoginStatus = store_index.store.getters["user/isLoggedIn"];
  common_vendor.index.__f__("log", "at utils/router-guard-new.js:135", "[RouterGuard] 本地检查 - token:", !!token, "userInfo:", !!userInfo, "store:", storeLoginStatus);
  const isLoggedIn = !!(token && userInfo && storeLoginStatus);
  authCheckCache = isLoggedIn;
  authCheckTime = now;
  return isLoggedIn;
}
function handleLoginRequired(originalUrl) {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  if (currentPage && currentPage.route.includes("pages/user/login")) {
    common_vendor.index.__f__("log", "at utils/router-guard-new.js:157", "[RouterGuard] 已在登录页，不重复跳转");
    return;
  }
  common_vendor.index.showToast({
    title: "请先登录",
    icon: "none",
    duration: 1500
  });
  const redirectUrl = encodeURIComponent(originalUrl);
  setTimeout(() => {
    common_vendor.index.reLaunch({
      url: `/pages/user/login?redirect=${redirectUrl}`,
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/router-guard-new.js:176", "[RouterGuard] 跳转登录页失败:", err);
      }
    });
  }, 100);
}
function clearAuthCache() {
  common_vendor.index.__f__("log", "at utils/router-guard-new.js:186", "[RouterGuard] 清除登录状态缓存");
  authCheckCache = null;
  authCheckTime = 0;
}
function updateAuthCache(isLoggedIn) {
  common_vendor.index.__f__("log", "at utils/router-guard-new.js:196", "[RouterGuard] 更新登录状态缓存:", isLoggedIn);
  authCheckCache = isLoggedIn;
  authCheckTime = Date.now();
}
exports.clearAuthCache = clearAuthCache;
exports.setupRouterGuard = setupRouterGuard;
exports.updateAuthCache = updateAuthCache;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/router-guard-new.js.map
