"use strict";
const utils_request = require("../utils/request.js");
function login(data) {
  return utils_request.post("/auth/signin", data);
}
function smsLogin(data) {
  return utils_request.post("/auth/sms-login", data);
}
function register(data) {
  return utils_request.post("/auth/signup", data);
}
function getSmsCode(phone) {
  return utils_request.post("/auth/sms-code", { phone });
}
function logout() {
  return utils_request.post("/auth/logout");
}
exports.getSmsCode = getSmsCode;
exports.login = login;
exports.logout = logout;
exports.register = register;
exports.smsLogin = smsLogin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/auth.js.map
