"use strict";
const utils_request = require("../utils/request.js");
function createBooking(data) {
  return utils_request.post("/bookings", data);
}
function getBookingDetail(id) {
  return utils_request.get(`/bookings/${id}`);
}
function cancelBooking(id) {
  return utils_request.put(`/bookings/${id}/cancel`);
}
function createSharedBooking(data) {
  return utils_request.post("/bookings/shared", data);
}
exports.cancelBooking = cancelBooking;
exports.createBooking = createBooking;
exports.createSharedBooking = createSharedBooking;
exports.getBookingDetail = getBookingDetail;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/booking.js.map
