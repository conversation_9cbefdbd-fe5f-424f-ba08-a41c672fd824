"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_routerGuardNew = require("./utils/router-guard-new.js");
const store_index = require("./store/index.js");
const stores_plugins_vuexSync = require("./stores/plugins/vuex-sync.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/venue/list.js";
  "./pages/venue/detail.js";
  "./pages/sharing/list.js";
  "./pages/test-store.js";
  "./pages/booking/list.js";
  "./pages/user/profile.js";
  "./pages/booking/create.js";
  "./pages/test/booking-detail-test.js";
  "./pages/booking/detail.js";
  "./pages/sharing/create.js";
  "./pages/sharing/detail.js";
  "./pages/sharing/manage.js";
  "./pages/sharing/my-orders.js";
  "./pages/user/login.js";
  "./pages/user/register.js";
  "./pages/user/edit-profile.js";
  "./pages/test/auth-test.js";
  "./pages/sharing/requests.js";
  "./pages/sharing/received.js";
  "./pages/test/pinia-migration-test.js";
  "./pages/test/simple-test.js";
  "./pages/test/payment-test.js";
  "./pages/test/order-status-test.js";
  "./pages/payment/index.js";
  "./pages/payment/success.js";
  "./pages/payment/failed.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:7", "[App] 应用启动");
    utils_routerGuardNew.setupRouterGuard();
    this.initUserState();
    this.checkAndRedirectToLogin();
    this.$nextTick(() => {
      this.setupNetworkListener();
    });
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:24", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:27", "App Hide");
  },
  methods: {
    ...common_vendor.mapActions("user", ["checkLoginStatus", "initUserState"]),
    ...common_vendor.mapActions(["setNetworkStatus"]),
    // 检查登录状态并跳转到登录页
    checkAndRedirectToLogin() {
      try {
        common_vendor.index.__f__("log", "at App.vue:36", "[App] 检查登录状态");
        const token = common_vendor.index.getStorageSync("token");
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (!token || !userInfo) {
          common_vendor.index.__f__("log", "at App.vue:41", "[App] 未登录，跳转到登录页");
          common_vendor.index.reLaunch({
            url: "/pages/user/login"
          });
          return;
        }
        common_vendor.index.__f__("log", "at App.vue:48", "[App] 已登录，继续正常流程");
      } catch (error) {
        common_vendor.index.__f__("warn", "at App.vue:50", "[App] 登录状态检查失败:", error.message);
        common_vendor.index.reLaunch({
          url: "/pages/user/login"
        });
      }
    },
    // 设置网络监听
    setupNetworkListener() {
      common_vendor.index.onNetworkStatusChange((res) => {
        this.setNetworkStatus(res.isConnected);
        if (!res.isConnected) {
          common_vendor.index.showToast({
            title: "网络连接已断开",
            icon: "none"
          });
        }
      });
    }
  }
};
store_index.pinia.use(stores_plugins_vuexSync.vuexSyncPlugin());
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.use(store_index.pinia);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
