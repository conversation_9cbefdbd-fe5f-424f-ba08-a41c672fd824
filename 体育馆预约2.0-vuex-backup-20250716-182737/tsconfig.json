{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "sourceMap": true, "baseUrl": ".", "types": ["@dcloudio/types"], "paths": {"@/*": ["./*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["**/*.ts", "**/*.d.ts", "**/*.tsx", "**/*.vue", "pages/**/*.vue", "components/**/*.vue", "api/**/*.js", "utils/**/*.js", "store/**/*.js"], "exclude": ["node_modules", "unpackage", "dist"]}