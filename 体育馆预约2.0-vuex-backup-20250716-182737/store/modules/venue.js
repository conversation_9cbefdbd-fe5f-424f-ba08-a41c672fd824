import * as venueApi from '@/api/venue.js'
import * as timeslotApi from '@/api/timeslot.js'
import { showError } from '@/utils/ui.js'

const state = {
  venueList: [],
  popularVenues: [],
  venueDetail: null,
  venueTypes: [],
  timeSlots: [],
  searchResults: [],
  loading: false,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  }
}

const mutations = {
  SET_VENUE_LIST(state, { list, pagination }) {
    state.venueList = list
    if (pagination) {
      state.pagination = { ...state.pagination, ...pagination }
    }
  },
  
  APPEND_VENUE_LIST(state, list) {
    state.venueList = [...state.venueList, ...list]
  },
  
  SET_POPULAR_VENUES(state, venues) {
    state.popularVenues = venues
  },
  
  SET_VENUE_DETAIL(state, venue) {
    state.venueDetail = venue
  },
  
  SET_VENUE_TYPES(state, types) {
    state.venueTypes = types
  },
  
  SET_TIME_SLOTS(state, slots) {
    state.timeSlots = slots
  },
  
  SET_SEARCH_RESULTS(state, results) {
    state.searchResults = results
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  UPDATE_TIME_SLOT_STATUS(state, { slotId, status }) {
    const slot = state.timeSlots.find(s => s.id === slotId)
    if (slot) {
      slot.status = status
    }
  },

  // 批量更新mutation，用于优化同步性能
  BATCH_UPDATE(state, updates) {
    if (updates.venueList !== undefined) {
      state.venueList = updates.venueList
    }
    if (updates.popularVenues !== undefined) {
      state.popularVenues = updates.popularVenues
    }
    if (updates.venueDetail !== undefined) {
      state.venueDetail = updates.venueDetail
    }
    if (updates.venueTypes !== undefined) {
      state.venueTypes = updates.venueTypes
    }
    if (updates.timeSlots !== undefined) {
      state.timeSlots = updates.timeSlots
    }
    if (updates.searchResults !== undefined) {
      state.searchResults = updates.searchResults
    }
    if (updates.loading !== undefined) {
      state.loading = updates.loading
    }
    if (updates.pagination !== undefined) {
      state.pagination = { ...state.pagination, ...updates.pagination }
    }
  }
}

const actions = {
  // 获取场馆列表
  async getVenueList({ commit, state }, params = {}) {
    try {
      console.log('开始获取场馆列表，参数:', params)
      commit('SET_LOADING', true)
      
      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时
      })
      
      const apiPromise = venueApi.getVenueList(params)
      const response = await Promise.race([apiPromise, timeoutPromise])
      
      console.log('场馆API响应:', response)
      
      // 处理响应数据
      let list = []
      let pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      }
      
      if (response && response.data) {
        if (Array.isArray(response.data)) {
          list = response.data
          pagination = {
            current: response.page || params.page || 1,
            pageSize: response.pageSize || params.pageSize || 10,
            total: response.total || response.data.length,
            totalPages: response.totalPages || 1
          }
        } else {
          console.warn('API响应数据格式异常，使用空数组:', response)
        }
      } else if (response && Array.isArray(response)) {
        // 直接返回数组的情况
        list = response
        pagination.total = response.length
      } else {
        console.warn('API响应为空或格式错误，使用空数组:', response)
      }
      
      console.log('解析的场馆列表:', list)
      console.log('分页信息:', pagination)
      
      if (params.page === 1 || params.refresh) {
        commit('SET_VENUE_LIST', { list, pagination })
      } else {
        commit('APPEND_VENUE_LIST', list)
        commit('SET_VENUE_LIST', { list: state.venueList, pagination })
      }
      
      return response
    } catch (error) {
      console.error('获取场馆列表失败:', error)
      
      // 如果是网络错误或超时，提供模拟数据
      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('请求超时') || error.statusCode === undefined) {
        console.log('使用模拟场馆数据')
        const mockData = [
          {
            id: 1,
            name: '示例篮球场',
            image: 'https://via.placeholder.com/300x200/ff6b35/ffffff?text=篮球场',
            location: '北京市朝阳区示例路123号',
            rating: 4.5,
            price: 120,
            type: '篮球场',
            status: 'available',
            supportSharing: true
          },
          {
            id: 2,
            name: '示例羽毛球馆',
            image: 'https://via.placeholder.com/300x200/4CAF50/ffffff?text=羽毛球馆',
            location: '北京市海淀区示例街456号',
            rating: 4.3,
            price: 80,
            type: '羽毛球',
            status: 'available',
            supportSharing: false
          },
          {
            id: 3,
            name: '示例网球场',
            image: 'https://via.placeholder.com/300x200/2196F3/ffffff?text=网球场',
            location: '北京市西城区示例大道789号',
            rating: 4.7,
            price: 200,
            type: '网球',
            status: 'maintenance',
            supportSharing: true
          }
        ]
        
        const mockPagination = {
          current: 1,
          pageSize: 10,
          total: mockData.length,
          totalPages: 1
        }
        
        commit('SET_VENUE_LIST', { list: mockData, pagination: mockPagination })
        console.log('已设置模拟场馆数据:', mockData)
        return { data: mockData }
      }
      
      showError(error.message || '获取场馆列表失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取热门场馆
  async getPopularVenues({ commit }) {
    try {
      console.log('开始获取热门场馆...')
      const response = await venueApi.getPopularVenues()
      console.log('热门场馆API响应:', response)
      
      // 处理两种可能的响应格式：直接返回数组 或 包装在data中
      let venues = []
      if (Array.isArray(response)) {
        venues = response
      } else if (response && Array.isArray(response.data)) {
        venues = response.data
      } else {
        console.warn('热门场馆API响应格式异常:', response)
      }
      
      commit('SET_POPULAR_VENUES', venues)
      console.log('热门场馆数据已设置:', venues)
      return response
    } catch (error) {
      console.error('获取热门场馆失败:', error)
      commit('SET_POPULAR_VENUES', [])
      showError(error.message || '获取热门场馆失败')
      throw error
    }
  },
  
  // 获取场馆详情
  async getVenueDetail({ commit }, venueId) {
    try {
      console.log('开始获取场馆详情，ID:', venueId)
      commit('SET_LOADING', true)
      
      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('获取详情超时')), 10000) // 10秒超时
      })
      
      const apiPromise = venueApi.getVenueDetail(venueId)
      const response = await Promise.race([apiPromise, timeoutPromise])
      console.log('场馆详情API响应:', response)
      
      // 处理响应数据
      let venueDetail = null
      if (response && response.data) {
        venueDetail = response.data
      } else if (response && !response.data) {
        // 如果响应直接是数据对象
        venueDetail = response
      }
      
      if (venueDetail) {
        // 数据转换：将后端数据格式转换为前端期望的格式
        const transformedDetail = {
          ...venueDetail,
          // 转换营业时间格式
          openingHours: venueDetail.openingHours || (venueDetail.openTime && venueDetail.closeTime ? 
            `${venueDetail.openTime.substring(0, 5)} - ${venueDetail.closeTime.substring(0, 5)}` : '08:00 - 22:00'),
          // 转换设施格式
          facilities: venueDetail.facilities ? 
            (Array.isArray(venueDetail.facilities) ? venueDetail.facilities : 
             venueDetail.facilities.split(',').map((facility, index) => ({
               name: facility.trim(),
               icon: ['🚪', '🚿', '🗄️', '💧', '🏀', '⚽', '🎾', '🏐'][index] || '🏢'
             }))) : [],
          // 确保价格字段存在
          price: venueDetail.price || 0
        }
        
        commit('SET_VENUE_DETAIL', transformedDetail)
        console.log('场馆详情已设置:', transformedDetail)
      } else {
        console.error('场馆详情数据为空')
        throw new Error('场馆详情数据为空')
      }
      
      return response
    } catch (error) {
      console.error('获取场馆详情失败:', error)
      
      // 如果是网络错误、超时或后端服务未启动，提供模拟数据
      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('获取详情超时') || error.statusCode === undefined) {
        console.log('使用模拟数据')
        const mockData = {
          id: venueId,
          name: '示例体育馆',
          images: [
            'https://via.placeholder.com/400x300/ff6b35/ffffff?text=场馆图片1',
            'https://via.placeholder.com/400x300/4CAF50/ffffff?text=场馆图片2'
          ],
          rating: 4.5,
          reviewCount: 128,
          location: '北京市朝阳区示例路123号',
          distance: 2.5,
          price: 120,
          tags: ['室内', '空调', '停车场', 'WiFi'],
          description: '这是一个现代化的体育馆，设施齐全，环境优美。',
          facilities: [
            { name: '更衣室', icon: '🚪' },
            { name: '淋浴间', icon: '🚿' },
            { name: '储物柜', icon: '🗄️' },
            { name: '饮水机', icon: '💧' }
          ],
          openTime: '06:00',
          closeTime: '22:00',
          openingHours: '06:00 - 22:00',
          phone: '010-12345678'
        }
        commit('SET_VENUE_DETAIL', mockData)
        console.log('已设置模拟场馆数据:', mockData)
        return { data: mockData }
      }
      
      showError(error.message || '获取场馆详情失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取场馆时间段
  async getVenueTimeSlots({ commit }, { venueId, date }) {
    try {
      console.log('开始获取场馆时间段，venueId:', venueId, 'date:', date)
      commit('SET_LOADING', true)
      
      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('获取时间段超时')), 8000) // 8秒超时
      })
      
      // 添加时间戳参数强制刷新，避免缓存问题
      const timestamp = Date.now()
      const apiPromise = venueApi.getVenueTimeSlots(venueId, date, { _t: timestamp })
      const response = await Promise.race([apiPromise, timeoutPromise])
      
      console.log('时间段API响应:', response)
      
      let timeSlots = []
      if (response && response.data && response.data.slots && Array.isArray(response.data.slots)) {
        timeSlots = response.data.slots
      } else if (response && response.slots && Array.isArray(response.slots)) {
        timeSlots = response.slots
      } else if (response && response.data && Array.isArray(response.data)) {
        timeSlots = response.data
      } else if (response && Array.isArray(response)) {
        timeSlots = response
      } else {
        console.warn('时间段API响应格式异常:', response)
        timeSlots = []
      }
      
      // 如果没有时间段，尝试生成时间段
      if (timeSlots.length === 0) {
        console.log('没有找到时间段，尝试生成时间段')
        try {
          const generateResponse = await timeslotApi.generateTimeSlots(venueId, date)
          console.log('生成时间段响应:', generateResponse)
          
          // 重新获取时间段
          const retryResponse = await venueApi.getVenueTimeSlots(venueId, date)
          console.log('重新获取时间段响应:', retryResponse)
          
          if (retryResponse && retryResponse.data && Array.isArray(retryResponse.data)) {
            timeSlots = retryResponse.data
          } else if (retryResponse && Array.isArray(retryResponse)) {
            timeSlots = retryResponse
          }
        } catch (generateError) {
          console.error('生成时间段失败:', generateError)
        }
      }
      
      // 确保时间段数据格式正确并过滤过期时间段
      const now = new Date()
      const currentDate = now.toISOString().split('T')[0] // 获取当前日期 YYYY-MM-DD
      const currentTime = now.getHours() * 60 + now.getMinutes() // 当前时间转换为分钟
      
      timeSlots = timeSlots.map(slot => {
        let processedSlot = slot
        
        // 如果后端返回的是 available 字段，转换为 status 字段
        if (slot.available !== undefined && slot.status === undefined) {
          processedSlot = {
            ...slot,
            status: slot.available ? 'AVAILABLE' : 'OCCUPIED'
          }
        }
        
        // 检查时间段是否已过期（仅对当天的时间段进行检查）
        if (date === currentDate && processedSlot.status === 'AVAILABLE') {
          const slotStartTime = processedSlot.startTime.split(':').map(Number)
          const slotStartMinutes = slotStartTime[0] * 60 + slotStartTime[1]
          
          // 如果时间段开始时间已过，将状态设置为已过期
          if (slotStartMinutes <= currentTime) {
            processedSlot = {
              ...processedSlot,
              status: 'EXPIRED',
              originalStatus: slot.status || (slot.available ? 'AVAILABLE' : 'OCCUPIED')
            }
          }
        }
        
        return processedSlot
      })
      
      console.log('转换后的时间段数据:', timeSlots)
      
      commit('SET_TIME_SLOTS', timeSlots)
      console.log('时间段已设置:', timeSlots)
      return response
    } catch (error) {
      console.error('获取时间段失败:', error)
      
      // 如果是网络错误或超时，提供模拟数据
      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('获取时间段超时') || error.statusCode === undefined) {
        console.log('使用模拟时间段数据')
        const mockTimeSlots = [
          { id: 1, startTime: '09:00', endTime: '10:00', price: 120, status: 'AVAILABLE' },
          { id: 2, startTime: '10:00', endTime: '11:00', price: 120, status: 'OCCUPIED' },
          { id: 3, startTime: '11:00', endTime: '12:00', price: 120, status: 'AVAILABLE' },
          { id: 4, startTime: '14:00', endTime: '15:00', price: 120, status: 'AVAILABLE' },
          { id: 5, startTime: '15:00', endTime: '16:00', price: 120, status: 'AVAILABLE' },
          { id: 6, startTime: '16:00', endTime: '17:00', price: 120, status: 'OCCUPIED' },
          { id: 7, startTime: '17:00', endTime: '18:00', price: 120, status: 'MAINTENANCE' },
          { id: 8, startTime: '19:00', endTime: '20:00', price: 150, status: 'AVAILABLE' },
          { id: 9, startTime: '20:00', endTime: '21:00', price: 150, status: 'AVAILABLE' }
        ]
        commit('SET_TIME_SLOTS', mockTimeSlots)
        console.log('已设置模拟时间段数据:', mockTimeSlots)
        return { data: mockTimeSlots }
      }
      
      showError(error.message || '获取时间段失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取场馆类型
  async getVenueTypes({ commit }) {
    try {
      console.log('开始获取场馆类型...')
      
      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), 8000) // 8秒超时
      })
      
      const apiPromise = venueApi.getVenueTypes()
      const response = await Promise.race([apiPromise, timeoutPromise])
      
      console.log('场馆类型API响应:', response)
      
      let types = []
      if (response && response.data && Array.isArray(response.data)) {
        types = response.data
      } else if (response && Array.isArray(response)) {
        types = response
      } else {
        console.warn('场馆类型API响应格式异常:', response)
      }
      
      commit('SET_VENUE_TYPES', types)
      console.log('场馆类型已设置:', types)
      return response
    } catch (error) {
      console.error('获取场馆类型失败:', error)
      
      // 如果是网络错误或超时，提供模拟数据
      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('请求超时') || error.statusCode === undefined) {
        console.log('使用模拟场馆类型数据')
        const mockTypes = [
          { id: 1, name: '篮球场' },
          { id: 2, name: '羽毛球' },
          { id: 3, name: '网球' },
          { id: 4, name: '乒乓球' },
          { id: 5, name: '足球场' }
        ]
        commit('SET_VENUE_TYPES', mockTypes)
        console.log('已设置模拟场馆类型数据:', mockTypes)
        return { data: mockTypes }
      }
      
      showError(error.message || '获取场馆类型失败')
      throw error
    }
  },
  
  // 搜索场馆
  async searchVenues({ commit }, params) {
    try {
      console.log('开始搜索场馆，参数:', params)
      commit('SET_LOADING', true)
      
      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('搜索超时')), 8000) // 8秒超时
      })
      
      const apiPromise = venueApi.searchVenues(params)
      const response = await Promise.race([apiPromise, timeoutPromise])
      
      console.log('搜索API响应:', response)
      
      let results = []
      if (response && response.data && Array.isArray(response.data)) {
        results = response.data
      } else if (response && Array.isArray(response)) {
        results = response
      } else {
        console.warn('搜索API响应格式异常:', response)
      }
      
      commit('SET_SEARCH_RESULTS', results)
      console.log('搜索结果已设置:', results)
      return response
    } catch (error) {
      console.error('搜索场馆失败:', error)
      
      // 如果是网络错误或超时，返回空结果
      if (error.message.includes('Network Error') || error.message.includes('timeout') || error.message.includes('搜索超时') || error.statusCode === undefined) {
        console.log('搜索超时或网络错误，返回空结果')
        commit('SET_SEARCH_RESULTS', [])
        return { data: [] }
      }
      
      showError(error.message || '搜索失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 更新时间段状态
  updateTimeSlotStatus({ commit }, { slotId, status }) {
    commit('UPDATE_TIME_SLOT_STATUS', { slotId, status })
  },
  
  // getTimeSlots 别名，指向 getVenueTimeSlots
  getTimeSlots({ dispatch }, params) {
    return dispatch('getVenueTimeSlots', params)
  }
}

const getters = {
  venueList: state => Array.isArray(state.venueList) ? state.venueList : [],
  popularVenues: state => Array.isArray(state.popularVenues) ? state.popularVenues : [],
  venueDetail: state => state.venueDetail,
  venueTypes: state => Array.isArray(state.venueTypes) ? state.venueTypes : [],
  timeSlots: state => Array.isArray(state.timeSlots) ? state.timeSlots : [],
  searchResults: state => Array.isArray(state.searchResults) ? state.searchResults : [],
  loading: state => state.loading,
  pagination: state => state.pagination,
  
  // 根据状态筛选时间段
  availableTimeSlots: state => {
    const slots = Array.isArray(state.timeSlots) ? state.timeSlots : []
    return slots.filter(slot => slot.status === 'AVAILABLE')
  },
  occupiedTimeSlots: state => {
    const slots = Array.isArray(state.timeSlots) ? state.timeSlots : []
    return slots.filter(slot => slot.status === 'OCCUPIED')
  },
  maintenanceTimeSlots: state => {
    const slots = Array.isArray(state.timeSlots) ? state.timeSlots : []
    return slots.filter(slot => slot.status === 'MAINTENANCE')
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}