import * as bookingApi from '@/api/booking.js'
import * as userApi from '@/api/user.js'
import * as sharingApi from '@/api/sharing.js'
import { showSuccess, showError } from '@/utils/ui.js'

const state = {
  bookingList: [],
  sharingOrders: [],
  userSharingOrders: [],
  joinedSharingOrders: [],
  bookingDetail: null,
  sharingOrderDetail: null,
  sharingDetail: null,
  loading: false,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  }
}

const mutations = {
  SET_BOOKING_LIST(state, { list, pagination }) {
    state.bookingList = list
    if (pagination) {
      state.pagination = { ...state.pagination, ...pagination }
    }
  },
  
  APPEND_BOOKING_LIST(state, list) {
    state.bookingList = [...state.bookingList, ...list]
  },
  
  SET_SHARING_ORDERS(state, orders) {
    state.sharingOrders = orders
  },
  
  SET_USER_SHARING_ORDERS(state, orders) {
    state.userSharingOrders = orders
  },
  
  SET_JOINED_SHARING_ORDERS(state, orders) {
    state.joinedSharingOrders = orders
  },
  
  SET_BOOKING_DETAIL(state, booking) {
    // 只有当booking有效时才设置，避免undefined覆盖现有数据
    if (booking && typeof booking === 'object') {
      state.bookingDetail = booking
    } else {
      console.warn('⚠️ 收到无效的booking数据:', booking)
      // 如果数据无效，设置一个默认的空对象，避免模板报错
      state.bookingDetail = {
        status: null,
        orderNo: null,
        venueName: null,
        bookingType: null,
        venueLocation: null,
        bookingDate: null,
        startTime: null,
        endTime: null,
        totalPrice: null,
        createdAt: null,
        venuePhone: null,
        sharingOrder: null
      }
    }
  },
  
  SET_SHARING_ORDER_DETAIL(state, order) {
    state.sharingOrderDetail = order
  },
  
  SET_SHARING_DETAIL(state, detail) {
    state.sharingDetail = detail
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  SET_PAGINATION(state, pagination) {
    state.pagination = pagination
  },
  
  UPDATE_BOOKING_STATUS(state, { bookingId, status }) {
    const booking = state.bookingList.find(b => b.id === bookingId)
    if (booking) {
      booking.status = status
    }
    if (state.bookingDetail && state.bookingDetail.id === bookingId) {
      state.bookingDetail.status = status
    }
  },
  
  UPDATE_SHARING_ORDER_PARTICIPANTS(state, { orderId, currentParticipants }) {
    const order = state.sharingOrders.find(o => o.id === orderId)
    if (order) {
      order.currentParticipants = currentParticipants
    }
    if (state.sharingOrderDetail && state.sharingOrderDetail.id === orderId) {
      state.sharingOrderDetail.currentParticipants = currentParticipants
    }
  },

  // 批量更新mutation，用于优化同步性能
  BATCH_UPDATE(state, updates) {
    if (updates.bookingList !== undefined) {
      state.bookingList = updates.bookingList
    }
    if (updates.bookingDetail !== undefined) {
      state.bookingDetail = updates.bookingDetail
    }
    if (updates.sharingOrders !== undefined) {
      state.sharingOrders = updates.sharingOrders
    }
    if (updates.userSharingOrders !== undefined) {
      state.userSharingOrders = updates.userSharingOrders
    }
    if (updates.joinedSharingOrders !== undefined) {
      state.joinedSharingOrders = updates.joinedSharingOrders
    }
    if (updates.sharingDetail !== undefined) {
      state.sharingDetail = updates.sharingDetail
    }
    if (updates.loading !== undefined) {
      state.loading = updates.loading
    }
    if (updates.pagination !== undefined) {
      state.pagination = { ...state.pagination, ...updates.pagination }
    }
  }
}

const actions = {
  // 创建预约
  async createBooking({ commit }, bookingData) {
    try {
      commit('SET_LOADING', true)
      const response = await bookingApi.createBooking(bookingData)
      showSuccess('预约成功')
      return response
    } catch (error) {
      showError(error.message || '预约失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 创建拼场预约
  async createSharedBooking({ commit }, bookingData) {
    try {
      commit('SET_LOADING', true)
      const response = await bookingApi.createSharedBooking(bookingData)
      showSuccess('拼场预约成功')
      return response
    } catch (error) {
      showError(error.message || '拼场预约失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取用户预约列表
  async getUserBookings({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      const response = await userApi.getUserBookings(params)
      
      console.log('API响应原始数据:', response)
      console.log('response.data:', response.data)
      console.log('response.data类型:', typeof response.data)
      
      const { data, total, page, pageSize, totalPages } = response
      
      console.log('解构后的数据:')
      console.log('data:', data)
      console.log('data类型:', typeof data)
      console.log('data是否为数组:', Array.isArray(data))
      console.log('total:', total)
      console.log('page:', page)
      console.log('pageSize:', pageSize)
      console.log('totalPages:', totalPages)
      
      const pagination = {
        current: page,
        pageSize: pageSize,
        total: total,
        totalPages: totalPages,
        currentPage: page
      }
      
      if (params.page === 1 || params.refresh) {
        console.log('设置新的预约列表，数据长度:', (data || []).length)
        commit('SET_BOOKING_LIST', { list: data || [], pagination: pagination })
      } else {
        console.log('追加预约列表，新增数据长度:', (data || []).length)
        commit('APPEND_BOOKING_LIST', data || [])
        commit('SET_PAGINATION', pagination)
      }
      
      return response
    } catch (error) {
      console.error('获取用户预约列表失败:', error)
      // 清空列表并重置分页
      commit('SET_BOOKING_LIST', { list: [], pagination: { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 } })
      showError(error.message || '获取预约列表失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取预约详情
  async getBookingDetail({ commit }, bookingId) {
    try {
      commit('SET_LOADING', true)
      console.log('🌐 发起API请求获取订单详情, ID:', bookingId)
      console.log('🌐 ID类型:', typeof bookingId)
      
      if (!bookingId) {
        throw new Error('订单ID不能为空')
      }
      
      const response = await bookingApi.getBookingDetail(bookingId)
      console.log('📡 完整API响应:', response)
      console.log('📡 响应类型:', typeof response)
      console.log('📡 响应是否为空:', !response)
      
      // 处理不同的响应数据结构
      let bookingData = null
      if (response && typeof response === 'object') {
        // 如果response直接是数据对象
        if (response.id || response.orderNo) {
          bookingData = response
          console.log('📡 使用response作为数据')
        }
        // 如果response有data属性
        else if (response.data) {
          bookingData = response.data
          console.log('📡 使用response.data作为数据')
        }
        // 如果response有result属性
        else if (response.result) {
          bookingData = response.result
          console.log('📡 使用response.result作为数据')
        }
        else {
          console.warn('📡 响应数据结构未知:', Object.keys(response))
          // 尝试直接使用response
          bookingData = response
        }
      } else {
        console.error('📡 响应数据无效:', response)
        throw new Error('服务器返回的数据格式不正确')
      }
      
      console.log('📡 处理后的订单数据:', bookingData)
      console.log('📡 数据类型:', typeof bookingData)
      console.log('📡 数据键:', bookingData ? Object.keys(bookingData) : 'null')
      console.log('⏰ API返回的开始时间:', bookingData?.startTime)
      console.log('⏰ API返回的结束时间:', bookingData?.endTime)
      console.log('💰 API返回的总价格:', bookingData?.totalPrice)
      console.log('🏷️ API返回的订单号(orderNo):', bookingData?.orderNo)
      console.log('🏷️ API返回的订单号(orderNumber):', bookingData?.orderNumber)
      console.log('🆔 API返回的ID:', bookingData?.id)
      
      if (!bookingData) {
        throw new Error('未能获取到有效的订单数据')
      }
      
      // 字段映射：如果后端返回的是orderNumber，映射为orderNo
      if (bookingData.orderNumber && !bookingData.orderNo) {
        bookingData.orderNo = bookingData.orderNumber
        console.log('🔄 字段映射: orderNumber -> orderNo:', bookingData.orderNo)
      }
      
      commit('SET_BOOKING_DETAIL', bookingData)
      console.log('✅ 数据已存储到store')
      return response
    } catch (error) {
      console.error('❌ API请求失败:', error)
      console.error('❌ 错误类型:', error.constructor.name)
      console.error('❌ 错误消息:', error.message)
      console.error('❌ 错误堆栈:', error.stack)
      
      // 清空详情数据
      commit('SET_BOOKING_DETAIL', null)
      
      showError(error.message || '获取预约详情失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 取消预约
  async cancelBooking({ commit, dispatch }, bookingId) {
    try {
      const response = await bookingApi.cancelBooking(bookingId)
      // 立即更新本地状态
      commit('UPDATE_BOOKING_STATUS', { bookingId, status: 'CANCELLED' })
      
      // 延迟重新获取数据以确保服务器状态同步
      setTimeout(() => {
        dispatch('getUserBookings', { page: 1, pageSize: 10, refresh: true })
      }, 1000)
      
      showSuccess('预约已取消')
      return response
    } catch (error) {
      showError(error.message || '取消预约失败')
      throw error
    }
  },
  
  // 申请拼场
  async createSharingOrder({ commit }, { orderId, data }) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.applySharedBooking(orderId, data)
      showSuccess('拼场申请已发送')
      return response
    } catch (error) {
      showError(error.message || '申请拼场失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取可拼场的订单列表
  async getSharingOrders({ commit, state }, params = {}) {
    try {
      commit('SET_LOADING', true)
      console.log('开始获取拼场订单，参数:', params)
      const response = await sharingApi.getJoinableSharingOrders(params)
      console.log('拼场订单API响应:', response)
      
      if (response && (response.list || response.data)) {
        // 处理两种可能的响应格式：直接返回数据 或 包装在data中
        const responseData = response.list ? response : response.data
        const { list, pagination } = responseData
        console.log('拼场订单列表:', list, '分页信息:', pagination)
        
        if (list && Array.isArray(list)) {
          if (params.page === 1 || params.refresh) {
            commit('SET_SHARING_ORDERS', list)
          } else {
            // 加载更多时追加数据
            const currentList = state.sharingOrders || []
            commit('SET_SHARING_ORDERS', [...currentList, ...list])
          }
          
          if (pagination) {
            commit('SET_PAGINATION', pagination)
          }
          
          console.log('成功设置拼场订单数据，数量:', list.length)
        } else {
          console.warn('拼场订单列表为空或格式不正确')
          commit('SET_SHARING_ORDERS', [])
        }
      } else {
        console.warn('拼场订单API响应数据为空')
        commit('SET_SHARING_ORDERS', [])
      }
      
      return response
    } catch (error) {
      console.error('获取拼场订单失败:', error)
      showError(error.message || '获取拼场订单失败')
      commit('SET_SHARING_ORDERS', [])
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 创建拼场订单
  async createSharingOrder({ commit }, sharingData) {
    try {
      const response = await sharingApi.createSharingOrder(sharingData)
      showSuccess('拼场订单创建成功')
      return response.data
    } catch (error) {
      showError(error.message || '创建拼场订单失败')
      throw error
    }
  },

  // 获取拼场订单详情
  async getSharingOrderDetail({ commit }, orderId) {
    try {
      const response = await sharingApi.getSharingOrderById(orderId)
      return response.data
    } catch (error) {
      showError(error.message || '获取拼场订单详情失败')
      throw error
    }
  },

  // 加入拼场订单
  async joinSharingOrder({ commit }, orderId) {
    try {
      const response = await sharingApi.joinSharingOrder(orderId)
      showSuccess('加入拼场成功')
      return response.data
    } catch (error) {
      showError(error.message || '加入拼场失败')
      throw error
    }
  },

  // 获取我创建的拼场订单
  async getMyCreatedSharingOrders({ commit }) {
    try {
      const response = await sharingApi.getMyCreatedSharingOrders()
      return response.data
    } catch (error) {
      showError(error.message || '获取我创建的拼场订单失败')
      throw error
    }
  },
  
  // 创建预约
  async createBooking({ commit }, bookingData) {
    try {
      commit('SET_LOADING', true)
      console.log('发起预约创建请求，数据:', bookingData)
      const response = await bookingApi.createBooking(bookingData)
      console.log('预约创建API响应:', response)
      
      // 返回响应数据，确保包含订单ID
      return response.data || response
    } catch (error) {
      console.error('创建预约失败:', error)
      showError(error.message || '创建预约失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 处理拼场申请
  async handleSharingRequest({ commit }, { requestId, data }) {
    try {
      const response = await sharingApi.handleSharedRequest(requestId, data)
      showSuccess(data.status === 'APPROVED' ? '已同意拼场申请' : '已拒绝拼场申请')
      return response
    } catch (error) {
      showError(error.message || '处理拼场申请失败')
      throw error
    }
  },
  
  // 获取我发出的拼场申请
  async getUserSharingOrders({ commit }, params = {}) {
    try {
      const response = await sharingApi.getMySharedRequests(params)
      commit('SET_USER_SHARING_ORDERS', response.data)
      return response
    } catch (error) {
      showError(error.message || '获取拼场申请失败')
      throw error
    }
  },

  // 获取我收到的拼场申请
  async getUserJoinedSharingOrders({ commit }, params = {}) {
    try {
      const response = await sharingApi.getReceivedSharedRequests(params)
      commit('SET_JOINED_SHARING_ORDERS', response.data)
      return response
    } catch (error) {
      showError(error.message || '获取拼场申请失败')
      throw error
    }
  },
  
  // 获取拼场详情
  async getSharingDetail({ commit }, sharingId) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.getSharingOrderById(sharingId)
      commit('SET_SHARING_DETAIL', response.data)
      return response
    } catch (error) {
      showError(error.message || '获取拼场详情失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 移除拼场参与者
  async removeSharingParticipant({ commit, dispatch, state }, { sharingId, participantId }) {
    try {
      commit('SET_LOADING', true)
      // 调用API移除参与者
      const response = await sharingApi.removeSharingParticipant(sharingId, participantId)
      
      // 重新获取拼场详情以确保数据同步
      await dispatch('getSharingDetail', sharingId)
      
      return response
    } catch (error) {
      showError(error.message || '移除参与者失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 更新拼场设置
  async updateSharingSettings({ commit, dispatch, state }, { sharingId, settings }) {
    try {
      commit('SET_LOADING', true)
      // 调用API更新设置
      const response = await sharingApi.updateSharingSettings(sharingId, settings)
      
      // 重新获取拼场详情以确保数据同步
      await dispatch('getSharingDetail', sharingId)
      
      return response
    } catch (error) {
      showError(error.message || '更新设置失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

const getters = {
  bookingList: state => Array.isArray(state.bookingList) ? state.bookingList : [],
  sharingOrders: state => Array.isArray(state.sharingOrders) ? state.sharingOrders : [],
  userSharingOrders: state => Array.isArray(state.userSharingOrders) ? state.userSharingOrders : [],
  joinedSharingOrders: state => Array.isArray(state.joinedSharingOrders) ? state.joinedSharingOrders : [],
  bookingDetail: state => state.bookingDetail,
  sharingOrderDetail: state => state.sharingOrderDetail,
  sharingDetail: state => state.sharingDetail,
  loading: state => state.loading,
  pagination: state => state.pagination,
  
  // 根据状态筛选预约
  activeBookings: state => (state.bookingList || []).filter(booking => 
    booking && ['PENDING', 'CONFIRMED'].includes(booking.status)
  ),
  completedBookings: state => (state.bookingList || []).filter(booking => 
    booking && booking.status === 'COMPLETED'
  ),
  cancelledBookings: state => (state.bookingList || []).filter(booking => 
    booking && booking.status === 'CANCELLED'
  ),
  
  // 可加入的拼场订单
  availableSharingOrders: state => (state.sharingOrders || []).filter(order => 
    order && order.status === 'OPEN' && order.currentParticipants < order.maxParticipants
  )
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}