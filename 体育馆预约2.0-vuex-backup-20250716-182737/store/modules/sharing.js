import * as sharingApi from '@/api/sharing.js'
import { showSuccess, showError } from '@/utils/ui.js'

const state = {
  sharingOrders: [],
  mySharingOrders: [],
  receivedRequests: [],
  sentRequests: [],
  sharingOrderDetail: null,
  loading: false,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  }
}

const mutations = {
  SET_SHARING_ORDERS(state, orders) {
    state.sharingOrders = orders
  },
  
  SET_MY_SHARING_ORDERS(state, orders) {
    state.mySharingOrders = orders
  },
  
  SET_RECEIVED_REQUESTS(state, requests) {
    state.receivedRequests = requests
  },
  
  SET_SENT_REQUESTS(state, requests) {
    state.sentRequests = requests
  },
  
  SET_SHARING_ORDER_DETAIL(state, order) {
    state.sharingOrderDetail = order
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },
  
  UPDATE_SHARING_ORDER_STATUS(state, { orderId, status }) {
    const order = state.sharingOrders.find(o => o.id === orderId)
    if (order) {
      order.status = status
    }
    if (state.sharingOrderDetail && state.sharingOrderDetail.id === orderId) {
      state.sharingOrderDetail.status = status
    }
  },
  
  UPDATE_SHARING_ORDER_PARTICIPANTS(state, { orderId, currentParticipants }) {
    const order = state.sharingOrders.find(o => o.id === orderId)
    if (order) {
      order.currentParticipants = currentParticipants
    }
    if (state.sharingOrderDetail && state.sharingOrderDetail.id === orderId) {
      state.sharingOrderDetail.currentParticipants = currentParticipants
    }
  },

  // 批量更新mutation，用于优化同步性能
  BATCH_UPDATE(state, updates) {
    if (updates.sharingOrders !== undefined) {
      state.sharingOrders = updates.sharingOrders
    }
    if (updates.mySharingOrders !== undefined) {
      state.mySharingOrders = updates.mySharingOrders
    }
    if (updates.receivedRequests !== undefined) {
      state.receivedRequests = updates.receivedRequests
    }
    if (updates.sentRequests !== undefined) {
      state.sentRequests = updates.sentRequests
    }
    if (updates.sharingOrderDetail !== undefined) {
      state.sharingOrderDetail = updates.sharingOrderDetail
    }
    if (updates.loading !== undefined) {
      state.loading = updates.loading
    }
    if (updates.pagination !== undefined) {
      state.pagination = { ...state.pagination, ...updates.pagination }
    }
  }
}

const actions = {
  // 获取可加入的拼场订单
  async getSharingOrders({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      
      const response = await sharingApi.getJoinableSharingOrders(params)
      console.log('Store: API返回的原始数据:', response)
      
      const orders = response.list || response.data || []
      console.log('Store: 提取的订单数据:', orders)
      
      commit('SET_SHARING_ORDERS', orders)
      if (response.pagination) {
        commit('SET_PAGINATION', response.pagination)
      }
      return response
    } catch (error) {
      console.error('获取拼场订单失败:', error)
      showError(error.message || '获取拼场订单失败')
      commit('SET_SHARING_ORDERS', [])
      return { data: [] }
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取可加入的拼场订单（别名方法）
  async getJoinableSharingOrders({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      
      const response = await sharingApi.getJoinableSharingOrders(params)
      console.log('Store: API返回的原始数据:', response)
      
      const orders = response.list || response.data || []
      console.log('Store: 提取的订单数据:', orders)
      
      commit('SET_SHARING_ORDERS', orders)
      if (response.pagination) {
        commit('SET_PAGINATION', response.pagination)
      }
      return response
    } catch (error) {
      console.error('获取拼场订单失败:', error)
      showError(error.message || '获取拼场订单失败')
      commit('SET_SHARING_ORDERS', [])
      return { data: [] }
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取所有拼场订单（包括所有状态）
  async getAllSharingOrders({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      
      const response = await sharingApi.getAllSharingOrders(params)
      console.log('Store: 获取所有拼场订单API返回的原始数据:', response)
      
      const orders = response.list || response.data || []
      console.log('Store: 提取的所有拼场订单数据:', orders)
      
      commit('SET_SHARING_ORDERS', orders)
      if (response.pagination) {
        commit('SET_PAGINATION', response.pagination)
      }
      return response
    } catch (error) {
      console.error('获取所有拼场订单失败:', error)
      showError(error.message || '获取所有拼场订单失败')
      commit('SET_SHARING_ORDERS', [])
      return { data: [] }
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 按场馆ID获取可加入的拼场订单
  async getJoinableSharingOrdersByVenueId({ commit }, { venueId, params = {} }) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.getJoinableSharingOrdersByVenueId(venueId, params)
      const orders = response.list || response.data || []
      commit('SET_SHARING_ORDERS', orders)
      return response
    } catch (error) {
      showError(error.message || '获取场馆拼场订单失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 按日期获取可加入的拼场订单
  async getJoinableSharingOrdersByDate({ commit }, { date, params = {} }) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.getJoinableSharingOrdersByDate(date, params)
      const orders = response.list || response.data || []
      commit('SET_SHARING_ORDERS', orders)
      return response
    } catch (error) {
      showError(error.message || '获取日期拼场订单失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 按场馆ID和日期获取可加入的拼场订单
  async getJoinableSharingOrdersByVenueIdAndDate({ commit }, { venueId, date, params = {} }) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.getJoinableSharingOrdersByVenueIdAndDate(venueId, date, params)
      const orders = response.list || response.data || []
      commit('SET_SHARING_ORDERS', orders)
      return response
    } catch (error) {
      showError(error.message || '获取拼场订单失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取我创建的拼场订单
  async getMyCreatedSharingOrders({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.getMyCreatedSharingOrders(params)
      const orders = response.list || response.data || []
      commit('SET_MY_SHARING_ORDERS', orders)
      return response
    } catch (error) {
      showError(error.message || '获取我的拼场订单失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取拼场订单详情
  async getSharingOrderDetail({ commit }, orderId) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.getSharingOrderById(orderId)
      const orderDetail = response.data || response
      commit('SET_SHARING_ORDER_DETAIL', orderDetail)
      return response
    } catch (error) {
      showError(error.message || '获取拼场订单详情失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 加入拼场订单
  async joinSharingOrder({ commit }, payload) {
    try {
      // 支持两种调用方式：直接传orderId或传{orderId, data}对象
      const orderId = typeof payload === 'object' ? payload.orderId : payload
      const applicationData = typeof payload === 'object' ? payload.data : {}
      
      const response = await sharingApi.joinSharingOrder(orderId, applicationData)
      showSuccess('申请提交成功')
      // 更新参与人数
      if (response.data && response.data.currentParticipants !== undefined) {
        commit('UPDATE_SHARING_ORDER_PARTICIPANTS', {
          orderId,
          currentParticipants: response.data.currentParticipants
        })
      }
      return response
    } catch (error) {
      showError(error.message || '申请提交失败')
      throw error
    }
  },
  
  // 取消加入拼场订单
  async cancelJoinSharingOrder({ commit }, orderId) {
    try {
      const response = await sharingApi.cancelJoinSharingOrder(orderId)
      showSuccess('已取消加入拼场')
      // 更新参与人数
      if (response.data && response.data.currentParticipants !== undefined) {
        commit('UPDATE_SHARING_ORDER_PARTICIPANTS', {
          orderId,
          currentParticipants: response.data.currentParticipants
        })
      }
      return response
    } catch (error) {
      showError(error.message || '取消加入拼场失败')
      throw error
    }
  },
  
  // 确认拼场订单
  async confirmSharingOrder({ commit }, orderId) {
    try {
      const response = await sharingApi.confirmSharingOrder(orderId)
      showSuccess('拼场订单已确认')
      commit('UPDATE_SHARING_ORDER_STATUS', { orderId, status: 'CONFIRMED' })
      return response
    } catch (error) {
      showError(error.message || '确认拼场订单失败')
      throw error
    }
  },
  
  // 取消拼场订单
  async cancelSharingOrder({ commit }, orderId) {
    try {
      const response = await sharingApi.cancelSharingOrder(orderId)
      showSuccess('拼场订单已取消')
      commit('UPDATE_SHARING_ORDER_STATUS', { orderId, status: 'CANCELLED' })
      return response
    } catch (error) {
      showError(error.message || '取消拼场订单失败')
      throw error
    }
  },
  
  // 获取我的拼场申请
  async getMySharingRequests({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.getMySharedRequests(params)
      commit('SET_SENT_REQUESTS', response.data || [])
      return response.data || []
    } catch (error) {
      console.error('获取我的拼场申请失败:', error)
      showError(error.message || '获取我的拼场申请失败')
      commit('SET_SENT_REQUESTS', [])
      return []
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取收到的拼场申请
  async getReceivedSharingRequests({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      const response = await sharingApi.getReceivedSharedRequests(params)
      commit('SET_RECEIVED_REQUESTS', response.data || [])
      return response.data || []
    } catch (error) {
      console.error('获取收到的拼场申请失败:', error)
      showError(error.message || '获取收到的拼场申请失败')
      commit('SET_RECEIVED_REQUESTS', [])
      return []
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 申请拼场
  async applySharingOrder({ commit }, { orderId, data }) {
    try {
      const response = await sharingApi.applySharedBooking(orderId, data)
      // 不在这里显示消息，让前端页面根据响应状态决定显示内容
      return response
    } catch (error) {
      showError(error.message || '申请拼场失败')
      throw error
    }
  },

  // 申请加入拼场订单（需要支付）
  async applyJoinSharingOrder({ commit }, orderId) {
    try {
      console.log('[SharingStore] 开始申请加入拼场订单:', orderId)
      commit('SET_LOADING', true)

      const response = await sharingApi.applyJoinSharingOrder(orderId)

      if (response && response.success) {
        console.log('[SharingStore] 申请加入拼场订单成功')
        return response
      } else {
        throw new Error(response.message || '申请失败')
      }
    } catch (error) {
      console.error('[SharingStore] 申请加入拼场订单失败:', error)
      showError(error.message || '申请加入拼场订单失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 取消拼场申请
  async cancelSharingRequest({ commit }, requestId) {
    try {
      const response = await sharingApi.cancelSharingRequest(requestId)
      showSuccess('申请已取消')
      return response
    } catch (error) {
      showError(error.message || '取消申请失败')
      throw error
    }
  },
  
  // 处理拼场申请
  async processSharingRequest({ commit }, { requestId, action, reason = '' }) {
    try {
      const data = {
        action: action, // 直接传递action参数：'approve' 或 'reject'
        responseMessage: reason || ''
      }

      const response = await sharingApi.handleSharedRequest(requestId, data)
      showSuccess(action === 'approve' ? '已同意拼场申请' : '已拒绝拼场申请')
      return response
    } catch (error) {
      // 检查是否是需要支付的错误
      if (error.needPayment) {
        // 保留完整的错误信息，包括needPayment和orderId
        const enhancedError = new Error(error.message || '处理拼场申请失败')
        enhancedError.needPayment = error.needPayment
        enhancedError.orderId = error.orderId
        enhancedError.orderStatus = error.orderStatus
        throw enhancedError
      } else {
        showError(error.message || '处理拼场申请失败')
        throw error
      }
    }
  },
  
  // 移除拼场参与者
  async removeSharingParticipant({ commit }, { orderId, participantId }) {
    try {
      const response = await sharingApi.removeSharingParticipant(orderId, participantId)
      showSuccess('已移除参与者')
      // 更新参与人数
      if (response.data && response.data.currentParticipants !== undefined) {
        commit('UPDATE_SHARING_ORDER_PARTICIPANTS', {
          orderId,
          currentParticipants: response.data.currentParticipants
        })
      }
      return response
    } catch (error) {
      showError(error.message || '移除参与者失败')
      throw error
    }
  },
  
  // 更新拼场设置
  async updateSharingSettings({ commit }, { sharingId, settings }) {
    try {
      const response = await sharingApi.updateSharingSettings(sharingId, settings)
      showSuccess('设置已更新')
      return response
    } catch (error) {
      showError(error.message || '更新设置失败')
      throw error
    }
  }
}

const getters = {
  sharingOrders: state => state.sharingOrders,
  mySharingOrders: state => state.mySharingOrders,
  receivedRequests: state => state.receivedRequests,
  sentRequests: state => state.sentRequests,
  sharingOrderDetail: state => state.sharingOrderDetail,
  loading: state => state.loading,
  pagination: state => state.pagination,
  
  // 可加入的拼场订单
  availableSharingOrders: state => state.sharingOrders.filter(order => 
    order.status === 'OPEN' && order.currentParticipants < order.maxParticipants
  ),
  
  // 进行中的拼场订单
  activeSharingOrders: state => state.mySharingOrders.filter(order => 
    ['OPEN', 'CONFIRMED'].includes(order.status)
  ),
  
  // 已完成的拼场订单
  completedSharingOrders: state => state.mySharingOrders.filter(order => 
    order.status === 'COMPLETED'
  ),
  
  // 已取消的拼场订单
  cancelledSharingOrders: state => state.mySharingOrders.filter(order => 
    order.status === 'CANCELLED'
  ),
  
  // 待处理的拼场申请
  pendingRequests: state => state.receivedRequests.filter(request => 
    request.status === 'PENDING'
  )
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}