import * as authApi from '@/api/auth.js'
import * as userApi from '@/api/user.js'
import { setToken, removeToken, setUserInfo, removeUserInfo, getUserInfo, getToken } from '@/utils/auth.js'
import { showSuccess, showError } from '@/utils/ui.js'
import { clearAuthCache, updateAuthCache } from '@/utils/router-guard-new.js'

const state = {
  token: getToken(),
  userInfo: getUserInfo(),
  userStats: {
    totalBookings: 0,
    totalSharings: 0
  },
  isLoggedIn: !!(getToken() && getUserInfo()),
  loginChecking: false // 是否正在检查登录状态
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    setToken(token)
  },
  
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    setUserInfo(userInfo)
  },
  
  SET_LOGIN_STATUS(state, status) {
    console.log('[UserStore] 设置登录状态:', status)
    state.isLoggedIn = status
    // 同步更新路由守卫缓存
    updateAuthCache(status)
  },
  
  SET_LOGIN_CHECKING(state, checking) {
    state.loginChecking = checking
  },
  
  SET_USER_STATS(state, stats) {
    state.userStats = stats
  },
  
  CLEAR_USER_DATA(state) {
    console.log('[UserStore] 清除用户数据')
    state.token = ''
    state.userInfo = null
    state.userStats = {
      totalBookings: 0,
      totalSharings: 0
    }
    state.isLoggedIn = false
    state.loginChecking = false
    removeToken()
    removeUserInfo()
    // 清除路由守卫缓存
    clearAuthCache()
  }
}

const actions = {
  // 用户登录
  async login({ commit }, loginData) {
    try {
      console.log('[UserStore] 开始登录')
      const response = await authApi.login(loginData)
      console.log('[UserStore] 登录响应:', response)
      
      if (!response) {
        throw new Error('登录响应为空')
      }
      
      const responseData = response.data || response
      const token = responseData.accessToken || responseData.token
      
      if (!token) {
        console.error('[UserStore] 响应数据:', responseData)
        throw new Error('未获取到登录令牌')
      }
      
      const user = {
        id: responseData.id,
        username: responseData.username,
        email: responseData.email,
        phone: responseData.phone,
        nickname: responseData.nickname,
        avatar: responseData.avatar,
        roles: responseData.roles
      }
      
      // 更新状态
      commit('SET_TOKEN', token)
      commit('SET_USER_INFO', user)
      commit('SET_LOGIN_STATUS', true)
      
      console.log('[UserStore] 登录成功，用户信息:', user)
      return response
    } catch (error) {
      console.error('[UserStore] 登录错误:', error)
      commit('SET_LOGIN_STATUS', false)
      throw error
    }
  },
  
  // 用户注册
  async register({ commit }, registerData) {
    try {
      const response = await authApi.register(registerData)
      showSuccess('注册成功')
      return response
    } catch (error) {
      showError(error.message || '注册失败')
      throw error
    }
  },
  
  // 获取短信验证码
  async getSmsCode({ commit }, smsData) {
    try {
      const response = await authApi.getSmsCode(smsData.phone)
      return response
    } catch (error) {
      throw error
    }
  },

  // 短信验证码登录
  async smsLogin({ commit }, loginData) {
    try {
      console.log('[UserStore] 开始短信登录')
      const response = await authApi.smsLogin(loginData)
      console.log('[UserStore] 短信登录响应:', response)
      
      if (!response) {
        throw new Error('短信登录响应为空')
      }
      
      const responseData = response.data || response
      const token = responseData.accessToken || responseData.token
      
      if (!token) {
        console.error('[UserStore] 响应数据:', responseData)
        throw new Error('未获取到登录令牌')
      }
      
      const user = {
        id: responseData.id,
        username: responseData.username,
        email: responseData.email,
        phone: responseData.phone,
        nickname: responseData.nickname,
        avatar: responseData.avatar,
        roles: responseData.roles
      }
      
      // 更新状态
      commit('SET_TOKEN', token)
      commit('SET_USER_INFO', user)
      commit('SET_LOGIN_STATUS', true)
      
      console.log('[UserStore] 短信登录成功，用户信息:', user)
      return response
    } catch (error) {
      console.error('[UserStore] 短信登录错误:', error)
      commit('SET_LOGIN_STATUS', false)
      throw error
    }
  },
  
  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      console.log('[UserStore] 开始获取用户信息')
      const response = await userApi.getUserInfo()
      console.log('[UserStore] 获取用户信息响应:', response)
      
      if (response && response.data) {
        console.log('[UserStore] 用户信息数据:', response.data)
        commit('SET_USER_INFO', response.data)
        commit('SET_LOGIN_STATUS', true)
        return response
      } else {
        console.warn('[UserStore] 用户信息响应为空或无data字段')
        return null
      }
    } catch (error) {
      console.error('[UserStore] 获取用户信息失败:', error)
      // 如果是认证错误，清除登录状态
      if (error.code === 'LOGIN_EXPIRED' || error.message.includes('未授权')) {
        commit('CLEAR_USER_DATA')
      }
      throw error
    }
  },
  
  // 获取用户统计信息
  async getUserStats({ commit }) {
    try {
      const response = await userApi.getUserStats()
      commit('SET_USER_STATS', response.data)
      return response
    } catch (error) {
      console.error('[UserStore] 获取用户统计信息失败:', error)
      commit('SET_USER_STATS', {
        totalBookings: 0,
        totalSharings: 0
      })
      throw error
    }
  },
  
  // 更新用户信息
  async updateUserInfo({ commit }, userData) {
    try {
      console.log('[UserStore] 发送到后端的用户数据:', JSON.stringify(userData, null, 2))
      const response = await userApi.updateUserInfo(userData)
      console.log('[UserStore] 后端返回的响应:', response)
      commit('SET_USER_INFO', response.data)
      showSuccess('信息更新成功')
      return response
    } catch (error) {
      console.error('[UserStore] 更新用户信息失败:', error)
      showError(error.message || '更新失败')
      throw error
    }
  },
  
  // 修改密码
  async changeUserPassword({ commit }, passwordData) {
    try {
      const response = await userApi.changePassword(passwordData)
      showSuccess('密码修改成功')
      return response
    } catch (error) {
      showError(error.message || '密码修改失败')
      throw error
    }
  },
  
  // 用户登出
  async logout({ commit }) {
    try {
      console.log('[UserStore] 开始登出')
      // 尝试调用后端API退出
      await authApi.logout().catch(err => {
        console.warn('[UserStore] 调用退出API失败，可能token已过期:', err)
      })
    } finally {
      // 无论API调用成功与否，都清理前端数据
      commit('CLEAR_USER_DATA')
      showSuccess('已退出登录')
      console.log('[UserStore] 登出完成')
    }
  },

  // 检查登录状态（优化版本）
  async checkLoginStatus({ commit, state }) {
    // 避免重复检查
    if (state.loginChecking) {
      console.log('[UserStore] 正在检查登录状态，跳过重复检查')
      return state.isLoggedIn
    }
    
    try {
      commit('SET_LOGIN_CHECKING', true)
      
      const token = getToken()
      const userInfo = getUserInfo()
      console.log('[UserStore] 检查登录状态 - token:', !!token, 'userInfo:', !!userInfo)

      // 如果本地没有token或用户信息，直接返回未登录
      if (!token || !userInfo) {
        console.log('[UserStore] 本地无登录信息，清除状态')
        commit('CLEAR_USER_DATA')
        return false
      }

      // 验证token有效性
      try {
        const response = await userApi.getUserInfo()
        console.log('[UserStore] token验证成功')
        
        if (response && response.data) {
          // token有效，更新用户信息和登录状态
          commit('SET_TOKEN', token)
          commit('SET_USER_INFO', response.data)
          commit('SET_LOGIN_STATUS', true)
          return true
        } else {
          console.log('[UserStore] 用户信息响应异常，清除状态')
          commit('CLEAR_USER_DATA')
          return false
        }
      } catch (error) {
        console.log('[UserStore] token验证失败:', error.message)
        
        // 检查是否是认证相关错误
        if (error.code === 'LOGIN_EXPIRED' || 
            error.message.includes('登录已过期') || 
            error.message.includes('未授权') ||
            error.message.includes('token') ||
            error.status === 401) {
          console.log('[UserStore] 认证失效，清除本地数据')
          commit('CLEAR_USER_DATA')
          return false
        }
        
        // 网络错误等其他情况，暂时保持登录状态
        console.log('[UserStore] 网络异常，保持本地登录状态')
        commit('SET_TOKEN', token)
        commit('SET_USER_INFO', userInfo)
        commit('SET_LOGIN_STATUS', true)
        return true
      }
    } catch (error) {
      console.error('[UserStore] 检查登录状态异常:', error)
      commit('CLEAR_USER_DATA')
      return false
    } finally {
      commit('SET_LOGIN_CHECKING', false)
    }
  },
  
  // 强制更新登录状态
  updateLoginStatus({ commit }, isLoggedIn) {
    console.log('[UserStore] 强制更新登录状态:', isLoggedIn)
    commit('SET_LOGIN_STATUS', isLoggedIn)
  },
  
  // 初始化用户状态
  initUserState({ commit, dispatch }) {
    console.log('[UserStore] 初始化用户状态')
    const token = getToken()
    const userInfo = getUserInfo()
    
    if (token && userInfo) {
      commit('SET_TOKEN', token)
      commit('SET_USER_INFO', userInfo)
      commit('SET_LOGIN_STATUS', true)
      console.log('[UserStore] 从本地存储恢复登录状态')
    } else {
      commit('SET_LOGIN_STATUS', false)
      console.log('[UserStore] 本地无登录信息')
    }
  }
}

const getters = {
  isLoggedIn: state => state.isLoggedIn,
  userInfo: state => state.userInfo,
  userStats: state => state.userStats,
  token: state => state.token,
  userId: state => state.userInfo?.id,
  username: state => state.userInfo?.username,
  nickname: state => state.userInfo?.nickname,
  avatar: state => state.userInfo?.avatar,
  phone: state => state.userInfo?.phone,
  email: state => state.userInfo?.email,
  loginChecking: state => state.loginChecking
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}