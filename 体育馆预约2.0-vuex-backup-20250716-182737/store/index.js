import { createStore } from 'vuex'
import { createPinia } from 'pinia'
import user from './modules/user.js'
import venue from './modules/venue.js'
import booking from './modules/booking.js'
import sharing from './modules/sharing.js'

// 创建pinia实例
export const pinia = createPinia()

// 创建vuex store
const store = createStore({
  modules: {
    user,
    venue,
    booking,
    sharing
  },

  state: {
    // 全局状态
    loading: false,
    networkStatus: true
  },

  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading
    },

    SET_NETWORK_STATUS(state, status) {
      state.networkStatus = status
    }
  },

  actions: {
    setLoading({ commit }, loading) {
      commit('SET_LOADING', loading)
    },

    setNetworkStatus({ commit }, status) {
      commit('SET_NETWORK_STATUS', status)
    }
  },

  getters: {
    isLoading: state => state.loading,
    isOnline: state => state.networkStatus
  }
})

export default store
