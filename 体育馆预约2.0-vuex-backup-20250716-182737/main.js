import App from './App.vue'

// #ifndef VUE3
import Vue from 'vue'
import store from './store/index.js'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import store, { pinia } from './store/index.js'
import { vuexSyncPlugin } from './stores/plugins/vuex-sync.js'

// 添加同步插件
pinia.use(vuexSyncPlugin())

export function createApp() {
  const app = createSSRApp(App)
  app.use(store) // 保留Vuex
  app.use(pinia) // 添加Pinia
  return {
    app
  }
}
// #endif