import { get, post, patch } from '@/utils/request.js'

// 获取场馆指定日期的所有时间段
export function getVenueTimeSlots(venueId, date) {
  return get(`/timeslots/venue/${venueId}/date/${date}`)
}

// 获取场馆指定日期的可用时间段
export function getAvailableTimeSlots(venueId, date) {
  return get(`/timeslots/venue/${venueId}/date/${date}/available`)
}

// 检查时间段是否可预约
export function checkTimeSlotAvailability(params) {
  return get('/timeslots/check', params)
}

// 为场馆生成指定日期的时间段（仅限管理员）
export function generateTimeSlots(venueId, date) {
  return post(`/timeslots/venue/${venueId}/date/${date}/generate`)
}

// 批量生成未来一周的时间段（仅限管理员）
export function generateWeekTimeSlots(venueId) {
  return post(`/timeslots/venue/${venueId}/generate-week`)
}

// 更新时间段状态（仅限管理员）
export function updateTimeSlotStatus(id, data) {
  return patch(`/timeslots/${id}/status`, data)
}