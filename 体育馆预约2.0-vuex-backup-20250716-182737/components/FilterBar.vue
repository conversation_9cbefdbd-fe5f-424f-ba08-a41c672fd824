<template>
  <view class="filter-bar">
    <!-- 筛选按钮组 -->
    <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
      <view class="filter-buttons">
        <!-- 位置筛选 -->
        <view 
          class="filter-btn"
          :class="{ 'active': activeFilters.location }"
          @click="showLocationPicker"
        >
          <text class="btn-text">{{ locationText }}</text>
          <text class="btn-icon">▼</text>
        </view>
        
        <!-- 类型筛选 -->
        <view 
          class="filter-btn"
          :class="{ 'active': activeFilters.type }"
          @click="showTypePicker"
        >
          <text class="btn-text">{{ typeText }}</text>
          <text class="btn-icon">▼</text>
        </view>
        
        <!-- 价格筛选 -->
        <view 
          class="filter-btn"
          :class="{ 'active': activeFilters.price }"
          @click="showPricePicker"
        >
          <text class="btn-text">{{ priceText }}</text>
          <text class="btn-icon">▼</text>
        </view>
        
        <!-- 时间筛选 -->
        <view 
          class="filter-btn"
          :class="{ 'active': activeFilters.time }"
          @click="showTimePicker"
          v-if="showTimeFilter"
        >
          <text class="btn-text">{{ timeText }}</text>
          <text class="btn-icon">▼</text>
        </view>
        
        <!-- 状态筛选 -->
        <view 
          class="filter-btn"
          :class="{ 'active': activeFilters.status }"
          @click="showStatusPicker"
          v-if="showStatusFilter"
        >
          <text class="btn-text">{{ statusText }}</text>
          <text class="btn-icon">▼</text>
        </view>
        
        <!-- 更多筛选 -->
        <view 
          class="filter-btn more-btn"
          :class="{ 'active': hasMoreFilters }"
          @click="showMoreFilters"
          v-if="showMoreButton"
        >
          <text class="btn-text">筛选</text>
          <text class="btn-icon">⚙</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 排序按钮 -->
    <view class="sort-section">
      <view 
        class="sort-btn"
        :class="{ 'active': sortBy }"
        @click="showSortPicker"
      >
        <text class="sort-text">{{ sortText }}</text>
        <text class="sort-icon" :class="{ 'desc': sortOrder === 'desc' }">↑</text>
      </view>
    </view>
    
    <!-- 位置选择器 -->
    <uni-popup ref="locationPopup" type="bottom">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-title">选择位置</text>
          <text class="picker-close" @click="closeLocationPicker">✕</text>
        </view>
        <view class="picker-content">
          <view class="location-options">
            <view 
              class="option-item"
              :class="{ 'selected': selectedLocation === '' }"
              @click="selectLocation('')"
            >
              <text class="option-text">不限</text>
            </view>
            <view 
              class="option-item"
              :class="{ 'selected': selectedLocation === location.value }"
              v-for="location in locationOptions"
              :key="location.value"
              @click="selectLocation(location.value)"
            >
              <text class="option-text">{{ location.label }}</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 类型选择器 -->
    <uni-popup ref="typePopup" type="bottom">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-title">选择类型</text>
          <text class="picker-close" @click="closeTypePicker">✕</text>
        </view>
        <view class="picker-content">
          <view class="type-options">
            <view 
              class="option-item"
              :class="{ 'selected': selectedType === '' }"
              @click="selectType('')"
            >
              <text class="option-text">不限</text>
            </view>
            <view 
              class="option-item"
              :class="{ 'selected': selectedType === type.value }"
              v-for="type in typeOptions"
              :key="type.value"
              @click="selectType(type.value)"
            >
              <text class="option-text">{{ type.label }}</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 价格选择器 -->
    <uni-popup ref="pricePopup" type="bottom">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-title">选择价格</text>
          <text class="picker-close" @click="closePricePicker">✕</text>
        </view>
        <view class="picker-content">
          <view class="price-options">
            <view 
              class="option-item"
              :class="{ 'selected': selectedPriceRange === '' }"
              @click="selectPriceRange('')"
            >
              <text class="option-text">不限</text>
            </view>
            <view 
              class="option-item"
              :class="{ 'selected': selectedPriceRange === price.value }"
              v-for="price in priceOptions"
              :key="price.value"
              @click="selectPriceRange(price.value)"
            >
              <text class="option-text">{{ price.label }}</text>
            </view>
          </view>
          
          <!-- 自定义价格范围 -->
          <view class="custom-price">
            <text class="custom-label">自定义价格范围</text>
            <view class="price-inputs">
              <input 
                class="price-input"
                type="number"
                placeholder="最低价"
                v-model="customMinPrice"
              />
              <text class="price-separator">-</text>
              <input 
                class="price-input"
                type="number"
                placeholder="最高价"
                v-model="customMaxPrice"
              />
              <button class="confirm-btn" @click="confirmCustomPrice">确定</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 排序选择器 -->
    <uni-popup ref="sortPopup" type="bottom">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-title">选择排序</text>
          <text class="picker-close" @click="closeSortPicker">✕</text>
        </view>
        <view class="picker-content">
          <view class="sort-options">
            <view 
              class="option-item"
              :class="{ 'selected': selectedSort === sort.value }"
              v-for="sort in sortOptions"
              :key="sort.value"
              @click="selectSort(sort.value)"
            >
              <text class="option-text">{{ sort.label }}</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 更多筛选弹窗 -->
    <uni-popup ref="morePopup" type="center">
      <view class="more-filters-container">
        <view class="more-header">
          <text class="more-title">筛选条件</text>
          <text class="more-close" @click="closeMoreFilters">✕</text>
        </view>
        
        <scroll-view class="more-content" scroll-y="true">
          <!-- 距离筛选 -->
          <view class="filter-section" v-if="showDistanceFilter">
            <text class="section-title">距离范围</text>
            <view class="distance-options">
              <view 
                class="distance-item"
                :class="{ 'selected': selectedDistance === distance.value }"
                v-for="distance in distanceOptions"
                :key="distance.value"
                @click="selectDistance(distance.value)"
              >
                <text class="distance-text">{{ distance.label }}</text>
              </view>
            </view>
          </view>
          
          <!-- 评分筛选 -->
          <view class="filter-section" v-if="showRatingFilter">
            <text class="section-title">最低评分</text>
            <view class="rating-options">
              <view 
                class="rating-item"
                :class="{ 'selected': selectedRating === rating.value }"
                v-for="rating in ratingOptions"
                :key="rating.value"
                @click="selectRating(rating.value)"
              >
                <text class="rating-text">{{ rating.label }}</text>
              </view>
            </view>
          </view>
          
          <!-- 设施筛选 -->
          <view class="filter-section" v-if="showFacilitiesFilter">
            <text class="section-title">设施要求</text>
            <view class="facilities-options">
              <view 
                class="facility-item"
                :class="{ 'selected': selectedFacilities.includes(facility.value) }"
                v-for="facility in facilitiesOptions"
                :key="facility.value"
                @click="toggleFacility(facility.value)"
              >
                <text class="facility-text">{{ facility.label }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
        
        <view class="more-actions">
          <button class="reset-btn" @click="resetMoreFilters">重置</button>
          <button class="confirm-btn" @click="confirmMoreFilters">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'FilterBar',
  
  props: {
    // 位置选项
    locationOptions: {
      type: Array,
      default: () => [
        { label: '附近1km', value: '1km' },
        { label: '附近3km', value: '3km' },
        { label: '附近5km', value: '5km' },
        { label: '全城', value: 'all' }
      ]
    },
    
    // 类型选项
    typeOptions: {
      type: Array,
      default: () => [
        { label: '羽毛球', value: 'badminton' },
        { label: '篮球', value: 'basketball' },
        { label: '网球', value: 'tennis' },
        { label: '乒乓球', value: 'pingpong' },
        { label: '足球', value: 'football' }
      ]
    },
    
    // 价格选项
    priceOptions: {
      type: Array,
      default: () => [
        { label: '50元以下', value: '0-50' },
        { label: '50-100元', value: '50-100' },
        { label: '100-200元', value: '100-200' },
        { label: '200元以上', value: '200-999999' }
      ]
    },
    
    // 排序选项
    sortOptions: {
      type: Array,
      default: () => [
        { label: '默认排序', value: 'default' },
        { label: '距离最近', value: 'distance' },
        { label: '价格最低', value: 'price' },
        { label: '评分最高', value: 'rating' },
        { label: '最新发布', value: 'createTime' }
      ]
    },
    
    // 距离选项
    distanceOptions: {
      type: Array,
      default: () => [
        { label: '不限', value: '' },
        { label: '1km内', value: '1' },
        { label: '3km内', value: '3' },
        { label: '5km内', value: '5' },
        { label: '10km内', value: '10' }
      ]
    },
    
    // 评分选项
    ratingOptions: {
      type: Array,
      default: () => [
        { label: '不限', value: '' },
        { label: '3分以上', value: '3' },
        { label: '4分以上', value: '4' },
        { label: '4.5分以上', value: '4.5' }
      ]
    },
    
    // 设施选项
    facilitiesOptions: {
      type: Array,
      default: () => [
        { label: '停车场', value: 'parking' },
        { label: '淋浴间', value: 'shower' },
        { label: '空调', value: 'aircon' },
        { label: '储物柜', value: 'locker' },
        { label: 'WiFi', value: 'wifi' }
      ]
    },
    
    // 是否显示时间筛选
    showTimeFilter: {
      type: Boolean,
      default: false
    },
    
    // 是否显示状态筛选
    showStatusFilter: {
      type: Boolean,
      default: false
    },
    
    // 是否显示更多按钮
    showMoreButton: {
      type: Boolean,
      default: true
    },
    
    // 是否显示距离筛选
    showDistanceFilter: {
      type: Boolean,
      default: true
    },
    
    // 是否显示评分筛选
    showRatingFilter: {
      type: Boolean,
      default: true
    },
    
    // 是否显示设施筛选
    showFacilitiesFilter: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      // 当前选中的筛选条件
      selectedLocation: '',
      selectedType: '',
      selectedPriceRange: '',
      selectedTime: '',
      selectedStatus: '',
      selectedSort: 'default',
      sortOrder: 'asc',
      
      // 更多筛选条件
      selectedDistance: '',
      selectedRating: '',
      selectedFacilities: [],
      
      // 自定义价格
      customMinPrice: '',
      customMaxPrice: ''
    }
  },
  
  computed: {
    // 激活的筛选条件
    activeFilters() {
      return {
        location: !!this.selectedLocation,
        type: !!this.selectedType,
        price: !!this.selectedPriceRange,
        time: !!this.selectedTime,
        status: !!this.selectedStatus
      }
    },
    
    // 是否有更多筛选条件
    hasMoreFilters() {
      return !!this.selectedDistance || !!this.selectedRating || this.selectedFacilities.length > 0
    },
    
    // 位置文本
    locationText() {
      if (!this.selectedLocation) return '位置'
      const option = this.locationOptions.find(item => item.value === this.selectedLocation)
      return option ? option.label : '位置'
    },
    
    // 类型文本
    typeText() {
      if (!this.selectedType) return '类型'
      const option = this.typeOptions.find(item => item.value === this.selectedType)
      return option ? option.label : '类型'
    },
    
    // 价格文本
    priceText() {
      if (!this.selectedPriceRange) return '价格'
      const option = this.priceOptions.find(item => item.value === this.selectedPriceRange)
      return option ? option.label : '价格'
    },
    
    // 时间文本
    timeText() {
      return this.selectedTime || '时间'
    },
    
    // 状态文本
    statusText() {
      return this.selectedStatus || '状态'
    },
    
    // 排序文本
    sortText() {
      const option = this.sortOptions.find(item => item.value === this.selectedSort)
      return option ? option.label : '排序'
    }
  },
  
  methods: {
    // 显示位置选择器
    showLocationPicker() {
      this.$refs.locationPopup.open()
    },
    
    // 关闭位置选择器
    closeLocationPicker() {
      this.$refs.locationPopup.close()
    },
    
    // 选择位置
    selectLocation(value) {
      this.selectedLocation = value
      this.closeLocationPicker()
      this.emitFilterChange()
    },
    
    // 显示类型选择器
    showTypePicker() {
      this.$refs.typePopup.open()
    },
    
    // 关闭类型选择器
    closeTypePicker() {
      this.$refs.typePopup.close()
    },
    
    // 选择类型
    selectType(value) {
      this.selectedType = value
      this.closeTypePicker()
      this.emitFilterChange()
    },
    
    // 显示价格选择器
    showPricePicker() {
      this.$refs.pricePopup.open()
    },
    
    // 关闭价格选择器
    closePricePicker() {
      this.$refs.pricePopup.close()
    },
    
    // 选择价格范围
    selectPriceRange(value) {
      this.selectedPriceRange = value
      this.closePricePicker()
      this.emitFilterChange()
    },
    
    // 确认自定义价格
    confirmCustomPrice() {
      if (this.customMinPrice || this.customMaxPrice) {
        const min = this.customMinPrice || '0'
        const max = this.customMaxPrice || '999999'
        this.selectedPriceRange = `${min}-${max}`
        this.closePricePicker()
        this.emitFilterChange()
      }
    },
    
    // 显示时间选择器
    showTimePicker() {
      // 可以根据需要实现时间选择逻辑
      this.$emit('time-picker')
    },
    
    // 显示状态选择器
    showStatusPicker() {
      // 可以根据需要实现状态选择逻辑
      this.$emit('status-picker')
    },
    
    // 显示排序选择器
    showSortPicker() {
      this.$refs.sortPopup.open()
    },
    
    // 关闭排序选择器
    closeSortPicker() {
      this.$refs.sortPopup.close()
    },
    
    // 选择排序
    selectSort(value) {
      if (this.selectedSort === value) {
        // 切换排序方向
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc'
      } else {
        this.selectedSort = value
        this.sortOrder = 'asc'
      }
      this.closeSortPicker()
      this.emitSortChange()
    },
    
    // 显示更多筛选
    showMoreFilters() {
      this.$refs.morePopup.open()
    },
    
    // 关闭更多筛选
    closeMoreFilters() {
      this.$refs.morePopup.close()
    },
    
    // 选择距离
    selectDistance(value) {
      this.selectedDistance = value
    },
    
    // 选择评分
    selectRating(value) {
      this.selectedRating = value
    },
    
    // 切换设施
    toggleFacility(value) {
      const index = this.selectedFacilities.indexOf(value)
      if (index > -1) {
        this.selectedFacilities.splice(index, 1)
      } else {
        this.selectedFacilities.push(value)
      }
    },
    
    // 重置更多筛选
    resetMoreFilters() {
      this.selectedDistance = ''
      this.selectedRating = ''
      this.selectedFacilities = []
    },
    
    // 确认更多筛选
    confirmMoreFilters() {
      this.closeMoreFilters()
      this.emitFilterChange()
    },
    
    // 发送筛选变化事件
    emitFilterChange() {
      const filters = {
        location: this.selectedLocation,
        type: this.selectedType,
        priceRange: this.selectedPriceRange,
        time: this.selectedTime,
        status: this.selectedStatus,
        distance: this.selectedDistance,
        rating: this.selectedRating,
        facilities: this.selectedFacilities
      }
      
      this.$emit('filter-change', filters)
    },
    
    // 发送排序变化事件
    emitSortChange() {
      this.$emit('sort-change', {
        sortBy: this.selectedSort,
        sortOrder: this.sortOrder
      })
    },
    
    // 重置所有筛选
    resetAllFilters() {
      this.selectedLocation = ''
      this.selectedType = ''
      this.selectedPriceRange = ''
      this.selectedTime = ''
      this.selectedStatus = ''
      this.selectedSort = 'default'
      this.sortOrder = 'asc'
      this.resetMoreFilters()
      this.emitFilterChange()
      this.emitSortChange()
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-bar {
  background-color: #ffffff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
}

// 筛选滚动区域
.filter-scroll {
  flex: 1;
  
  .filter-buttons {
    display: flex;
    align-items: center;
    white-space: nowrap;
    gap: 16rpx;
    
    .filter-btn {
      display: flex;
      align-items: center;
      padding: 12rpx 20rpx;
      background-color: #f8f8f8;
      border-radius: 20rpx;
      border: 1rpx solid transparent;
      
      &.active {
        background-color: #fff7e6;
        border-color: #ff6b35;
        
        .btn-text {
          color: #ff6b35;
        }
        
        .btn-icon {
          color: #ff6b35;
        }
      }
      
      &.more-btn {
        background-color: #f0f0f0;
        
        &.active {
          background-color: #e6f7ff;
          border-color: #1890ff;
          
          .btn-text {
            color: #1890ff;
          }
          
          .btn-icon {
            color: #1890ff;
          }
        }
      }
      
      .btn-text {
        font-size: 26rpx;
        color: #666666;
        margin-right: 8rpx;
      }
      
      .btn-icon {
        font-size: 20rpx;
        color: #999999;
      }
    }
  }
}

// 排序区域
.sort-section {
  margin-left: 20rpx;
  
  .sort-btn {
    display: flex;
    align-items: center;
    padding: 12rpx 16rpx;
    background-color: #f8f8f8;
    border-radius: 20rpx;
    border: 1rpx solid transparent;
    
    &.active {
      background-color: #fff7e6;
      border-color: #ff6b35;
      
      .sort-text {
        color: #ff6b35;
      }
      
      .sort-icon {
        color: #ff6b35;
      }
    }
    
    .sort-text {
      font-size: 26rpx;
      color: #666666;
      margin-right: 8rpx;
    }
    
    .sort-icon {
      font-size: 20rpx;
      color: #999999;
      transition: transform 0.3s;
      
      &.desc {
        transform: rotate(180deg);
      }
    }
  }
}

// 选择器容器
.picker-container {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  
  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .picker-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .picker-close {
      font-size: 32rpx;
      color: #999999;
      padding: 10rpx;
    }
  }
  
  .picker-content {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;
    
    .option-item {
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f8f8f8;
      
      &.selected {
        .option-text {
          color: #ff6b35;
          font-weight: 600;
        }
      }
      
      .option-text {
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}

// 自定义价格
.custom-price {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  
  .custom-label {
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .price-inputs {
    display: flex;
    align-items: center;
    gap: 16rpx;
    
    .price-input {
      flex: 1;
      height: 64rpx;
      padding: 0 20rpx;
      border: 1rpx solid #d9d9d9;
      border-radius: 8rpx;
      font-size: 26rpx;
    }
    
    .price-separator {
      font-size: 26rpx;
      color: #999999;
    }
    
    .confirm-btn {
      padding: 16rpx 32rpx;
      background-color: #ff6b35;
      color: #ffffff;
      border: none;
      border-radius: 8rpx;
      font-size: 26rpx;
    }
  }
}

// 更多筛选容器
.more-filters-container {
  background-color: #ffffff;
  border-radius: 20rpx;
  width: 80vw;
  max-height: 80vh;
  
  .more-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .more-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .more-close {
      font-size: 32rpx;
      color: #999999;
      padding: 10rpx;
    }
  }
  
  .more-content {
    padding: 30rpx;
    max-height: 50vh;
    
    .filter-section {
      margin-bottom: 40rpx;
      
      .section-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 20rpx;
      }
      
      // 距离选项
      .distance-options,
      .rating-options {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        
        .distance-item,
        .rating-item {
          padding: 16rpx 24rpx;
          background-color: #f8f8f8;
          border-radius: 20rpx;
          border: 1rpx solid transparent;
          
          &.selected {
            background-color: #fff7e6;
            border-color: #ff6b35;
            
            .distance-text,
            .rating-text {
              color: #ff6b35;
            }
          }
          
          .distance-text,
          .rating-text {
            font-size: 26rpx;
            color: #666666;
          }
        }
      }
      
      // 设施选项
      .facilities-options {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        
        .facility-item {
          padding: 16rpx 24rpx;
          background-color: #f8f8f8;
          border-radius: 20rpx;
          border: 1rpx solid transparent;
          
          &.selected {
            background-color: #fff7e6;
            border-color: #ff6b35;
            
            .facility-text {
              color: #ff6b35;
            }
          }
          
          .facility-text {
            font-size: 26rpx;
            color: #666666;
          }
        }
      }
    }
  }
  
  .more-actions {
    display: flex;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    gap: 20rpx;
    
    .reset-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
      font-weight: 600;
    }
    
    .reset-btn {
      background-color: #f5f5f5;
      color: #666666;
    }
    
    .confirm-btn {
      background-color: #ff6b35;
      color: #ffffff;
    }
  }
}
</style>