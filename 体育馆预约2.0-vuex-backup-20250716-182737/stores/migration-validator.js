import store from '@/store'
import { useAppStore } from './app.js'
import { useUserStore } from './user.js'
import { useVenueStore } from './venue.js'
import { useSharingStore } from './sharing.js'
import { useBookingStore } from './booking.js'
import { migrationConfig, logMigration } from './migration-config.js'

// 状态验证器
export class MigrationValidator {
  constructor() {
    this.appStore = null
    this.userStore = null
    this.venueStore = null
    this.sharingStore = null
    this.bookingStore = null
    this.validationResults = {}
  }
  
  // 初始化stores
  init() {
    try {
      this.appStore = useAppStore()
      this.userStore = useUserStore()
      this.venueStore = useVenueStore()
      this.sharingStore = useSharingStore()
      this.bookingStore = useBookingStore()
      logMigration('迁移验证器初始化成功')
    } catch (error) {
      console.error('[Migration Validator] 初始化失败:', error)
    }
  }
  
  // 验证App模块状态同步
  validateAppModule() {
    if (!this.appStore) return { valid: false, error: 'AppStore未初始化' }
    
    const vuexState = store.state
    const piniaState = this.appStore.$state
    
    const results = {
      loading: vuexState.loading === piniaState.loading,
      networkStatus: vuexState.networkStatus === piniaState.networkStatus
    }
    
    const allValid = Object.values(results).every(v => v)
    
    logMigration('App模块验证结果', { results, allValid })
    
    return {
      valid: allValid,
      details: results,
      vuexState: { loading: vuexState.loading, networkStatus: vuexState.networkStatus },
      piniaState: { loading: piniaState.loading, networkStatus: piniaState.networkStatus }
    }
  }
  
  // 验证User模块状态同步
  validateUserModule() {
    if (!this.userStore) return { valid: false, error: 'UserStore未初始化' }
    
    const vuexUserState = store.state.user
    const piniaUserState = this.userStore.$state
    
    if (!vuexUserState) {
      return { valid: false, error: 'Vuex User模块未找到' }
    }
    
    const results = {
      token: vuexUserState.token === piniaUserState.token,
      isLoggedIn: vuexUserState.isLoggedIn === piniaUserState.isLoggedIn,
      loginChecking: vuexUserState.loginChecking === piniaUserState.loginChecking,
      userInfo: JSON.stringify(vuexUserState.userInfo) === JSON.stringify(piniaUserState.userInfo),
      userStats: JSON.stringify(vuexUserState.userStats) === JSON.stringify(piniaUserState.userStats)
    }
    
    const allValid = Object.values(results).every(v => v)
    
    logMigration('User模块验证结果', { results, allValid })
    
    return {
      valid: allValid,
      details: results,
      vuexState: {
        token: vuexUserState.token,
        isLoggedIn: vuexUserState.isLoggedIn,
        loginChecking: vuexUserState.loginChecking,
        userInfo: vuexUserState.userInfo,
        userStats: vuexUserState.userStats
      },
      piniaState: {
        token: piniaUserState.token,
        isLoggedIn: piniaUserState.isLoggedIn,
        loginChecking: piniaUserState.loginChecking,
        userInfo: piniaUserState.userInfo,
        userStats: piniaUserState.userStats
      }
    }
  }

  // 验证Venue模块状态同步
  validateVenueModule() {
    if (!this.venueStore) return { valid: false, error: 'VenueStore未初始化' }

    const vuexVenueState = store.state.venue
    const piniaVenueState = this.venueStore.$state

    if (!vuexVenueState) {
      return { valid: false, error: 'Vuex Venue模块未找到' }
    }

    const results = {
      venueList: JSON.stringify(vuexVenueState.venueList) === JSON.stringify(piniaVenueState.venueList),
      popularVenues: JSON.stringify(vuexVenueState.popularVenues) === JSON.stringify(piniaVenueState.popularVenues),
      venueDetail: JSON.stringify(vuexVenueState.venueDetail) === JSON.stringify(piniaVenueState.venueDetail),
      venueTypes: JSON.stringify(vuexVenueState.venueTypes) === JSON.stringify(piniaVenueState.venueTypes),
      timeSlots: JSON.stringify(vuexVenueState.timeSlots) === JSON.stringify(piniaVenueState.timeSlots),
      searchResults: JSON.stringify(vuexVenueState.searchResults) === JSON.stringify(piniaVenueState.searchResults),
      loading: vuexVenueState.loading === piniaVenueState.loading,
      pagination: JSON.stringify(vuexVenueState.pagination) === JSON.stringify(piniaVenueState.pagination)
    }

    const allValid = Object.values(results).every(v => v)

    logMigration('Venue模块验证结果', { results, allValid })

    return {
      valid: allValid,
      details: results,
      vuexState: {
        venueList: vuexVenueState.venueList,
        popularVenues: vuexVenueState.popularVenues,
        venueDetail: vuexVenueState.venueDetail,
        venueTypes: vuexVenueState.venueTypes,
        timeSlots: vuexVenueState.timeSlots,
        searchResults: vuexVenueState.searchResults,
        loading: vuexVenueState.loading,
        pagination: vuexVenueState.pagination
      },
      piniaState: {
        venueList: piniaVenueState.venueList,
        popularVenues: piniaVenueState.popularVenues,
        venueDetail: piniaVenueState.venueDetail,
        venueTypes: piniaVenueState.venueTypes,
        timeSlots: piniaVenueState.timeSlots,
        searchResults: piniaVenueState.searchResults,
        loading: piniaVenueState.loading,
        pagination: piniaVenueState.pagination
      }
    }
  }

  // 验证Sharing模块状态同步
  validateSharingModule() {
    if (!this.sharingStore) return { valid: false, error: 'SharingStore未初始化' }

    const vuexSharingState = store.state.sharing
    const piniaSharingState = this.sharingStore.$state

    if (!vuexSharingState) {
      return { valid: false, error: 'Vuex Sharing模块未找到' }
    }

    const results = {
      sharingOrders: JSON.stringify(vuexSharingState.sharingOrders) === JSON.stringify(piniaSharingState.sharingOrders),
      mySharingOrders: JSON.stringify(vuexSharingState.mySharingOrders) === JSON.stringify(piniaSharingState.mySharingOrders),
      receivedRequests: JSON.stringify(vuexSharingState.receivedRequests) === JSON.stringify(piniaSharingState.receivedRequests),
      sentRequests: JSON.stringify(vuexSharingState.sentRequests) === JSON.stringify(piniaSharingState.sentRequests),
      sharingOrderDetail: JSON.stringify(vuexSharingState.sharingOrderDetail) === JSON.stringify(piniaSharingState.sharingOrderDetail),
      loading: vuexSharingState.loading === piniaSharingState.loading,
      pagination: JSON.stringify(vuexSharingState.pagination) === JSON.stringify(piniaSharingState.pagination)
    }

    const allValid = Object.values(results).every(v => v)

    logMigration('Sharing模块验证结果', { results, allValid })

    return {
      valid: allValid,
      details: results,
      vuexState: {
        sharingOrders: vuexSharingState.sharingOrders,
        mySharingOrders: vuexSharingState.mySharingOrders,
        receivedRequests: vuexSharingState.receivedRequests,
        sentRequests: vuexSharingState.sentRequests,
        sharingOrderDetail: vuexSharingState.sharingOrderDetail,
        loading: vuexSharingState.loading,
        pagination: vuexSharingState.pagination
      },
      piniaState: {
        sharingOrders: piniaSharingState.sharingOrders,
        mySharingOrders: piniaSharingState.mySharingOrders,
        receivedRequests: piniaSharingState.receivedRequests,
        sentRequests: piniaSharingState.sentRequests,
        sharingOrderDetail: piniaSharingState.sharingOrderDetail,
        loading: piniaSharingState.loading,
        pagination: piniaSharingState.pagination
      }
    }
  }

  // 验证Booking模块状态同步
  validateBookingModule() {
    if (!this.bookingStore) return { valid: false, error: 'BookingStore未初始化' }

    const vuexBookingState = store.state.booking
    const piniaBookingState = this.bookingStore.$state

    if (!vuexBookingState) {
      return { valid: false, error: 'Vuex Booking模块未找到' }
    }

    const results = {
      bookingList: JSON.stringify(vuexBookingState.bookingList) === JSON.stringify(piniaBookingState.bookingList),
      bookingDetail: JSON.stringify(vuexBookingState.bookingDetail) === JSON.stringify(piniaBookingState.bookingDetail),
      sharingOrders: JSON.stringify(vuexBookingState.sharingOrders) === JSON.stringify(piniaBookingState.sharingOrders),
      userSharingOrders: JSON.stringify(vuexBookingState.userSharingOrders) === JSON.stringify(piniaBookingState.userSharingOrders),
      joinedSharingOrders: JSON.stringify(vuexBookingState.joinedSharingOrders) === JSON.stringify(piniaBookingState.joinedSharingOrders),
      sharingDetail: JSON.stringify(vuexBookingState.sharingDetail) === JSON.stringify(piniaBookingState.sharingDetail),
      loading: vuexBookingState.loading === piniaBookingState.loading,
      pagination: JSON.stringify(vuexBookingState.pagination) === JSON.stringify(piniaBookingState.pagination)
    }

    const allValid = Object.values(results).every(v => v)

    logMigration('Booking模块验证结果', { results, allValid })

    return {
      valid: allValid,
      details: results,
      vuexState: {
        bookingList: vuexBookingState.bookingList,
        bookingDetail: vuexBookingState.bookingDetail,
        sharingOrders: vuexBookingState.sharingOrders,
        userSharingOrders: vuexBookingState.userSharingOrders,
        joinedSharingOrders: vuexBookingState.joinedSharingOrders,
        sharingDetail: vuexBookingState.sharingDetail,
        loading: vuexBookingState.loading,
        pagination: vuexBookingState.pagination
      },
      piniaState: {
        bookingList: piniaBookingState.bookingList,
        bookingDetail: piniaBookingState.bookingDetail,
        sharingOrders: piniaBookingState.sharingOrders,
        userSharingOrders: piniaBookingState.userSharingOrders,
        joinedSharingOrders: piniaBookingState.joinedSharingOrders,
        sharingDetail: piniaBookingState.sharingDetail,
        loading: piniaBookingState.loading,
        pagination: piniaBookingState.pagination
      }
    }
  }

  // 验证所有已激活模块
  validateAllModules() {
    const results = {}
    
    if (migrationConfig.activeModules.includes('app')) {
      results.app = this.validateAppModule()
    }
    
    if (migrationConfig.activeModules.includes('user')) {
      results.user = this.validateUserModule()
    }

    if (migrationConfig.activeModules.includes('venue')) {
      results.venue = this.validateVenueModule()
    }

    if (migrationConfig.activeModules.includes('sharing')) {
      results.sharing = this.validateSharingModule()
    }

    if (migrationConfig.activeModules.includes('booking')) {
      results.booking = this.validateBookingModule()
    }

    const overallValid = Object.values(results).every(r => r.valid)
    
    this.validationResults = {
      timestamp: new Date().toISOString(),
      overall: overallValid,
      modules: results
    }
    
    logMigration('全模块验证完成', this.validationResults)
    
    return this.validationResults
  }
  
  // 获取验证报告
  getValidationReport() {
    return this.validationResults
  }
  
  // 持续验证 - 定期检查状态同步
  startContinuousValidation(interval = 5000) {
    logMigration('开始持续验证', { interval })
    
    return setInterval(() => {
      const results = this.validateAllModules()
      
      if (!results.overall) {
        console.warn('[Migration Validator] 发现状态不同步:', results)
        
        // 可以在这里添加自动修复逻辑
        this.attemptAutoFix(results)
      }
    }, interval)
  }
  
  // 尝试自动修复状态不同步
  attemptAutoFix(validationResults) {
    logMigration('尝试自动修复状态不同步')
    
    // App模块修复
    if (validationResults.modules.app && !validationResults.modules.app.valid) {
      const vuexState = store.state
      if (this.appStore) {
        this.appStore.loading = vuexState.loading
        this.appStore.networkStatus = vuexState.networkStatus
        logMigration('App模块状态已修复')
      }
    }
    
    // User模块修复
    if (validationResults.modules.user && !validationResults.modules.user.valid) {
      const vuexUserState = store.state.user
      if (this.userStore && vuexUserState) {
        this.userStore.token = vuexUserState.token
        this.userStore.userInfo = vuexUserState.userInfo
        this.userStore.isLoggedIn = vuexUserState.isLoggedIn
        this.userStore.loginChecking = vuexUserState.loginChecking
        this.userStore.userStats = vuexUserState.userStats
        logMigration('User模块状态已修复')
      }
    }

    // Venue模块修复
    if (validationResults.modules.venue && !validationResults.modules.venue.valid) {
      const vuexVenueState = store.state.venue
      if (this.venueStore && vuexVenueState) {
        this.venueStore.venueList = vuexVenueState.venueList || []
        this.venueStore.popularVenues = vuexVenueState.popularVenues || []
        this.venueStore.venueDetail = vuexVenueState.venueDetail
        this.venueStore.venueTypes = vuexVenueState.venueTypes || []
        this.venueStore.timeSlots = vuexVenueState.timeSlots || []
        this.venueStore.searchResults = vuexVenueState.searchResults || []
        this.venueStore.loading = vuexVenueState.loading || false
        this.venueStore.pagination = vuexVenueState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1 }
        logMigration('Venue模块状态已修复')
      }
    }

    // Sharing模块修复
    if (validationResults.modules.sharing && !validationResults.modules.sharing.valid) {
      const vuexSharingState = store.state.sharing
      if (this.sharingStore && vuexSharingState) {
        this.sharingStore.sharingOrders = vuexSharingState.sharingOrders || []
        this.sharingStore.mySharingOrders = vuexSharingState.mySharingOrders || []
        this.sharingStore.receivedRequests = vuexSharingState.receivedRequests || []
        this.sharingStore.sentRequests = vuexSharingState.sentRequests || []
        this.sharingStore.sharingOrderDetail = vuexSharingState.sharingOrderDetail
        this.sharingStore.loading = vuexSharingState.loading || false
        this.sharingStore.pagination = vuexSharingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1 }
        logMigration('Sharing模块状态已修复')
      }
    }

    // Booking模块修复
    if (validationResults.modules.booking && !validationResults.modules.booking.valid) {
      const vuexBookingState = store.state.booking
      if (this.bookingStore && vuexBookingState) {
        this.bookingStore.bookingList = vuexBookingState.bookingList || []
        this.bookingStore.bookingDetail = vuexBookingState.bookingDetail
        this.bookingStore.sharingOrders = vuexBookingState.sharingOrders || []
        this.bookingStore.userSharingOrders = vuexBookingState.userSharingOrders || []
        this.bookingStore.joinedSharingOrders = vuexBookingState.joinedSharingOrders || []
        this.bookingStore.sharingDetail = vuexBookingState.sharingDetail
        this.bookingStore.loading = vuexBookingState.loading || false
        this.bookingStore.pagination = vuexBookingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 }
        logMigration('Booking模块状态已修复')
      }
    }
  }
  
  // 停止持续验证
  stopContinuousValidation(intervalId) {
    if (intervalId) {
      clearInterval(intervalId)
      logMigration('持续验证已停止')
    }
  }
}

// 创建全局验证器实例
export const migrationValidator = new MigrationValidator()

// 便捷方法
export function validateMigration() {
  migrationValidator.init()
  return migrationValidator.validateAllModules()
}

export function startValidation(interval) {
  migrationValidator.init()
  return migrationValidator.startContinuousValidation(interval)
}

export function stopValidation(intervalId) {
  migrationValidator.stopContinuousValidation(intervalId)
}
