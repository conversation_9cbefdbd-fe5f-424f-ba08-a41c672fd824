import { defineStore } from 'pinia'
import * as sharingApi from '@/api/sharing.js'
import { showSuccess, showError } from '@/utils/ui.js'

export const useSharingStore = defineStore('sharing', {
  state: () => ({
    sharingOrders: [],
    mySharingOrders: [],
    receivedRequests: [],
    sentRequests: [],
    sharingOrderDetail: null,
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1
    }
  }),

  getters: {
    // 基础getters
    getSharingOrders: (state) => state.sharingOrders,
    getMySharingOrders: (state) => state.mySharingOrders,
    getReceivedRequests: (state) => state.receivedRequests,
    getSentRequests: (state) => state.sentRequests,
    getSharingOrderDetail: (state) => state.sharingOrderDetail,
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,
    
    // 计算属性
    totalSharingOrders: (state) => state.sharingOrders.length,
    totalMySharingOrders: (state) => state.mySharingOrders.length,
    totalReceivedRequests: (state) => state.receivedRequests.length,
    totalSentRequests: (state) => state.sentRequests.length,
    
    // 按状态筛选
    getOrdersByStatus: (state) => (status) => {
      return state.sharingOrders.filter(order => order.status === status)
    },
    
    // 待处理的请求
    getPendingRequests: (state) => {
      return state.receivedRequests.filter(request => request.status === 'PENDING')
    },
    
    // 是否有更多数据
    hasMoreData: (state) => {
      return state.pagination.current < state.pagination.totalPages
    }
  },

  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    },
    
    // 设置分享订单列表
    setSharingOrders(orders) {
      this.sharingOrders = Array.isArray(orders) ? orders : []
    },
    
    // 设置我的分享订单
    setMySharingOrders(orders) {
      this.mySharingOrders = Array.isArray(orders) ? orders : []
    },
    
    // 设置收到的请求
    setReceivedRequests(requests) {
      this.receivedRequests = Array.isArray(requests) ? requests : []
    },
    
    // 设置发送的请求
    setSentRequests(requests) {
      this.sentRequests = Array.isArray(requests) ? requests : []
    },
    
    // 设置分享订单详情
    setSharingOrderDetail(order) {
      this.sharingOrderDetail = order
    },
    
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },
    
    // 更新订单状态
    updateOrderStatus({ orderId, status }) {
      const order = this.sharingOrders.find(o => o.id === orderId)
      if (order) {
        order.status = status
      }
      
      const myOrder = this.mySharingOrders.find(o => o.id === orderId)
      if (myOrder) {
        myOrder.status = status
      }
    },
    
    // 获取分享订单列表
    async getSharingOrdersList(params = {}) {
      try {
        console.log('[SharingStore] 开始获取分享订单列表，参数:', params)
        this.setLoading(true)

        const response = await sharingApi.getJoinableSharingOrders(params)

        if (response && response.data) {
          const orders = Array.isArray(response.data) ? response.data : []
          this.setSharingOrders(orders)

          if (response.pagination) {
            this.setPagination(response.pagination)
          }

          console.log('[SharingStore] 分享订单列表获取成功:', orders.length, '条')
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 获取分享订单列表失败:', error)
        showError(error.message || '获取分享订单列表失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取可加入的拼场订单
    async getJoinableSharingOrders(params = {}) {
      try {
        console.log('[SharingStore] 开始获取可加入的拼场订单，参数:', params)
        this.setLoading(true)

        const response = await sharingApi.getJoinableSharingOrders(params)

        if (response) {
          // 处理后端返回的数据格式：{list: [...], pagination: {...}}
          const orders = response.list || response.data || []

          console.log('[SharingStore] 可加入拼场订单后端返回数据格式:', {
            hasData: !!response.data,
            hasList: !!response.list,
            ordersLength: orders.length,
            pagination: response.pagination
          })

          // 如果是刷新或第一页，替换数据；否则追加数据
          if (params.refresh || params.page === 1) {
            this.setSharingOrders(orders)
          } else {
            this.sharingOrders.push(...orders)
          }

          if (response.pagination) {
            this.setPagination(response.pagination)
          }

          console.log('[SharingStore] 可加入拼场订单获取成功:', orders.length, '条')
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 获取可加入拼场订单失败:', error)
        showError(error.message || '获取可加入拼场订单失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取所有拼场订单
    async getAllSharingOrders(params = {}) {
      try {
        console.log('[SharingStore] 开始获取所有拼场订单，参数:', params)
        this.setLoading(true)

        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时
        })

        const apiPromise = sharingApi.getAllSharingOrders(params)
        const response = await Promise.race([apiPromise, timeoutPromise])

        if (response) {
          // 处理后端返回的数据格式：{list: [...], pagination: {...}}
          const orders = response.list || response.data || []

          console.log('[SharingStore] 后端返回数据格式:', {
            hasData: !!response.data,
            hasList: !!response.list,
            ordersLength: orders.length,
            pagination: response.pagination
          })

          // 如果是刷新或第一页，替换数据；否则追加数据
          if (params.refresh || params.page === 1) {
            this.setSharingOrders(orders)
          } else {
            this.sharingOrders.push(...orders)
          }

          if (response.pagination) {
            this.setPagination(response.pagination)
          }

          console.log('[SharingStore] 所有拼场订单获取成功:', orders.length, '条')
        } else {
          console.warn('[SharingStore] 获取所有拼场订单返回空数据')
          // 如果是刷新，清空数据
          if (params.refresh || params.page === 1) {
            this.setSharingOrders([])
          }
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 获取所有拼场订单失败:', error)

        // 如果是超时错误，显示特定提示
        if (error.message === '请求超时') {
          showError('加载超时，请检查网络连接')
        } else {
          showError(error.message || '获取所有拼场订单失败')
        }

        // 如果是刷新，确保清空加载状态
        if (params.refresh || params.page === 1) {
          this.setSharingOrders([])
        }

        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 获取我的分享订单
    async getMyOrders(params = {}) {
      try {
        console.log('[SharingStore] 开始获取我的分享订单')
        this.setLoading(true)

        const response = await sharingApi.getMyCreatedSharingOrders(params)

        if (response && response.data) {
          const orders = Array.isArray(response.data) ? response.data : []
          this.setMySharingOrders(orders)
          console.log('[SharingStore] 我的分享订单获取成功:', orders.length, '条')
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 获取我的分享订单失败:', error)
        showError(error.message || '获取我的分享订单失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 获取收到的请求
    async getReceivedRequestsList(params = {}) {
      try {
        console.log('[SharingStore] 开始获取收到的请求')
        this.setLoading(true)

        const response = await sharingApi.getReceivedSharedRequests(params)

        if (response && response.data) {
          const requests = Array.isArray(response.data) ? response.data : []
          this.setReceivedRequests(requests)
          console.log('[SharingStore] 收到的请求获取成功:', requests.length, '条')
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 获取收到的请求失败:', error)
        showError(error.message || '获取收到的请求失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 获取发送的请求
    async getSentRequestsList(params = {}) {
      try {
        console.log('[SharingStore] 开始获取发送的请求')
        this.setLoading(true)

        const response = await sharingApi.getMySharedRequests(params)

        if (response && response.data) {
          const requests = Array.isArray(response.data) ? response.data : []
          this.setSentRequests(requests)
          console.log('[SharingStore] 发送的请求获取成功:', requests.length, '条')
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 获取发送的请求失败:', error)
        showError(error.message || '获取发送的请求失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 获取分享订单详情
    async getOrderDetail(orderId, forceRefresh = false) {
      try {
        console.log('[SharingStore] 开始获取分享订单详情:', orderId, forceRefresh ? '(强制刷新)' : '')
        this.setLoading(true)

        // 如果是强制刷新，清除当前数据
        if (forceRefresh) {
          this.sharingOrderDetail = null
        }

        const response = await sharingApi.getSharingOrderById(orderId)

        console.log('[SharingStore] API响应详情:', response)

        if (response) {
          // 检查是否是错误响应（包含message字段）
          if (response.message && !response.id) {
            console.warn('[SharingStore] 获取分享订单详情失败:', response.message)
            this.setSharingOrderDetail(null)
          } else if (response.id) {
            // 如果响应包含id字段，说明是有效的拼场订单数据
            this.setSharingOrderDetail(response)
            console.log('[SharingStore] 分享订单详情获取成功:', response)
          } else {
            console.warn('[SharingStore] 获取分享订单详情返回无效数据:', response)
            this.setSharingOrderDetail(null)
          }
        } else {
          console.warn('[SharingStore] 获取分享订单详情返回空响应')
          this.setSharingOrderDetail(null)
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 获取分享订单详情失败:', error)
        showError(error.message || '获取分享订单详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 通过主订单ID获取分享订单详情
    async getOrderDetailByMainOrderId(mainOrderId) {
      try {
        console.log('[SharingStore] 通过主订单ID获取分享订单详情:', mainOrderId)
        this.setLoading(true)

        const response = await sharingApi.getSharingOrderByMainOrderId(mainOrderId)

        console.log('[SharingStore] 主订单API响应详情:', response)

        if (response) {
          // 检查是否是错误响应（包含message字段）
          if (response.message && !response.id) {
            console.warn('[SharingStore] 通过主订单ID获取分享订单详情失败:', response.message)
            this.setSharingOrderDetail(null)
          } else if (response.id) {
            // 如果响应包含id字段，说明是有效的拼场订单数据
            this.setSharingOrderDetail(response)
            console.log('[SharingStore] 通过主订单ID获取分享订单详情成功:', response)
            return response.id // 返回拼场订单ID
          } else {
            console.warn('[SharingStore] 通过主订单ID获取分享订单详情返回无效数据:', response)
            this.setSharingOrderDetail(null)
          }
        } else {
          console.warn('[SharingStore] 通过主订单ID获取分享订单详情返回空响应')
          this.setSharingOrderDetail(null)
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 通过主订单ID获取分享订单详情失败:', error)
        showError(error.message || '获取分享订单详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 创建分享订单
    async createOrder(orderData) {
      try {
        console.log('[SharingStore] 开始创建分享订单')
        this.setLoading(true)

        const response = await sharingApi.createSharingOrder(orderData)

        if (response && response.data) {
          // 创建成功后，刷新我的订单列表
          await this.getMyOrders()
          showSuccess('分享订单创建成功')
          console.log('[SharingStore] 分享订单创建成功')
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 创建分享订单失败:', error)
        showError(error.message || '创建分享订单失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 处理分享请求
    async handleRequest({ requestId, action }) {
      try {
        console.log('[SharingStore] 开始处理分享请求:', { requestId, action })
        this.setLoading(true)

        const response = await sharingApi.handleSharedRequest(requestId, action)

        if (response && response.success) {
          // 处理成功后，刷新相关列表
          await this.getReceivedRequestsList()
          showSuccess(`请求${action === 'accept' ? '接受' : '拒绝'}成功`)
          console.log('[SharingStore] 分享请求处理成功')
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 处理分享请求失败:', error)
        showError(error.message || '处理分享请求失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 处理拼场申请（新增方法，对应Vuex中的processSharingRequest）
    async processSharingRequest({ requestId, action, reason = '' }) {
      try {
        console.log('[SharingStore] 开始处理拼场申请:', { requestId, action, reason })
        this.setLoading(true)

        const data = {
          action: action, // 直接传递action参数：'approve' 或 'reject'
          responseMessage: reason || ''
        }

        const response = await sharingApi.handleSharedRequest(requestId, data)

        if (response && response.success) {
          showSuccess(action === 'approve' ? '已同意拼场申请' : '已拒绝拼场申请')
          console.log('[SharingStore] 拼场申请处理成功')

          // 刷新相关列表
          await this.getReceivedRequestsList()
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 处理拼场申请失败:', error)

        // 检查是否是需要支付的错误
        if (error.needPayment) {
          // 保留完整的错误信息，包括needPayment和orderId
          const enhancedError = new Error(error.message || '处理拼场申请失败')
          enhancedError.needPayment = error.needPayment
          enhancedError.orderId = error.orderId
          enhancedError.orderStatus = error.orderStatus
          throw enhancedError
        } else {
          showError(error.message || '处理拼场申请失败')
          throw error
        }
      } finally {
        this.setLoading(false)
      }
    },

    // 申请加入拼场订单（需要支付）
    async applyJoinSharingOrder(orderId) {
      try {
        console.log('[SharingStore] 开始申请加入拼场订单:', orderId)
        this.setLoading(true)

        const response = await sharingApi.applyJoinSharingOrder(orderId)

        if (response && response.success) {
          console.log('[SharingStore] 申请加入拼场订单成功')
          return response
        } else {
          throw new Error(response.message || '申请失败')
        }
      } catch (error) {
        console.error('[SharingStore] 申请加入拼场订单失败:', error)
        showError(error.message || '申请加入拼场订单失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 更新拼场设置
    async updateSharingSettings({ sharingId, settings }) {
      try {
        console.log('[SharingStore] 开始更新拼场设置:', { sharingId, settings })
        this.setLoading(true)

        const response = await sharingApi.updateSharingSettings(sharingId, settings)

        showSuccess('设置已更新')
        console.log('[SharingStore] 拼场设置更新成功')

        // 刷新订单详情
        await this.getOrderDetail(sharingId)

        return response
      } catch (error) {
        console.error('[SharingStore] 更新拼场设置失败:', error)
        showError(error.message || '更新设置失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 申请拼场
    async applySharingOrder({ orderId, data }) {
      try {
        console.log('[SharingStore] 开始申请拼场:', { orderId, data })
        this.setLoading(true)

        const response = await sharingApi.applySharedBooking(orderId, data)

        // 不在这里显示消息，让前端页面根据响应状态决定显示内容
        console.log('[SharingStore] 拼场申请成功')

        return response
      } catch (error) {
        console.error('[SharingStore] 申请拼场失败:', error)
        showError(error.message || '申请拼场失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 移除拼场参与者
    async removeSharingParticipant({ sharingId, participantId }) {
      try {
        console.log('[SharingStore] 开始移除拼场参与者:', { sharingId, participantId })
        this.setLoading(true)

        const response = await sharingApi.removeSharingParticipant(sharingId, participantId)

        if (response && response.success) {
          showSuccess('参与者移除成功')
          console.log('[SharingStore] 参与者移除成功')

          // 刷新订单详情
          await this.getOrderDetail(sharingId)
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 移除参与者失败:', error)
        showError(error.message || '移除参与者失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 确认拼场订单
    async confirmSharingOrder(orderId) {
      try {
        console.log('[SharingStore] 开始确认拼场订单:', orderId)
        this.setLoading(true)

        const response = await sharingApi.confirmSharingOrder(orderId)

        if (response && response.success) {
          showSuccess('拼场订单确认成功')
          console.log('[SharingStore] 拼场订单确认成功')

          // 刷新相关列表
          await this.getMyOrders()
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 确认拼场订单失败:', error)
        showError(error.message || '确认拼场订单失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 取消拼场订单
    async cancelSharingOrder(orderId) {
      try {
        console.log('[SharingStore] 开始取消拼场订单:', orderId)
        this.setLoading(true)

        const response = await sharingApi.cancelSharingOrder(orderId)

        if (response && response.success) {
          showSuccess('拼场订单取消成功')
          console.log('[SharingStore] 拼场订单取消成功')

          // 刷新相关列表
          await this.getMyOrders()
        }

        return response
      } catch (error) {
        console.error('[SharingStore] 取消拼场订单失败:', error)
        showError(error.message || '取消拼场订单失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 清空订单详情
    clearOrderDetail() {
      this.sharingOrderDetail = null
    },

    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      }
    }
  }
})
