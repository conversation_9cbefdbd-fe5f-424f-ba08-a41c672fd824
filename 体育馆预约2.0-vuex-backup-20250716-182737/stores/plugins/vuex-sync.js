import store from '@/store'

// 同步状态标志，防止无限递归
let syncInProgress = false

// Pinia插件：将Pinia状态同步到Vuex
export function vuexSyncPlugin() {
  return ({ store: piniaStore }) => {
    // App store同步
    if (piniaStore.$id === 'app') {
      console.log('[Pinia-Vuex Sync] 初始化app store同步')

      // 初始同步Vuex到Pinia
      piniaStore.$state.loading = store.state.loading
      piniaStore.$state.networkStatus = store.state.networkStatus

      // 监听Pinia变化，同步到Vuex
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress) return // 防止递归同步

        console.log('[Pinia->Vuex] App同步状态变化:', mutation.type, mutation.payload)

        // 简化同步逻辑：对于任何Pinia状态变化，都同步到Vuex
        // 这样可以避免复杂的事件检测，确保状态一致性
        try {
          syncInProgress = true
          store.dispatch('setLoading', state.loading)
          store.dispatch('setNetworkStatus', state.networkStatus)
          console.log('[Pinia->Vuex] App状态已同步:', { loading: state.loading, networkStatus: state.networkStatus })
        } catch (error) {
          console.error('[Pinia->Vuex] App同步失败:', error)
        } finally {
          syncInProgress = false
        }
      })

      // 监听Vuex变化，同步到Pinia
      store.subscribe((mutation, state) => {
        // 只处理App相关的mutations，不处理其他模块
        if (mutation.type === 'SET_LOADING') {
          console.log('[Vuex->Pinia] App同步状态变化:', mutation.type)
          piniaStore.loading = state.loading
        }
        if (mutation.type === 'SET_NETWORK_STATUS') {
          console.log('[Vuex->Pinia] App同步状态变化:', mutation.type)
          piniaStore.networkStatus = state.networkStatus
        }
        // 不处理venue/开头的mutations，那些由venue store处理
      })
    }

    // User store同步
    if (piniaStore.$id === 'user') {
      console.log('[Pinia-Vuex Sync] 初始化user store同步')

      // 初始同步Vuex到Pinia
      const vuexUserState = store.state.user
      if (vuexUserState) {
        piniaStore.$state.token = vuexUserState.token
        piniaStore.$state.userInfo = vuexUserState.userInfo
        piniaStore.$state.userStats = vuexUserState.userStats
        piniaStore.$state.isLoggedIn = vuexUserState.isLoggedIn
        piniaStore.$state.loginChecking = vuexUserState.loginChecking
      }

      // 监听Pinia变化，同步到Vuex
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress) return // 防止递归同步

        console.log('[Pinia->Vuex] User同步状态变化:', mutation.type, mutation.payload)

        // 简化同步逻辑：对于任何Pinia状态变化，都同步到Vuex
        // 使用mutations而不是actions，因为这些actions在Vuex中不存在
        try {
          syncInProgress = true
          store.commit('user/SET_TOKEN', state.token)
          store.commit('user/SET_USER_INFO', state.userInfo)
          store.commit('user/SET_LOGIN_STATUS', state.isLoggedIn)
          store.commit('user/SET_LOGIN_CHECKING', state.loginChecking)
          store.commit('user/SET_USER_STATS', state.userStats)
          console.log('[Pinia->Vuex] User状态已同步')
        } catch (error) {
          console.error('[Pinia->Vuex] User同步失败:', error)
        } finally {
          syncInProgress = false
        }
      })

      // 监听Vuex变化，同步到Pinia
      store.subscribe((mutation, state) => {
        if (mutation.type.startsWith('user/')) {
          console.log('[Vuex->Pinia] User同步状态变化:', mutation.type)

          const userState = state.user
          if (mutation.type === 'user/SET_TOKEN') {
            piniaStore.token = userState.token
          }
          if (mutation.type === 'user/SET_USER_INFO') {
            piniaStore.userInfo = userState.userInfo
          }
          if (mutation.type === 'user/SET_LOGIN_STATUS') {
            piniaStore.isLoggedIn = userState.isLoggedIn
          }
          if (mutation.type === 'user/SET_LOGIN_CHECKING') {
            piniaStore.loginChecking = userState.loginChecking
          }
          if (mutation.type === 'user/SET_USER_STATS') {
            piniaStore.userStats = userState.userStats
          }
          if (mutation.type === 'user/CLEAR_USER_DATA') {
            piniaStore.token = ''
            piniaStore.userInfo = null
            piniaStore.userStats = { totalBookings: 0, totalSharings: 0 }
            piniaStore.isLoggedIn = false
            piniaStore.loginChecking = false
          }
        }
      })
    }

    // Venue store同步
    if (piniaStore.$id === 'venue') {
      console.log('[Pinia-Vuex Sync] 初始化venue store同步')

      // 初始同步Vuex到Pinia
      const vuexVenueState = store.state.venue
      if (vuexVenueState) {
        piniaStore.$state.venueList = vuexVenueState.venueList
        piniaStore.$state.popularVenues = vuexVenueState.popularVenues
        piniaStore.$state.venueDetail = vuexVenueState.venueDetail
        piniaStore.$state.venueTypes = vuexVenueState.venueTypes
        piniaStore.$state.timeSlots = vuexVenueState.timeSlots
        piniaStore.$state.searchResults = vuexVenueState.searchResults
        piniaStore.$state.loading = vuexVenueState.loading
        piniaStore.$state.pagination = vuexVenueState.pagination
      }

      // 监听Pinia变化，同步到Vuex
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress) return // 防止递归同步

        console.log('[Pinia->Vuex] Venue同步状态变化:', mutation.type)

        // 优化同步逻辑：只同步必要的状态，减少性能开销
        try {
          syncInProgress = true

          // 批量同步，减少单独的commit调用
          const venueUpdates = {
            venueList: state.venueList,
            popularVenues: state.popularVenues,
            venueDetail: state.venueDetail,
            venueTypes: state.venueTypes,
            timeSlots: state.timeSlots,
            searchResults: state.searchResults,
            loading: state.loading,
            pagination: state.pagination
          }

          // 使用单次commit进行批量更新
          store.commit('venue/BATCH_UPDATE', venueUpdates)

          console.log('[Pinia->Vuex] Venue状态已批量同步:', Object.keys(venueUpdates))
        } catch (error) {
          // 如果批量更新失败，回退到单独更新
          console.warn('[Pinia->Vuex] 批量同步失败，使用单独同步:', error)
          try {
            store.commit('venue/SET_VENUE_LIST', { list: state.venueList, pagination: state.pagination })
            store.commit('venue/SET_POPULAR_VENUES', state.popularVenues)
            store.commit('venue/SET_VENUE_DETAIL', state.venueDetail)
            store.commit('venue/SET_VENUE_TYPES', state.venueTypes)
            store.commit('venue/SET_TIME_SLOTS', state.timeSlots)
            store.commit('venue/SET_SEARCH_RESULTS', state.searchResults)
            store.commit('venue/SET_LOADING', state.loading)
            console.log('[Pinia->Vuex] Venue状态已单独同步')
          } catch (fallbackError) {
            console.error('[Pinia->Vuex] Venue同步完全失败:', fallbackError)
          }
        } finally {
          syncInProgress = false
        }
      })

      // 监听Vuex变化，同步到Pinia
      store.subscribe((mutation, state) => {
        if (syncInProgress) return // 防止递归同步

        if (mutation.type.startsWith('venue/')) {
          console.log('[Vuex->Pinia] Venue同步状态变化:', mutation.type)

          try {
            syncInProgress = true
            const venueState = state.venue

            // 根据具体的mutation类型进行精确同步
            switch (mutation.type) {
              case 'venue/SET_VENUE_LIST':
                piniaStore.venueList = venueState.venueList
                piniaStore.pagination = venueState.pagination
                break
              case 'venue/APPEND_VENUE_LIST':
                piniaStore.venueList = venueState.venueList
                break
              case 'venue/SET_POPULAR_VENUES':
                piniaStore.popularVenues = venueState.popularVenues
                break
              case 'venue/SET_VENUE_DETAIL':
                piniaStore.venueDetail = venueState.venueDetail
                break
              case 'venue/SET_VENUE_TYPES':
                piniaStore.venueTypes = venueState.venueTypes
                break
              case 'venue/SET_TIME_SLOTS':
                piniaStore.timeSlots = venueState.timeSlots
                break
              case 'venue/SET_SEARCH_RESULTS':
                piniaStore.searchResults = venueState.searchResults
                break
              case 'venue/SET_LOADING':
                piniaStore.loading = venueState.loading
                break
              case 'venue/BATCH_UPDATE':
                // 批量更新时同步所有状态
                piniaStore.venueList = venueState.venueList
                piniaStore.popularVenues = venueState.popularVenues
                piniaStore.venueDetail = venueState.venueDetail
                piniaStore.venueTypes = venueState.venueTypes
                piniaStore.timeSlots = venueState.timeSlots
                piniaStore.searchResults = venueState.searchResults
                piniaStore.loading = venueState.loading
                piniaStore.pagination = venueState.pagination
                console.log('[Vuex->Pinia] Venue批量同步完成')
                break
              default:
                // 其他未知的venue mutations，进行全量同步
                piniaStore.venueList = venueState.venueList
                piniaStore.popularVenues = venueState.popularVenues
                piniaStore.venueDetail = venueState.venueDetail
                piniaStore.venueTypes = venueState.venueTypes
                piniaStore.timeSlots = venueState.timeSlots
                piniaStore.searchResults = venueState.searchResults
                piniaStore.loading = venueState.loading
                piniaStore.pagination = venueState.pagination
                break
            }
          } finally {
            syncInProgress = false
          }
        }
      })
    }

    // Sharing store同步
    if (piniaStore.$id === 'sharing') {
      console.log('[Pinia-Vuex Sync] 初始化sharing store同步')

      // 初始同步Vuex到Pinia
      const vuexSharingState = store.state.sharing
      if (vuexSharingState) {
        piniaStore.$state.sharingOrders = vuexSharingState.sharingOrders || []
        piniaStore.$state.mySharingOrders = vuexSharingState.mySharingOrders || []
        piniaStore.$state.receivedRequests = vuexSharingState.receivedRequests || []
        piniaStore.$state.sentRequests = vuexSharingState.sentRequests || []
        piniaStore.$state.sharingOrderDetail = vuexSharingState.sharingOrderDetail
        piniaStore.$state.loading = vuexSharingState.loading || false
        piniaStore.$state.pagination = vuexSharingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1 }
      }

      // 监听Pinia变化，同步到Vuex
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress) return // 防止递归同步

        console.log('[Pinia->Vuex] Sharing同步状态变化:', mutation.type)

        // 批量同步到Vuex
        try {
          syncInProgress = true

          const sharingUpdates = {
            sharingOrders: state.sharingOrders,
            mySharingOrders: state.mySharingOrders,
            receivedRequests: state.receivedRequests,
            sentRequests: state.sentRequests,
            sharingOrderDetail: state.sharingOrderDetail,
            loading: state.loading,
            pagination: state.pagination
          }

          // 尝试批量更新
          store.commit('sharing/BATCH_UPDATE', sharingUpdates)
          console.log('[Pinia->Vuex] Sharing状态已批量同步:', Object.keys(sharingUpdates))
        } catch (error) {
          // 如果批量更新失败，回退到单独更新
          console.warn('[Pinia->Vuex] Sharing批量同步失败，使用单独同步:', error)
          try {
            store.commit('sharing/SET_SHARING_ORDERS', state.sharingOrders)
            store.commit('sharing/SET_MY_SHARING_ORDERS', state.mySharingOrders)
            store.commit('sharing/SET_RECEIVED_REQUESTS', state.receivedRequests)
            store.commit('sharing/SET_SENT_REQUESTS', state.sentRequests)
            store.commit('sharing/SET_SHARING_ORDER_DETAIL', state.sharingOrderDetail)
            store.commit('sharing/SET_LOADING', state.loading)
            store.commit('sharing/SET_PAGINATION', state.pagination)
            console.log('[Pinia->Vuex] Sharing状态已单独同步')
          } catch (fallbackError) {
            console.error('[Pinia->Vuex] Sharing同步完全失败:', fallbackError)
          }
        } finally {
          syncInProgress = false
        }
      })

      // 监听Vuex变化，同步到Pinia
      store.subscribe((mutation, state) => {
        if (syncInProgress) return // 防止递归同步

        if (mutation.type.startsWith('sharing/')) {
          console.log('[Vuex->Pinia] Sharing同步状态变化:', mutation.type)

          try {
            syncInProgress = true
            const sharingState = state.sharing

            // 根据具体的mutation类型进行精确同步
            switch (mutation.type) {
              case 'sharing/SET_SHARING_ORDERS':
                piniaStore.sharingOrders = sharingState.sharingOrders
                break
              case 'sharing/SET_MY_SHARING_ORDERS':
                piniaStore.mySharingOrders = sharingState.mySharingOrders
                break
              case 'sharing/SET_RECEIVED_REQUESTS':
                piniaStore.receivedRequests = sharingState.receivedRequests
                break
              case 'sharing/SET_SENT_REQUESTS':
                piniaStore.sentRequests = sharingState.sentRequests
                break
              case 'sharing/SET_SHARING_ORDER_DETAIL':
                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail
                break
              case 'sharing/SET_LOADING':
                piniaStore.loading = sharingState.loading
                break
              case 'sharing/SET_PAGINATION':
                piniaStore.pagination = sharingState.pagination
                break
              case 'sharing/BATCH_UPDATE':
                // 批量更新时同步所有状态
                piniaStore.sharingOrders = sharingState.sharingOrders
                piniaStore.mySharingOrders = sharingState.mySharingOrders
                piniaStore.receivedRequests = sharingState.receivedRequests
                piniaStore.sentRequests = sharingState.sentRequests
                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail
                piniaStore.loading = sharingState.loading
                piniaStore.pagination = sharingState.pagination
                console.log('[Vuex->Pinia] Sharing批量同步完成')
                break
              default:
                // 其他未知的sharing mutations，进行全量同步
                piniaStore.sharingOrders = sharingState.sharingOrders
                piniaStore.mySharingOrders = sharingState.mySharingOrders
                piniaStore.receivedRequests = sharingState.receivedRequests
                piniaStore.sentRequests = sharingState.sentRequests
                piniaStore.sharingOrderDetail = sharingState.sharingOrderDetail
                piniaStore.loading = sharingState.loading
                piniaStore.pagination = sharingState.pagination
                break
            }
          } finally {
            syncInProgress = false
          }
        }
      })
    }

    // Booking store同步
    if (piniaStore.$id === 'booking') {
      console.log('[Pinia-Vuex Sync] 初始化booking store同步')

      // 初始同步Vuex到Pinia
      const vuexBookingState = store.state.booking
      if (vuexBookingState) {
        piniaStore.$state.bookingList = vuexBookingState.bookingList || []
        piniaStore.$state.bookingDetail = vuexBookingState.bookingDetail
        piniaStore.$state.sharingOrders = vuexBookingState.sharingOrders || []
        piniaStore.$state.userSharingOrders = vuexBookingState.userSharingOrders || []
        piniaStore.$state.joinedSharingOrders = vuexBookingState.joinedSharingOrders || []
        piniaStore.$state.sharingDetail = vuexBookingState.sharingDetail
        piniaStore.$state.loading = vuexBookingState.loading || false
        piniaStore.$state.pagination = vuexBookingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 }
      }

      // 监听Pinia变化，同步到Vuex
      piniaStore.$subscribe((mutation, state) => {
        if (syncInProgress) return // 防止递归同步

        console.log('[Pinia->Vuex] Booking同步状态变化:', mutation.type)

        // 批量同步到Vuex
        try {
          syncInProgress = true

          const bookingUpdates = {
            bookingList: state.bookingList,
            bookingDetail: state.bookingDetail,
            sharingOrders: state.sharingOrders,
            userSharingOrders: state.userSharingOrders,
            joinedSharingOrders: state.joinedSharingOrders,
            sharingDetail: state.sharingDetail,
            loading: state.loading,
            pagination: state.pagination
          }

          // 尝试批量更新
          store.commit('booking/BATCH_UPDATE', bookingUpdates)
          console.log('[Pinia->Vuex] Booking状态已批量同步:', Object.keys(bookingUpdates))
        } catch (error) {
          // 如果批量更新失败，回退到单独更新
          console.warn('[Pinia->Vuex] Booking批量同步失败，使用单独同步:', error)
          try {
            store.commit('booking/SET_BOOKING_LIST', { list: state.bookingList, pagination: state.pagination })
            store.commit('booking/SET_BOOKING_DETAIL', state.bookingDetail)
            store.commit('booking/SET_SHARING_ORDERS', state.sharingOrders)
            store.commit('booking/SET_USER_SHARING_ORDERS', state.userSharingOrders)
            store.commit('booking/SET_JOINED_SHARING_ORDERS', state.joinedSharingOrders)
            store.commit('booking/SET_SHARING_DETAIL', state.sharingDetail)
            store.commit('booking/SET_LOADING', state.loading)
            store.commit('booking/SET_PAGINATION', state.pagination)
            console.log('[Pinia->Vuex] Booking状态已单独同步')
          } catch (fallbackError) {
            console.error('[Pinia->Vuex] Booking同步完全失败:', fallbackError)
          }
        } finally {
          syncInProgress = false
        }
      })

      // 监听Vuex变化，同步到Pinia
      store.subscribe((mutation, state) => {
        if (syncInProgress) return // 防止递归同步

        if (mutation.type.startsWith('booking/')) {
          console.log('[Vuex->Pinia] Booking同步状态变化:', mutation.type)

          try {
            syncInProgress = true
            const bookingState = state.booking

            // 根据具体的mutation类型进行精确同步
            switch (mutation.type) {
              case 'booking/SET_BOOKING_LIST':
                piniaStore.bookingList = bookingState.bookingList
                piniaStore.pagination = bookingState.pagination
                break
              case 'booking/APPEND_BOOKING_LIST':
                piniaStore.bookingList = bookingState.bookingList
                break
              case 'booking/SET_BOOKING_DETAIL':
                piniaStore.bookingDetail = bookingState.bookingDetail
                break
              case 'booking/SET_SHARING_ORDERS':
                piniaStore.sharingOrders = bookingState.sharingOrders
                break
              case 'booking/SET_USER_SHARING_ORDERS':
                piniaStore.userSharingOrders = bookingState.userSharingOrders
                break
              case 'booking/SET_JOINED_SHARING_ORDERS':
                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders
                break
              case 'booking/SET_SHARING_DETAIL':
                piniaStore.sharingDetail = bookingState.sharingDetail
                break
              case 'booking/SET_LOADING':
                piniaStore.loading = bookingState.loading
                break
              case 'booking/SET_PAGINATION':
                piniaStore.pagination = bookingState.pagination
                break
              case 'booking/UPDATE_BOOKING_STATUS':
                piniaStore.bookingList = bookingState.bookingList
                break
              case 'booking/BATCH_UPDATE':
                // 批量更新时同步所有状态
                piniaStore.bookingList = bookingState.bookingList
                piniaStore.bookingDetail = bookingState.bookingDetail
                piniaStore.sharingOrders = bookingState.sharingOrders
                piniaStore.userSharingOrders = bookingState.userSharingOrders
                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders
                piniaStore.sharingDetail = bookingState.sharingDetail
                piniaStore.loading = bookingState.loading
                piniaStore.pagination = bookingState.pagination
                console.log('[Vuex->Pinia] Booking批量同步完成')
                break
              default:
                // 其他未知的booking mutations，进行全量同步
                piniaStore.bookingList = bookingState.bookingList
                piniaStore.bookingDetail = bookingState.bookingDetail
                piniaStore.sharingOrders = bookingState.sharingOrders
                piniaStore.userSharingOrders = bookingState.userSharingOrders
                piniaStore.joinedSharingOrders = bookingState.joinedSharingOrders
                piniaStore.sharingDetail = bookingState.sharingDetail
                piniaStore.loading = bookingState.loading
                piniaStore.pagination = bookingState.pagination
                break
            }
          } finally {
            syncInProgress = false
          }
        }
      })
    }
  }
}
