// Vuex到Pinia迁移配置
export const migrationConfig = {
  // 迁移阶段配置
  phases: {
    // 阶段1: 基础设施 - 已完成
    phase1: {
      name: '基础设施搭建',
      status: 'completed',
      modules: ['app'],
      description: '设置Pinia实例，创建同步插件，建立并行运行环境'
    },
    
    // 阶段2: 用户模块 - 进行中
    phase2: {
      name: '用户模块迁移',
      status: 'in-progress',
      modules: ['user'],
      description: '迁移用户认证、登录状态、用户信息管理'
    },
    
    // 阶段3: 场馆模块 - 进行中
    phase3: {
      name: '场馆模块迁移',
      status: 'in-progress',
      modules: ['venue'],
      description: '迁移场馆列表、详情、时间段管理'
    },
    
    // 阶段4: 预约模块 - 待开始
    phase4: {
      name: '预约模块迁移',
      status: 'pending',
      modules: ['booking'],
      description: '迁移预约创建、列表、状态管理'
    },
    
    // 阶段5: 拼场模块 - 已完成
    phase5: {
      name: '拼场模块迁移',
      status: 'completed',
      modules: ['sharing'],
      description: '迁移拼场创建、申请、管理功能'
    },

    // 阶段6: 预订模块 - 进行中
    phase6: {
      name: '预订模块迁移',
      status: 'in-progress',
      modules: ['booking'],
      description: '迁移预订管理、订单处理、拼场功能'
    },

    // 阶段7: 清理 - 待开始
    phase7: {
      name: '清理Vuex',
      status: 'pending',
      modules: [],
      description: '移除Vuex依赖，清理同步代码'
    }
  },
  
  // 当前激活的模块 - 控制哪些模块使用Pinia
  activeModules: ['app', 'user', 'venue', 'sharing', 'booking'],
  
  // 特性标志 - 控制是否启用Pinia功能
  features: {
    usePiniaForApp: true,
    usePiniaForUser: true,
    usePiniaForVenue: true,
    usePiniaForBooking: true,
    usePiniaForSharing: true,
    enableSync: true, // 是否启用Vuex-Pinia同步
    enableLogging: true // 是否启用迁移日志
  },
  
  // 测试配置
  testing: {
    enableMigrationTests: true,
    testModules: ['app', 'user', 'venue', 'sharing', 'booking'],
    autoValidation: true // 自动验证状态同步
  }
}

// 获取当前迁移状态
export function getMigrationStatus() {
  const phases = migrationConfig.phases
  const completed = Object.values(phases).filter(p => p.status === 'completed').length
  const total = Object.keys(phases).length
  
  return {
    progress: Math.round((completed / total) * 100),
    currentPhase: Object.values(phases).find(p => p.status === 'in-progress')?.name || '未开始',
    completedPhases: completed,
    totalPhases: total
  }
}

// 检查模块是否应该使用Pinia
export function shouldUsePinia(moduleName) {
  return migrationConfig.activeModules.includes(moduleName)
}

// 检查特性是否启用
export function isFeatureEnabled(featureName) {
  return migrationConfig.features[featureName] || false
}

// 更新迁移阶段状态
export function updatePhaseStatus(phaseName, status) {
  if (migrationConfig.phases[phaseName]) {
    migrationConfig.phases[phaseName].status = status
    console.log(`[Migration] 阶段 ${phaseName} 状态更新为: ${status}`)
  }
}

// 激活模块使用Pinia
export function activateModule(moduleName) {
  if (!migrationConfig.activeModules.includes(moduleName)) {
    migrationConfig.activeModules.push(moduleName)
    console.log(`[Migration] 模块 ${moduleName} 已激活使用Pinia`)
  }
}

// 记录迁移日志
export function logMigration(message, data = null) {
  if (isFeatureEnabled('enableLogging')) {
    console.log(`[Migration] ${message}`, data || '')
  }
}
