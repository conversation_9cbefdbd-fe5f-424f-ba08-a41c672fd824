import { defineStore } from 'pinia'
import * as venueApi from '@/api/venue.js'
import * as timeslotApi from '@/api/timeslot.js'
import { showError } from '@/utils/ui.js'

export const useVenueStore = defineStore('venue', {
  state: () => ({
    venueList: [],
    popularVenues: [],
    venueDetail: null,
    venueTypes: [],
    timeSlots: [],
    searchResults: [],
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1
    }
  }),

  getters: {
    // 场馆列表相关 - 这些应该是getter，返回状态值
    venueListGetter: (state) => state.venueList,
    popularVenuesGetter: (state) => state.popularVenues,
    venueDetailGetter: (state) => state.venueDetail,
    venueTypesGetter: (state) => state.venueTypes,
    timeSlotsGetter: (state) => state.timeSlots,
    searchResultsGetter: (state) => state.searchResults,
    
    // 状态相关
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,
    
    // 计算属性
    totalVenues: (state) => state.venueList.length,
    hasMoreVenues: (state) => state.pagination.current < state.pagination.totalPages,
    
    // 按类型筛选场馆
    getVenuesByType: (state) => (typeId) => {
      if (!typeId) return state.venueList
      return state.venueList.filter(venue => venue.typeId === typeId)
    },
    
    // 获取可用时间段
    getAvailableTimeSlots: (state) => {
      return state.timeSlots.filter(slot => slot.status === 'AVAILABLE')
    }
  },

  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    },
    
    // 设置场馆列表
    setVenueList({ list, pagination }) {
      this.venueList = list
      if (pagination) {
        this.pagination = { ...this.pagination, ...pagination }
      }
    },
    
    // 追加场馆列表（分页加载）
    appendVenueList(list) {
      this.venueList = [...this.venueList, ...list]
    },
    
    // 设置热门场馆
    setPopularVenues(venues) {
      this.popularVenues = venues
    },
    
    // 设置场馆详情
    setVenueDetail(venue) {
      this.venueDetail = venue
    },
    
    // 设置场馆类型
    setVenueTypes(types) {
      this.venueTypes = types
    },
    
    // 设置时间段
    setTimeSlots(slots) {
      this.timeSlots = slots
    },
    
    // 设置搜索结果
    setSearchResults(results) {
      this.searchResults = results
    },
    
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 获取场馆列表
    async getVenueList(params = {}) {
      try {
        console.log('[VenueStore] 开始获取场馆列表，参数:', params)
        this.setLoading(true)
        
        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时
        })
        
        const apiPromise = venueApi.getVenueList(params)
        const response = await Promise.race([apiPromise, timeoutPromise])
        
        console.log('[VenueStore] 场馆API响应:', response)
        
        // 处理响应数据
        let list = []
        let pagination = {
          current: 1,
          pageSize: 10,
          total: 0,
          totalPages: 1
        }
        
        if (response && response.data) {
          if (Array.isArray(response.data)) {
            list = response.data
            pagination = {
              current: response.page || params.page || 1,
              pageSize: response.pageSize || params.pageSize || 10,
              total: response.total || response.data.length,
              totalPages: response.totalPages || 1
            }
          } else {
            console.warn('[VenueStore] API响应数据格式异常，使用空数组:', response)
          }
        } else if (response && Array.isArray(response)) {
          // 直接返回数组的情况
          list = response
          pagination.total = response.length
        } else {
          console.warn('[VenueStore] API响应为空或格式错误，使用空数组:', response)
        }
        
        console.log('[VenueStore] 解析的场馆列表:', list)
        console.log('[VenueStore] 分页信息:', pagination)
        
        if (params.page === 1 || params.refresh) {
          this.setVenueList({ list, pagination })
        } else {
          this.appendVenueList(list)
          this.setVenueList({ list: this.venueList, pagination })
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取场馆列表失败:', error)
        showError(error.message || '获取场馆列表失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取热门场馆
    async getPopularVenues() {
      try {
        console.log('[VenueStore] 开始获取热门场馆')
        const response = await venueApi.getPopularVenues()
        
        if (response && response.data) {
          this.setPopularVenues(response.data)
          console.log('[VenueStore] 热门场馆获取成功:', response.data)
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取热门场馆失败:', error)
        showError(error.message || '获取热门场馆失败')
        throw error
      }
    },

    // 获取场馆详情
    async getVenueDetail(venueId) {
      try {
        console.log('[VenueStore] 开始获取场馆详情:', venueId)
        this.setLoading(true)
        
        const response = await venueApi.getVenueDetail(venueId)
        
        if (response && response.data) {
          this.setVenueDetail(response.data)
          console.log('[VenueStore] 场馆详情获取成功:', response.data)
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取场馆详情失败:', error)
        showError(error.message || '获取场馆详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取场馆类型
    async getVenueTypes() {
      try {
        console.log('[VenueStore] 开始获取场馆类型')
        const response = await venueApi.getVenueTypes()
        
        if (response && response.data) {
          this.setVenueTypes(response.data)
          console.log('[VenueStore] 场馆类型获取成功:', response.data)
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取场馆类型失败:', error)
        showError(error.message || '获取场馆类型失败')
        throw error
      }
    },

    // 获取时间段
    async getTimeSlots(venueId, date) {
      try {
        console.log('[VenueStore] 开始获取时间段:', { venueId, date })
        this.setLoading(true)
        
        const response = await timeslotApi.getTimeSlots(venueId, date)
        
        if (response && response.data) {
          this.setTimeSlots(response.data)
          console.log('[VenueStore] 时间段获取成功:', response.data)
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取时间段失败:', error)
        showError(error.message || '获取时间段失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 搜索场馆
    async searchVenues(keyword, filters = {}) {
      try {
        console.log('[VenueStore] 开始搜索场馆:', { keyword, filters })
        this.setLoading(true)
        
        const response = await venueApi.searchVenues(keyword, filters)
        
        if (response && response.data) {
          this.setSearchResults(response.data)
          console.log('[VenueStore] 场馆搜索成功:', response.data)
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 搜索场馆失败:', error)
        showError(error.message || '搜索场馆失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 清空场馆详情
    clearVenueDetail() {
      this.venueDetail = null
    },
    
    // 清空搜索结果
    clearSearchResults() {
      this.searchResults = []
    },
    
    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      }
    }
  }
})
