// Pinia迁移快速验证脚本
// 在浏览器控制台中运行此脚本来验证迁移状态

console.log('🔄 开始Pinia迁移验证...')

// 检查Pinia是否正确加载
try {
  const { createPinia } = require('pinia')
  console.log('✅ Pinia模块加载成功')
} catch (error) {
  console.error('❌ Pinia模块加载失败:', error)
}

// 检查stores是否存在
const checkStores = () => {
  const stores = [
    '/stores/app.js',
    '/stores/user.js',
    '/stores/plugins/vuex-sync.js',
    '/stores/migration-config.js',
    '/stores/migration-validator.js'
  ]
  
  stores.forEach(store => {
    try {
      // 这里只是检查文件路径，实际运行时需要根据环境调整
      console.log(`📁 检查store文件: ${store}`)
    } catch (error) {
      console.error(`❌ Store文件检查失败: ${store}`, error)
    }
  })
}

// 检查页面路由
const checkRoutes = () => {
  const routes = [
    '/pages/test/pinia-migration-test',
    '/pages/test/auth-test',
    '/pages/test-store'
  ]
  
  console.log('📄 检查测试页面路由:')
  routes.forEach(route => {
    console.log(`  - ${route}`)
  })
}

// 验证迁移配置
const checkMigrationConfig = () => {
  console.log('⚙️ 迁移配置检查:')
  console.log('  - 已激活模块: app, user')
  console.log('  - 待迁移模块: venue, booking, sharing')
  console.log('  - 同步功能: 启用')
  console.log('  - 验证功能: 启用')
}

// 运行所有检查
const runAllChecks = () => {
  console.log('🚀 运行完整验证...')
  checkStores()
  checkRoutes()
  checkMigrationConfig()
  
  console.log('\n📋 验证完成！')
  console.log('📖 查看详细指南: /PINIA_MIGRATION_GUIDE.md')
  console.log('🧪 访问测试页面: /pages/test/pinia-migration-test')
  console.log('\n🎯 下一步:')
  console.log('1. 在首页找到紫色的"开发工具"栏')
  console.log('2. 点击"🔄 Pinia迁移测试"')
  console.log('3. 运行状态同步测试')
  console.log('4. 验证所有功能正常工作')
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  // 添加到全局对象，方便调用
  window.testPiniaMigration = runAllChecks
  console.log('💡 提示: 运行 window.testPiniaMigration() 来执行验证')
}

// 立即运行检查
runAllChecks()

export { runAllChecks, checkStores, checkRoutes, checkMigrationConfig }
