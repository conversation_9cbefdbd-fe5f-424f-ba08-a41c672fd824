<template>
  <view class="container">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="nav-left" @click="goBack">
        <text class="nav-icon">‹</text>
      </view>
      <text class="nav-title">我的申请</text>
      <view class="nav-right"></view>
    </view>
    
    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="tab in filterTabs" 
        :key="tab.value"
        class="filter-tab"
        :class="{ active: currentFilter === tab.value }"
        @click="switchFilter(tab.value)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text v-if="tab.count > 0" class="tab-count">{{ tab.count }}</text>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <text>加载中...</text>
    </view>
    
    <!-- 错误状态 -->
    <view v-else-if="error" class="error-state">
      <text class="error-icon">⚠️</text>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="loadRequests">
        重新加载
      </button>
    </view>
    
    <!-- 申请列表 -->
    <view v-else class="content">
      <view v-if="filteredRequests.length > 0" class="requests-list">
        <view 
          v-for="request in filteredRequests" 
          :key="request.id"
          class="request-item"
          @click="goToSharingDetail(request.orderId || request.sharingId)"
        >
          <!-- 拼场信息 -->
          <view class="sharing-info">
            <view class="sharing-header">
              <text class="venue-name">{{ request.venueName }}</text>
              <view class="status-badge" :class="getStatusClass(request.status)">
                <text class="status-text">{{ getStatusText(request.status) }}</text>
              </view>
            </view>
            
            <view class="sharing-details">
              <text class="team-name">{{ request.teamName || request.applicantTeamName }}</text>
              <text class="activity-time">{{ formatActivityTime(request) }}</text>
              <text class="price">支付金额 ¥{{ getRequestPrice(request) }}</text>
            </view>
            
            <!-- 参与人数进度 -->
            <view class="participants-progress">
              <view class="progress-info">
                <text class="progress-text">
                  {{ request.currentParticipants }}/{{ request.maxParticipants }}人
                </text>
                <text class="progress-percent">
                  {{ getProgressPercent(request.currentParticipants, request.maxParticipants) }}%
                </text>
              </view>
              <view class="progress-bar">
                <view 
                  class="progress-fill"
                  :style="{ width: getProgressPercent(request.currentParticipants, request.maxParticipants) + '%' }"
                ></view>
              </view>
            </view>
          </view>
          
          <!-- 申请信息 -->
          <view class="request-info">
            <view class="request-meta">
              <text class="request-time">申请时间：{{ formatDateTime(request.createdAt) }}</text>
              <text v-if="request.processedAt" class="process-time">
                处理时间：{{ formatDateTime(request.processedAt) }}
              </text>
            </view>
            
            <!-- 申请操作 -->
            <view class="request-actions">
              <view v-if="request.status === 'PENDING'" class="pending-actions">
                <button 
                  class="action-btn cancel-btn"
                  @click.stop="showCancelConfirm(request)"
                >
                  取消申请
                </button>
              </view>
              
              <view v-else-if="request.status === 'APPROVED'" class="approved-actions">
                <text class="approved-text">申请已通过</text>
                <button 
                  class="action-btn join-btn"
                  @click.stop="goToSharingDetail(request.orderId || request.sharingId)"
                >
                  查看详情
                </button>
              </view>
              
              <view v-else-if="request.status === 'REJECTED'" class="rejected-actions">
                <text class="rejected-text">申请被拒绝</text>
                <text v-if="request.rejectReason" class="reject-reason">
                  原因：{{ request.rejectReason }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="empty-icon">📝</text>
        <text class="empty-title">{{ getEmptyTitle() }}</text>
        <text class="empty-desc">{{ getEmptyDesc() }}</text>
        <button class="browse-btn" @click="goToSharingList">
          去看看拼场
        </button>
      </view>
    </view>
    
    <!-- 取消申请确认弹窗 -->
    <uni-popup ref="cancelPopup" type="dialog">
      <uni-popup-dialog 
        type="warn"
        title="取消申请"
        :content="`确定要取消对 ${cancelTarget?.teamName} 的申请吗？`"
        @confirm="confirmCancel"
        @close="() => { cancelTarget = null }"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { formatDate, formatDateTime } from '@/utils/helpers.js'

export default {
  name: 'SharingRequests',
  
  data() {
    return {
      currentFilter: 'all',
      error: '',
      cancelTarget: null,
      requests: [],
      filterTabs: [
        { label: '全部', value: 'all', count: 0 },
        { label: '待处理', value: 'pending', count: 0 },
        { label: '待支付', value: 'approved_pending_payment', count: 0 },
        { label: '已完成', value: 'approved', count: 0 },
        { label: '已拒绝', value: 'rejected', count: 0 },
        { label: '已超时', value: 'timeout_cancelled', count: 0 }
      ]
    }
  },
  
  computed: {
    ...mapState('sharing', ['loading']),
    ...mapState('user', ['userInfo']),
    
    // 过滤后的申请列表
    filteredRequests() {
      if (this.currentFilter === 'all') {
        return this.requests
      }
      
      const statusMap = {
        'pending': 'PENDING',
        'approved_pending_payment': 'APPROVED_PENDING_PAYMENT',
        'approved': 'APPROVED',
        'rejected': 'REJECTED',
        'timeout_cancelled': 'TIMEOUT_CANCELLED'
      }
      
      return this.requests.filter(request => 
        request.status === statusMap[this.currentFilter]
      )
    }
  },
  
  onLoad() {
    this.loadRequests()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.loadRequests()
  },
  
  onPullDownRefresh() {
    this.loadRequests().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
  
  methods: {
    ...mapActions('sharing', [
      'getMySharingRequests',
      'cancelSharingRequest'
    ]),
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 加载申请列表
    async loadRequests() {
      try {
        this.error = ''
        console.log('拼场申请页面：开始加载申请列表')
        
        // 调用真实API
        const requests = await this.getMySharingRequests()
        this.requests = requests || []
        
        // 更新筛选标签计数
        this.updateFilterCounts()
        
        console.log('拼场申请页面：加载申请列表成功:', this.requests)

        // 调试：检查第一个请求的数据结构
        if (this.requests.length > 0) {
          console.log('第一个请求的完整数据结构:', this.requests[0])
          console.log('第一个请求的sharingId:', this.requests[0].sharingId)
          console.log('第一个请求的所有字段:', Object.keys(this.requests[0]))
        }
        
      } catch (error) {
        console.error('拼场申请页面：加载申请列表失败:', error)
        this.error = error.message || '加载失败，请重试'
        this.requests = []
      }
    },
    

    
    // 更新筛选标签计数
    updateFilterCounts() {
      const counts = {
        all: this.requests.length,
        pending: this.requests.filter(r => r.status === 'PENDING').length,
        approved: this.requests.filter(r => r.status === 'APPROVED').length,
        rejected: this.requests.filter(r => r.status === 'REJECTED').length
      }
      
      this.filterTabs.forEach(tab => {
        tab.count = counts[tab.value] || 0
      })
    },
    
    // 切换筛选
    switchFilter(filter) {
      this.currentFilter = filter
    },
    
    // 显示取消确认弹窗
    showCancelConfirm(request) {
      this.cancelTarget = request
      this.$refs.cancelPopup.open()
    },
    
    // 确认取消申请
    async confirmCancel() {
      if (!this.cancelTarget) return
      
      try {
        uni.showLoading({ title: '取消中...' })
        
        await this.cancelSharingRequest(this.cancelTarget.id)
        
        // 从本地列表中移除
        const index = this.requests.findIndex(r => r.id === this.cancelTarget.id)
        if (index > -1) {
          this.requests.splice(index, 1)
        }
        
        // 更新计数
        this.updateFilterCounts()
        
        uni.hideLoading()
        
        uni.showToast({
          title: '取消成功',
          icon: 'success'
        })
        
        this.cancelTarget = null
        
      } catch (error) {
        uni.hideLoading()
        console.error('拼场申请页面：取消申请失败:', error)
        uni.showToast({
          title: error.message || '取消失败',
          icon: 'error'
        })
      }
    },
    
    // 跳转到拼场详情
    goToSharingDetail(sharingId) {
      console.log('跳转到拼场详情，sharingId:', sharingId)

      if (!sharingId) {
        console.error('sharingId为空，无法跳转')
        uni.showToast({
          title: '订单ID不存在',
          icon: 'error'
        })
        return
      }

      uni.navigateTo({
        url: `/pages/sharing/detail?id=${sharingId}`
      })
    },
    
    // 跳转到拼场列表
    goToSharingList() {
      uni.navigateTo({
        url: '/pages/sharing/list'
      })
    },
    
    // 获取进度百分比
    getProgressPercent(current, max) {
      if (!max || max === 0) return 0
      return Math.round((current / max) * 100)
    },
    
    // 格式化活动时间
    formatActivityTime(request) {
      if (!request) return '--'

      // 优先使用 bookingTime，如果没有则使用 bookingDate + startTime
      if (request.bookingTime) {
        try {
          // 处理iOS兼容性：确保时间格式正确
          let bookingTimeStr = request.bookingTime
          if (typeof bookingTimeStr === 'string' && bookingTimeStr.includes(' ') && !bookingTimeStr.includes('T')) {
            bookingTimeStr = bookingTimeStr.replace(' ', 'T')
          }
          const bookingTime = new Date(bookingTimeStr)

          console.log('拼场申请时间转换 - bookingTime:', request.bookingTime, '→', bookingTimeStr, '→', bookingTime)
          console.log('拼场申请时间字段 - startTime:', request.startTime, 'endTime:', request.endTime)

          const dateStr = bookingTime.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit'
          })

          const startTimeStr = bookingTime.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          })

          // 使用后端返回的 startTime 和 endTime 字段（这些是纯时间字符串）
          let timeSlot = startTimeStr
          if (request.startTime && request.endTime) {
            timeSlot = `${request.startTime}-${request.endTime}`
          } else if (request.endTime) {
            timeSlot = `${startTimeStr}-${request.endTime}`
          }

          return `${dateStr} ${timeSlot}`
        } catch (error) {
          console.error('时间格式化错误:', error)
          return '--'
        }
      }

      // 回退到原有逻辑
      const date = this.formatDate(request.bookingDate)
      const timeSlot = this.formatTimeSlot(request.startTime, request.endTime)

      return `${date} ${timeSlot}`
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return '--'
      return formatDate(date, 'MM-DD')
    },
    
    // 格式化日期时间
    formatDateTime(datetime) {
      if (!datetime) return '--'
      return formatDateTime(datetime, 'MM-DD HH:mm')
    },
    
    // 格式化时间段
    formatTimeSlot(startTime, endTime) {
      if (!startTime && !endTime) {
        return '时间未指定'
      }
      if (startTime && !endTime) {
        return startTime
      }
      if (!startTime && endTime) {
        return endTime
      }
      return `${startTime}-${endTime}`
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待处理',
        'APPROVED_PENDING_PAYMENT': '已批准待支付',
        'APPROVED': '已完成',
        'PAID': '拼场成功',
        'REJECTED': '已拒绝',
        'TIMEOUT_CANCELLED': '超时取消'
      }
      return statusMap[status] || '未知状态'
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        'PENDING': 'status-pending',
        'APPROVED_PENDING_PAYMENT': 'status-pending',
        'APPROVED': 'status-approved',
        'PAID': 'status-success',
        'REJECTED': 'status-rejected',
        'TIMEOUT_CANCELLED': 'status-cancelled'
      }
      return classMap[status] || 'status-unknown'
    },
    
    // 获取空状态标题
    getEmptyTitle() {
      const titleMap = {
        'all': '暂无申请记录',
        'pending': '暂无待处理申请',
        'approved': '暂无已通过申请',
        'rejected': '暂无被拒绝申请'
      }
      return titleMap[this.currentFilter] || '暂无申请记录'
    },
    
    // 获取空状态描述
    getEmptyDesc() {
      const descMap = {
        'all': '快去申请加入感兴趣的拼场吧',
        'pending': '您的申请都已被处理',
        'approved': '暂时没有通过的申请',
        'rejected': '暂时没有被拒绝的申请'
      }
      return descMap[this.currentFilter] || '快去申请加入感兴趣的拼场吧'
    },

    // 获取申请价格
    getRequestPrice(request) {
      if (!request) return '0.00'

      // 优先使用 paymentAmount，如果没有则使用 pricePerPerson 或 totalPrice
      const price = request.paymentAmount || request.pricePerPerson || request.totalPrice || 0
      return typeof price === 'number' ? price.toFixed(2) : '0.00'
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 导航栏
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  
  .nav-left {
    width: 60rpx;
    
    .nav-icon {
      font-size: 40rpx;
      color: #333333;
    }
  }
  
  .nav-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
  }
  
  .nav-right {
    width: 60rpx;
  }
}

// 筛选标签
.filter-tabs {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .filter-tab {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    margin-right: 20rpx;
    border-radius: 20rpx;
    background-color: #f5f5f5;
    transition: all 0.3s ease;
    
    &:last-child {
      margin-right: 0;
    }
    
    &.active {
      background-color: #ff6b35;
      
      .tab-text {
        color: #ffffff;
      }
      
      .tab-count {
        background-color: rgba(255, 255, 255, 0.3);
        color: #ffffff;
      }
    }
    
    .tab-text {
      font-size: 26rpx;
      color: #666666;
      transition: color 0.3s ease;
    }
    
    .tab-count {
      font-size: 20rpx;
      color: #ff6b35;
      background-color: #fff7f0;
      padding: 2rpx 8rpx;
      border-radius: 10rpx;
      margin-left: 8rpx;
      min-width: 32rpx;
      text-align: center;
      transition: all 0.3s ease;
    }
  }
}

// 加载状态
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 200rpx 0;
  
  text {
    font-size: 28rpx;
    color: #999999;
  }
}

// 错误状态
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 200rpx 60rpx;
  
  .error-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
  }
  
  .error-text {
    font-size: 28rpx;
    color: #333333;
    text-align: center;
    margin-bottom: 40rpx;
    line-height: 1.4;
  }
  
  .retry-btn {
    width: 200rpx;
    height: 70rpx;
    background-color: #ff6b35;
    color: #ffffff;
    border: none;
    border-radius: 12rpx;
    font-size: 26rpx;
  }
}

// 内容区域
.content {
  padding: 20rpx;
}

// 申请列表
.requests-list {
  .request-item {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .sharing-info {
      margin-bottom: 24rpx;
      
      .sharing-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16rpx;
        
        .venue-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
        }
        
        .status-badge {
          padding: 6rpx 12rpx;
          border-radius: 12rpx;
          
          .status-text {
            font-size: 22rpx;
            font-weight: bold;
          }
          
          &.status-pending {
            background-color: #fff7e6;
            .status-text { color: #fa8c16; }
          }
          
          &.status-approved {
            background-color: #f6ffed;
            .status-text { color: #52c41a; }
          }

          &.status-success {
            background-color: #f6ffed;
            .status-text { color: #52c41a; }
          }

          &.status-rejected {
            background-color: #fff2f0;
            .status-text { color: #ff4d4f; }
          }

          &.status-cancelled {
            background-color: #f5f5f5;
            .status-text { color: #999999; }
          }

          &.status-unknown {
            background-color: #f5f5f5;
            .status-text { color: #999999; }
          }
        }
      }
      
      .sharing-details {
        margin-bottom: 16rpx;
        
        .team-name {
          font-size: 28rpx;
          color: #333333;
          font-weight: bold;
          display: block;
          margin-bottom: 8rpx;
        }
        
        .activity-time {
          font-size: 24rpx;
          color: #666666;
          display: block;
          margin-bottom: 6rpx;
        }
        
        .price {
          font-size: 24rpx;
          color: #ff6b35;
          font-weight: bold;
        }
      }
      
      .participants-progress {
        .progress-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8rpx;
          
          .progress-text {
            font-size: 22rpx;
            color: #666666;
          }
          
          .progress-percent {
            font-size: 20rpx;
            color: #ff6b35;
            font-weight: bold;
          }
        }
        
        .progress-bar {
          height: 6rpx;
          background-color: #f0f0f0;
          border-radius: 3rpx;
          overflow: hidden;
          
          .progress-fill {
            height: 100%;
            background-color: #ff6b35;
            transition: width 0.3s ease;
          }
        }
      }
    }
    
    .request-info {
      .request-meta {
        margin-bottom: 16rpx;
        
        .request-time {
          font-size: 22rpx;
          color: #999999;
          display: block;
          margin-bottom: 4rpx;
        }
        
        .process-time {
          font-size: 22rpx;
          color: #999999;
        }
      }
      
      .request-actions {
        .pending-actions {
          display: flex;
          justify-content: flex-end;
          
          .action-btn {
            padding: 12rpx 24rpx;
            border: none;
            border-radius: 20rpx;
            font-size: 24rpx;
            
            &.cancel-btn {
              background-color: #fff2f0;
              color: #ff4d4f;
            }
          }
        }
        
        .approved-actions {
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          .approved-text {
            font-size: 24rpx;
            color: #52c41a;
            font-weight: bold;
          }
          
          .action-btn {
            padding: 12rpx 24rpx;
            border: none;
            border-radius: 20rpx;
            font-size: 24rpx;
            
            &.join-btn {
              background-color: #ff6b35;
              color: #ffffff;
            }
          }
        }
        
        .rejected-actions {
          .rejected-text {
            font-size: 24rpx;
            color: #ff4d4f;
            font-weight: bold;
            display: block;
            margin-bottom: 6rpx;
          }
          
          .reject-reason {
            font-size: 22rpx;
            color: #999999;
          }
        }
      }
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 200rpx 60rpx;
  
  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-title {
    font-size: 32rpx;
    color: #333333;
    font-weight: bold;
    margin-bottom: 16rpx;
  }
  
  .empty-desc {
    font-size: 26rpx;
    color: #999999;
    text-align: center;
    line-height: 1.4;
    margin-bottom: 40rpx;
  }
  
  .browse-btn {
    width: 200rpx;
    height: 70rpx;
    background-color: #ff6b35;
    color: #ffffff;
    border: none;
    border-radius: 12rpx;
    font-size: 26rpx;
  }
}
</style>