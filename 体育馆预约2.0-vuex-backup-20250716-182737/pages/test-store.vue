<template>
  <view class="container">
    <view class="header">
      <text class="title">Store测试页面</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">Store连接状态</text>
      <text class="test-result">{{ $store ? '✅ Store已连接' : '❌ Store未连接' }}</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">Sharing模块状态</text>
      <text class="test-result">加载状态: {{ loading ? '加载中' : '已完成' }}</text>
      <text class="test-result">数据数量: {{ sharingOrders?.length || 0 }}</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">操作按钮</text>
      <button class="test-btn" @click="testGetData">测试获取数据</button>
      <button class="test-btn" @click="testGenerateMock">测试生成模拟数据</button>
    </view>
    
    <view class="test-section">
      <text class="section-title">数据详情</text>
      <view v-if="sharingOrders && sharingOrders.length > 0">
        <view v-for="(item, index) in sharingOrders" :key="index" class="data-item">
          <text class="data-text">{{ index + 1 }}. {{ item.venueName }} - {{ item.teamName }}</text>
          <text class="data-text">状态: {{ item.status }}, 价格: ¥{{ item.totalPrice }}</text>
        </view>
      </view>
      <text v-else class="no-data">暂无数据</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">控制台日志</text>
      <text class="log-text">请查看控制台输出</text>
    </view>
  </view>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'

export default {
  name: 'TestStore',
  
  computed: {
    ...mapGetters('sharing', ['sharingOrders', 'loading'])
  },
  
  onLoad() {
    console.log('测试页面加载')
    console.log('Store状态:', this.$store)
    console.log('Sharing模块:', this.$store?.state?.sharing)
    this.testGetData()
  },
  
  methods: {
    ...mapActions('sharing', ['getJoinableSharingOrders']),
    
    async testGetData() {
      try {
        console.log('开始测试获取数据')
        console.log('getJoinableSharingOrders方法:', this.getJoinableSharingOrders)
        
        const result = await this.getJoinableSharingOrders({ page: 1, pageSize: 10 })
        console.log('获取数据结果:', result)
        console.log('Store中的数据:', this.sharingOrders)
        
        uni.showToast({
          title: `获取到${this.sharingOrders?.length || 0}条数据`,
          icon: 'none'
        })
      } catch (error) {
        console.error('获取数据失败:', error)
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        })
      }
    },
    
    testGenerateMock() {
      console.log('测试生成模拟数据')
      // 直接调用store的mutation来设置模拟数据
      const mockData = [
        {
          id: 999,
          venueName: '测试场馆',
          venueLocation: '测试位置',
          teamName: '测试队伍',
          status: 'OPEN',
          maxParticipants: 8,
          currentParticipants: 3,
          totalPrice: 500,
          bookingDate: '2025-06-30',
          startTime: '14:00',
          endTime: '16:00',
          creatorUsername: '测试用户',
          description: '这是一条测试数据'
        }
      ]
      
      this.$store.commit('sharing/SET_SHARING_ORDERS', mockData)
      console.log('设置模拟数据完成:', mockData)
      
      uni.showToast({
        title: '模拟数据已设置',
        icon: 'success'
      })
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.test-result {
  display: block;
  margin-bottom: 5px;
  color: #666;
}

.test-btn {
  margin: 5px;
  padding: 10px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 5px;
}

.data-item {
  margin-bottom: 10px;
  padding: 10px;
  background: white;
  border-radius: 5px;
}

.data-text {
  display: block;
  margin-bottom: 3px;
  color: #333;
  font-size: 14px;
}

.no-data {
  color: #999;
  font-style: italic;
}

.log-text {
  color: #666;
  font-size: 12px;
}
</style>