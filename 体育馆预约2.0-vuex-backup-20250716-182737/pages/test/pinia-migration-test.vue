<template>
  <view class="container">
    <view class="header">
      <text class="title">Pinia迁移测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">Vuex状态</text>
      <view class="state-display">
        <text>Loading: {{ vuexLoading }}</text>
        <text>Network: {{ vuexNetworkStatus }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <text class="section-title">Pinia状态</text>
      <view class="state-display">
        <text>Loading: {{ piniaLoading }}</text>
        <text>Network: {{ piniaNetworkStatus }}</text>
      </view>
    </view>
    
    <view class="test-actions">
      <button @click="testVuexUpdate" class="test-btn">测试Vuex更新</button>
      <button @click="testPiniaUpdate" class="test-btn">测试Pinia更新</button>
      <button @click="toggleLoading" class="test-btn">切换Loading</button>
      <button @click="toggleNetwork" class="test-btn">切换Network</button>
      <button @click="runValidation" class="test-btn validation-btn">运行验证</button>
      <button @click="toggleContinuousValidation" class="test-btn"
              :class="{ 'active': continuousValidation }">
        {{ continuousValidation ? '停止' : '开始' }}持续验证
      </button>
      <button @click="testPiniaDirectUpdate" class="test-btn direct-btn">
        直接设置Pinia状态
      </button>
      <button @click="forceSync" class="test-btn sync-btn">
        强制同步状态
      </button>
      <button @click="testVenueVuex" class="test-btn venue-btn">
        测试Venue Vuex
      </button>
      <button @click="testVenuePinia" class="test-btn venue-btn">
        测试Venue Pinia
      </button>
      <button @click="diagnoseVenueStore" class="test-btn debug-btn">
        诊断Venue Store
      </button>
      <button @click="testVenueBasic" class="test-btn success-btn">
        基础Venue测试
      </button>
      <button @click="testSharingVuex" class="test-btn sharing-btn">
        测试Sharing Vuex
      </button>
      <button @click="testSharingPinia" class="test-btn sharing-btn">
        测试Sharing Pinia
      </button>
      <button @click="testBookingVuex" class="test-btn booking-btn">
        测试Booking Vuex
      </button>
      <button @click="testBookingPinia" class="test-btn booking-btn">
        测试Booking Pinia
      </button>
    </view>
    
    <view class="test-section">
      <text class="section-title">User状态测试</text>
      <view class="state-display">
        <text>Vuex登录状态: {{ vuexUserLoggedIn }}</text>
        <text>Pinia登录状态: {{ piniaUserLoggedIn }}</text>
        <text>Vuex用户名: {{ vuexUsername || '未登录' }}</text>
        <text>Pinia用户名: {{ piniaUsername || '未登录' }}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">Venue状态测试</text>
      <view class="state-display">
        <text>Vuex场馆数量: {{ vuexVenueCount }}</text>
        <text>Pinia场馆数量: {{ piniaVenueCount }}</text>
        <text>Vuex加载状态: {{ vuexVenueLoading }}</text>
        <text>Pinia加载状态: {{ piniaVenueLoading }}</text>
        <text>Vuex搜索结果: {{ vuexSearchCount }}</text>
        <text>Pinia搜索结果: {{ piniaSearchCount }}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">Sharing状态测试</text>
      <view class="state-display">
        <text>Vuex分享订单: {{ vuexSharingCount }}</text>
        <text>Pinia分享订单: {{ piniaSharingCount }}</text>
        <text>Vuex我的订单: {{ vuexMySharingCount }}</text>
        <text>Pinia我的订单: {{ piniaMySharingCount }}</text>
        <text>Vuex加载状态: {{ vuexSharingLoading }}</text>
        <text>Pinia加载状态: {{ piniaSharingLoading }}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">Booking状态测试</text>
      <view class="state-display">
        <text>Vuex预订数量: {{ vuexBookingCount }}</text>
        <text>Pinia预订数量: {{ piniaBookingCount }}</text>
        <text>Vuex拼场订单: {{ vuexBookingSharingCount }}</text>
        <text>Pinia拼场订单: {{ piniaBookingSharingCount }}</text>
        <text>Vuex加载状态: {{ vuexBookingLoading }}</text>
        <text>Pinia加载状态: {{ piniaBookingLoading }}</text>
      </view>
    </view>

    <view class="sync-status">
      <text class="sync-title">同步状态检查</text>
      <text :class="['sync-result', syncStatus.loading ? 'success' : 'error']">
        Loading同步: {{ syncStatus.loading ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.network ? 'success' : 'error']">
        Network同步: {{ syncStatus.network ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.userLogin ? 'success' : 'error']">
        User登录同步: {{ syncStatus.userLogin ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.venueCount ? 'success' : 'error']">
        Venue数量同步: {{ syncStatus.venueCount ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.venueLoading ? 'success' : 'error']">
        Venue加载同步: {{ syncStatus.venueLoading ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.venueSearch ? 'success' : 'error']">
        Venue搜索同步: {{ syncStatus.venueSearch ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.sharingOrders ? 'success' : 'error']">
        Sharing订单同步: {{ syncStatus.sharingOrders ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.sharingLoading ? 'success' : 'error']">
        Sharing加载同步: {{ syncStatus.sharingLoading ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.bookingCount ? 'success' : 'error']">
        Booking数量同步: {{ syncStatus.bookingCount ? '✓' : '✗' }}
      </text>
      <text :class="['sync-result', syncStatus.bookingLoading ? 'success' : 'error']">
        Booking加载同步: {{ syncStatus.bookingLoading ? '✓' : '✗' }}
      </text>
    </view>

    <view class="validation-results" v-if="validationResults">
      <text class="section-title">验证结果</text>
      <view class="result-item">
        <text :class="['result-status', validationResults.overall ? 'success' : 'error']">
          总体状态: {{ validationResults.overall ? '通过' : '失败' }}
        </text>
      </view>
      <view class="result-item" v-if="validationResults.modules.app">
        <text :class="['result-status', validationResults.modules.app.valid ? 'success' : 'error']">
          App模块: {{ validationResults.modules.app.valid ? '✓' : '✗' }}
        </text>
      </view>
      <view class="result-item" v-if="validationResults.modules.user">
        <text :class="['result-status', validationResults.modules.user.valid ? 'success' : 'error']">
          User模块: {{ validationResults.modules.user.valid ? '✓' : '✗' }}
        </text>
      </view>
      <view class="result-item" v-if="validationResults.modules.venue">
        <text :class="['result-status', validationResults.modules.venue.valid ? 'success' : 'error']">
          Venue模块: {{ validationResults.modules.venue.valid ? '✓' : '✗' }}
        </text>
      </view>
      <view class="result-item" v-if="validationResults.modules.sharing">
        <text :class="['result-status', validationResults.modules.sharing.valid ? 'success' : 'error']">
          Sharing模块: {{ validationResults.modules.sharing.valid ? '✓' : '✗' }}
        </text>
      </view>
      <view class="result-item" v-if="validationResults.modules.booking">
        <text :class="['result-status', validationResults.modules.booking.valid ? 'success' : 'error']">
          Booking模块: {{ validationResults.modules.booking.valid ? '✓' : '✗' }}
        </text>
      </view>
      <text class="timestamp">验证时间: {{ validationResults.timestamp }}</text>
    </view>

    <view class="migration-status" v-if="migrationStatus">
      <text class="section-title">迁移进度</text>
      <view class="progress-info">
        <text>当前阶段: {{ migrationStatus.currentPhase }}</text>
        <text>完成进度: {{ migrationStatus.progress }}%</text>
        <text>已完成: {{ migrationStatus.completedPhases }}/{{ migrationStatus.totalPhases }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { useAppStore } from '@/stores/app.js'
import { useUserStore } from '@/stores/user.js'
import { useVenueStore } from '@/stores/venue.js'
import { useSharingStore } from '@/stores/sharing.js'
import { useBookingStore } from '@/stores/booking.js'
import { validateMigration, startValidation, stopValidation } from '@/stores/migration-validator.js'
import { getMigrationStatus } from '@/stores/migration-config.js'

export default {
  name: 'PiniaMigrationTest',

  data() {
    return {
      appStore: null,
      userStore: null,
      venueStore: null,
      sharingStore: null,
      bookingStore: null,
      validationResults: null,
      continuousValidation: false,
      validationInterval: null,
      migrationStatus: null
    }
  },
  
  computed: {
    ...mapState(['loading', 'networkStatus']),
    ...mapState('user', ['isLoggedIn', 'userInfo']),
    ...mapState('venue', {
      vuexVenueList: 'venueList',
      vuexVenueLoading: 'loading',
      vuexSearchResults: 'searchResults'
    }),
    ...mapState('sharing', {
      vuexSharingOrders: 'sharingOrders',
      vuexMySharingOrders: 'mySharingOrders',
      vuexSharingLoading: 'loading'
    }),
    ...mapState('booking', {
      vuexBookingList: 'bookingList',
      vuexBookingSharingOrders: 'sharingOrders',
      vuexBookingLoading: 'loading'
    }),

    vuexLoading() {
      return this.loading
    },

    vuexNetworkStatus() {
      return this.networkStatus
    },

    vuexUserLoggedIn() {
      return this.isLoggedIn
    },

    vuexUsername() {
      return this.userInfo?.username
    },

    piniaLoading() {
      return this.appStore?.loading || false
    },

    piniaNetworkStatus() {
      return this.appStore?.networkStatus || true
    },

    piniaUserLoggedIn() {
      return this.userStore?.isLoggedIn || false
    },

    piniaUsername() {
      return this.userStore?.userInfo?.username
    },

    vuexVenueCount() {
      return this.vuexVenueList?.length || 0
    },

    piniaVenueCount() {
      return this.venueStore?.venueList?.length || 0
    },

    piniaVenueLoading() {
      return this.venueStore?.loading || false
    },

    vuexSearchCount() {
      return this.vuexSearchResults?.length || 0
    },

    piniaSearchCount() {
      return this.venueStore?.searchResults?.length || 0
    },

    vuexSharingCount() {
      return this.vuexSharingOrders?.length || 0
    },

    piniaSharingCount() {
      return this.sharingStore?.sharingOrders?.length || 0
    },

    vuexMySharingCount() {
      return this.vuexMySharingOrders?.length || 0
    },

    piniaMySharingCount() {
      return this.sharingStore?.mySharingOrders?.length || 0
    },

    piniaSharingLoading() {
      return this.sharingStore?.loading || false
    },

    vuexBookingCount() {
      return this.vuexBookingList?.length || 0
    },

    piniaBookingCount() {
      return this.bookingStore?.bookingList?.length || 0
    },

    vuexBookingSharingCount() {
      return this.vuexBookingSharingOrders?.length || 0
    },

    piniaBookingSharingCount() {
      return this.bookingStore?.sharingOrders?.length || 0
    },

    piniaBookingLoading() {
      return this.bookingStore?.loading || false
    },

    syncStatus() {
      return {
        loading: this.vuexLoading === this.piniaLoading,
        network: this.vuexNetworkStatus === this.piniaNetworkStatus,
        userLogin: this.vuexUserLoggedIn === this.piniaUserLoggedIn,
        venueCount: this.vuexVenueCount === this.piniaVenueCount,
        venueLoading: this.vuexVenueLoading === this.piniaVenueLoading,
        venueSearch: this.vuexSearchCount === this.piniaSearchCount,
        sharingOrders: this.vuexSharingCount === this.piniaSharingCount,
        sharingLoading: this.vuexSharingLoading === this.piniaSharingLoading,
        bookingCount: this.vuexBookingCount === this.piniaBookingCount,
        bookingLoading: this.vuexBookingLoading === this.piniaBookingLoading
      }
    }
  },
  
  methods: {
    ...mapActions(['setLoading', 'setNetworkStatus']),
    
    testVuexUpdate() {
      console.log('测试Vuex更新')
      this.setLoading(!this.vuexLoading)
    },
    
    testPiniaUpdate() {
      console.log('测试Pinia更新')
      const newValue = !this.piniaLoading
      console.log('Pinia设置loading为:', newValue)
      this.appStore.setLoading(newValue)
    },
    
    toggleLoading() {
      const newValue = !this.vuexLoading
      console.log('切换Loading状态到:', newValue)
      this.setLoading(newValue)
    },
    
    toggleNetwork() {
      const newValue = !this.vuexNetworkStatus
      console.log('切换Network状态到:', newValue)
      this.setNetworkStatus(newValue)
    },

    // 运行验证
    runValidation() {
      console.log('运行迁移验证')
      this.validationResults = validateMigration()

      uni.showToast({
        title: this.validationResults.overall ? '验证通过' : '验证失败',
        icon: this.validationResults.overall ? 'success' : 'error'
      })
    },

    // 切换持续验证
    toggleContinuousValidation() {
      if (this.continuousValidation) {
        // 停止持续验证
        if (this.validationInterval) {
          stopValidation(this.validationInterval)
          this.validationInterval = null
        }
        this.continuousValidation = false
        console.log('持续验证已停止')
      } else {
        // 开始持续验证
        this.validationInterval = startValidation(3000) // 每3秒验证一次
        this.continuousValidation = true
        console.log('持续验证已开始')
      }
    },

    // 直接更新Pinia状态
    testPiniaDirectUpdate() {
      console.log('直接设置Pinia状态')
      const newLoading = !this.piniaLoading
      const newNetwork = !this.piniaNetworkStatus

      // 直接设置状态属性
      this.appStore.loading = newLoading
      this.appStore.networkStatus = newNetwork

      console.log('Pinia状态已直接设置:', { loading: newLoading, network: newNetwork })
    },

    // 强制同步状态
    forceSync() {
      console.log('强制同步状态')

      // 从Vuex同步到Pinia
      this.appStore.loading = this.vuexLoading
      this.appStore.networkStatus = this.vuexNetworkStatus

      console.log('状态已强制同步')

      // 运行验证
      setTimeout(() => {
        this.runValidation()
      }, 100)
    },

    // 诊断Venue Store
    diagnoseVenueStore() {
      console.log('=== Venue Store 诊断开始 ===')

      try {
        // 测试多种获取方式
        const store1 = this.venueStore
        const store2 = useVenueStore()

        console.log('this.venueStore:', {
          exists: !!store1,
          type: typeof store1,
          constructor: store1?.constructor?.name,
          getVenueListType: typeof store1?.getVenueList,
          getVenueListValue: store1?.getVenueList
        })

        console.log('useVenueStore():', {
          exists: !!store2,
          type: typeof store2,
          constructor: store2?.constructor?.name,
          getVenueListType: typeof store2?.getVenueList,
          getVenueListValue: store2?.getVenueList
        })

        console.log('相等性检查:', store1 === store2)

        // 检查store的内部结构
        if (store2) {
          console.log('Store内部结构:')
          console.log('- $id:', store2.$id)
          console.log('- state keys:', Object.keys(store2.$state || {}))
          console.log('- 所有属性:', Object.getOwnPropertyNames(store2))
          console.log('- 原型链:', Object.getPrototypeOf(store2))

          // 尝试直接访问actions
          const descriptor = Object.getOwnPropertyDescriptor(store2, 'getVenueList')
          console.log('getVenueList属性描述符:', descriptor)
        }

        uni.showToast({
          title: '诊断完成，查看控制台',
          icon: 'success'
        })

      } catch (error) {
        console.error('诊断失败:', error)
        uni.showToast({
          title: '诊断失败',
          icon: 'error'
        })
      }

      console.log('=== Venue Store 诊断结束 ===')
    },

    // 基础Venue测试（避开getVenueList问题）
    testVenueBasic() {
      console.log('=== 基础Venue测试开始 ===')

      try {
        const venueStore = useVenueStore()

        if (!venueStore) {
          throw new Error('VenueStore未初始化')
        }

        console.log('1. 测试状态读取:')
        console.log('- venueList:', venueStore.venueList)
        console.log('- loading:', venueStore.loading)
        console.log('- searchResults:', venueStore.searchResults)

        console.log('2. 测试状态设置:')
        venueStore.setLoading(true)
        console.log('- 设置loading为true，当前值:', venueStore.loading)

        venueStore.setSearchResults(['测试场馆A', '测试场馆B'])
        console.log('- 设置搜索结果，当前值:', venueStore.searchResults)

        console.log('3. 测试状态清除:')
        setTimeout(() => {
          venueStore.setLoading(false)
          venueStore.clearSearchResults()
          console.log('- 清除后loading:', venueStore.loading)
          console.log('- 清除后searchResults:', venueStore.searchResults)
        }, 1000)

        console.log('4. 测试getters:')
        console.log('- totalVenues:', venueStore.totalVenues)
        console.log('- isLoading:', venueStore.isLoading)

        uni.showToast({
          title: '基础测试完成',
          icon: 'success'
        })

      } catch (error) {
        console.error('基础测试失败:', error)
        uni.showToast({
          title: '基础测试失败',
          icon: 'error'
        })
      }

      console.log('=== 基础Venue测试结束 ===')
    },

    // 测试Sharing Vuex功能
    async testSharingVuex() {
      console.log('测试Sharing Vuex功能')
      try {
        // 通过Vuex获取分享订单列表
        await this.$store.dispatch('sharing/getSharingOrders', { page: 1, pageSize: 5 })
        console.log('Vuex分享订单列表获取成功')

        // 设置loading状态测试
        this.$store.commit('sharing/SET_LOADING', true)
        setTimeout(() => {
          this.$store.commit('sharing/SET_LOADING', false)
        }, 1000)

        uni.showToast({
          title: 'Sharing Vuex测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('Sharing Vuex测试失败:', error)
        uni.showToast({
          title: 'Sharing Vuex测试失败',
          icon: 'error'
        })
      }
    },

    // 测试Sharing Pinia功能
    async testSharingPinia() {
      console.log('测试Sharing Pinia功能')
      try {
        const sharingStore = useSharingStore()

        if (!sharingStore) {
          throw new Error('SharingStore未初始化')
        }

        console.log('1. 测试状态读取:')
        console.log('- sharingOrders:', sharingStore.sharingOrders)
        console.log('- mySharingOrders:', sharingStore.mySharingOrders)
        console.log('- loading:', sharingStore.loading)

        console.log('2. 测试API调用:')
        try {
          await sharingStore.getSharingOrdersList({ page: 1, pageSize: 5 })
          console.log('Pinia分享订单列表获取成功')
        } catch (apiError) {
          console.warn('API调用失败，继续其他测试:', apiError)
        }

        console.log('3. 测试状态设置:')
        sharingStore.setLoading(true)
        console.log('- 设置loading为true，当前值:', sharingStore.loading)

        setTimeout(() => {
          sharingStore.setLoading(false)
          console.log('- 设置loading为false，当前值:', sharingStore.loading)
        }, 1000)

        console.log('4. 测试getters:')
        console.log('- totalSharingOrders:', sharingStore.totalSharingOrders)
        console.log('- isLoading:', sharingStore.isLoading)

        uni.showToast({
          title: 'Sharing Pinia测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('Sharing Pinia测试失败:', error)
        uni.showToast({
          title: `Sharing Pinia测试失败: ${error.message}`,
          icon: 'error'
        })
      }
    },

    // 测试Venue Vuex功能
    async testVenueVuex() {
      console.log('测试Venue Vuex功能')
      try {
        // 通过Vuex获取场馆列表
        await this.$store.dispatch('venue/getVenueList', { page: 1, pageSize: 5 })
        console.log('Vuex场馆列表获取成功')

        // 设置loading状态测试
        this.$store.commit('venue/SET_LOADING', true)
        setTimeout(() => {
          this.$store.commit('venue/SET_LOADING', false)
        }, 1000)

        uni.showToast({
          title: 'Venue Vuex测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('Venue Vuex测试失败:', error)
        uni.showToast({
          title: 'Venue Vuex测试失败',
          icon: 'error'
        })
      }
    },

    // 测试Venue Pinia功能
    async testVenuePinia() {
      console.log('测试Venue Pinia功能')
      try {
        // 重新获取store实例，确保最新状态
        const freshVenueStore = useVenueStore()

        if (!freshVenueStore) {
          throw new Error('VenueStore未初始化')
        }

        console.log('Fresh VenueStore可用方法:', Object.getOwnPropertyNames(freshVenueStore))
        console.log('Fresh getVenueList类型:', typeof freshVenueStore.getVenueList)

        // 尝试调用getVenueList方法
        if (typeof freshVenueStore.getVenueList === 'function') {
          try {
            await freshVenueStore.getVenueList({ page: 1, pageSize: 5 })
            console.log('Pinia场馆列表获取成功')
          } catch (error) {
            console.warn('getVenueList调用失败:', error)
          }
        } else {
          console.warn('getVenueList不是函数，跳过API调用测试')
        }

        // 测试基础状态设置
        freshVenueStore.setLoading(true)
        console.log('设置loading为true')

        setTimeout(() => {
          freshVenueStore.setLoading(false)
          console.log('设置loading为false')
        }, 1000)

        // 测试搜索结果
        freshVenueStore.setSearchResults(['测试场馆1', '测试场馆2'])
        console.log('设置搜索结果')

        setTimeout(() => {
          freshVenueStore.clearSearchResults()
          console.log('清除搜索结果')
        }, 2000)

        // 更新实例引用
        this.venueStore = freshVenueStore

        uni.showToast({
          title: 'Venue Pinia测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('Venue Pinia测试失败:', error)
        uni.showToast({
          title: `Venue Pinia测试失败: ${error.message}`,
          icon: 'error'
        })
      }
    },

    // 测试Booking Vuex功能
    async testBookingVuex() {
      console.log('测试Booking Vuex功能')
      try {
        // 通过Vuex获取用户预订列表
        await this.$store.dispatch('booking/getUserBookings', { page: 1, pageSize: 5 })
        console.log('Vuex用户预订列表获取成功')

        // 设置loading状态测试
        this.$store.commit('booking/SET_LOADING', true)
        setTimeout(() => {
          this.$store.commit('booking/SET_LOADING', false)
        }, 1000)

        uni.showToast({
          title: 'Booking Vuex测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('Booking Vuex测试失败:', error)
        uni.showToast({
          title: 'Booking Vuex测试失败',
          icon: 'error'
        })
      }
    },

    // 测试Booking Pinia功能
    async testBookingPinia() {
      console.log('测试Booking Pinia功能')
      try {
        const bookingStore = useBookingStore()

        if (!bookingStore) {
          throw new Error('BookingStore未初始化')
        }

        console.log('1. 测试状态读取:')
        console.log('- bookingList:', bookingStore.bookingList)
        console.log('- sharingOrders:', bookingStore.sharingOrders)
        console.log('- loading:', bookingStore.loading)

        console.log('2. 测试API调用:')
        try {
          await bookingStore.getUserBookings({ page: 1, pageSize: 5 })
          console.log('Pinia用户预订列表获取成功')
        } catch (apiError) {
          console.warn('API调用失败，继续其他测试:', apiError)
        }

        console.log('3. 测试状态设置:')
        bookingStore.setLoading(true)
        console.log('- 设置loading为true，当前值:', bookingStore.loading)

        setTimeout(() => {
          bookingStore.setLoading(false)
          console.log('- 设置loading为false，当前值:', bookingStore.loading)
        }, 1000)

        console.log('4. 测试getters:')
        console.log('- totalBookings:', bookingStore.totalBookings)
        console.log('- isLoading:', bookingStore.isLoading)

        uni.showToast({
          title: 'Booking Pinia测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('Booking Pinia测试失败:', error)
        uni.showToast({
          title: `Booking Pinia测试失败: ${error.message}`,
          icon: 'error'
        })
      }
    }
  },

  onLoad() {
    console.log('初始化Pinia迁移测试页面')
    try {
      this.appStore = useAppStore()
      this.userStore = useUserStore()
      this.venueStore = useVenueStore()
      this.sharingStore = useSharingStore()
      this.bookingStore = useBookingStore()
      this.migrationStatus = getMigrationStatus()

      console.log('Stores初始化成功:', {
        appStore: !!this.appStore,
        userStore: !!this.userStore,
        venueStore: !!this.venueStore,
        venueStoreType: typeof this.venueStore,
        venueStoreGetVenueListType: typeof this.venueStore?.getVenueList
      })

      // 强制重新绑定actions（如果需要）
      if (this.venueStore && typeof this.venueStore.getVenueList !== 'function') {
        console.warn('VenueStore actions未正确绑定，尝试重新初始化')
        // 重新获取store实例
        setTimeout(() => {
          this.venueStore = useVenueStore()
          console.log('重新初始化后的getVenueList类型:', typeof this.venueStore?.getVenueList)
        }, 100)
      }
    } catch (error) {
      console.error('Stores初始化失败:', error)
    }

    // 验证初始状态同步
    setTimeout(() => {
      console.log('初始状态检查:', {
        vuex: {
          loading: this.vuexLoading,
          network: this.vuexNetworkStatus,
          userLogin: this.vuexUserLoggedIn,
          username: this.vuexUsername
        },
        pinia: {
          loading: this.piniaLoading,
          network: this.piniaNetworkStatus,
          userLogin: this.piniaUserLoggedIn,
          username: this.piniaUsername
        }
      })

      // 自动运行一次验证
      this.runValidation()
    }, 100)
  },

  onUnload() {
    // 清理持续验证
    if (this.validationInterval) {
      stopValidation(this.validationInterval)
      this.validationInterval = null
      this.continuousValidation = false
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.test-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  
  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #666;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .state-display {
    text {
      display: block;
      font-size: 26rpx;
      color: #333;
      margin-bottom: 10rpx;
    }
  }
}

.test-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
  
  .test-btn {
    flex: 1;
    min-width: 200rpx;
    height: 80rpx;
    background: #007aff;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 26rpx;

    &.validation-btn {
      background: #52c41a;
    }

    &.active {
      background: #ff4d4f;
    }

    &.direct-btn {
      background: #fa8c16;
    }

    &.sync-btn {
      background: #722ed1;
    }

    &.venue-btn {
      background: #13c2c2;
    }

    &.debug-btn {
      background: #fa8c16;
    }

    &.success-btn {
      background: #52c41a;
    }

    &.sharing-btn {
      background: #eb2f96;
    }

    &.booking-btn {
      background: #fa8c16;
    }
  }
}

.sync-status {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  
  .sync-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #666;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .sync-result {
    display: block;
    font-size: 26rpx;
    margin-bottom: 10rpx;
    
    &.success {
      color: #52c41a;
    }
    
    &.error {
      color: #ff4d4f;
    }
  }
}

.validation-results, .migration-status {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #666;
    margin-bottom: 20rpx;
    display: block;
  }

  .result-item {
    margin-bottom: 10rpx;

    .result-status {
      font-size: 26rpx;

      &.success {
        color: #52c41a;
      }

      &.error {
        color: #ff4d4f;
      }
    }
  }

  .timestamp {
    font-size: 22rpx;
    color: #999;
    margin-top: 10rpx;
    display: block;
  }

  .progress-info {
    text {
      display: block;
      font-size: 26rpx;
      color: #333;
      margin-bottom: 8rpx;
    }
  }
}
</style>
