"use strict";
const config = {
  development: {
    baseURL: "http://localhost:8080/api",
    timeout: 15e3,
    // 增加到15秒超时，适应复杂查询
    debug: true,
    cache: true,
    // 启用缓存
    retryTimes: 2,
    // 重试次数
    retryDelay: 1e3
    // 重试延迟
  },
  production: {
    baseURL: "https://api.example.com/api",
    timeout: 2e4,
    // 生产环境增加到20秒超时
    debug: false,
    cache: true,
    retryTimes: 3,
    retryDelay: 1e3
  }
};
const config$1 = config["development"];
exports.config = config$1;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/index.js.map
