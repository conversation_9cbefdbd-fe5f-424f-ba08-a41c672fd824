"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
function createSharingOrder(data) {
  return utils_request.post("/sharing-orders", data);
}
function getSharingOrderById(id) {
  common_vendor.index.__f__("log", "at api/sharing.js:10", "[SharingAPI] 请求拼场订单详情，ID:", id);
  return utils_request.get(`/sharing-orders/${id}`, {}, { cache: false });
}
function getSharingOrderByMainOrderId(orderId) {
  common_vendor.index.__f__("log", "at api/sharing.js:16", "[SharingAPI] 通过主订单ID请求拼场订单详情，主订单ID:", orderId);
  return utils_request.get(`/sharing-orders/by-order/${orderId}`, {}, { cache: false });
}
function getJoinableSharingOrders(params) {
  return utils_request.get("/sharing-orders/joinable", params);
}
function getAllSharingOrders(params) {
  return utils_request.get("/sharing-orders", params, {
    cache: false
    // 确保不使用缓存
  });
}
function getMyCreatedSharingOrders() {
  return utils_request.get("/sharing-orders/my-created", {}, { cache: false });
}
function joinSharingOrder(id, data = {}) {
  return utils_request.post(`/bookings/shared/${id}/apply`, {
    participantsCount: data.participantsCount || 1,
    teamName: data.teamName || "",
    contactInfo: data.contactInfo || "",
    message: data.message || ""
  });
}
function applyJoinSharingOrder(id) {
  return utils_request.post(`/sharing-orders/${id}/apply-join`);
}
function confirmSharingOrder(id) {
  return utils_request.post(`/sharing-orders/${id}/confirm`);
}
function cancelSharingOrder(id) {
  return utils_request.post(`/sharing-orders/${id}/cancel`);
}
function removeSharingParticipant(sharingId, participantId) {
  return utils_request.request({
    url: `/sharing-orders/${sharingId}/participants/${participantId}/remove`,
    method: "post"
  });
}
function updateSharingSettings(sharingId, settings) {
  return utils_request.request({
    url: `/sharing-orders/${sharingId}/settings`,
    method: "put",
    data: settings
  });
}
function applySharedBooking(orderId, data) {
  return utils_request.post(`/bookings/shared/${orderId}/apply`, data);
}
function handleSharedRequest(requestId, data) {
  return utils_request.put(`/shared/requests/${requestId}`, data);
}
function getMySharedRequests(params) {
  return utils_request.get("/shared/my-requests", params, { cache: false });
}
function getReceivedSharedRequests(params) {
  return utils_request.get("/shared/received-requests", params);
}
exports.applyJoinSharingOrder = applyJoinSharingOrder;
exports.applySharedBooking = applySharedBooking;
exports.cancelSharingOrder = cancelSharingOrder;
exports.confirmSharingOrder = confirmSharingOrder;
exports.createSharingOrder = createSharingOrder;
exports.getAllSharingOrders = getAllSharingOrders;
exports.getJoinableSharingOrders = getJoinableSharingOrders;
exports.getMyCreatedSharingOrders = getMyCreatedSharingOrders;
exports.getMySharedRequests = getMySharedRequests;
exports.getReceivedSharedRequests = getReceivedSharedRequests;
exports.getSharingOrderById = getSharingOrderById;
exports.getSharingOrderByMainOrderId = getSharingOrderByMainOrderId;
exports.handleSharedRequest = handleSharedRequest;
exports.joinSharingOrder = joinSharingOrder;
exports.removeSharingParticipant = removeSharingParticipant;
exports.updateSharingSettings = updateSharingSettings;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/sharing.js.map
