"use strict";
const common_vendor = require("../common/vendor.js");
const api_auth = require("../api/auth.js");
const api_user = require("../api/user.js");
const utils_auth = require("../utils/auth.js");
const utils_ui = require("../utils/ui.js");
const utils_routerGuardNew = require("../utils/router-guard-new.js");
const useUserStore = common_vendor.defineStore("user", {
  state: () => ({
    token: utils_auth.getToken(),
    userInfo: utils_auth.getUserInfo(),
    userStats: {
      totalBookings: 0,
      totalSharings: 0
    },
    isLoggedIn: !!(utils_auth.getToken() && utils_auth.getUserInfo()),
    loginChecking: false
    // 是否正在检查登录状态
  }),
  getters: {
    // Token相关
    getToken: (state) => state.token,
    // 用户信息相关
    getUserInfo: (state) => state.userInfo,
    getIsLoggedIn: (state) => state.isLoggedIn,
    userId: (state) => {
      var _a;
      return (_a = state.userInfo) == null ? void 0 : _a.id;
    },
    username: (state) => {
      var _a;
      return (_a = state.userInfo) == null ? void 0 : _a.username;
    },
    nickname: (state) => {
      var _a, _b;
      return ((_a = state.userInfo) == null ? void 0 : _a.nickname) || ((_b = state.userInfo) == null ? void 0 : _b.username);
    },
    avatar: (state) => {
      var _a;
      return (_a = state.userInfo) == null ? void 0 : _a.avatar;
    },
    phone: (state) => {
      var _a;
      return (_a = state.userInfo) == null ? void 0 : _a.phone;
    },
    email: (state) => {
      var _a;
      return (_a = state.userInfo) == null ? void 0 : _a.email;
    },
    // 统计信息
    totalBookings: (state) => state.userStats.totalBookings,
    totalSharings: (state) => state.userStats.totalSharings,
    // 状态检查
    isLoginChecking: (state) => state.loginChecking
  },
  actions: {
    // 初始化用户状态（从本地存储恢复）
    initUserState() {
      common_vendor.index.__f__("log", "at stores/user.js:45", "[UserStore] 初始化用户状态");
      const token = utils_auth.getToken();
      const userInfo = utils_auth.getUserInfo();
      if (token && userInfo) {
        this.token = token;
        this.userInfo = userInfo;
        this.isLoggedIn = true;
        common_vendor.index.__f__("log", "at stores/user.js:53", "[UserStore] 从本地存储恢复用户状态成功");
      } else {
        common_vendor.index.__f__("log", "at stores/user.js:55", "[UserStore] 本地存储中无有效用户状态");
        this.logout();
      }
    },
    // 设置token
    setToken(token) {
      this.token = token;
      utils_auth.setToken(token);
    },
    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo;
      utils_auth.setUserInfo(userInfo);
    },
    // 设置登录状态
    setLoginStatus(status) {
      common_vendor.index.__f__("log", "at stores/user.js:74", "[UserStore] 设置登录状态:", status);
      this.isLoggedIn = status;
      utils_routerGuardNew.updateAuthCache(status);
    },
    // 设置登录检查状态
    setLoginChecking(checking) {
      this.loginChecking = checking;
    },
    // 设置用户统计
    setUserStats(stats) {
      this.userStats = stats;
    },
    // 清除用户数据
    clearUserData() {
      common_vendor.index.__f__("log", "at stores/user.js:92", "[UserStore] 清除用户数据");
      this.token = "";
      this.userInfo = null;
      this.userStats = {
        totalBookings: 0,
        totalSharings: 0
      };
      this.isLoggedIn = false;
      this.loginChecking = false;
      utils_auth.removeToken();
      utils_auth.removeUserInfo();
      utils_routerGuardNew.clearAuthCache();
    },
    // 用户登录
    async login(loginData) {
      try {
        common_vendor.index.__f__("log", "at stores/user.js:110", "[UserStore] 开始登录");
        const response = await api_auth.login(loginData);
        common_vendor.index.__f__("log", "at stores/user.js:112", "[UserStore] 登录响应:", response);
        if (!response) {
          throw new Error("登录响应为空");
        }
        const responseData = response.data || response;
        const token = responseData.accessToken || responseData.token;
        if (!token) {
          common_vendor.index.__f__("error", "at stores/user.js:122", "[UserStore] 响应数据:", responseData);
          throw new Error("未获取到登录令牌");
        }
        const user = {
          id: responseData.id,
          username: responseData.username,
          email: responseData.email,
          phone: responseData.phone,
          nickname: responseData.nickname,
          avatar: responseData.avatar,
          roles: responseData.roles
        };
        this.setToken(token);
        this.setUserInfo(user);
        this.setLoginStatus(true);
        common_vendor.index.__f__("log", "at stores/user.js:141", "[UserStore] 登录成功，用户信息:", user);
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:144", "[UserStore] 登录错误:", error);
        this.setLoginStatus(false);
        throw error;
      }
    },
    // 用户注册
    async register(registerData) {
      try {
        common_vendor.index.__f__("log", "at stores/user.js:153", "[UserStore] 开始注册");
        const response = await api_auth.register(registerData);
        common_vendor.index.__f__("log", "at stores/user.js:155", "[UserStore] 注册响应:", response);
        utils_ui.showSuccess("注册成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:160", "[UserStore] 注册错误:", error);
        utils_ui.showError(error.message || "注册失败");
        throw error;
      }
    },
    // 用户退出
    async logout() {
      try {
        common_vendor.index.__f__("log", "at stores/user.js:169", "[UserStore] 开始退出登录");
        try {
          await api_auth.logout();
        } catch (error) {
          common_vendor.index.__f__("warn", "at stores/user.js:175", "[UserStore] 后端退出接口调用失败:", error);
        }
        this.clearUserData();
        common_vendor.index.__f__("log", "at stores/user.js:182", "[UserStore] 退出登录成功");
        utils_ui.showSuccess("退出成功");
        common_vendor.index.reLaunch({
          url: "/pages/user/login"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:191", "[UserStore] 退出登录错误:", error);
        this.clearUserData();
        throw error;
      }
    },
    // 检查登录状态
    async checkLoginStatus() {
      if (this.loginChecking) {
        common_vendor.index.__f__("log", "at stores/user.js:201", "[UserStore] 正在检查登录状态，跳过重复检查");
        return this.isLoggedIn;
      }
      this.setLoginChecking(true);
      try {
        common_vendor.index.__f__("log", "at stores/user.js:208", "[UserStore] 开始检查登录状态");
        const token = utils_auth.getToken();
        const userInfo = utils_auth.getUserInfo();
        if (!token || !userInfo) {
          common_vendor.index.__f__("log", "at stores/user.js:214", "[UserStore] 本地无登录信息");
          this.setLoginStatus(false);
          return false;
        }
        try {
          const response = await (void 0)();
          common_vendor.index.__f__("log", "at stores/user.js:222", "[UserStore] 用户信息验证成功:", response);
          if (response && response.data) {
            this.setUserInfo(response.data);
          }
          this.setLoginStatus(true);
          return true;
        } catch (error) {
          common_vendor.index.__f__("error", "at stores/user.js:232", "[UserStore] Token验证失败:", error);
          this.clearUserData();
          return false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:239", "[UserStore] 检查登录状态错误:", error);
        this.setLoginStatus(false);
        return false;
      } finally {
        this.setLoginChecking(false);
      }
    },
    // 获取用户统计信息
    async getUserStats() {
      try {
        common_vendor.index.__f__("log", "at stores/user.js:250", "[UserStore] 获取用户统计信息");
        const response = await api_user.getUserStats();
        if (response && response.data) {
          this.setUserStats(response.data);
          common_vendor.index.__f__("log", "at stores/user.js:255", "[UserStore] 用户统计信息更新成功:", response.data);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:260", "[UserStore] 获取用户统计信息失败:", error);
        throw error;
      }
    },
    // 更新用户信息
    async updateProfile(profileData) {
      try {
        common_vendor.index.__f__("log", "at stores/user.js:268", "[UserStore] 更新用户信息");
        const response = await (void 0)(profileData);
        if (response && response.data) {
          this.setUserInfo({
            ...this.userInfo,
            ...response.data
          });
          common_vendor.index.__f__("log", "at stores/user.js:277", "[UserStore] 用户信息更新成功");
          utils_ui.showSuccess("信息更新成功");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:283", "[UserStore] 更新用户信息失败:", error);
        utils_ui.showError(error.message || "更新失败");
        throw error;
      }
    },
    // 修改密码
    async changePassword(passwordData) {
      try {
        common_vendor.index.__f__("log", "at stores/user.js:292", "[UserStore] 修改密码");
        const response = await api_user.changePassword(passwordData);
        common_vendor.index.__f__("log", "at stores/user.js:295", "[UserStore] 密码修改成功");
        utils_ui.showSuccess("密码修改成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:300", "[UserStore] 修改密码失败:", error);
        utils_ui.showError(error.message || "密码修改失败");
        throw error;
      }
    }
  }
});
exports.useUserStore = useUserStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/stores/user.js.map
