
.skeleton-container.data-v-da12d541 {
  padding: 20rpx;
}
.skeleton-item.data-v-da12d541 {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading-da12d541 1.5s infinite;
  border-radius: 8rpx;
}
@keyframes skeleton-loading-da12d541 {
0% {
    background-position: 200% 0;
}
100% {
    background-position: -200% 0;
}
}

/* 轮播图骨架屏 */
.skeleton-banner.data-v-da12d541 {
  margin-bottom: 30rpx;
}
.skeleton-banner-item.data-v-da12d541 {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
}

/* 快捷功能骨架屏 */
.skeleton-actions.data-v-da12d541 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  padding: 0 20rpx;
}
.skeleton-action-item.data-v-da12d541 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
}

/* 场馆列表骨架屏 */
.skeleton-venues.data-v-da12d541 {
  margin-bottom: 40rpx;
}
.skeleton-section-header.data-v-da12d541 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.skeleton-title.data-v-da12d541 {
  width: 160rpx;
  height: 40rpx;
}
.skeleton-more.data-v-da12d541 {
  width: 80rpx;
  height: 30rpx;
}
.skeleton-venue-list.data-v-da12d541 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.skeleton-venue-card.data-v-da12d541 {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.skeleton-venue-image.data-v-da12d541 {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}
.skeleton-venue-info.data-v-da12d541 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.skeleton-venue-name.data-v-da12d541 {
  width: 200rpx;
  height: 32rpx;
}
.skeleton-venue-location.data-v-da12d541 {
  width: 160rpx;
  height: 24rpx;
}
.skeleton-venue-price.data-v-da12d541 {
  width: 120rpx;
  height: 28rpx;
}

/* 拼场列表骨架屏 */
.skeleton-sharings.data-v-da12d541 {
  margin-bottom: 40rpx;
}
.skeleton-sharing-card.data-v-da12d541 {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.skeleton-sharing-header.data-v-da12d541 {
  width: 100%;
  height: 32rpx;
  margin-bottom: 16rpx;
}
.skeleton-sharing-info.data-v-da12d541 {
  width: 80%;
  height: 24rpx;
  margin-bottom: 12rpx;
}
.skeleton-sharing-price.data-v-da12d541 {
  width: 120rpx;
  height: 28rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
.skeleton-actions.data-v-da12d541 {
    padding: 0 10rpx;
}
.skeleton-action-item.data-v-da12d541 {
    width: 100rpx;
    height: 100rpx;
}
.skeleton-venue-image.data-v-da12d541 {
    width: 140rpx;
    height: 100rpx;
}
}
