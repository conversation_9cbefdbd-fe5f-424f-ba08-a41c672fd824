<view class="payment-container data-v-7695f594"><view class="nav-bar data-v-7695f594"><view class="nav-left data-v-7695f594" bindtap="{{a}}"><text class="nav-icon data-v-7695f594">←</text></view><view class="nav-title data-v-7695f594">订单支付</view><view class="nav-right data-v-7695f594"></view></view><view wx:if="{{b}}" class="loading-container data-v-7695f594"><text class="loading-text data-v-7695f594">加载中...</text></view><view wx:elif="{{c}}" class="order-section data-v-7695f594"><view class="order-header data-v-7695f594"><text class="order-title data-v-7695f594">订单信息</text><text class="order-no data-v-7695f594">{{d}}</text></view><view class="order-details data-v-7695f594"><view class="detail-item data-v-7695f594"><text class="detail-label data-v-7695f594">场馆名称</text><text class="detail-value data-v-7695f594">{{e}}</text></view><view class="detail-item data-v-7695f594"><text class="detail-label data-v-7695f594">预约时间</text><text class="detail-value data-v-7695f594">{{f}}</text></view><view class="detail-item data-v-7695f594"><text class="detail-label data-v-7695f594">预约类型</text><text class="detail-value data-v-7695f594">{{g}}</text></view><view wx:if="{{h}}" class="detail-item data-v-7695f594"><text class="detail-label data-v-7695f594">队伍名称</text><text class="detail-value data-v-7695f594">{{i}}</text></view><view class="detail-item data-v-7695f594"><text class="detail-label data-v-7695f594">联系方式</text><text class="detail-value data-v-7695f594">{{j}}</text></view></view><view class="price-section data-v-7695f594"><view class="price-item data-v-7695f594"><text class="price-label data-v-7695f594">订单金额</text><text class="price-value data-v-7695f594">¥{{k}}</text></view><view wx:if="{{l}}" class="price-note data-v-7695f594"><text class="note-text data-v-7695f594">* 拼场订单按队伍收费</text></view></view></view><view class="payment-methods data-v-7695f594"><view class="method-header data-v-7695f594"><text class="method-title data-v-7695f594">支付方式</text></view><view class="method-list data-v-7695f594"><view class="{{['method-item', 'data-v-7695f594', n && 'active']}}" bindtap="{{o}}"><view class="method-info data-v-7695f594"><text class="method-icon data-v-7695f594">💳</text><text class="method-name data-v-7695f594">微信支付</text></view><view class="method-radio data-v-7695f594"><text wx:if="{{m}}" class="radio-checked data-v-7695f594">✓</text></view></view><view class="{{['method-item', 'data-v-7695f594', q && 'active']}}" bindtap="{{r}}"><view class="method-info data-v-7695f594"><text class="method-icon data-v-7695f594">💰</text><text class="method-name data-v-7695f594">支付宝</text></view><view class="method-radio data-v-7695f594"><text wx:if="{{p}}" class="radio-checked data-v-7695f594">✓</text></view></view></view></view><view class="payment-footer data-v-7695f594"><view class="footer-info data-v-7695f594"><text class="footer-label data-v-7695f594">应付金额</text><text class="footer-amount data-v-7695f594">¥{{s}}</text></view><button class="{{['pay-button', 'data-v-7695f594', v && 'disabled']}}" disabled="{{w}}" bindtap="{{x}}">{{t}}</button></view><uni-popup wx:if="{{E}}" class="r data-v-7695f594" u-s="{{['d']}}" u-r="resultPopup" u-i="7695f594-0" bind:__l="__l" u-p="{{E}}"><view class="result-popup data-v-7695f594"><view class="result-icon data-v-7695f594"><text wx:if="{{y}}" class="success-icon data-v-7695f594">✓</text><text wx:else class="error-icon data-v-7695f594">✗</text></view><text class="result-title data-v-7695f594">{{z}}</text><text class="result-message data-v-7695f594">{{A}}</text><button class="result-button data-v-7695f594" bindtap="{{C}}">{{B}}</button></view></uni-popup></view>