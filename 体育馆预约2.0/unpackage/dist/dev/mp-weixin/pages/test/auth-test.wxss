/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-74191e9e {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-74191e9e {
  text-align: center;
  margin-bottom: 40rpx;
}
.title.data-v-74191e9e {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.status-section.data-v-74191e9e, .action-section.data-v-74191e9e {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.section-title.data-v-74191e9e {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.status-item.data-v-74191e9e {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}
.status-item.data-v-74191e9e:last-child {
  border-bottom: none;
}
.status-item text.data-v-74191e9e {
  font-size: 28rpx;
  color: #666;
  word-break: break-all;
}
.action-btn.data-v-74191e9e {
  width: 100%;
  height: 80rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}
.action-btn.data-v-74191e9e:last-child {
  margin-bottom: 0;
}
.action-btn.data-v-74191e9e:active {
  background-color: #0056cc;
}
.divider.data-v-74191e9e {
  height: 1rpx;
  background-color: #eee;
  margin: 30rpx 0;
}