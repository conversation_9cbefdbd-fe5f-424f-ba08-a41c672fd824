
.test-container.data-v-53050e82 {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-53050e82 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title.data-v-53050e82 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.subtitle.data-v-53050e82 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.section-title.data-v-53050e82 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0 20rpx 0;
}
.scenario-list.data-v-53050e82 {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}
.scenario-item.data-v-53050e82 {
  background: white;
  border-radius: 12rpx;
  padding: 25rpx;
  border: 2rpx solid transparent;
  cursor: pointer;
}
.scenario-item.active.data-v-53050e82 {
  border-color: #1890ff;
  background-color: #e6f7ff;
}
.scenario-name.data-v-53050e82 {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.scenario-desc.data-v-53050e82 {
  display: block;
  font-size: 26rpx;
  color: #666;
}
.current-order.data-v-53050e82 {
  margin: 30rpx 0;
}
.order-info.data-v-53050e82 {
  background: white;
  border-radius: 12rpx;
  padding: 25rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.order-id.data-v-53050e82 {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.order-status.data-v-53050e82 {
  font-size: 26rpx;
  color: #1890ff;
  background-color: #e6f7ff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}
.refresh-btn.data-v-53050e82 {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}
.test-steps.data-v-53050e82 {
  margin: 30rpx 0;
}
.step-item.data-v-53050e82 {
  background: white;
  border-radius: 12rpx;
  padding: 25rpx;
  margin-bottom: 15rpx;
  border-left: 4rpx solid #e0e0e0;
}
.step-item.active.data-v-53050e82 {
  border-left-color: #1890ff;
}
.step-item.completed.data-v-53050e82 {
  border-left-color: #52c41a;
  background-color: #f6ffed;
}
.step-item.failed.data-v-53050e82 {
  border-left-color: #ff4d4f;
  background-color: #fff2f0;
}
.step-header.data-v-53050e82 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.step-number.data-v-53050e82 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  text-align: center;
  line-height: 40rpx;
  font-size: 24rpx;
  margin-right: 15rpx;
}
.step-title.data-v-53050e82 {
  flex: 1;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.step-status.data-v-53050e82 {
  font-size: 24rpx;
  color: #666;
}
.step-desc.data-v-53050e82 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}
.step-action-btn.data-v-53050e82 {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
}
.test-results.data-v-53050e82 {
  margin: 30rpx 0;
}
.result-item.data-v-53050e82 {
  background: white;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
}
.result-time.data-v-53050e82 {
  font-size: 24rpx;
  color: #999;
  min-width: 120rpx;
}
.result-action.data-v-53050e82 {
  font-size: 26rpx;
  color: #333;
  min-width: 150rpx;
}
.result-status.data-v-53050e82 {
  font-size: 24rpx;
  font-weight: bold;
  min-width: 100rpx;
}
.result-status.success.data-v-53050e82 {
  color: #52c41a;
}
.result-status.error.data-v-53050e82 {
  color: #ff4d4f;
}
.result-message.data-v-53050e82 {
  flex: 1;
  font-size: 24rpx;
  color: #666;
}
.flow-diagram.data-v-53050e82 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
}
.flow-text.data-v-53050e82 {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-line;
}
