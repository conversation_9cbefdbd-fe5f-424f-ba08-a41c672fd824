
.test-container.data-v-a3fdf199 {
  padding: 20px;
  background-color: #f5f5f5;
}
.test-header.data-v-a3fdf199 {
  text-align: center;
  margin-bottom: 30px;
}
.test-title.data-v-a3fdf199 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}
.test-subtitle.data-v-a3fdf199 {
  font-size: 14px;
  color: #666;
  display: block;
}
.section-title.data-v-a3fdf199 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  display: block;
}
.test-section.data-v-a3fdf199 {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.status-grid.data-v-a3fdf199, .test-grid.data-v-a3fdf199 {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.status-item.data-v-a3fdf199, .test-item.data-v-a3fdf199 {
  flex: 1;
  min-width: 120px;
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  border: 2px solid #ddd;
}
.status-item.success.data-v-a3fdf199 {
  background-color: #d4edda;
  border-color: #28a745;
}
.status-item.error.data-v-a3fdf199 {
  background-color: #f8d7da;
  border-color: #dc3545;
}
.test-item.success.data-v-a3fdf199 {
  background-color: #d4edda;
  border-color: #28a745;
}
.test-item.error.data-v-a3fdf199 {
  background-color: #f8d7da;
  border-color: #dc3545;
}
.function-tests.data-v-a3fdf199 {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.function-group.data-v-a3fdf199 {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fafafa;
}
.group-title.data-v-a3fdf199 {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  display: block;
}
.test-btn.data-v-a3fdf199, .nav-btn.data-v-a3fdf199, .full-test-btn.data-v-a3fdf199 {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background-color: #007bff;
  color: white;
  font-size: 14px;
  margin: 5px;
}
.test-btn.data-v-a3fdf199:disabled, .full-test-btn.data-v-a3fdf199:disabled {
  background-color: #6c757d;
}
.full-test-btn.data-v-a3fdf199 {
  width: 100%;
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  background-color: #28a745;
}
.nav-tests.data-v-a3fdf199 {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.log-section.data-v-a3fdf199 {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
}
.log-container.data-v-a3fdf199 {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
}
.log-item.data-v-a3fdf199 {
  display: block;
  padding: 5px 0;
  font-family: monospace;
  font-size: 12px;
}
.log-item.success.data-v-a3fdf199 {
  color: #28a745;
}
.log-item.error.data-v-a3fdf199 {
  color: #dc3545;
}
.log-item.info.data-v-a3fdf199 {
  color: #17a2b8;
}
