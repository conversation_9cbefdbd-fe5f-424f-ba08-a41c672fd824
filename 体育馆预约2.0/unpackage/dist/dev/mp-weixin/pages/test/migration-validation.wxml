<view class="test-container data-v-a3fdf199"><view class="test-header data-v-a3fdf199"><text class="test-title data-v-a3fdf199">🧪 Pinia迁移全面验证</text><text class="test-subtitle data-v-a3fdf199">测试所有核心功能确保迁移成功</text></view><view class="status-section data-v-a3fdf199"><text class="section-title data-v-a3fdf199">📊 总体测试状态</text><view class="status-grid data-v-a3fdf199"><view class="{{['status-item', 'data-v-a3fdf199', b && 'success', c && 'error']}}"><text class="status-text data-v-a3fdf199">{{a}}</text></view><view class="status-item data-v-a3fdf199"><text class="status-text data-v-a3fdf199">通过: {{d}}/{{e}}</text></view></view></view><view class="test-section data-v-a3fdf199"><text class="section-title data-v-a3fdf199">🔗 Store连接测试</text><view class="test-grid data-v-a3fdf199"><view class="{{['test-item', 'data-v-a3fdf199', g]}}"><text class="test-name data-v-a3fdf199">User Store</text><text class="test-result data-v-a3fdf199">{{f}}</text></view><view class="{{['test-item', 'data-v-a3fdf199', i]}}"><text class="test-name data-v-a3fdf199">Venue Store</text><text class="test-result data-v-a3fdf199">{{h}}</text></view><view class="{{['test-item', 'data-v-a3fdf199', k]}}"><text class="test-name data-v-a3fdf199">Booking Store</text><text class="test-result data-v-a3fdf199">{{j}}</text></view><view class="{{['test-item', 'data-v-a3fdf199', m]}}"><text class="test-name data-v-a3fdf199">Sharing Store</text><text class="test-result data-v-a3fdf199">{{l}}</text></view><view class="{{['test-item', 'data-v-a3fdf199', o]}}"><text class="test-name data-v-a3fdf199">App Store</text><text class="test-result data-v-a3fdf199">{{n}}</text></view></view></view><view class="test-section data-v-a3fdf199"><text class="section-title data-v-a3fdf199">⚡ 核心功能测试</text><view class="function-tests data-v-a3fdf199"><view class="function-group data-v-a3fdf199"><text class="group-title data-v-a3fdf199">用户功能</text><button class="test-btn data-v-a3fdf199" bindtap="{{q}}" disabled="{{r}}">{{p}}</button><text class="test-status data-v-a3fdf199">{{s}}</text></view><view class="function-group data-v-a3fdf199"><text class="group-title data-v-a3fdf199">场馆功能</text><button class="test-btn data-v-a3fdf199" bindtap="{{v}}" disabled="{{w}}">{{t}}</button><text class="test-status data-v-a3fdf199">{{x}}</text></view><view class="function-group data-v-a3fdf199"><text class="group-title data-v-a3fdf199">预订功能</text><button class="test-btn data-v-a3fdf199" bindtap="{{z}}" disabled="{{A}}">{{y}}</button><text class="test-status data-v-a3fdf199">{{B}}</text></view><view class="function-group data-v-a3fdf199"><text class="group-title data-v-a3fdf199">拼场功能</text><button class="test-btn data-v-a3fdf199" bindtap="{{D}}" disabled="{{E}}">{{C}}</button><text class="test-status data-v-a3fdf199">{{F}}</text></view></view></view><view class="test-section data-v-a3fdf199"><text class="section-title data-v-a3fdf199">📱 页面导航测试</text><view class="nav-tests data-v-a3fdf199"><button class="nav-btn data-v-a3fdf199" bindtap="{{G}}">登录页面</button><button class="nav-btn data-v-a3fdf199" bindtap="{{H}}">个人中心</button><button class="nav-btn data-v-a3fdf199" bindtap="{{I}}">场馆列表</button><button class="nav-btn data-v-a3fdf199" bindtap="{{J}}">我的预订</button><button class="nav-btn data-v-a3fdf199" bindtap="{{K}}">拼场列表</button></view></view><view class="action-section data-v-a3fdf199"><button class="full-test-btn data-v-a3fdf199" bindtap="{{M}}" disabled="{{N}}">{{L}}</button></view><view wx:if="{{O}}" class="log-section data-v-a3fdf199"><text class="section-title data-v-a3fdf199">📝 测试日志</text><view class="log-container data-v-a3fdf199"><text wx:for="{{P}}" wx:for-item="log" wx:key="c" class="{{['log-item', 'data-v-a3fdf199', log.d]}}">{{log.a}} - {{log.b}}</text></view></view></view>