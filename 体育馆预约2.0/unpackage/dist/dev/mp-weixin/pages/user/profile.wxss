/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-036958a5 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header.data-v-036958a5 {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f65 100%);
  padding: 40rpx 40rpx 30rpx;
}
.header .user-info.data-v-036958a5 {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.header .user-info .avatar-wrapper.data-v-036958a5 {
  position: relative;
  margin-right: 24rpx;
}
.header .user-info .avatar-wrapper .avatar.data-v-036958a5 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.header .user-info .avatar-wrapper .avatar-edit.data-v-036958a5 {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.header .user-info .avatar-wrapper .avatar-edit .edit-icon.data-v-036958a5 {
  font-size: 20rpx;
}
.header .user-info .user-details.data-v-036958a5 {
  flex: 1;
}
.header .user-info .user-details .nickname.data-v-036958a5 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
}
.header .user-info .user-details .username.data-v-036958a5 {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}
.header .user-info .user-details .phone.data-v-036958a5 {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12rpx;
}
.header .user-info .edit-btn.data-v-036958a5 {
  padding: 12rpx 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
}
.header .user-info .edit-btn .edit-text.data-v-036958a5 {
  font-size: 24rpx;
  color: #ffffff;
}
.header .stats.data-v-036958a5 {
  display: flex;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx 0;
}
.header .stats .stat-item.data-v-036958a5 {
  flex: 1;
  text-align: center;
}
.header .stats .stat-item .stat-number.data-v-036958a5 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
}
.header .stats .stat-item .stat-label.data-v-036958a5 {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}
.menu-section.data-v-036958a5 {
  padding: 20rpx;
}
.menu-section .menu-group.data-v-036958a5 {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}
.menu-section .menu-group .group-title.data-v-036958a5 {
  padding: 24rpx 32rpx 16rpx;
}
.menu-section .menu-group .group-title .title-text.data-v-036958a5 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.menu-section .menu-group .menu-item.data-v-036958a5 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.menu-section .menu-group .menu-item.data-v-036958a5:last-child {
  border-bottom: none;
}
.menu-section .menu-group .menu-item .item-left.data-v-036958a5 {
  display: flex;
  align-items: center;
}
.menu-section .menu-group .menu-item .item-left .item-icon.data-v-036958a5 {
  font-size: 32rpx;
  margin-right: 20rpx;
}
.menu-section .menu-group .menu-item .item-left .item-text.data-v-036958a5 {
  font-size: 28rpx;
  color: #333333;
}
.menu-section .menu-group .menu-item .item-right.data-v-036958a5 {
  display: flex;
  align-items: center;
}
.menu-section .menu-group .menu-item .item-right .badge.data-v-036958a5 {
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-right: 12rpx;
  min-width: 32rpx;
  text-align: center;
}
.menu-section .menu-group .menu-item .item-right .arrow.data-v-036958a5 {
  font-size: 24rpx;
  color: #cccccc;
}