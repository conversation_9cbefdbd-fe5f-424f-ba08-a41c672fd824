"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_sharing = require("../../stores/sharing.js");
const stores_user = require("../../stores/user.js");
const _sfc_main = {
  name: "SharingManage",
  setup() {
    const sharingStore = stores_sharing.useSharingStore();
    const userStore = stores_user.useUserStore();
    return {
      sharingStore,
      userStore
    };
  },
  data() {
    return {
      sharingId: "",
      error: "",
      removeTarget: null,
      pendingSettings: {},
      // 暂存未支付用户的设置
      settings: {
        autoApprove: true,
        allowExit: true
      },
      participants: [],
      requests: []
    };
  },
  computed: {
    // 使用Pinia store的getters
    sharingOrderDetail() {
      return this.sharingStore.getSharingOrderDetail;
    },
    loading() {
      return this.sharingStore.isLoading;
    },
    userInfo() {
      return this.userStore.getUserInfo;
    },
    // 拼场详情
    sharingDetail() {
      return this.sharingOrderDetail;
    },
    // 检查发起者是否已支付
    isCreatorPaid() {
      if (!this.sharingDetail)
        return false;
      const mainOrderStatus = this.sharingDetail.orderStatus;
      if (mainOrderStatus) {
        const paidStatuses = [
          "PAID",
          // 已支付（普通订单）
          "OPEN",
          // 开放中（拼场订单发起者已支付）
          "APPROVED_PENDING_PAYMENT",
          // 已批准待支付（发起者已支付，等待申请者支付）
          "SHARING_SUCCESS",
          // 拼场成功（双方都已支付）
          "CONFIRMED",
          // 已确认
          "VERIFIED",
          // 已核销
          "COMPLETED"
          // 已完成
        ];
        const isPaid = paidStatuses.includes(mainOrderStatus);
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:340", "=== isCreatorPaid 调试信息 ===");
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:341", "主订单状态:", mainOrderStatus);
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:342", "是否已支付:", isPaid);
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:343", "支付状态列表:", paidStatuses);
        return isPaid;
      }
      const fallbackPaid = this.sharingDetail.status === "OPEN";
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:351", "=== isCreatorPaid 备用逻辑 ===");
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:352", "拼场订单状态:", this.sharingDetail.status);
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:353", "备用判断结果:", fallbackPaid);
      return fallbackPaid;
    },
    // 是否可以管理（基础权限检查，不包括支付状态）
    canManage() {
      var _a, _b, _c, _d, _e, _f, _g;
      const hasSharingDetail = !!this.sharingDetail;
      const hasUserInfo = !!this.userInfo;
      const creatorMatch = ((_a = this.sharingDetail) == null ? void 0 : _a.creatorUsername) === ((_b = this.userInfo) == null ? void 0 : _b.username);
      const statusCheck = ((_c = this.sharingDetail) == null ? void 0 : _c.status) === "ACTIVE" || ((_d = this.sharingDetail) == null ? void 0 : _d.status) === "RECRUITING" || ((_e = this.sharingDetail) == null ? void 0 : _e.status) === "OPEN";
      const result = hasSharingDetail && hasUserInfo && creatorMatch && statusCheck;
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:369", "=== canManage 调试信息 ===");
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:370", "hasSharingDetail:", hasSharingDetail);
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:371", "hasUserInfo:", hasUserInfo);
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:372", "creatorMatch:", creatorMatch);
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:373", "statusCheck:", statusCheck);
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:374", "sharingDetail.status:", (_f = this.sharingDetail) == null ? void 0 : _f.status);
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:375", "creatorPaid:", this.isCreatorPaid);
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:376", "sharingDetail.orderStatus:", (_g = this.sharingDetail) == null ? void 0 : _g.orderStatus);
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:377", "canManage result:", result);
      return result;
    },
    // 是否可以确认
    canConfirm() {
      var _a, _b;
      return ((_a = this.sharingDetail) == null ? void 0 : _a.status) === "OPEN" && ((_b = this.sharingDetail) == null ? void 0 : _b.currentParticipants) >= 2;
    },
    // 是否可以取消
    canCancel() {
      var _a;
      return ["OPEN", "FULL"].includes((_a = this.sharingDetail) == null ? void 0 : _a.status);
    },
    // 待处理申请
    pendingRequests() {
      return this.requests.filter((request) => request.status === "PENDING");
    }
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/sharing/manage.vue:400", "=== 拼场管理页面 onLoad ===");
    common_vendor.index.__f__("log", "at pages/sharing/manage.vue:401", "接收到的参数:", options);
    if (options.id) {
      this.sharingId = options.id;
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:405", "设置拼场ID:", this.sharingId);
      this.loadSharingDetail();
    } else {
      common_vendor.index.__f__("error", "at pages/sharing/manage.vue:408", "缺少拼场ID参数");
      this.error = "缺少拼场ID参数";
    }
    common_vendor.index.$on("paymentSuccess", this.onPaymentSuccess);
  },
  onShow() {
    common_vendor.index.__f__("log", "at pages/sharing/manage.vue:417", "=== 拼场管理页面 onShow 触发 ===");
    if (this.sharingId) {
      this.loadPendingSettings();
      this.forceRefreshData().then(() => {
        this.applyPendingSettings();
      });
    }
  },
  onPullDownRefresh() {
    this.loadSharingDetail().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  onUnload() {
    common_vendor.index.$off("paymentSuccess", this.onPaymentSuccess);
  },
  methods: {
    // 使用Pinia store的actions
    async getSharingOrderDetail(orderId, forceRefresh = false) {
      return await this.sharingStore.getOrderDetail(orderId, forceRefresh);
    },
    async updateSharingSettings(data) {
      return await this.sharingStore.updateSharingSettings(data);
    },
    async removeSharingParticipant(data) {
      common_vendor.index.__f__("warn", "at pages/sharing/manage.vue:452", "removeSharingParticipant 方法需要在Pinia store中实现");
    },
    async confirmSharingOrder(orderId) {
      common_vendor.index.__f__("warn", "at pages/sharing/manage.vue:456", "confirmSharingOrder 方法需要在Pinia store中实现");
    },
    async cancelSharingOrder(orderId) {
      common_vendor.index.__f__("warn", "at pages/sharing/manage.vue:460", "cancelSharingOrder 方法需要在Pinia store中实现");
    },
    async processSharingRequest(data) {
      return await this.sharingStore.processSharingRequest(data);
    },
    async getSharingRequests(params) {
      return await this.sharingStore.getReceivedRequestsList(params);
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载拼场详情
    async loadSharingDetail() {
      if (!this.sharingId)
        return;
      try {
        this.error = "";
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:480", "拼场管理页面：开始加载拼场详情:", this.sharingId);
        await this.getSharingOrderDetail(this.sharingId);
        if (this.sharingDetail) {
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:486", "=== 拼场详情加载完成 ===");
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:487", "拼场订单状态:", this.sharingDetail.status);
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:488", "主订单状态:", this.sharingDetail.orderStatus);
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:489", "发起者是否已支付:", this.isCreatorPaid);
          this.settings = {
            autoApprove: this.sharingDetail.autoApprove || false,
            allowExit: this.sharingDetail.allowExit || false
          };
          this.participants = [
            {
              id: "creator",
              username: this.sharingDetail.creatorUsername,
              nickname: this.sharingDetail.creatorUsername,
              avatar: "",
              isCreator: true
            }
          ];
          for (let i = 1; i < this.sharingDetail.currentParticipants; i++) {
            this.participants.push({
              id: `participant_${i}`,
              username: `user_${i}`,
              nickname: `用户${i}`,
              avatar: "",
              isCreator: false
            });
          }
          await this.loadSharingRequests();
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:522", "拼场管理页面：加载拼场详情成功:", this.sharingDetail);
        } else {
          this.error = "拼场不存在或已被删除";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:528", "拼场管理页面：加载拼场详情失败:", error);
        this.error = error.message || "加载失败，请重试";
      }
    },
    // 加载拼场申请
    async loadSharingRequests() {
      try {
        const requests = await this.getSharingRequests(this.sharingId);
        this.requests = requests || [];
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:538", "拼场管理页面：加载拼场申请成功:", this.requests);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:540", "拼场管理页面：加载拼场申请失败:", error);
        this.requests = [];
      }
    },
    // 自动通过申请开关变化
    async onAutoApproveChange(e) {
      const newValue = e.detail.value;
      if (newValue && !this.isCreatorPaid) {
        this.pendingSettings = {
          ...this.pendingSettings,
          autoApprove: newValue
        };
        common_vendor.index.setStorageSync(`pendingSettings_${this.sharingId}`, this.pendingSettings);
        common_vendor.index.showModal({
          title: "需要先支付",
          content: "支付完成后，自动通过申请功能将自动开启。",
          showCancel: true,
          cancelText: "取消",
          confirmText: "去支付",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: `/pages/payment/index?orderId=${this.sharingDetail.orderId}&type=sharing&from=sharing-manage`
              });
            } else {
              this.pendingSettings = {};
              common_vendor.index.removeStorageSync(`pendingSettings_${this.sharingId}`);
              this.$forceUpdate();
            }
          }
        });
        return;
      }
      try {
        await this.updateSharingSettings({
          sharingId: this.sharingId,
          settings: {
            autoApprove: newValue,
            allowExit: this.settings.allowExit
          }
        });
        this.settings.autoApprove = newValue;
        common_vendor.index.showToast({
          title: newValue ? "已开启自动通过" : "已关闭自动通过",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:601", "拼场管理页面：更新自动通过设置失败:", error);
        common_vendor.index.showToast({
          title: "设置失败",
          icon: "error"
        });
      }
    },
    // 允许中途退出开关变化
    async onAllowExitChange(e) {
      const newValue = e.detail.value;
      try {
        await this.updateSharingSettings({
          sharingId: this.sharingId,
          settings: {
            autoApprove: this.settings.autoApprove,
            allowExit: newValue
          }
        });
        this.settings.allowExit = newValue;
        common_vendor.index.showToast({
          title: newValue ? "已允许中途退出" : "已禁止中途退出",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:630", "拼场管理页面：更新退出设置失败:", error);
        common_vendor.index.showToast({
          title: "设置失败",
          icon: "error"
        });
      }
    },
    // 显示移除确认弹窗
    showRemoveConfirm(participant) {
      this.removeTarget = participant;
      this.$refs.removePopup.open();
    },
    // 确认移除参与者
    async confirmRemove() {
      if (!this.removeTarget)
        return;
      try {
        await this.removeSharingParticipant({
          sharingId: this.sharingId,
          participantId: this.removeTarget.id
        });
        const index = this.participants.findIndex((p) => p.id === this.removeTarget.id);
        if (index > -1) {
          this.participants.splice(index, 1);
        }
        if (this.sharingDetail) {
          this.sharingDetail.currentParticipants--;
        }
        common_vendor.index.showToast({
          title: "移除成功",
          icon: "success"
        });
        this.removeTarget = null;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:673", "拼场管理页面：移除参与者失败:", error);
        common_vendor.index.showToast({
          title: error.message || "移除失败",
          icon: "error"
        });
      }
    },
    // 取消移除
    cancelRemove() {
      this.removeTarget = null;
    },
    // 处理拼场申请
    async handleRequest(requestId, action) {
      var _a;
      try {
        await this.processSharingRequest({
          requestId,
          action: action === "APPROVED" ? "approve" : "reject"
        });
        const request = this.requests.find((r) => r.id === requestId);
        if (request) {
          request.status = action;
        }
        if (action === "APPROVED") {
          if (request) {
            this.participants.push({
              id: request.userId,
              username: request.username,
              nickname: request.userNickname,
              avatar: request.userAvatar,
              isCreator: false
            });
          }
          if (this.sharingDetail) {
            this.sharingDetail.currentParticipants++;
          }
        }
        common_vendor.index.showToast({
          title: action === "APPROVED" ? "已同意申请" : "已拒绝申请",
          icon: "success"
        });
        common_vendor.index.$emit("sharingDataChanged", {
          orderId: this.sharingId,
          action,
          currentParticipants: ((_a = this.sharingDetail) == null ? void 0 : _a.currentParticipants) || 0
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:730", "拼场管理页面：处理申请失败:", error);
        if (error.needPayment && error.orderId) {
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:734", "发起者需要先支付，跳转到支付页面，订单ID:", error.orderId);
          common_vendor.index.showModal({
            title: "需要先支付",
            content: error.message || "发起者尚未支付，无法批准申请。请先完成支付后再处理申请。",
            showCancel: true,
            cancelText: "取消",
            confirmText: "去支付",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.navigateTo({
                  url: `/pages/payment/index?orderId=${error.orderId}&from=sharing-manage`
                });
              }
            }
          });
        } else {
          common_vendor.index.showToast({
            title: error.message || "操作失败",
            icon: "error"
          });
        }
      }
    },
    // 处理确认拼场点击
    handleConfirmSharing() {
      if (!this.isCreatorPaid) {
        this.showPaymentPrompt();
        return;
      }
      this.confirmSharing();
    },
    // 处理取消拼场点击
    handleCancelSharing() {
      if (!this.isCreatorPaid) {
        this.showPaymentPrompt();
        return;
      }
      this.showCancelConfirm();
    },
    // 显示支付提示
    showPaymentPrompt() {
      common_vendor.index.showModal({
        title: "需要先支付",
        content: "请先完成订单支付后再管理拼场",
        confirmText: "去支付",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateTo({
              url: `/pages/payment/index?orderId=${this.sharingDetail.orderId}&type=sharing`
            });
          }
        }
      });
    },
    // 强制刷新数据（不使用缓存）
    async forceRefreshData() {
      var _a, _b;
      try {
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:801", "=== 强制刷新拼场数据 ===");
        this.error = "";
        this.sharingStore.setSharingOrderDetail(null);
        await this.getSharingOrderDetail(this.sharingId, true);
        if (this.sharingDetail) {
          this.settings = {
            autoApprove: this.sharingDetail.autoApprove || false,
            allowExit: this.sharingDetail.allowExit || false
          };
          await this.loadSharingRequests();
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:823", "=== 数据刷新完成 ===");
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:824", "当前拼场状态:", (_a = this.sharingDetail) == null ? void 0 : _a.status);
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:825", "当前主订单状态:", (_b = this.sharingDetail) == null ? void 0 : _b.orderStatus);
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:826", "发起者是否已支付:", this.isCreatorPaid);
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:827", "当前设置状态:", this.settings);
        } else {
          common_vendor.index.__f__("warn", "at pages/sharing/manage.vue:829", "=== 数据刷新失败：拼场详情为空 ===");
          this.error = "拼场不存在或已被删除";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:834", "强制刷新数据失败:", error);
        common_vendor.index.showToast({
          title: "数据刷新失败",
          icon: "error"
        });
      }
    },
    // 处理支付成功事件
    async onPaymentSuccess(eventData) {
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:844", "=== 收到支付成功事件 ===");
      common_vendor.index.__f__("log", "at pages/sharing/manage.vue:845", "事件数据:", eventData);
      if (eventData.type === "sharing" && eventData.fromPage === "sharing-manage") {
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:848", "拼场支付成功，重新查找拼场订单");
        try {
          const mainOrderId = eventData.orderId;
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:853", "支付的主订单ID:", mainOrderId);
          setTimeout(async () => {
            try {
              await this.findSharingOrderByMainOrderId(mainOrderId);
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/sharing/manage.vue:861", "通过主订单ID查找拼场订单失败:", error);
              this.forceRefreshData();
            }
          }, 1e3);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/sharing/manage.vue:868", "处理支付成功事件失败:", error);
          setTimeout(() => {
            this.forceRefreshData();
          }, 1e3);
        }
      }
    },
    // 通过主订单ID查找拼场订单
    async findSharingOrderByMainOrderId(mainOrderId) {
      try {
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:880", "=== 通过主订单ID查找拼场订单 ===");
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:881", "主订单ID:", mainOrderId);
        const newSharingOrderId = await this.sharingStore.getOrderDetailByMainOrderId(mainOrderId);
        if (newSharingOrderId) {
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:887", "找到新的拼场订单ID:", newSharingOrderId);
          this.sharingId = newSharingOrderId.toString();
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:890", "更新页面拼场ID为:", this.sharingId);
          this.error = "";
          if (this.sharingDetail) {
            this.settings = {
              autoApprove: this.sharingDetail.autoApprove || false,
              allowExit: this.sharingDetail.allowExit || false
            };
            await this.loadSharingRequests();
          }
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:906", "=== 拼场订单查找和更新完成 ===");
        } else {
          throw new Error("未找到对应的拼场订单");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:912", "通过主订单ID查找拼场订单失败:", error);
        throw error;
      }
    },
    // 加载暂存设置
    loadPendingSettings() {
      try {
        const savedSettings = common_vendor.index.getStorageSync(`pendingSettings_${this.sharingId}`);
        if (savedSettings && typeof savedSettings === "object") {
          this.pendingSettings = savedSettings;
          common_vendor.index.__f__("log", "at pages/sharing/manage.vue:923", "加载暂存设置:", this.pendingSettings);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:926", "加载暂存设置失败:", error);
        this.pendingSettings = {};
      }
    },
    // 应用暂存的设置
    async applyPendingSettings() {
      if (Object.keys(this.pendingSettings).length === 0) {
        return;
      }
      if (!this.isCreatorPaid) {
        return;
      }
      try {
        common_vendor.index.__f__("log", "at pages/sharing/manage.vue:942", "应用暂存设置:", this.pendingSettings);
        const newSettings = {
          autoApprove: this.pendingSettings.autoApprove !== void 0 ? this.pendingSettings.autoApprove : this.settings.autoApprove,
          allowExit: this.pendingSettings.allowExit !== void 0 ? this.pendingSettings.allowExit : this.settings.allowExit
        };
        await this.updateSharingSettings({
          sharingId: this.sharingId,
          settings: newSettings
        });
        this.settings = { ...newSettings };
        this.pendingSettings = {};
        common_vendor.index.removeStorageSync(`pendingSettings_${this.sharingId}`);
        common_vendor.index.showToast({
          title: "设置已自动应用",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:968", "应用暂存设置失败:", error);
      }
    },
    // 确认拼场
    async confirmSharing() {
      try {
        common_vendor.index.showLoading({ title: "确认中..." });
        await this.confirmSharingOrder(this.sharingId);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "确认成功",
          icon: "success"
        });
        setTimeout(() => {
          this.loadSharingDetail();
        }, 1500);
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:994", "拼场管理页面：确认拼场失败:", error);
        common_vendor.index.showToast({
          title: error.message || "确认失败",
          icon: "error"
        });
      }
    },
    // 显示取消确认弹窗
    showCancelConfirm() {
      this.$refs.cancelPopup.open();
    },
    // 确认取消拼场
    async confirmCancel() {
      try {
        common_vendor.index.showLoading({ title: "取消中..." });
        await this.cancelSharingOrder(this.sharingId);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "取消成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:1028", "拼场管理页面：取消拼场失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消失败",
          icon: "error"
        });
      }
    },
    // 获取进度百分比
    getProgressPercent(current, max) {
      if (!max || max === 0)
        return 0;
      return Math.round(current / max * 100);
    },
    // 格式化活动时间（参考预约订单的实现）
    formatActivityTime(sharing) {
      if (!sharing)
        return "--";
      const bookingDate = sharing.bookingDate || sharing.date;
      const startTime = sharing.startTime || sharing.bookingStartTime;
      const endTime = sharing.endTime || sharing.bookingEndTime;
      const timeSlotCount = sharing.timeSlotCount || 1;
      if (!bookingDate) {
        return "时间未知";
      }
      const date = this.formatDate(bookingDate);
      if (!startTime || !endTime) {
        if (timeSlotCount && timeSlotCount > 0) {
          return `${date} (${timeSlotCount}个时段)`;
        }
        return `${date} 时间待定`;
      }
      const formatTime = (timeStr) => {
        if (!timeStr)
          return "";
        if (timeStr.length > 5 && timeStr.includes(":")) {
          return timeStr.substring(0, 5);
        }
        return timeStr;
      };
      const formattedStart = formatTime(startTime);
      const formattedEnd = formatTime(endTime);
      if (timeSlotCount > 1) {
        return `${date} ${formattedStart}-${formattedEnd} (${timeSlotCount}个时段)`;
      }
      return `${date} ${formattedStart}-${formattedEnd}`;
    },
    // 格式化日期
    formatDate(date) {
      if (!date)
        return "--";
      try {
        const dateObj = new Date(date);
        if (isNaN(dateObj.getTime()))
          return "--";
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, "0");
        const day = String(dateObj.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:1095", "日期格式化错误:", error);
        return "--";
      }
    },
    // 格式化日期时间
    formatDateTime(datetime) {
      if (!datetime)
        return "--";
      try {
        let dateStr = datetime;
        if (typeof datetime === "string" && datetime.includes(" ") && datetime.includes("-")) {
          dateStr = datetime.replace(/-/g, "/");
        }
        const date = new Date(dateStr);
        if (isNaN(date.getTime()))
          return "--";
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hour = String(date.getHours()).padStart(2, "0");
        const minute = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hour}:${minute}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/manage.vue:1120", "时间格式化错误:", error);
        return "--";
      }
    },
    // 格式化时间段
    formatTimeSlot(startTime, endTime) {
      if (!startTime && !endTime) {
        return "时间未指定";
      }
      if (startTime && !endTime) {
        return startTime;
      }
      if (!startTime && endTime) {
        return endTime;
      }
      const formatTime = (timeStr) => {
        if (!timeStr)
          return "";
        if (timeStr.length > 5 && timeStr.includes(":")) {
          return timeStr.substring(0, 5);
        }
        return timeStr;
      };
      const formattedStart = formatTime(startTime);
      const formattedEnd = formatTime(endTime);
      return `${formattedStart}-${formattedEnd}`;
    },
    // 格式化价格显示
    formatPrice(price) {
      if (!price && price !== 0)
        return "0.00";
      const numPrice = Number(price);
      if (isNaN(numPrice))
        return "0.00";
      return numPrice.toFixed(2);
    },
    // 获取每队费用
    getPerTeamPrice() {
      if (!this.sharingDetail)
        return "0.00";
      if (this.sharingDetail.pricePerPerson) {
        return this.formatPrice(this.sharingDetail.pricePerPerson);
      }
      const totalPrice = this.sharingDetail.totalPrice || this.sharingDetail.price || 0;
      const maxParticipants = this.sharingDetail.maxParticipants || 2;
      const perTeamPrice = totalPrice / maxParticipants;
      return this.formatPrice(perTeamPrice);
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "OPEN": "招募中",
        "FULL": "已满员",
        "CONFIRMED": "已确认",
        "CANCELLED": "已取消",
        "EXPIRED": "已过期"
      };
      return statusMap[status] || "未知状态";
    },
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        "OPEN": "status-open",
        "FULL": "status-full",
        "CONFIRMED": "status-confirmed",
        "CANCELLED": "status-cancelled",
        "EXPIRED": "status-expired"
      };
      return classMap[status] || "status-unknown";
    },
    // 获取申请状态文本
    getRequestStatusText(status) {
      const statusMap = {
        "PENDING": "待处理",
        "APPROVED": "已同意",
        "REJECTED": "已拒绝"
      };
      return statusMap[status] || "未知状态";
    },
    // 获取申请状态样式类
    getRequestStatusClass(status) {
      const classMap = {
        "PENDING": "request-pending",
        "APPROVED": "request-approved",
        "REJECTED": "request-rejected"
      };
      return classMap[status] || "request-unknown";
    }
  }
};
if (!Array) {
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_popup_dialog2 + _easycom_uni_popup2)();
}
const _easycom_uni_popup_dialog = () => "../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_popup_dialog + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b;
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $options.loading
  }, $options.loading ? {} : $data.error ? {
    d: common_vendor.t($data.error),
    e: common_vendor.o((...args) => $options.loadSharingDetail && $options.loadSharingDetail(...args))
  } : $options.sharingDetail ? common_vendor.e({
    g: common_vendor.t($options.sharingDetail.venueName),
    h: common_vendor.t($options.getStatusText($options.sharingDetail.status)),
    i: common_vendor.n($options.getStatusClass($options.sharingDetail.status)),
    j: common_vendor.t($options.sharingDetail.teamName),
    k: common_vendor.t($options.sharingDetail.currentParticipants),
    l: common_vendor.t($options.sharingDetail.maxParticipants),
    m: common_vendor.t($options.getProgressPercent($options.sharingDetail.currentParticipants, $options.sharingDetail.maxParticipants)),
    n: $options.getProgressPercent($options.sharingDetail.currentParticipants, $options.sharingDetail.maxParticipants) + "%",
    o: common_vendor.t($options.formatActivityTime($options.sharingDetail)),
    p: common_vendor.t($options.getPerTeamPrice()),
    q: common_vendor.t($options.sharingDetail.orderNo),
    r: common_vendor.t($options.formatDateTime($options.sharingDetail.createdAt)),
    s: $options.sharingDetail.description
  }, $options.sharingDetail.description ? {
    t: common_vendor.t($options.sharingDetail.description)
  } : {}, {
    v: common_vendor.t($data.participants.length),
    w: common_vendor.f($data.participants, (participant, k0, i0) => {
      return common_vendor.e({
        a: participant.avatar || "/static/images/default-avatar.png",
        b: common_vendor.t(participant.nickname || participant.username),
        c: common_vendor.t(participant.isCreator ? "队长" : "队员"),
        d: !participant.isCreator && $options.canManage
      }, !participant.isCreator && $options.canManage ? {
        e: common_vendor.o(($event) => $options.showRemoveConfirm(participant), participant.id)
      } : {}, {
        f: participant.id
      });
    }),
    x: $data.participants.length === 0
  }, $data.participants.length === 0 ? {} : {}, {
    y: common_vendor.t($options.canManage ? "开启后，其他用户申请加入时将自动通过" : "请先完成支付后才能设置自动通过"),
    z: $data.settings.autoApprove,
    A: common_vendor.o((...args) => $options.onAutoApproveChange && $options.onAutoApproveChange(...args)),
    B: !$options.canManage,
    C: $data.settings.allowExit,
    D: common_vendor.o((...args) => $options.onAllowExitChange && $options.onAllowExitChange(...args)),
    E: !$options.canManage,
    F: $data.requests.length > 0
  }, $data.requests.length > 0 ? {
    G: common_vendor.t($options.pendingRequests.length),
    H: common_vendor.f($data.requests, (request, k0, i0) => {
      return common_vendor.e({
        a: request.userAvatar || "/static/images/default-avatar.png",
        b: common_vendor.t(request.userNickname || request.username),
        c: common_vendor.t($options.formatDateTime(request.createdAt)),
        d: request.status === "PENDING"
      }, request.status === "PENDING" ? {
        e: common_vendor.o(($event) => $options.handleRequest(request.id, "REJECTED"), request.id),
        f: common_vendor.o(($event) => $options.handleRequest(request.id, "APPROVED"), request.id)
      } : {
        g: common_vendor.t($options.getRequestStatusText(request.status)),
        h: common_vendor.n($options.getRequestStatusClass(request.status))
      }, {
        i: request.id
      });
    })
  } : {}) : {}, {
    c: $data.error,
    f: $options.sharingDetail,
    I: $options.sharingDetail && $options.canManage
  }, $options.sharingDetail && $options.canManage ? common_vendor.e({
    J: $options.canConfirm
  }, $options.canConfirm ? {
    K: common_vendor.o((...args) => $options.handleConfirmSharing && $options.handleConfirmSharing(...args))
  } : {}, {
    L: $options.canCancel
  }, $options.canCancel ? {
    M: common_vendor.o((...args) => $options.handleCancelSharing && $options.handleCancelSharing(...args))
  } : {}) : {}, {
    N: common_vendor.o($options.confirmRemove),
    O: common_vendor.o($options.cancelRemove),
    P: common_vendor.p({
      type: "warn",
      title: "移除参与者",
      content: `确定要移除 ${((_a = $data.removeTarget) == null ? void 0 : _a.nickname) || ((_b = $data.removeTarget) == null ? void 0 : _b.username)} 吗？`
    }),
    Q: common_vendor.sr("removePopup", "d012c4ce-0"),
    R: common_vendor.p({
      type: "dialog"
    }),
    S: common_vendor.o($options.confirmCancel),
    T: common_vendor.o(() => {
    }),
    U: common_vendor.p({
      type: "warn",
      title: "取消拼场",
      content: "确定要取消这个拼场吗？取消后将无法恢复。"
    }),
    V: common_vendor.sr("cancelPopup", "d012c4ce-2"),
    W: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d012c4ce"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/sharing/manage.js.map
