<view class="container data-v-95722986"><view class="navbar data-v-95722986"><view class="nav-left data-v-95722986" bindtap="{{a}}"><text class="nav-icon data-v-95722986">‹</text></view><text class="nav-title data-v-95722986">收到的申请</text><view class="nav-right data-v-95722986"></view></view><view class="filter-tabs data-v-95722986"><view wx:for="{{b}}" wx:for-item="tab" wx:key="d" class="{{['filter-tab', 'data-v-95722986', tab.e && 'active']}}" bindtap="{{tab.f}}"><text class="tab-text data-v-95722986">{{tab.a}}</text><text wx:if="{{tab.b}}" class="tab-count data-v-95722986">{{tab.c}}</text></view></view><view wx:if="{{c}}" class="loading-state data-v-95722986"><text class="data-v-95722986">加载中...</text></view><view wx:elif="{{d}}" class="error-state data-v-95722986"><text class="error-icon data-v-95722986">⚠️</text><text class="error-text data-v-95722986">{{e}}</text><button class="retry-btn data-v-95722986" bindtap="{{f}}"> 重新加载 </button></view><view wx:else class="content data-v-95722986"><view wx:if="{{g}}" class="requests-list data-v-95722986"><view wx:for="{{h}}" wx:for-item="request" wx:key="B" class="request-item data-v-95722986"><view class="sharing-info data-v-95722986"><view class="sharing-header data-v-95722986"><text class="venue-name data-v-95722986">{{request.a}}</text><view class="{{['status-badge', 'data-v-95722986', request.c]}}"><text class="status-text data-v-95722986">{{request.b}}</text></view></view><view class="sharing-details data-v-95722986"><text class="team-name data-v-95722986">{{request.d}}</text><text class="activity-time data-v-95722986">{{request.e}}</text><text class="price data-v-95722986">人均 ¥{{request.f}}</text></view><view class="participants-progress data-v-95722986"><view class="progress-info data-v-95722986"><text class="progress-text data-v-95722986">{{request.g}}/{{request.h}}人 </text><text class="progress-percent data-v-95722986">{{request.i}}% </text></view><view class="progress-bar data-v-95722986"><view class="progress-fill data-v-95722986" style="{{'width:' + request.j}}"></view></view></view></view><view class="applicant-info data-v-95722986"><view class="applicant-header data-v-95722986"><view class="applicant-avatar data-v-95722986"><image wx:if="{{request.k}}" src="{{request.l}}" class="avatar-img data-v-95722986"/><text wx:else class="avatar-placeholder data-v-95722986">{{request.m}}</text></view><view class="applicant-details data-v-95722986"><text class="applicant-name data-v-95722986">{{request.n}}</text><text class="apply-time data-v-95722986">申请时间：{{request.o}}</text></view></view><view wx:if="{{request.p}}" class="apply-message data-v-95722986"><text class="message-label data-v-95722986">申请留言：</text><text class="message-content data-v-95722986">{{request.q}}</text></view></view><view class="request-actions data-v-95722986"><view wx:if="{{request.r}}" class="pending-actions data-v-95722986"><button class="action-btn reject-btn data-v-95722986" bindtap="{{request.s}}"> 拒绝 </button><button class="action-btn approve-btn data-v-95722986" bindtap="{{request.t}}"> 同意 </button></view><view wx:elif="{{request.v}}" class="processed-actions data-v-95722986"><text class="processed-text data-v-95722986">已同意</text><text class="process-time data-v-95722986">{{request.w}}</text></view><view wx:elif="{{request.x}}" class="processed-actions data-v-95722986"><text class="processed-text rejected data-v-95722986">已拒绝</text><text class="process-time data-v-95722986">{{request.y}}</text><text wx:if="{{request.z}}" class="reject-reason data-v-95722986"> 拒绝原因：{{request.A}}</text></view></view></view></view><view wx:else class="empty-state data-v-95722986"><text class="empty-icon data-v-95722986">📬</text><text class="empty-title data-v-95722986">{{i}}</text><text class="empty-desc data-v-95722986">{{j}}</text><button class="create-btn data-v-95722986" bindtap="{{k}}"> 创建拼场 </button></view></view><uni-popup wx:if="{{p}}" class="r data-v-95722986" u-s="{{['d']}}" u-r="approvePopup" u-i="95722986-0" bind:__l="__l" u-p="{{p}}"><uni-popup-dialog wx:if="{{n}}" class="data-v-95722986" bindconfirm="{{l}}" bindclose="{{m}}" u-i="95722986-1,95722986-0" bind:__l="__l" u-p="{{n}}"></uni-popup-dialog></uni-popup><uni-popup wx:if="{{z}}" class="r data-v-95722986" u-s="{{['d']}}" u-r="rejectPopup" u-i="95722986-2" bind:__l="__l" u-p="{{z}}"><view class="reject-dialog data-v-95722986"><view class="dialog-header data-v-95722986"><text class="dialog-title data-v-95722986">拒绝申请</text><text class="close-btn data-v-95722986" bindtap="{{q}}">✕</text></view><view class="dialog-content data-v-95722986"><view class="applicant-info data-v-95722986"><text class="applicant-name data-v-95722986">申请人：{{r}}</text></view><view class="reason-section data-v-95722986"><text class="reason-label data-v-95722986">拒绝原因（可选）</text><block wx:if="{{r0}}"><textarea class="reason-input data-v-95722986" placeholder="请输入拒绝原因..." maxlength="100" value="{{s}}" bindinput="{{t}}"></textarea></block><text class="char-count data-v-95722986">{{v}}/100</text></view></view><view class="dialog-actions data-v-95722986"><button class="cancel-btn data-v-95722986" bindtap="{{w}}"> 取消 </button><button class="confirm-btn data-v-95722986" bindtap="{{x}}"> 确定拒绝 </button></view></view></uni-popup></view>