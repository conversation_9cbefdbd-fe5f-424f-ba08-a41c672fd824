/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-f400ab5e {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 通知区域样式 */
.notification-area.data-v-f400ab5e {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  padding: 20rpx;
}
.notification-item.data-v-f400ab5e {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  animation: slideDown-f400ab5e 0.3s ease-out;
}
.notification-item.success.data-v-f400ab5e {
  background-color: #f0f9ff;
  border-left: 8rpx solid #10b981;
}
.notification-item.info.data-v-f400ab5e {
  background-color: #eff6ff;
  border-left: 8rpx solid #3b82f6;
}
.notification-item.warning.data-v-f400ab5e {
  background-color: #fffbeb;
  border-left: 8rpx solid #f59e0b;
}
.notification-item.error.data-v-f400ab5e {
  background-color: #fef2f2;
  border-left: 8rpx solid #ef4444;
}
.notification-icon.data-v-f400ab5e {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.notification-text.data-v-f400ab5e {
  flex: 1;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.4;
}
.notification-close.data-v-f400ab5e {
  font-size: 36rpx;
  color: #9ca3af;
  margin-left: 16rpx;
  cursor: pointer;
}
@keyframes slideDown-f400ab5e {
from {
    transform: translateY(-100%);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
.navbar.data-v-f400ab5e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eee;
}
.navbar .nav-left.data-v-f400ab5e {
  width: 80rpx;
}
.navbar .nav-left .nav-icon.data-v-f400ab5e {
  font-size: 48rpx;
  color: #333;
}
.navbar .nav-title.data-v-f400ab5e {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.navbar .nav-right.data-v-f400ab5e {
  width: 80rpx;
}
.tabs-container.data-v-f400ab5e {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eee;
}
.tabs-container .tab-item.data-v-f400ab5e {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 16rpx;
  position: relative;
}
.tabs-container .tab-item .tab-text.data-v-f400ab5e {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}
.tabs-container .tab-item .tab-count.data-v-f400ab5e {
  background-color: #ff6b35;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}
.tabs-container .tab-item.active .tab-text.data-v-f400ab5e {
  color: #ff6b35;
  font-weight: 600;
}
.tabs-container .tab-item.active.data-v-f400ab5e::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff6b35;
  border-radius: 2rpx;
}
.loading-state.data-v-f400ab5e {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.loading-state text.data-v-f400ab5e {
  font-size: 28rpx;
  color: #999;
}
.error-state.data-v-f400ab5e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.error-state .error-icon.data-v-f400ab5e {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}
.error-state .error-text.data-v-f400ab5e {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 32rpx;
}
.error-state .retry-btn.data-v-f400ab5e {
  padding: 16rpx 32rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.content.data-v-f400ab5e {
  padding: 24rpx;
}
.orders-list .order-item.data-v-f400ab5e {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
.orders-list .order-item.success-item.data-v-f400ab5e {
  border: 2rpx solid #4caf50;
}
.orders-list .order-item .order-header.data-v-f400ab5e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.orders-list .order-item .order-header .venue-name.data-v-f400ab5e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.orders-list .order-item .order-header .status-badge.data-v-f400ab5e {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.orders-list .order-item .order-header .status-badge .status-text.data-v-f400ab5e {
  font-size: 24rpx;
  font-weight: 500;
}
.orders-list .order-item .order-header .status-badge.open.data-v-f400ab5e {
  background-color: #e3f2fd;
  color: #1976d2;
}
.orders-list .order-item .order-header .status-badge.full.data-v-f400ab5e {
  background-color: #fff3e0;
  color: #f57c00;
}
.orders-list .order-item .order-header .status-badge.confirmed.data-v-f400ab5e {
  background-color: #e8f5e8;
  color: #4caf50;
}
.orders-list .order-item .order-header .status-badge.success.data-v-f400ab5e {
  background-color: #e8f5e8;
  color: #4caf50;
}
.orders-list .order-item .order-header .status-badge.cancelled.data-v-f400ab5e {
  background-color: #ffebee;
  color: #f44336;
}
.orders-list .order-item .order-header .status-badge.pending.data-v-f400ab5e {
  background-color: #fff3e0;
  color: #f57c00;
}
.orders-list .order-item .order-header .status-badge.approved.data-v-f400ab5e {
  background-color: #e8f5e8;
  color: #4caf50;
}
.orders-list .order-item .order-header .status-badge.rejected.data-v-f400ab5e {
  background-color: #ffebee;
  color: #f44336;
}
.orders-list .order-item .order-info.data-v-f400ab5e {
  margin-bottom: 24rpx;
}
.orders-list .order-item .order-info .info-row.data-v-f400ab5e {
  display: flex;
  margin-bottom: 8rpx;
  align-items: center;
}
.orders-list .order-item .order-info .time-icon.data-v-f400ab5e,
.orders-list .order-item .order-info .location-icon.data-v-f400ab5e,
.orders-list .order-item .order-info .team-icon.data-v-f400ab5e,
.orders-list .order-item .order-info .participants-icon.data-v-f400ab5e,
.orders-list .order-item .order-info .price-icon.data-v-f400ab5e,
.orders-list .order-item .order-info .create-icon.data-v-f400ab5e,
.orders-list .order-item .order-info .message-icon.data-v-f400ab5e {
  font-size: 28rpx;
  margin-right: 12rpx;
  width: 32rpx;
  text-align: center;
  flex-shrink: 0;
}
.orders-list .order-item .order-info .info-value.data-v-f400ab5e {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}
.orders-list .order-item .order-actions.data-v-f400ab5e {
  display: flex;
  gap: 16rpx;
}
.orders-list .order-item .order-actions .action-btn.data-v-f400ab5e {
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}
.orders-list .order-item .order-actions .action-btn.manage-btn.data-v-f400ab5e {
  background-color: #ff6b35;
  color: #ffffff;
}
.orders-list .order-item .order-actions .action-btn.cancel-btn.data-v-f400ab5e {
  background-color: #f5f5f5;
  color: #666;
}
.orders-list .order-item .order-actions .action-btn.contact-btn.data-v-f400ab5e {
  background-color: #4caf50;
  color: #ffffff;
}
.empty-state.data-v-f400ab5e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.empty-state .empty-icon.data-v-f400ab5e {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}
.empty-state .empty-text.data-v-f400ab5e {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 32rpx;
}
.empty-state .create-btn.data-v-f400ab5e,
.empty-state .browse-btn.data-v-f400ab5e {
  padding: 20rpx 40rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}