"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_sharing = require("../../stores/sharing.js");
const stores_user = require("../../stores/user.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "SharingRequests",
  data() {
    return {
      sharingStore: null,
      userStore: null,
      currentFilter: "all",
      error: "",
      cancelTarget: null,
      requests: [],
      filterTabs: [
        { label: "全部", value: "all", count: 0 },
        { label: "待处理", value: "pending", count: 0 },
        { label: "待支付", value: "approved_pending_payment", count: 0 },
        { label: "已完成", value: "approved", count: 0 },
        { label: "已拒绝", value: "rejected", count: 0 },
        { label: "已超时", value: "timeout_cancelled", count: 0 }
      ]
    };
  },
  computed: {
    loading() {
      var _a;
      return ((_a = this.sharingStore) == null ? void 0 : _a.isLoading) || false;
    },
    userInfo() {
      var _a;
      return ((_a = this.userStore) == null ? void 0 : _a.getUserInfo) || {};
    },
    // 过滤后的申请列表
    filteredRequests() {
      if (this.currentFilter === "all") {
        return this.requests;
      }
      const statusMap = {
        "pending": "PENDING",
        "approved_pending_payment": "APPROVED_PENDING_PAYMENT",
        "approved": "APPROVED",
        "rejected": "REJECTED",
        "timeout_cancelled": "TIMEOUT_CANCELLED"
      };
      return this.requests.filter(
        (request) => request.status === statusMap[this.currentFilter]
      );
    }
  },
  onLoad() {
    this.sharingStore = stores_sharing.useSharingStore();
    this.userStore = stores_user.useUserStore();
    this.loadRequests();
  },
  onShow() {
    this.loadRequests();
  },
  onPullDownRefresh() {
    this.loadRequests().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载申请列表
    async loadRequests() {
      try {
        this.error = "";
        common_vendor.index.__f__("log", "at pages/sharing/requests.vue:234", "拼场申请页面：开始加载申请列表");
        const requests = await this.sharingStore.getMySharingRequests();
        this.requests = requests || [];
        this.updateFilterCounts();
        common_vendor.index.__f__("log", "at pages/sharing/requests.vue:243", "拼场申请页面：加载申请列表成功:", this.requests);
        if (this.requests.length > 0) {
          common_vendor.index.__f__("log", "at pages/sharing/requests.vue:247", "第一个请求的完整数据结构:", this.requests[0]);
          common_vendor.index.__f__("log", "at pages/sharing/requests.vue:248", "第一个请求的sharingId:", this.requests[0].sharingId);
          common_vendor.index.__f__("log", "at pages/sharing/requests.vue:249", "第一个请求的所有字段:", Object.keys(this.requests[0]));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/requests.vue:253", "拼场申请页面：加载申请列表失败:", error);
        this.error = error.message || "加载失败，请重试";
        this.requests = [];
      }
    },
    // 更新筛选标签计数
    updateFilterCounts() {
      const counts = {
        all: this.requests.length,
        pending: this.requests.filter((r) => r.status === "PENDING").length,
        approved: this.requests.filter((r) => r.status === "APPROVED").length,
        rejected: this.requests.filter((r) => r.status === "REJECTED").length
      };
      this.filterTabs.forEach((tab) => {
        tab.count = counts[tab.value] || 0;
      });
    },
    // 切换筛选
    switchFilter(filter) {
      this.currentFilter = filter;
    },
    // 显示取消确认弹窗
    showCancelConfirm(request) {
      this.cancelTarget = request;
      this.$refs.cancelPopup.open();
    },
    // 确认取消申请
    async confirmCancel() {
      if (!this.cancelTarget)
        return;
      try {
        common_vendor.index.showLoading({ title: "取消中..." });
        await this.sharingStore.cancelSharingRequest(this.cancelTarget.id);
        const index = this.requests.findIndex((r) => r.id === this.cancelTarget.id);
        if (index > -1) {
          this.requests.splice(index, 1);
        }
        this.updateFilterCounts();
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "取消成功",
          icon: "success"
        });
        this.cancelTarget = null;
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/sharing/requests.vue:315", "拼场申请页面：取消申请失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消失败",
          icon: "error"
        });
      }
    },
    // 跳转到拼场详情
    goToSharingDetail(sharingId) {
      common_vendor.index.__f__("log", "at pages/sharing/requests.vue:325", "跳转到拼场详情，sharingId:", sharingId);
      if (!sharingId) {
        common_vendor.index.__f__("error", "at pages/sharing/requests.vue:328", "sharingId为空，无法跳转");
        common_vendor.index.showToast({
          title: "订单ID不存在",
          icon: "error"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/sharing/detail?id=${sharingId}`
      });
    },
    // 跳转到拼场列表
    goToSharingList() {
      common_vendor.index.navigateTo({
        url: "/pages/sharing/list"
      });
    },
    // 获取进度百分比
    getProgressPercent(current, max) {
      if (!max || max === 0)
        return 0;
      return Math.round(current / max * 100);
    },
    // 格式化活动时间
    formatActivityTime(request) {
      if (!request)
        return "--";
      if (request.bookingTime) {
        try {
          let bookingTimeStr = request.bookingTime;
          if (typeof bookingTimeStr === "string" && bookingTimeStr.includes(" ") && !bookingTimeStr.includes("T")) {
            bookingTimeStr = bookingTimeStr.replace(" ", "T");
          }
          const bookingTime = new Date(bookingTimeStr);
          common_vendor.index.__f__("log", "at pages/sharing/requests.vue:368", "拼场申请时间转换 - bookingTime:", request.bookingTime, "→", bookingTimeStr, "→", bookingTime);
          common_vendor.index.__f__("log", "at pages/sharing/requests.vue:369", "拼场申请时间字段 - startTime:", request.startTime, "endTime:", request.endTime);
          const dateStr = bookingTime.toLocaleDateString("zh-CN", {
            month: "2-digit",
            day: "2-digit"
          });
          const startTimeStr = bookingTime.toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false
          });
          let timeSlot2 = startTimeStr;
          if (request.startTime && request.endTime) {
            timeSlot2 = `${request.startTime}-${request.endTime}`;
          } else if (request.endTime) {
            timeSlot2 = `${startTimeStr}-${request.endTime}`;
          }
          return `${dateStr} ${timeSlot2}`;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/sharing/requests.vue:392", "时间格式化错误:", error);
          return "--";
        }
      }
      const date = this.formatDate(request.bookingDate);
      const timeSlot = this.formatTimeSlot(request.startTime, request.endTime);
      return `${date} ${timeSlot}`;
    },
    // 格式化日期
    formatDate(date) {
      if (!date)
        return "--";
      return utils_helpers.formatDate(date, "MM-DD");
    },
    // 格式化日期时间
    formatDateTime(datetime) {
      if (!datetime)
        return "--";
      return utils_helpers.formatDateTime(datetime, "MM-DD HH:mm");
    },
    // 格式化时间段
    formatTimeSlot(startTime, endTime) {
      if (!startTime && !endTime) {
        return "时间未指定";
      }
      if (startTime && !endTime) {
        return startTime;
      }
      if (!startTime && endTime) {
        return endTime;
      }
      return `${startTime}-${endTime}`;
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "PENDING": "待处理",
        "APPROVED_PENDING_PAYMENT": "已批准待支付",
        "APPROVED": "已完成",
        "PAID": "拼场成功",
        "REJECTED": "已拒绝",
        "TIMEOUT_CANCELLED": "超时取消"
      };
      return statusMap[status] || "未知状态";
    },
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        "PENDING": "status-pending",
        "APPROVED_PENDING_PAYMENT": "status-pending",
        "APPROVED": "status-approved",
        "PAID": "status-success",
        "REJECTED": "status-rejected",
        "TIMEOUT_CANCELLED": "status-cancelled"
      };
      return classMap[status] || "status-unknown";
    },
    // 获取空状态标题
    getEmptyTitle() {
      const titleMap = {
        "all": "暂无申请记录",
        "pending": "暂无待处理申请",
        "approved": "暂无已通过申请",
        "rejected": "暂无被拒绝申请"
      };
      return titleMap[this.currentFilter] || "暂无申请记录";
    },
    // 获取空状态描述
    getEmptyDesc() {
      const descMap = {
        "all": "快去申请加入感兴趣的拼场吧",
        "pending": "您的申请都已被处理",
        "approved": "暂时没有通过的申请",
        "rejected": "暂时没有被拒绝的申请"
      };
      return descMap[this.currentFilter] || "快去申请加入感兴趣的拼场吧";
    },
    // 获取申请价格
    getRequestPrice(request) {
      if (!request)
        return "0.00";
      const price = request.paymentAmount || request.pricePerPerson || request.totalPrice || 0;
      return typeof price === "number" ? price.toFixed(2) : "0.00";
    }
  }
};
if (!Array) {
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_popup_dialog2 + _easycom_uni_popup2)();
}
const _easycom_uni_popup_dialog = () => "../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_popup_dialog + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.f($data.filterTabs, (tab, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.label),
        b: tab.count > 0
      }, tab.count > 0 ? {
        c: common_vendor.t(tab.count)
      } : {}, {
        d: tab.value,
        e: $data.currentFilter === tab.value ? 1 : "",
        f: common_vendor.o(($event) => $options.switchFilter(tab.value), tab.value)
      });
    }),
    c: $options.loading
  }, $options.loading ? {} : $data.error ? {
    e: common_vendor.t($data.error),
    f: common_vendor.o((...args) => $options.loadRequests && $options.loadRequests(...args))
  } : common_vendor.e({
    g: $options.filteredRequests.length > 0
  }, $options.filteredRequests.length > 0 ? {
    h: common_vendor.f($options.filteredRequests, (request, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(request.venueName),
        b: common_vendor.t($options.getStatusText(request.status)),
        c: common_vendor.n($options.getStatusClass(request.status)),
        d: common_vendor.t(request.teamName || request.applicantTeamName),
        e: common_vendor.t($options.formatActivityTime(request)),
        f: common_vendor.t($options.getRequestPrice(request)),
        g: common_vendor.t(request.currentParticipants),
        h: common_vendor.t(request.maxParticipants),
        i: common_vendor.t($options.getProgressPercent(request.currentParticipants, request.maxParticipants)),
        j: $options.getProgressPercent(request.currentParticipants, request.maxParticipants) + "%",
        k: common_vendor.t($options.formatDateTime(request.createdAt)),
        l: request.processedAt
      }, request.processedAt ? {
        m: common_vendor.t($options.formatDateTime(request.processedAt))
      } : {}, {
        n: request.status === "PENDING"
      }, request.status === "PENDING" ? {
        o: common_vendor.o(($event) => $options.showCancelConfirm(request), request.id)
      } : request.status === "APPROVED" ? {
        q: common_vendor.o(($event) => $options.goToSharingDetail(request.orderId || request.sharingId), request.id)
      } : request.status === "REJECTED" ? common_vendor.e({
        s: request.rejectReason
      }, request.rejectReason ? {
        t: common_vendor.t(request.rejectReason)
      } : {}) : {}, {
        p: request.status === "APPROVED",
        r: request.status === "REJECTED",
        v: request.id,
        w: common_vendor.o(($event) => $options.goToSharingDetail(request.orderId || request.sharingId), request.id)
      });
    })
  } : {
    i: common_vendor.t($options.getEmptyTitle()),
    j: common_vendor.t($options.getEmptyDesc()),
    k: common_vendor.o((...args) => $options.goToSharingList && $options.goToSharingList(...args))
  }), {
    d: $data.error,
    l: common_vendor.o($options.confirmCancel),
    m: common_vendor.o(() => {
      $data.cancelTarget = null;
    }),
    n: common_vendor.p({
      type: "warn",
      title: "取消申请",
      content: `确定要取消对 ${(_a = $data.cancelTarget) == null ? void 0 : _a.teamName} 的申请吗？`
    }),
    o: common_vendor.sr("cancelPopup", "f108858d-0"),
    p: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f108858d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/sharing/requests.js.map
