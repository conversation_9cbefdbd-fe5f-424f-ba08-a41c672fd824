<view class="container data-v-1cf27b2a"><view class="dev-toolbar data-v-1cf27b2a"><text class="dev-title data-v-1cf27b2a">🔧 开发工具</text><view class="dev-actions data-v-1cf27b2a"><button class="dev-btn data-v-1cf27b2a" bindtap="{{a}}"> 🧪 简单测试 </button><button class="dev-btn data-v-1cf27b2a" bindtap="{{b}}"> 🔄 Pinia迁移测试 </button><button class="dev-btn data-v-1cf27b2a" bindtap="{{c}}"> 🔐 认证测试 </button><button class="dev-btn data-v-1cf27b2a" bindtap="{{d}}"> 📦 Store测试 </button></view></view><skeleton-screen wx:if="{{e}}" class="data-v-1cf27b2a" u-i="1cf27b2a-0" bind:__l="__l" u-p="{{f}}"/><view wx:else class="data-v-1cf27b2a"><view class="banner-section data-v-1cf27b2a"><swiper class="banner-swiper data-v-1cf27b2a" indicator-dots circular autoplay><swiper-item wx:for="{{g}}" wx:for-item="banner" wx:key="d" class="data-v-1cf27b2a"><image src="{{banner.a}}" class="banner-image data-v-1cf27b2a" mode="aspectFill" lazy-load bindload="{{banner.b}}" binderror="{{banner.c}}"/></swiper-item></swiper></view><view class="quick-actions data-v-1cf27b2a"><view class="action-item data-v-1cf27b2a" bindtap="{{h}}"><view class="action-icon data-v-1cf27b2a"><text class="iconfont icon-venue data-v-1cf27b2a">🏟️</text></view><text class="action-text data-v-1cf27b2a">场馆预约</text></view><view class="action-item data-v-1cf27b2a" bindtap="{{i}}"><view class="action-icon data-v-1cf27b2a"><text class="iconfont icon-sharing data-v-1cf27b2a">👥</text></view><text class="action-text data-v-1cf27b2a">拼场活动</text></view><view class="action-item data-v-1cf27b2a" bindtap="{{j}}"><view class="action-icon data-v-1cf27b2a"><text class="iconfont icon-booking data-v-1cf27b2a">📅</text></view><text class="action-text data-v-1cf27b2a">我的预约</text></view><view class="action-item data-v-1cf27b2a" bindtap="{{k}}"><view class="action-icon data-v-1cf27b2a"><text class="iconfont icon-test data-v-1cf27b2a">🔧</text></view><text class="action-text data-v-1cf27b2a">Store测试</text></view><view class="action-item data-v-1cf27b2a" bindtap="{{l}}"><view class="action-icon data-v-1cf27b2a"><text class="iconfont icon-test data-v-1cf27b2a">🧪</text></view><text class="action-text data-v-1cf27b2a">API测试</text></view><view class="action-item data-v-1cf27b2a" bindtap="{{m}}"><view class="action-icon data-v-1cf27b2a"><text class="iconfont icon-user data-v-1cf27b2a">👤</text></view><text class="action-text data-v-1cf27b2a">个人中心</text></view><view class="action-item pinia-test-item data-v-1cf27b2a" bindtap="{{n}}"><view class="action-icon pinia-test-icon data-v-1cf27b2a"><text class="iconfont icon-test data-v-1cf27b2a">🔄</text></view><text class="action-text data-v-1cf27b2a">Pinia测试</text></view></view><view class="test-button data-v-1cf27b2a" bindtap="{{o}}"><text class="data-v-1cf27b2a">认证测试</text></view><view class="section data-v-1cf27b2a"><view class="section-header data-v-1cf27b2a"><text class="section-title data-v-1cf27b2a">热门场馆</text><text class="section-more data-v-1cf27b2a" bindtap="{{p}}">更多 ></text></view><view class="venue-list data-v-1cf27b2a"><view wx:for="{{q}}" wx:for-item="venue" wx:key="i" class="venue-card data-v-1cf27b2a" bindtap="{{venue.j}}"><image src="{{venue.a}}" class="venue-image data-v-1cf27b2a" mode="aspectFill" lazy-load bindload="{{venue.b}}" binderror="{{venue.c}}"/><view class="venue-info data-v-1cf27b2a"><text class="venue-name data-v-1cf27b2a">{{venue.d}}</text><text class="venue-location data-v-1cf27b2a">{{venue.e}}</text><view class="venue-price data-v-1cf27b2a"><text class="price-text data-v-1cf27b2a">¥{{venue.f}}/小时</text><text class="{{['venue-status', 'data-v-1cf27b2a', venue.h]}}">{{venue.g}}</text></view></view></view><view wx:if="{{r}}" class="no-data data-v-1cf27b2a"><text class="no-data-text data-v-1cf27b2a">暂无热门场馆数据</text></view></view></view><view class="section data-v-1cf27b2a"><view class="section-header data-v-1cf27b2a"><text class="section-title data-v-1cf27b2a">最新拼场</text><text class="section-more data-v-1cf27b2a" bindtap="{{s}}">更多 ></text></view><view class="sharing-list data-v-1cf27b2a"><view wx:for="{{t}}" wx:for-item="sharing" wx:key="i" class="sharing-card data-v-1cf27b2a" bindtap="{{sharing.j}}"><view class="sharing-header data-v-1cf27b2a"><text class="sharing-venue data-v-1cf27b2a">{{sharing.a}}</text><text class="sharing-time data-v-1cf27b2a">{{sharing.b}} {{sharing.c}}-{{sharing.d}}</text></view><view class="sharing-info data-v-1cf27b2a"><text class="sharing-team data-v-1cf27b2a">{{sharing.e}}</text><text class="sharing-participants data-v-1cf27b2a">{{sharing.f}}/{{sharing.g}}人</text></view><view class="sharing-price data-v-1cf27b2a"><text class="price-per-person data-v-1cf27b2a">¥{{sharing.h}}/人</text></view></view></view></view></view><view class="floating-test-btn data-v-1cf27b2a" bindtap="{{v}}"><text class="floating-btn-text data-v-1cf27b2a">🧪</text></view></view>