"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_booking = require("../../stores/booking.js");
const stores_user = require("../../stores/user.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_countdown = require("../../utils/countdown.js");
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (CountdownTimer + _easycom_uni_popup)();
}
const CountdownTimer = () => "../../components/CountdownTimer.js";
const _sfc_main = {
  __name: "list",
  setup(__props) {
    const bookingStore = stores_booking.useBookingStore();
    stores_user.useUserStore();
    const selectedStatus = common_vendor.ref("all");
    const statusOptions = common_vendor.ref([
      { label: "全部", value: "all" },
      { label: "待支付", value: "PENDING" },
      { label: "已支付", value: "PAID" },
      { label: "已确认", value: "CONFIRMED" },
      { label: "已核销", value: "VERIFIED" },
      { label: "已完成", value: "COMPLETED" },
      { label: "已取消", value: "CANCELLED" },
      { label: "已过期", value: "EXPIRED" },
      // 拼场相关状态
      { label: "开放中", value: "OPEN" },
      { label: "等待对方支付", value: "APPROVED_PENDING_PAYMENT" },
      { label: "拼场成功", value: "SHARING_SUCCESS" }
    ]);
    const currentBookingId = common_vendor.ref(null);
    const showCancelModal = common_vendor.ref(false);
    const bookingList = common_vendor.computed(() => bookingStore.getBookingList);
    const loading = common_vendor.computed(() => bookingStore.isLoading);
    const pagination = common_vendor.computed(() => {
      return bookingStore.getPagination;
    });
    const hasMore = common_vendor.computed(() => {
      return pagination.value.current < pagination.value.totalPages;
    });
    const filteredBookings = common_vendor.computed(() => {
      const bookings = bookingList.value || [];
      common_vendor.index.__f__("log", "at pages/booking/list.vue:221", "[BookingList] filteredBookings计算中...");
      common_vendor.index.__f__("log", "at pages/booking/list.vue:222", "[BookingList] 原始数据:", bookings.length, "条");
      common_vendor.index.__f__("log", "at pages/booking/list.vue:223", "[BookingList] 选中状态:", selectedStatus.value);
      if (selectedStatus.value === "all") {
        common_vendor.index.__f__("log", "at pages/booking/list.vue:226", "[BookingList] 返回全部数据:", bookings.length, "条");
        return bookings;
      }
      const filtered = bookings.filter(
        (booking) => {
          common_vendor.index.__f__("log", "at pages/booking/list.vue:232", "[BookingList] 筛选订单:", booking.id, "状态:", booking.status, "匹配:", booking.status === selectedStatus.value);
          return booking.status === selectedStatus.value;
        }
      );
      common_vendor.index.__f__("log", "at pages/booking/list.vue:237", "[BookingList] 筛选后数据:", filtered.length, "条");
      return filtered;
    });
    const lastRequestStatus = common_vendor.ref("");
    const lastError = common_vendor.ref("");
    const cancelPopup = common_vendor.ref(null);
    const initData = async () => {
      try {
        lastRequestStatus.value = "请求中...";
        lastError.value = "无";
        await bookingStore.getUserBookings({ page: 1, pageSize: 10 });
        lastRequestStatus.value = "请求成功";
      } catch (error) {
        lastRequestStatus.value = "请求失败";
        lastError.value = error.message || "获取预约列表失败";
        common_vendor.index.__f__("error", "at pages/booking/list.vue:262", "初始化数据失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取预约列表失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const refreshData = async () => {
      try {
        lastRequestStatus.value = "刷新中...";
        lastError.value = "无";
        await bookingStore.getUserBookings({
          page: 1,
          pageSize: 10,
          refresh: true,
          timestamp: Date.now()
        });
        lastRequestStatus.value = "刷新成功";
        common_vendor.index.stopPullDownRefresh();
      } catch (error) {
        lastRequestStatus.value = "刷新失败";
        lastError.value = error.message || "刷新数据失败";
        common_vendor.index.stopPullDownRefresh();
        common_vendor.index.__f__("error", "at pages/booking/list.vue:289", "刷新数据失败:", error);
        common_vendor.index.showToast({
          title: error.message || "刷新数据失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const loadMore = async () => {
      if (loading.value || !hasMore.value)
        return;
      try {
        const nextPage = pagination.value.current + 1;
        await bookingStore.getUserBookings({ page: nextPage, pageSize: 10 });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/booking/list.vue:306", "加载更多失败:", error);
      }
    };
    const selectStatus = (status) => {
      selectedStatus.value = status;
    };
    const navigateToDetail = (bookingId) => {
      common_vendor.index.navigateTo({
        url: `/pages/booking/detail?id=${bookingId}`
      });
    };
    const closeCancelModal = () => {
      if (cancelPopup.value) {
        cancelPopup.value.close();
      }
      currentBookingId.value = null;
    };
    const confirmCancel = async () => {
      try {
        common_vendor.index.showLoading({ title: "取消中..." });
        await bookingStore.cancelBooking(currentBookingId.value);
        common_vendor.index.hideLoading();
        closeCancelModal();
        common_vendor.index.showToast({
          title: "取消成功",
          icon: "success"
        });
        await refreshData();
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/booking/list.vue:356", "取消预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消失败",
          icon: "error"
        });
      }
    };
    const reviewVenue = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/venue/review?venueId=${booking.venueId}&bookingId=${booking.id}`
      });
    };
    const rebookVenue = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/venue/detail?id=${booking.venueId}`
      });
    };
    const navigateToVenueList = () => {
      common_vendor.index.switchTab({
        url: "/pages/venue/list"
      });
    };
    const formatCreateTime = (datetime) => {
      return utils_helpers.formatTime(datetime, "YYYY-MM-DD HH:mm");
    };
    const getStatusClass = (status) => {
      const statusMap = {
        // 基础状态样式
        "PENDING": "status-pending",
        "PAID": "status-paid",
        "CONFIRMED": "status-confirmed",
        "VERIFIED": "status-verified",
        "COMPLETED": "status-completed",
        "CANCELLED": "status-cancelled",
        "EXPIRED": "status-expired",
        // 拼场状态样式
        "OPEN": "status-open",
        "APPROVED_PENDING_PAYMENT": "status-approved-pending-payment",
        "SHARING_SUCCESS": "status-sharing-success",
        "PENDING_FULL": "status-pending-full",
        "FULL": "status-full"
      };
      return statusMap[status] || "status-pending";
    };
    const getStatusText = (status) => {
      const statusMap = {
        // 基础状态（所有订单通用）
        "PENDING": "待支付",
        "PAID": "已支付",
        "CONFIRMED": "已确认",
        "VERIFIED": "已核销",
        "COMPLETED": "已完成",
        "CANCELLED": "已取消",
        "EXPIRED": "已过期",
        // 拼场订单特有状态
        "OPEN": "开放中(1/2)",
        "APPROVED_PENDING_PAYMENT": "等待对方支付",
        "SHARING_SUCCESS": "拼场成功(2人)",
        "PENDING_FULL": "待满员",
        "FULL": "已满员(2/2)"
      };
      return statusMap[status] || "待支付";
    };
    const formatTimeRange = (booking) => {
      const bookingId = typeof booking.id === "string" ? parseInt(booking.id) : booking.id;
      const isVirtual = bookingId < 0;
      if (isVirtual) {
        const startTime = booking.startTime;
        const endTime = booking.endTime;
        common_vendor.index.__f__("log", "at pages/booking/list.vue:451", "虚拟订单时间显示 - startTime:", startTime, "endTime:", endTime);
        if (!startTime)
          return "时间待定";
        try {
          const startTimeStr = startTime;
          const endTimeStr = endTime;
          if (endTimeStr) {
            return `${startTimeStr} - ${endTimeStr}`;
          } else {
            return startTimeStr;
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/booking/list.vue:466", "虚拟订单时间格式化错误:", error);
          return "时间待定";
        }
      } else {
        const startTime = booking.startTime || booking.bookingStartTime;
        const endTime = booking.endTime || booking.bookingEndTime;
        const timeSlotCount = booking.timeSlotCount || 1;
        if (!startTime || !endTime) {
          return "时间待定";
        }
        const formatTime = (timeStr) => {
          if (!timeStr)
            return "";
          if (timeStr.length > 5 && timeStr.includes(":")) {
            return timeStr.substring(0, 5);
          }
          return timeStr;
        };
        const formattedStart = formatTime(startTime);
        const formattedEnd = formatTime(endTime);
        if (timeSlotCount > 1) {
          return `${formattedStart} - ${formattedEnd} (${timeSlotCount}个时段)`;
        }
        return `${formattedStart} - ${formattedEnd}`;
      }
    };
    const getBookingTypeText = (bookingType) => {
      const typeMap = {
        "EXCLUSIVE": "包场",
        "SHARED": "拼场"
      };
      return bookingType ? typeMap[bookingType] || "普通" : "普通";
    };
    const getBookingTypeClass = (bookingType) => {
      const classMap = {
        "EXCLUSIVE": "tag-exclusive",
        "SHARED": "tag-shared"
      };
      return bookingType ? classMap[bookingType] || "tag-default" : "tag-default";
    };
    const isVirtualOrder = (booking) => {
      if (!booking)
        return false;
      const bookingId = typeof booking.id === "string" ? parseInt(booking.id) : booking.id;
      return bookingId < 0;
    };
    const getBookingPrice = (booking) => {
      if (!booking)
        return "0.00";
      const virtualOrder = isVirtualOrder(booking);
      let price;
      if (virtualOrder) {
        price = booking.paymentAmount || 0;
      } else {
        price = booking.totalPrice || 0;
      }
      return price.toFixed(2);
    };
    const formatBookingDate = (booking) => {
      if (!booking)
        return "";
      const virtualOrder = isVirtualOrder(booking);
      if (virtualOrder) {
        const bookingTime = booking.bookingTime;
        if (!bookingTime)
          return "";
        try {
          let dateTime;
          if (typeof bookingTime === "string") {
            let isoTime = bookingTime;
            if (bookingTime.includes(" ") && !bookingTime.includes("T")) {
              isoTime = bookingTime.replace(" ", "T");
            }
            dateTime = new Date(isoTime);
          } else {
            dateTime = new Date(bookingTime);
          }
          if (isNaN(dateTime.getTime())) {
            common_vendor.index.__f__("error", "at pages/booking/list.vue:567", "虚拟订单日期格式化错误 - 无效的时间:", bookingTime);
            return "";
          }
          return dateTime.toLocaleDateString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit"
          }).replace(/\//g, "-");
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/booking/list.vue:577", "虚拟订单日期格式化错误:", error);
          return "";
        }
      } else {
        if (booking.bookingDate) {
          return utils_helpers.formatDate(booking.bookingDate);
        }
        return "";
      }
    };
    common_vendor.onMounted(() => {
      common_vendor.index.$on("sharingDataChanged", onSharingDataChanged);
      initData();
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.$off("sharingDataChanged", onSharingDataChanged);
    });
    const onSharingDataChanged = (data) => {
      common_vendor.index.__f__("log", "at pages/booking/list.vue:602", "预约列表页面：收到拼场数据变化通知:", data);
      initData();
    };
    common_vendor.onShow(async () => {
      await refreshData();
    });
    common_vendor.onPullDownRefresh(async () => {
      await refreshData();
    });
    common_vendor.onReachBottom(() => {
      loadMore();
    });
    const onCountdownExpired = (_order) => {
      initData();
    };
    const payOrder = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/payment/index?orderId=${booking.id}&type=booking`
      });
    };
    const viewOrderDetail = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/booking/detail?id=${booking.id}`
      });
    };
    const viewParticipants = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/sharing/participants?orderId=${booking.id}`
      });
    };
    const checkinOrder = (_booking) => {
      common_vendor.index.showModal({
        title: "确认签到",
        content: "确认已到达场馆并开始使用？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "签到成功",
              icon: "success"
            });
            initData();
          }
        }
      });
    };
    const completeOrder = (_booking) => {
      common_vendor.index.showModal({
        title: "完成订单",
        content: "确认完成此次预约？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "订单已完成",
              icon: "success"
            });
            initData();
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(statusOptions.value, (status, k0, i0) => {
          return {
            a: common_vendor.t(status.label),
            b: status.value,
            c: selectedStatus.value === status.value ? 1 : "",
            d: common_vendor.o(($event) => selectStatus(status.value), status.value)
          };
        }),
        b: common_vendor.f(filteredBookings.value, (booking, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(booking.venueName || "未知场馆"),
            b: booking.bookingType || booking.type || booking.orderType
          }, booking.bookingType || booking.type || booking.orderType ? {
            c: common_vendor.t(getBookingTypeText(booking.bookingType || booking.type || booking.orderType)),
            d: common_vendor.n(getBookingTypeClass(booking.bookingType || booking.type || booking.orderType))
          } : {}, {
            e: isVirtualOrder(booking)
          }, isVirtualOrder(booking) ? {} : {}, {
            f: common_vendor.t(formatBookingDate(booking)),
            g: common_vendor.t(getStatusText(booking.status)),
            h: common_vendor.n(getStatusClass(booking.status)),
            i: common_vendor.t(formatTimeRange(booking)),
            j: common_vendor.t(booking.venueLocation || "未知地点"),
            k: common_vendor.t(booking.orderNo || (booking.id ? booking.id : "")),
            l: common_vendor.t(formatCreateTime(booking && (booking.createdAt || booking.createTime))),
            m: common_vendor.t(getBookingPrice(booking)),
            n: common_vendor.unref(utils_countdown.shouldShowCountdown)(booking)
          }, common_vendor.unref(utils_countdown.shouldShowCountdown)(booking) ? {
            o: common_vendor.o(onCountdownExpired, booking.id),
            p: "c1931fab-0-" + i0,
            q: common_vendor.p({
              order: booking,
              label: "自动取消",
              short: true
            })
          } : {}, {
            r: booking.status === "PENDING"
          }, booking.status === "PENDING" ? {
            s: common_vendor.o(($event) => payOrder(booking), booking.id),
            t: common_vendor.o(($event) => showCancelModal.value(booking.id), booking.id)
          } : booking.status === "PAID" ? {
            w: common_vendor.o(($event) => viewOrderDetail(booking), booking.id),
            x: common_vendor.o(($event) => showCancelModal.value(booking.id), booking.id)
          } : booking.status === "OPEN" || booking.status === "SHARING" || booking.status === "PENDING_FULL" ? {
            z: common_vendor.o(($event) => viewOrderDetail(booking), booking.id),
            A: common_vendor.o(($event) => viewParticipants(booking), booking.id),
            B: common_vendor.o(($event) => showCancelModal.value(booking.id), booking.id)
          } : booking.status === "SHARING_SUCCESS" || booking.status === "FULL" ? {
            D: common_vendor.o(($event) => viewOrderDetail(booking), booking.id),
            E: common_vendor.o(($event) => viewParticipants(booking), booking.id)
          } : booking.status === "CONFIRMED" ? {
            G: common_vendor.o(($event) => checkinOrder(), booking.id),
            H: common_vendor.o(($event) => showCancelModal.value(booking.id), booking.id)
          } : booking.status === "VERIFIED" ? {
            J: common_vendor.o(($event) => completeOrder(), booking.id)
          } : booking.status === "COMPLETED" ? {
            L: common_vendor.o(($event) => reviewVenue(booking), booking.id),
            M: common_vendor.o(($event) => rebookVenue(booking), booking.id)
          } : booking.status === "CANCELLED" || booking.status === "EXPIRED" ? {
            O: common_vendor.o(($event) => rebookVenue(booking), booking.id)
          } : {}, {
            v: booking.status === "PAID",
            y: booking.status === "OPEN" || booking.status === "SHARING" || booking.status === "PENDING_FULL",
            C: booking.status === "SHARING_SUCCESS" || booking.status === "FULL",
            F: booking.status === "CONFIRMED",
            I: booking.status === "VERIFIED",
            K: booking.status === "COMPLETED",
            N: booking.status === "CANCELLED" || booking.status === "EXPIRED",
            P: booking.id,
            Q: common_vendor.o(($event) => navigateToDetail(booking.id), booking.id)
          });
        }),
        c: filteredBookings.value.length === 0
      }, filteredBookings.value.length === 0 ? {
        d: common_vendor.o(navigateToVenueList)
      } : {}, {
        e: hasMore.value && filteredBookings.value.length > 0
      }, hasMore.value && filteredBookings.value.length > 0 ? {
        f: common_vendor.t(loading.value ? "加载中..." : "加载更多"),
        g: common_vendor.o(loadMore)
      } : {}, {
        h: common_vendor.o(closeCancelModal),
        i: common_vendor.o(confirmCancel),
        j: common_vendor.sr(cancelPopup, "c1931fab-1", {
          "k": "cancelPopup"
        }),
        k: common_vendor.p({
          type: "center"
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c1931fab"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/booking/list.js.map
