"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_venue = require("../../stores/venue.js");
const stores_booking = require("../../stores/booking.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "VenueDetail",
  data() {
    return {
      venueStore: null,
      bookingStore: null,
      venueId: "",
      selectedDate: "",
      selectedTimeSlots: [],
      // 改为数组以支持多时间段选择
      availableDates: [],
      bookingType: "EXCLUSIVE",
      // 预约类型：EXCLUSIVE(独享) 或 SHARED(拼场)
      teamName: "",
      // 拼场队伍名称
      contactInfo: "",
      // 拼场联系方式
      maxParticipants: 4
      // 最大参与人数
    };
  },
  computed: {
    venueDetail() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.venueDetailGetter) || {};
    },
    timeSlots() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.timeSlotsGetter) || [];
    },
    loading() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.isLoading) || false;
    },
    // 处理场馆图片
    venueImages() {
      if (this.venueDetail.image) {
        if (typeof this.venueDetail.image === "string") {
          return [this.venueDetail.image];
        }
        if (Array.isArray(this.venueDetail.image)) {
          return this.venueDetail.image;
        }
      }
      return ["/static/default-venue.jpg"];
    },
    // 处理设施列表
    facilitiesList() {
      if (this.venueDetail.facilities) {
        if (typeof this.venueDetail.facilities === "string") {
          return this.venueDetail.facilities.split(",").map((f) => f.trim()).filter((f) => f);
        }
        if (Array.isArray(this.venueDetail.facilities)) {
          return this.venueDetail.facilities;
        }
      }
      return [];
    },
    // 格式化营业时间
    formatOpeningHours() {
      if (this.venueDetail.openTime && this.venueDetail.closeTime) {
        return `${this.venueDetail.openTime} - ${this.venueDetail.closeTime}`;
      }
      return "营业时间待更新";
    },
    // 过滤掉已过期的时间段
    filteredTimeSlots() {
      common_vendor.index.__f__("log", "at pages/venue/detail.vue:370", "原始时间段数据:", this.timeSlots);
      if (!Array.isArray(this.timeSlots)) {
        common_vendor.index.__f__("warn", "at pages/venue/detail.vue:374", "timeSlots不是数组:", this.timeSlots);
        return [];
      }
      const now = /* @__PURE__ */ new Date();
      const currentTime = now.getHours() * 60 + now.getMinutes();
      const selectedDate = this.selectedDate;
      const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
      const slots = this.timeSlots.filter((slot) => {
        if (!slot || typeof slot !== "object") {
          common_vendor.index.__f__("warn", "at pages/venue/detail.vue:386", "无效的时间段对象:", slot);
          return false;
        }
        if (slot.status === "EXPIRED") {
          return false;
        }
        if (selectedDate === today && slot.startTime) {
          const slotStartTime = this.getMinutesFromTimeString(slot.startTime);
          if (slotStartTime <= currentTime) {
            return false;
          }
        }
        return true;
      });
      common_vendor.index.__f__("log", "at pages/venue/detail.vue:406", "过滤后的时间段数据:", slots);
      return slots;
    }
  },
  onLoad(options) {
    this.venueStore = stores_venue.useVenueStore();
    this.bookingStore = stores_booking.useBookingStore();
    this.venueId = options.id;
    this.initData();
  },
  onShow() {
    if (this.venueId && this.selectedDate) {
      setTimeout(() => {
        this.loadTimeSlots();
      }, 200);
    }
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  methods: {
    // 将时间字符串转换为分钟数（用于比较）
    getMinutesFromTimeString(timeStr) {
      if (!timeStr || typeof timeStr !== "string") {
        common_vendor.index.__f__("warn", "at pages/venue/detail.vue:439", "getMinutesFromTimeString: 无效的时间字符串:", timeStr);
        return 0;
      }
      try {
        const [hours, minutes] = timeStr.split(":").map(Number);
        if (isNaN(hours) || isNaN(minutes)) {
          common_vendor.index.__f__("warn", "at pages/venue/detail.vue:446", "getMinutesFromTimeString: 时间格式错误:", timeStr);
          return 0;
        }
        return hours * 60 + minutes;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:451", "getMinutesFromTimeString: 解析时间失败:", timeStr, error);
        return 0;
      }
    },
    // 初始化数据
    async initData() {
      try {
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:459", "开始初始化数据，场馆ID:", this.venueId);
        if (!this.venueId) {
          common_vendor.index.__f__("error", "at pages/venue/detail.vue:461", "场馆ID为空");
          common_vendor.index.showToast({
            title: "参数错误",
            icon: "error"
          });
          return;
        }
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:469", "调用getVenueDetail，参数:", this.venueId);
        await this.venueStore.getVenueDetail(this.venueId);
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:471", "获取场馆详情成功:", this.venueDetail);
        this.initDates();
        if (this.selectedDate) {
          await this.loadTimeSlots();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:478", "初始化数据失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "error"
        });
      }
    },
    // 刷新数据
    async refreshData() {
      try {
        await this.initData();
        common_vendor.index.stopPullDownRefresh();
      } catch (error) {
        common_vendor.index.stopPullDownRefresh();
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:493", "刷新数据失败:", error);
      }
    },
    // 初始化可选日期
    initDates() {
      const dates = [];
      const today = /* @__PURE__ */ new Date();
      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dayNames = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        const day = i === 0 ? "今天" : i === 1 ? "明天" : dayNames[date.getDay()];
        dates.push({
          value: utils_helpers.formatDate(date, "YYYY-MM-DD"),
          day,
          date: utils_helpers.formatDate(date, "MM/DD")
        });
      }
      this.availableDates = dates;
      this.selectedDate = dates[0].value;
    },
    // 选择日期
    async selectDate(date) {
      this.selectedDate = date;
      this.selectedTimeSlots = [];
      await this.loadTimeSlots();
    },
    // 预约类型变化
    onBookingTypeChange(type) {
      this.bookingType = type;
      this.selectedTimeSlots = [];
    },
    // 加载时间段
    async loadTimeSlots() {
      try {
        await this.venueStore.getVenueTimeSlots({
          venueId: this.venueId,
          date: this.selectedDate
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:543", "加载时间段失败:", error);
      }
    },
    // 选择时间段
    selectTimeSlot(slot) {
      common_vendor.index.__f__("log", "at pages/venue/detail.vue:549", "点击时间段:", slot);
      common_vendor.index.__f__("log", "at pages/venue/detail.vue:550", "时间段状态:", slot.status);
      if (slot.status === "OCCUPIED" || slot.status === "RESERVED") {
        common_vendor.index.showToast({
          title: "该时间段已被预约",
          icon: "none",
          duration: 2e3
        });
        return;
      } else if (slot.status === "MAINTENANCE") {
        common_vendor.index.showToast({
          title: "该时间段维护中",
          icon: "none",
          duration: 2e3
        });
        return;
      } else if (slot.status === "EXPIRED") {
        common_vendor.index.showToast({
          title: "该时间段已过期，无法预约",
          icon: "none",
          duration: 2e3
        });
        return;
      } else if (slot.status === "AVAILABLE") {
        if (this.bookingType === "SHARED") {
          if (!this.isTimeSlotValidForSharing(slot)) {
            common_vendor.index.showToast({
              title: "拼场预约请选择三个小时以后的时间段",
              icon: "none",
              duration: 3e3
            });
            return;
          }
        }
        const existingIndex = this.selectedTimeSlots.findIndex(
          (item) => item.id && item.id === slot.id || item.startTime === slot.startTime && item.endTime === slot.endTime
        );
        if (existingIndex !== -1) {
          this.selectedTimeSlots.splice(existingIndex, 1);
          common_vendor.index.__f__("log", "at pages/venue/detail.vue:595", "取消选择时间段:", slot);
          common_vendor.index.showToast({
            title: "已取消选择",
            icon: "success",
            duration: 1e3
          });
          return;
        }
        if (this.selectedTimeSlots.length > 0) {
          const hasConsecutive = this.selectedTimeSlots.some(
            (selectedSlot) => this.isConsecutiveTimeSlot(selectedSlot, slot)
          );
          if (!hasConsecutive) {
            common_vendor.index.showToast({
              title: "只能选择连续的时间段",
              icon: "none",
              duration: 2e3
            });
            return;
          }
        }
        this.selectedTimeSlots.push(slot);
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:624", "已选择时间段:", this.selectedTimeSlots);
        common_vendor.index.showToast({
          title: "已选择时间段",
          icon: "success",
          duration: 1e3
        });
      } else {
        common_vendor.index.showToast({
          title: "该时间段不可用",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 检查两个时间段是否连续
    isConsecutiveTimeSlot(slot1, slot2) {
      const slot1End = this.getMinutesFromTimeString(slot1.endTime);
      const slot2Start = this.getMinutesFromTimeString(slot2.startTime);
      const slot1Start = this.getMinutesFromTimeString(slot1.startTime);
      const slot2End = this.getMinutesFromTimeString(slot2.endTime);
      return slot1End === slot2Start || slot2End === slot1Start;
    },
    // 获取时间段样式类
    getSlotClass(slot) {
      const classes = ["timeslot-item"];
      if (slot.status === "OCCUPIED") {
        classes.push("occupied");
        classes.push("disabled");
      } else if (slot.status === "RESERVED") {
        classes.push("occupied");
        classes.push("disabled");
      } else if (slot.status === "MAINTENANCE") {
        classes.push("maintenance");
        classes.push("disabled");
      } else if (slot.status === "EXPIRED") {
        classes.push("expired");
        classes.push("disabled");
      }
      const isSelected = this.selectedTimeSlots.some(
        (selectedSlot) => slot.id && selectedSlot.id === slot.id || slot.startTime === selectedSlot.startTime && slot.endTime === selectedSlot.endTime
      );
      if (isSelected) {
        classes.push("selected");
        common_vendor.index.__f__("log", "at pages/venue/detail.vue:678", "添加选中样式:", slot);
      }
      return classes.join(" ");
    },
    // 获取第一个时间段（按开始时间排序）
    getFirstTimeSlot() {
      if (this.selectedTimeSlots.length === 0)
        return null;
      return this.selectedTimeSlots.reduce((earliest, current) => {
        const earliestTime = this.getMinutesFromTimeString(earliest.startTime);
        const currentTime = this.getMinutesFromTimeString(current.startTime);
        return currentTime < earliestTime ? current : earliest;
      }, this.selectedTimeSlots[0]);
    },
    // 获取最后一个时间段（按结束时间排序）
    getLastTimeSlot() {
      if (this.selectedTimeSlots.length === 0)
        return null;
      return this.selectedTimeSlots.reduce((latest, current) => {
        const latestTime = this.getMinutesFromTimeString(latest.endTime);
        const currentTime = this.getMinutesFromTimeString(current.endTime);
        return currentTime > latestTime ? current : latest;
      }, this.selectedTimeSlots[0]);
    },
    // 检查时间段是否满足拼场预约的时间限制（需要提前3小时）
    isTimeSlotValidForSharing(slot) {
      const now = /* @__PURE__ */ new Date();
      const selectedDateTime = /* @__PURE__ */ new Date(`${this.selectedDate} ${slot.startTime}`);
      const timeDiff = selectedDateTime.getTime() - now.getTime();
      const hoursDiff = timeDiff / (1e3 * 60 * 60);
      return hoursDiff >= 3;
    },
    // 计算总价格
    getTotalPrice() {
      if (this.selectedTimeSlots.length === 0)
        return 0;
      return this.selectedTimeSlots.reduce((total, slot) => {
        return total + (slot.price || 0);
      }, 0);
    },
    // 获取时间段状态文本
    getSlotStatusText(status) {
      const statusMap = {
        "AVAILABLE": "可预约",
        "OCCUPIED": "已预约",
        "RESERVED": "已预约",
        "MAINTENANCE": "维护中",
        "EXPIRED": "已过期"
      };
      return statusMap[status] || "可预约";
    },
    // 获取预约按钮文本
    getBookButtonText() {
      if (this.selectedTimeSlots.length === 0) {
        return "请选择时间段";
      }
      return `预约 ${this.selectedTimeSlots.length} 个时间段`;
    },
    // 预约场馆
    bookVenue() {
      if (this.selectedTimeSlots.length === 0) {
        common_vendor.index.showToast({
          title: "请选择时间段",
          icon: "none"
        });
        return;
      }
      const selectedSlotsData = JSON.stringify(this.selectedTimeSlots);
      common_vendor.index.navigateTo({
        url: `/pages/booking/create?venueId=${this.venueDetail.id}&date=${this.selectedDate}&bookingType=${this.bookingType}&selectedSlots=${encodeURIComponent(selectedSlotsData)}`
      });
    },
    // 关闭预约弹窗
    closeBookingModal() {
      this.$refs.bookingPopup.close();
    },
    // 确认预约
    async confirmBooking() {
      try {
        if (this.selectedTimeSlots.length === 0) {
          common_vendor.index.showToast({
            title: "请选择时间段",
            icon: "none"
          });
          return;
        }
        common_vendor.index.showLoading({ title: "预约中..." });
        const sortedSlots = [...this.selectedTimeSlots].sort((a, b) => {
          const aTime = this.getMinutesFromTimeString(a.startTime);
          const bTime = this.getMinutesFromTimeString(b.startTime);
          return aTime - bTime;
        });
        const startTime = sortedSlots[0].startTime;
        const endTime = sortedSlots[sortedSlots.length - 1].endTime;
        const slotIds = this.selectedTimeSlots.map((slot) => slot.id);
        const bookingData = {
          venueId: this.venueId,
          date: this.selectedDate,
          startTime,
          endTime,
          slotIds,
          // 传递所有时间段ID
          remark: this.selectedTimeSlots.length > 1 ? `连续预约${this.selectedTimeSlots.length}个时间段` : "",
          bookingType: this.bookingType
        };
        await this.bookingStore.createBooking(bookingData);
        await this.venueStore.getVenueTimeSlots({
          venueId: this.venueId,
          date: this.selectedDate
        });
        common_vendor.index.hideLoading();
        this.closeBookingModal();
        common_vendor.index.showToast({
          title: "预约成功",
          icon: "success"
        });
        this.selectedTimeSlots = [];
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/booking/list"
          });
        }, 1500);
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/venue/detail.vue:844", "预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "预约失败",
          icon: "error"
        });
      }
    },
    // 联系场馆
    contactVenue() {
      if (this.venueDetail.phone) {
        common_vendor.index.makePhoneCall({
          phoneNumber: this.venueDetail.phone
        });
      } else {
        common_vendor.index.showToast({
          title: "暂无联系方式",
          icon: "none"
        });
      }
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 格式化选中日期
    formatSelectedDate() {
      const selectedDateObj = this.availableDates.find((d) => d.value === this.selectedDate);
      return selectedDateObj ? `${selectedDateObj.day} ${selectedDateObj.date}` : this.selectedDate;
    },
    // 计算预约时长
    getBookingDuration() {
      if (this.selectedTimeSlots.length === 0) {
        return "0小时";
      }
      if (this.selectedTimeSlots.length === 1) {
        const slot = this.selectedTimeSlots[0];
        const startMinutes = this.getMinutesFromTimeString(slot.startTime);
        const endMinutes = this.getMinutesFromTimeString(slot.endTime);
        const durationMinutes = endMinutes - startMinutes;
        const hours = Math.floor(durationMinutes / 60);
        const minutes = durationMinutes % 60;
        if (minutes === 0) {
          return `${hours}小时`;
        } else {
          return `${hours}小时${minutes}分钟`;
        }
      } else {
        const firstSlot = this.getFirstTimeSlot();
        const lastSlot = this.getLastTimeSlot();
        if (firstSlot && lastSlot) {
          const startMinutes = this.getMinutesFromTimeString(firstSlot.startTime);
          const endMinutes = this.getMinutesFromTimeString(lastSlot.endTime);
          const durationMinutes = endMinutes - startMinutes;
          const hours = Math.floor(durationMinutes / 60);
          const minutes = durationMinutes % 60;
          if (minutes === 0) {
            return `${hours}小时`;
          } else {
            return `${hours}小时${minutes}分钟`;
          }
        }
        return "0小时";
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d;
  return common_vendor.e({
    a: $options.loading
  }, $options.loading ? {} : $options.venueDetail ? common_vendor.e({
    c: common_vendor.f($options.venueImages, (image, index, i0) => {
      return {
        a: image,
        b: index
      };
    }),
    d: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    e: common_vendor.t($options.venueDetail.name),
    f: common_vendor.t($options.venueDetail.rating || "暂无评分"),
    g: $options.venueDetail.rating
  }, $options.venueDetail.rating ? {} : {}, {
    h: $options.venueDetail.reviewCount
  }, $options.venueDetail.reviewCount ? {
    i: common_vendor.t($options.venueDetail.reviewCount)
  } : {}, {
    j: common_vendor.t($options.venueDetail.location),
    k: $options.venueDetail.distance
  }, $options.venueDetail.distance ? {
    l: common_vendor.t($options.venueDetail.distance)
  } : {}, {
    m: common_vendor.t($options.venueDetail.price),
    n: common_vendor.t($options.venueDetail.type),
    o: $options.venueDetail.supportSharing
  }, $options.venueDetail.supportSharing ? {} : {}, {
    p: common_vendor.t($options.venueDetail.status === "ACTIVE" ? "营业中" : "暂停营业"),
    q: common_vendor.t($options.venueDetail.description),
    r: common_vendor.f($options.facilitiesList, (facility, k0, i0) => {
      return {
        a: common_vendor.t(facility),
        b: facility
      };
    }),
    s: common_vendor.t($options.formatOpeningHours),
    t: $options.venueDetail.supportSharing
  }, $options.venueDetail.supportSharing ? common_vendor.e({
    v: $data.bookingType === "EXCLUSIVE"
  }, $data.bookingType === "EXCLUSIVE" ? {} : {}, {
    w: $data.bookingType === "EXCLUSIVE" ? 1 : "",
    x: common_vendor.o(($event) => $options.onBookingTypeChange("EXCLUSIVE")),
    y: $data.bookingType === "SHARED"
  }, $data.bookingType === "SHARED" ? {} : {}, {
    z: $data.bookingType === "SHARED" ? 1 : "",
    A: common_vendor.o(($event) => $options.onBookingTypeChange("SHARED")),
    B: $data.bookingType === "SHARED"
  }, $data.bookingType === "SHARED" ? {} : {}) : {}, {
    C: common_vendor.f($data.availableDates, (date, index, i0) => {
      return {
        a: common_vendor.t(date.day),
        b: common_vendor.t(date.date),
        c: index,
        d: $data.selectedDate === date.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectDate(date.value), index)
      };
    }),
    D: common_vendor.f($options.filteredTimeSlots, (slot, k0, i0) => {
      return {
        a: common_vendor.t(slot.startTime),
        b: common_vendor.t(slot.endTime),
        c: common_vendor.t(slot.price),
        d: common_vendor.t($options.getSlotStatusText(slot.status)),
        e: slot.id,
        f: common_vendor.n($options.getSlotClass(slot)),
        g: common_vendor.o(($event) => $options.selectTimeSlot(slot), slot.id)
      };
    }),
    E: common_vendor.o((...args) => $options.contactVenue && $options.contactVenue(...args)),
    F: common_vendor.t($options.getBookButtonText()),
    G: $data.selectedTimeSlots.length === 0,
    H: common_vendor.o((...args) => $options.bookVenue && $options.bookVenue(...args)),
    I: common_vendor.t($options.venueDetail.name),
    J: common_vendor.t($options.venueDetail.location),
    K: common_vendor.t($options.formatSelectedDate()),
    L: $data.selectedTimeSlots.length === 1
  }, $data.selectedTimeSlots.length === 1 ? {
    M: common_vendor.t((_a = $data.selectedTimeSlots[0]) == null ? void 0 : _a.startTime),
    N: common_vendor.t((_b = $data.selectedTimeSlots[0]) == null ? void 0 : _b.endTime)
  } : $data.selectedTimeSlots.length > 1 ? {
    P: common_vendor.t((_c = $options.getFirstTimeSlot()) == null ? void 0 : _c.startTime),
    Q: common_vendor.t((_d = $options.getLastTimeSlot()) == null ? void 0 : _d.endTime)
  } : {}, {
    O: $data.selectedTimeSlots.length > 1,
    R: common_vendor.t($options.getBookingDuration()),
    S: common_vendor.t($data.bookingType === "EXCLUSIVE" ? "独享预约" : "拼场预约"),
    T: $data.bookingType === "SHARED"
  }, $data.bookingType === "SHARED" ? {
    U: common_vendor.t($data.teamName),
    V: common_vendor.t($data.contactInfo),
    W: common_vendor.t($data.maxParticipants)
  } : {}, {
    X: common_vendor.f($data.selectedTimeSlots, (slot, index, i0) => {
      return {
        a: common_vendor.t(slot.startTime),
        b: common_vendor.t(slot.endTime),
        c: common_vendor.t(slot.price),
        d: index
      };
    }),
    Y: common_vendor.t($options.getTotalPrice()),
    Z: $options.venueDetail.phone
  }, $options.venueDetail.phone ? {
    aa: common_vendor.t($options.venueDetail.phone)
  } : {}, {
    ab: common_vendor.o((...args) => $options.closeBookingModal && $options.closeBookingModal(...args)),
    ac: common_vendor.o((...args) => $options.confirmBooking && $options.confirmBooking(...args)),
    ad: common_vendor.sr("bookingPopup", "7c8355cf-0"),
    ae: common_vendor.p({
      type: "center"
    })
  }) : {
    af: common_vendor.o((...args) => $options.initData && $options.initData(...args))
  }, {
    b: $options.venueDetail
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7c8355cf"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/venue/detail.js.map
