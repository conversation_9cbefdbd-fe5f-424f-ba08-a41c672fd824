"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_venue = require("../../stores/venue.js");
const utils_helpers = require("../../utils/helpers.js");
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const _sfc_main = {
  name: "VenueList",
  components: {
    uniPopup
  },
  data() {
    return {
      venueStore: null,
      searchKeyword: "",
      selectedType: "",
      // 筛选选项
      filterOptions: {
        minPrice: "",
        maxPrice: "",
        distance: "",
        facilities: []
      },
      // 距离选项
      distanceOptions: [
        { label: "1km内", value: 1 },
        { label: "3km内", value: 3 },
        { label: "5km内", value: 5 },
        { label: "10km内", value: 10 }
      ],
      // 设施选项
      facilityOptions: [
        { label: "停车场", value: "parking" },
        { label: "淋浴间", value: "shower" },
        { label: "更衣室", value: "locker" },
        { label: "WiFi", value: "wifi" },
        { label: "空调", value: "ac" },
        { label: "器材租赁", value: "equipment" }
      ]
    };
  },
  computed: {
    venueList() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.getVenueList) || [];
    },
    venueTypes() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.getVenueTypes) || [];
    },
    searchResults() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.getSearchResults) || [];
    },
    loading() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.isLoading) || false;
    },
    pagination() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.getPagination) || { current: 0, totalPages: 0 };
    },
    filteredVenues() {
      if (this.searchKeyword && this.searchKeyword.trim()) {
        return Array.isArray(this.searchResults) ? this.searchResults : [];
      }
      return Array.isArray(this.venueList) ? this.venueList : [];
    },
    hasMore() {
      return this.pagination && this.pagination.current < this.pagination.totalPages;
    }
  },
  onLoad() {
    this.venueStore = stores_venue.useVenueStore();
    this.initData();
  },
  onShow() {
    this.refreshData();
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore();
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        common_vendor.index.__f__("log", "at pages/venue/list.vue:265", "开始初始化场馆数据...");
        const results = await Promise.all([
          this.venueStore.getVenueList({ page: 1, pageSize: 50 }),
          this.venueStore.getVenueTypes()
        ]);
        common_vendor.index.__f__("log", "at pages/venue/list.vue:270", "场馆数据获取结果:", results);
        common_vendor.index.__f__("log", "at pages/venue/list.vue:271", "当前场馆列表:", this.venueList);
        common_vendor.index.__f__("log", "at pages/venue/list.vue:272", "当前场馆类型:", this.venueTypes);
        common_vendor.index.__f__("log", "at pages/venue/list.vue:273", "场馆类型数据类型:", typeof this.venueTypes, Array.isArray(this.venueTypes));
        this.updatePagination();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/list.vue:276", "初始化数据失败:", error);
        common_vendor.index.showToast({
          title: "数据加载失败",
          icon: "none"
        });
      }
    },
    // 刷新数据
    async refreshData() {
      try {
        await this.venueStore.getVenueList({ page: 1, pageSize: 50, refresh: true });
        this.updatePagination();
        common_vendor.index.stopPullDownRefresh();
      } catch (error) {
        common_vendor.index.stopPullDownRefresh();
        common_vendor.index.__f__("error", "at pages/venue/list.vue:292", "刷新数据失败:", error);
      }
    },
    // 加载更多
    async loadMore() {
      if (this.loading || !this.hasMore)
        return;
      try {
        const nextPage = this.pagination.current + 1;
        await this.venueStore.getVenueList({
          page: nextPage,
          pageSize: 50,
          type: this.selectedType,
          ...this.filterOptions
        });
        this.updatePagination();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/list.vue:310", "加载更多失败:", error);
      }
    },
    // 更新分页状态
    updatePagination() {
    },
    // 搜索输入处理
    onSearchInput: utils_helpers.debounce(function() {
    }, 500),
    // 处理搜索
    async handleSearch() {
      const trimmedKeyword = this.searchKeyword ? this.searchKeyword.trim() : "";
      if (!trimmedKeyword) {
        this.venueStore.setSearchResults([]);
        await this.venueStore.getVenueList({ page: 1, pageSize: 50, refresh: true });
        this.updatePagination();
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "搜索中..." });
        this.venueStore.setSearchResults([]);
        this.$forceUpdate();
        const params = {
          keyword: trimmedKeyword,
          page: 1,
          pageSize: 50
        };
        if (this.selectedType) {
          params.type = this.selectedType;
        }
        if (this.filterOptions.minPrice) {
          params.minPrice = Number(this.filterOptions.minPrice);
        }
        if (this.filterOptions.maxPrice) {
          params.maxPrice = Number(this.filterOptions.maxPrice);
        }
        if (this.filterOptions.distance) {
          params.distance = this.filterOptions.distance;
        }
        if (this.filterOptions.facilities && Array.isArray(this.filterOptions.facilities) && this.filterOptions.facilities.length > 0) {
          params.facilities = this.filterOptions.facilities.join(",");
        }
        common_vendor.index.__f__("log", "at pages/venue/list.vue:373", "执行搜索，参数:", params);
        await this.venueStore.searchVenues(params);
        common_vendor.index.__f__("log", "at pages/venue/list.vue:375", "搜索完成，结果:", this.searchResults);
        common_vendor.index.hideLoading();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/list.vue:379", "搜索失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "搜索失败，请重试",
          icon: "error"
        });
      }
    },
    // 选择场馆类型
    async selectType(typeId) {
      this.selectedType = typeId;
      if (typeId === "") {
        this.searchKeyword = "";
        this.resetFilter();
        this.venueStore.setSearchResults([]);
      }
      try {
        const params = {
          page: 1,
          pageSize: 50,
          refresh: true,
          ...this.filterOptions
        };
        if (typeId) {
          params.type = typeId;
        }
        common_vendor.index.__f__("log", "at pages/venue/list.vue:413", "选择场馆类型，参数:", params);
        await this.venueStore.getVenueList(params);
        this.updatePagination();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/list.vue:417", "筛选失败:", error);
      }
    },
    // 跳转到详情页
    navigateToDetail(venueId) {
      common_vendor.index.navigateTo({
        url: `/pages/venue/detail?id=${venueId}`
      });
    },
    // 显示筛选弹窗
    showFilterModal() {
      this.$refs.filterPopup.open();
    },
    // 关闭筛选弹窗
    closeFilterModal() {
      this.$refs.filterPopup.close();
    },
    // 选择距离
    selectDistance(distance) {
      this.filterOptions.distance = distance;
    },
    // 切换设施选择
    toggleFacility(facility) {
      if (!Array.isArray(this.filterOptions.facilities)) {
        this.filterOptions.facilities = [];
      }
      const index = this.filterOptions.facilities.indexOf(facility);
      if (index > -1) {
        this.filterOptions.facilities.splice(index, 1);
      } else {
        this.filterOptions.facilities.push(facility);
      }
    },
    // 重置筛选
    resetFilter() {
      this.filterOptions = {
        minPrice: "",
        maxPrice: "",
        distance: "",
        facilities: []
      };
    },
    // 应用筛选
    async applyFilter() {
      this.closeFilterModal();
      try {
        if (this.searchKeyword && this.searchKeyword.trim()) {
          const params = {
            keyword: this.searchKeyword.trim()
          };
          if (this.selectedType) {
            params.type = this.selectedType;
          }
          if (this.filterOptions.minPrice) {
            params.minPrice = Number(this.filterOptions.minPrice);
          }
          if (this.filterOptions.maxPrice) {
            params.maxPrice = Number(this.filterOptions.maxPrice);
          }
          if (this.filterOptions.distance) {
            params.distance = this.filterOptions.distance;
          }
          if (this.filterOptions.facilities && this.filterOptions.facilities.length > 0) {
            params.facilities = this.filterOptions.facilities.join(",");
          }
          common_vendor.index.__f__("log", "at pages/venue/list.vue:497", "应用筛选（搜索模式），参数:", params);
          await this.venueStore.searchVenues(params);
        } else {
          const params = {
            page: 1,
            pageSize: 50,
            refresh: true
          };
          if (this.selectedType) {
            params.type = this.selectedType;
          }
          if (this.filterOptions.minPrice) {
            params.minPrice = Number(this.filterOptions.minPrice);
          }
          if (this.filterOptions.maxPrice) {
            params.maxPrice = Number(this.filterOptions.maxPrice);
          }
          if (this.filterOptions.distance) {
            params.distance = this.filterOptions.distance;
          }
          if (this.filterOptions.facilities && this.filterOptions.facilities.length > 0) {
            params.facilities = this.filterOptions.facilities.join(",");
          }
          common_vendor.index.__f__("log", "at pages/venue/list.vue:528", "应用筛选（列表模式），参数:", params);
          await this.venueStore.getVenueList(params);
          this.updatePagination();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/venue/list.vue:533", "应用筛选失败:", error);
        common_vendor.index.showToast({
          title: "筛选失败，请重试",
          icon: "error"
        });
      }
    },
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        "AVAILABLE": "status-available",
        "MAINTENANCE": "status-maintenance",
        "OCCUPIED": "status-occupied"
      };
      return statusMap[status] || "status-available";
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "AVAILABLE": "可预约",
        "MAINTENANCE": "维护中",
        "OCCUPIED": "已满"
      };
      return statusMap[status] || "可预约";
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    b: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: $data.selectedType === "" ? 1 : "",
    f: common_vendor.o(($event) => $options.selectType("")),
    g: common_vendor.f($options.venueTypes && Array.isArray($options.venueTypes) ? $options.venueTypes : [], (type, k0, i0) => {
      return {
        a: common_vendor.t(type.name),
        b: type.id,
        c: $data.selectedType === type.id ? 1 : "",
        d: common_vendor.o(($event) => $options.selectType(type.id), type.id)
      };
    }),
    h: common_vendor.o((...args) => $options.showFilterModal && $options.showFilterModal(...args)),
    i: common_vendor.f($options.filteredVenues && Array.isArray($options.filteredVenues) ? $options.filteredVenues : [], (venue, k0, i0) => {
      return common_vendor.e({
        a: venue.image || "/static/default-venue.jpg",
        b: common_vendor.t(venue.name || "未知场馆"),
        c: common_vendor.t(venue.rating || "4.5"),
        d: common_vendor.t(venue.location || "位置未知"),
        e: common_vendor.t(venue.type || "运动场馆"),
        f: venue.supportSharing
      }, venue.supportSharing ? {} : {}, {
        g: common_vendor.t(venue.price || 0),
        h: common_vendor.t($options.getStatusText(venue.status)),
        i: common_vendor.n($options.getStatusClass(venue.status)),
        j: venue.id,
        k: common_vendor.o(($event) => $options.navigateToDetail(venue.id), venue.id)
      });
    }),
    j: (!$options.filteredVenues || !Array.isArray($options.filteredVenues) || $options.filteredVenues.length === 0) && !$options.loading
  }, (!$options.filteredVenues || !Array.isArray($options.filteredVenues) || $options.filteredVenues.length === 0) && !$options.loading ? {
    k: common_vendor.t($data.searchKeyword ? "未找到相关场馆" : "暂无场馆数据")
  } : {}, {
    l: $options.hasMore
  }, $options.hasMore ? {
    m: common_vendor.t($options.loading ? "加载中..." : "加载更多"),
    n: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  } : {}, {
    o: common_vendor.o((...args) => $options.closeFilterModal && $options.closeFilterModal(...args)),
    p: $data.filterOptions.minPrice,
    q: common_vendor.o(($event) => $data.filterOptions.minPrice = $event.detail.value),
    r: $data.filterOptions.maxPrice,
    s: common_vendor.o(($event) => $data.filterOptions.maxPrice = $event.detail.value),
    t: common_vendor.f($data.distanceOptions && Array.isArray($data.distanceOptions) ? $data.distanceOptions : [], (distance, k0, i0) => {
      return {
        a: common_vendor.t(distance.label),
        b: distance.value,
        c: $data.filterOptions.distance === distance.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectDistance(distance.value), distance.value)
      };
    }),
    v: common_vendor.f($data.facilityOptions && Array.isArray($data.facilityOptions) ? $data.facilityOptions : [], (facility, k0, i0) => {
      return {
        a: common_vendor.t(facility.label),
        b: facility.value,
        c: ($data.filterOptions.facilities && Array.isArray($data.filterOptions.facilities) ? $data.filterOptions.facilities : []).includes(facility.value) ? 1 : "",
        d: common_vendor.o(($event) => $options.toggleFacility(facility.value), facility.value)
      };
    }),
    w: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    x: common_vendor.o((...args) => $options.applyFilter && $options.applyFilter(...args)),
    y: common_vendor.sr("filterPopup", "f1604f90-0"),
    z: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f1604f90"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/venue/list.js.map
