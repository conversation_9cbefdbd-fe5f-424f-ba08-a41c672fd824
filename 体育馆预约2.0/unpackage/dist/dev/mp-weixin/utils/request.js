"use strict";
const common_vendor = require("../common/vendor.js");
const utils_auth = require("./auth.js");
const utils_ui = require("./ui.js");
const config_index = require("../config/index.js");
const utils_routerGuard = require("./router-guard.js");
const cacheManager = {
  cache: /* @__PURE__ */ new Map(),
  // 生成缓存键
  generateKey(options) {
    const { url, method = "GET", data = {} } = options;
    return `${method}:${url}:${JSON.stringify(data)}`;
  },
  // 获取缓存
  get(options) {
    if (options.cache === false)
      return null;
    if (options.method && options.method !== "GET")
      return null;
    const key = this.generateKey(options);
    const cached = this.cache.get(key);
    if (!cached)
      return null;
    const now = Date.now();
    if (cached.expiry && now > cached.expiry) {
      this.cache.delete(key);
      return null;
    }
    common_vendor.index.__f__("log", "at utils/request.js:35", "使用缓存数据:", key);
    return cached.data;
  },
  // 设置缓存
  set(options, data, ttl = 6e4) {
    if (options.cache === false)
      return;
    if (options.method && options.method !== "GET")
      return;
    const key = this.generateKey(options);
    const expiry = ttl ? Date.now() + ttl : null;
    this.cache.set(key, { data, expiry });
    common_vendor.index.__f__("log", "at utils/request.js:50", "缓存数据:", key);
  },
  // 清除缓存
  clear() {
    this.cache.clear();
    common_vendor.index.__f__("log", "at utils/request.js:56", "清除所有缓存");
  },
  // 清除指定URL的缓存
  clearUrl(url) {
    for (const key of this.cache.keys()) {
      if (key.includes(url)) {
        this.cache.delete(key);
        common_vendor.index.__f__("log", "at utils/request.js:64", "清除缓存:", key);
      }
    }
  }
};
function requestInterceptor(options) {
  if (!options.url.startsWith("http")) {
    options.url = config_index.config.baseURL + options.url;
  }
  const token = utils_auth.getToken();
  if (token) {
    options.header = {
      ...options.header,
      "Authorization": `Bearer ${token}`
    };
  }
  options.header = {
    "Content-Type": "application/json",
    ...options.header
  };
  options.timeout = options.timeout || config_index.config.timeout;
  return options;
}
function shouldRetry(error) {
  if (!error.statusCode || error.statusCode === 0) {
    return true;
  }
  if (error.statusCode >= 500 && error.statusCode < 600) {
    return true;
  }
  if (error.statusCode === 429) {
    return true;
  }
  return false;
}
function responseInterceptor(response, options) {
  const { data, statusCode } = response;
  if (options.url && options.url.includes("/sharing-orders/")) {
    common_vendor.index.__f__("log", "at utils/request.js:125", "[Request] 拼场订单API响应:", {
      url: options.url,
      statusCode,
      data,
      hasCodeField: data.hasOwnProperty("code"),
      dataKeys: data ? Object.keys(data) : []
    });
  }
  if (statusCode >= 200 && statusCode < 300) {
    if (data.code === 200 || data.success === true || !data.hasOwnProperty("code")) {
      return data;
    } else {
      const error = new Error(data.message || "请求失败");
      error.code = data.code;
      error.statusCode = statusCode;
      throw error;
    }
  } else if (statusCode === 401) {
    const errorMessage = data && data.message ? data.message : "认证失败";
    if (errorMessage.includes("用户名或密码错误") || errorMessage.includes("账号或密码错误") || errorMessage.includes("密码错误") || errorMessage.includes("用户不存在") || errorMessage.includes("Invalid credentials") || errorMessage.includes("Bad credentials")) {
      throw new Error(errorMessage);
    } else {
      utils_auth.removeToken();
      utils_auth.removeUserInfo();
      const pages = getCurrentPages();
      const currentPage = pages.length > 0 ? "/" + pages[pages.length - 1].route : "";
      utils_routerGuard.guestPages.some((page) => currentPage.includes(page));
      const error = new Error("登录已过期");
      error.code = "LOGIN_EXPIRED";
      throw error;
    }
  } else if (statusCode === 403) {
    throw new Error("权限不足");
  } else if (statusCode === 409) {
    const errorMessage = data && data.message ? data.message : "资源冲突";
    throw new Error(errorMessage);
  } else {
    const errorMessage = data && data.message ? data.message : `请求失败 (${statusCode})`;
    const error = new Error(errorMessage);
    if (data) {
      Object.keys(data).forEach((key) => {
        if (key !== "message") {
          error[key] = data[key];
        }
      });
    }
    throw error;
  }
}
function request(options, retryCount = 0) {
  return new Promise((resolve, reject) => {
    options = requestInterceptor(options);
    const cachedData = cacheManager.get(options);
    if (cachedData) {
      resolve(cachedData);
      return;
    }
    if (options.loading !== false) {
      utils_ui.showLoading(options.loadingText);
    }
    common_vendor.index.request({
      ...options,
      success: (response) => {
        try {
          const result = responseInterceptor(response, options);
          if (result && options.method !== "POST" && options.method !== "PUT" && options.method !== "DELETE" && options.method !== "PATCH") {
            cacheManager.set(options, result, options.cacheTTL);
          }
          resolve(result);
        } catch (error) {
          if (retryCount < config_index.config.retryTimes && shouldRetry(error)) {
            if (options.loading !== false) {
              utils_ui.hideLoading();
            }
            setTimeout(() => {
              request(options, retryCount + 1).then(resolve).catch(reject);
            }, config_index.config.retryDelay);
            return;
          }
          if (options.showError !== false) {
            utils_ui.showToast(error.message || "请求失败");
          }
          reject(error);
        }
      },
      fail: (error) => {
        common_vendor.index.__f__("error", "at utils/request.js:257", "请求失败:", error);
        const errorMsg = "网络请求失败，请检查网络连接";
        if (retryCount < config_index.config.retryTimes) {
          if (options.loading !== false) {
            utils_ui.hideLoading();
          }
          setTimeout(() => {
            request(options, retryCount + 1).then(resolve).catch(reject);
          }, config_index.config.retryDelay);
          return;
        }
        if (options.showError !== false) {
          utils_ui.showToast(errorMsg);
        }
        reject(new Error(errorMsg));
      },
      complete: () => {
        if (options.loading !== false) {
          utils_ui.hideLoading();
        }
      }
    });
  });
}
function get(url, params = {}, options = {}) {
  return request({
    url,
    method: "GET",
    data: params,
    ...options
  });
}
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: "POST",
    data,
    ...options
  });
}
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: "PUT",
    data,
    ...options
  });
}
function clearCache(url) {
  if (url) {
    cacheManager.clearUrl(url);
  } else {
    cacheManager.clear();
  }
}
if (typeof window !== "undefined") {
  window.cacheManager = cacheManager;
}
exports.clearCache = clearCache;
exports.get = get;
exports.post = post;
exports.put = put;
exports.request = request;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
