{"version": 3, "file": "index.js", "sources": ["stores/index.js"], "sourcesContent": ["import { createPinia } from 'pinia'\n\n// 创建pinia实例\nexport const pinia = createPinia()\n\n// 导出所有store以便统一管理\nexport { useAppStore } from './app.js'\nexport { useUserStore } from './user.js'\nexport { useVenueStore } from './venue.js'\nexport { useBookingStore } from './booking.js'\nexport { useSharingStore } from './sharing.js'\n\nexport default pinia\n"], "names": ["createPinia"], "mappings": ";;;;;;;AAGY,MAAC,QAAQA,cAAW,YAAA;;"}