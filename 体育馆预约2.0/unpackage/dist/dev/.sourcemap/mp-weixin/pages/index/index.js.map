{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "pages/index/index.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 开发者工具栏 - 始终显示 -->\n    <view class=\"dev-toolbar\">\n      <text class=\"dev-title\">🔧 开发工具</text>\n      <view class=\"dev-actions\">\n        <button class=\"dev-btn\" @click=\"navigateTo('/pages/test/migration-validation')\">\n          🧪 Pinia迁移验证\n        </button>\n        <button class=\"dev-btn\" @click=\"navigateTo('/pages/user/login')\">\n          🔐 登录页面\n        </button>\n        <button class=\"dev-btn\" @click=\"navigateTo('/pages/booking/list')\">\n          📋 我的预订\n        </button>\n        <button class=\"dev-btn\" @click=\"navigateTo('/pages/sharing/list')\">\n          🤝 拼场列表\n        </button>\n      </view>\n    </view>\n\n    <!-- 骨架屏 -->\n    <SkeletonScreen\n      v-if=\"loading\"\n      :show-banner=\"true\"\n      :show-actions=\"true\"\n      :show-venues=\"true\"\n      :show-sharings=\"true\"\n      :count=\"3\"\n    />\n\n    <!-- 实际内容 -->\n    <view v-else>\n\n    <!-- 轮播图 -->\n    <view class=\"banner-section\">\n      <swiper class=\"banner-swiper\" indicator-dots circular autoplay>\n        <swiper-item v-for=\"(banner, index) in banners\" :key=\"index\">\n          <image \n            :src=\"banner.image\" \n            class=\"banner-image\" \n            mode=\"aspectFill\"\n            lazy-load\n            @load=\"onImageLoad\"\n            @error=\"onImageError\"\n          />\n        </swiper-item>\n      </swiper>\n    </view>\n    \n    <!-- 快捷功能 -->\n    <view class=\"quick-actions\">\n      <view class=\"action-item\" @click=\"navigateTo('/pages/venue/list')\">\n        <view class=\"action-icon\">\n          <text class=\"iconfont icon-venue\">🏟️</text>\n        </view>\n        <text class=\"action-text\">场馆预约</text>\n      </view>\n      <view class=\"action-item\" @click=\"navigateTo('/pages/sharing/list')\">\n        <view class=\"action-icon\">\n          <text class=\"iconfont icon-sharing\">👥</text>\n        </view>\n        <text class=\"action-text\">拼场活动</text>\n      </view>\n      <view class=\"action-item\" @click=\"navigateTo('/pages/booking/list')\">\n        <view class=\"action-icon\">\n          <text class=\"iconfont icon-booking\">📅</text>\n        </view>\n        <text class=\"action-text\">我的预约</text>\n      </view>\n      <view class=\"action-item\" @click=\"navigateTo('/pages/venue/list')\">\n        <view class=\"action-icon\">\n          <text class=\"iconfont icon-venue\">🏟️</text>\n        </view>\n        <text class=\"action-text\">场馆列表</text>\n      </view>\n      <view class=\"action-item\" @click=\"navigateTo('/pages/test/booking-detail-test')\">\n        <view class=\"action-icon\">\n          <text class=\"iconfont icon-test\">🧪</text>\n        </view>\n        <text class=\"action-text\">API测试</text>\n      </view>\n      <view class=\"action-item\" @click=\"navigateTo('/pages/user/profile')\">\n        <view class=\"action-icon\">\n          <text class=\"iconfont icon-user\">👤</text>\n        </view>\n        <text class=\"action-text\">个人中心</text>\n      </view>\n      <view class=\"action-item pinia-test-item\" @click=\"navigateTo('/pages/test/migration-validation')\">\n        <view class=\"action-icon pinia-test-icon\">\n          <text class=\"iconfont icon-test\">🧪</text>\n        </view>\n        <text class=\"action-text\">迁移验证</text>\n      </view>\n    </view>\n    \n    <!-- 测试按钮 (仅开发环境) -->\n    <view class=\"test-button\" @click=\"navigateTo('/pages/test/migration-validation')\">\n      <text>🧪 迁移验证</text>\n    </view>\n    \n    <!-- 热门场馆 -->\n    <view class=\"section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">热门场馆</text>\n        <text class=\"section-more\" @click=\"navigateTo('/pages/venue/list')\">更多 ></text>\n      </view>\n      <view class=\"venue-list\">\n        <view \n          v-for=\"venue in safePopularVenues\" \n          :key=\"venue.id\" \n          class=\"venue-card\"\n          @click=\"navigateTo(`/pages/venue/detail?id=${venue.id}`)\"\n        >\n          <image \n            :src=\"(venue.images && venue.images[0]) || '/static/default-venue.jpg'\" \n            class=\"venue-image\" \n            mode=\"aspectFill\"\n            lazy-load\n            @load=\"onImageLoad\"\n            @error=\"onImageError\"\n          />\n          <view class=\"venue-info\">\n            <text class=\"venue-name\">{{ venue.name || '未知场馆' }}</text>\n            <text class=\"venue-location\">{{ venue.location || '位置未知' }}</text>\n            <view class=\"venue-price\">\n              <text class=\"price-text\">¥{{ venue.price || 0 }}/小时</text>\n              <text class=\"venue-status\" :class=\"getStatusClass(venue.status)\">{{ getStatusText(venue.status) }}</text>\n            </view>\n          </view>\n        </view>\n        <!-- 无数据提示 -->\n        <view v-if=\"safePopularVenues.length === 0\" class=\"no-data\">\n          <text class=\"no-data-text\">暂无热门场馆数据</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 最新拼场 -->\n    <view class=\"section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">最新拼场</text>\n        <text class=\"section-more\" @click=\"navigateTo('/pages/sharing/list')\">更多 ></text>\n      </view>\n      <view class=\"sharing-list\">\n        <view \n          v-for=\"sharing in latestSharingOrders\" \n          :key=\"sharing.id\" \n          class=\"sharing-card\"\n          @click=\"navigateTo(`/pages/sharing/detail?id=${sharing.id}`)\"\n        >\n          <view class=\"sharing-header\">\n            <text class=\"sharing-venue\">{{ sharing.venueName }}</text>\n            <text class=\"sharing-time\">{{ formatDate(sharing.bookingDate) }} {{ sharing.startTime }}-{{ sharing.endTime }}</text>\n          </view>\n          <view class=\"sharing-info\">\n            <text class=\"sharing-team\">{{ sharing.teamName }}</text>\n            <text class=\"sharing-participants\">{{ sharing.currentParticipants }}/{{ sharing.maxParticipants }}人</text>\n          </view>\n          <view class=\"sharing-price\">\n            <text class=\"price-per-person\">¥{{ sharing.pricePerPerson }}/人</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    </view>\n\n    <!-- 浮动测试按钮 -->\n    <view class=\"floating-test-btn\" @click=\"navigateTo('/pages/test/migration-validation')\">\n      <text class=\"floating-btn-text\">🧪</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useVenueStore } from '@/stores/venue.js'\nimport { useBookingStore } from '@/stores/booking.js'\nimport { smartNavigate } from '@/utils/navigation.js'\nimport { formatDate } from '@/utils/helpers.js'\nimport { CacheManager, PerformanceMonitor, debounce } from '@/utils/performance.js'\nimport SkeletonScreen from '@/components/SkeletonScreen.vue'\n\nexport default {\n  name: 'IndexPage',\n\n  components: {\n    SkeletonScreen\n  },\n\n  data() {\n    return {\n      venueStore: null,\n      bookingStore: null,\n      loading: false,\n      banners: [\n        {\n          image: '/static/banner1.jpg',\n          title: '专业体育场馆'\n        },\n        {\n          image: '/static/banner2.jpg',\n          title: '便捷预约服务'\n        },\n        {\n          image: '/static/banner3.jpg',\n          title: '拼场找队友'\n        }\n      ]\n    }\n  },\n  \n  computed: {\n    popularVenues() {\n      return this.venueStore?.popularVenues || []\n    },\n\n    availableSharingOrders() {\n      return this.bookingStore?.getSharingOrders || []\n    },\n\n    latestSharingOrders() {\n      // 确保数据存在且为数组\n      if (!this.availableSharingOrders || !Array.isArray(this.availableSharingOrders)) {\n        return []\n      }\n      return this.availableSharingOrders.slice(0, 3)\n    },\n\n    // 确保热门场馆数据安全\n    safePopularVenues() {\n      if (!this.popularVenues || !Array.isArray(this.popularVenues)) {\n        return []\n      }\n      return this.popularVenues\n    }\n  },\n  \n  onLoad() {\n    // 初始化Pinia stores\n    this.venueStore = useVenueStore()\n    this.bookingStore = useBookingStore()\n\n    // 使用缓存优化的数据加载\n    this.loadHomeDataWithCache()\n  },\n  \n  onPullDownRefresh() {\n    this.refreshData()\n  },\n  \n  onShow() {\n    // 页面显示时检查缓存，避免频繁刷新\n    const cacheKey = 'homePageData'\n    const cached = CacheManager.get(cacheKey)\n    \n    // 如果没有缓存或缓存已过期，才刷新数据\n    if (!cached) {\n      this.loadHomeDataWithCache()\n    }\n  },\n  \n  methods: {\n    \n    // 优化的首页数据加载（带缓存和超时处理）\n    async loadHomeDataWithCache() {\n      const cacheKey = 'homePageData'\n      \n      try {\n        // 开始性能监控\n        PerformanceMonitor.mark('homeDataLoad')\n        \n        // 检查缓存\n        const cached = CacheManager.get(cacheKey)\n        if (cached) {\n          // 通过store更新数据，而不是直接赋值给computed属性\n          this.venueStore.setPopularVenues(cached.popularVenues || [])\n          this.bookingStore.setSharingOrders(cached.latestSharingOrders || [])\n          console.log('使用缓存数据')\n          PerformanceMonitor.measure('homeDataLoad')\n          return\n        }\n        \n        this.loading = true\n        \n        // 无论是否登录都加载首页数据\n        // 移除登录检查，允许未登录用户查看首页信息\n        console.log('加载首页数据，无需登录验证')\n        \n        // 设置请求超时时间（5秒）\n        const timeout = new Promise((_, reject) => {\n          setTimeout(() => reject(new Error('请求超时')), 5000)\n        })\n        \n        // 并发请求所有数据，添加超时控制\n        const dataPromise = Promise.allSettled([\n          this.venueStore.getPopularVenues(),\n          this.bookingStore.getSharingOrdersList({ page: 1, pageSize: 3 })\n        ])\n        \n        // 使用Promise.race竞争超时\n        const results = await Promise.race([\n          dataPromise,\n          timeout.then(() => {\n            // 超时时使用缓存或空数据\n            console.warn('请求超时，使用备用数据')\n            return [\n              { status: 'rejected', reason: '请求超时' },\n              { status: 'rejected', reason: '请求超时' }\n            ]\n          })\n        ])\n        \n        // 处理请求结果 - 数据已通过store actions自动更新\n        const [venuesResult, sharingsResult] = results\n        \n        if (venuesResult.status === 'rejected') {\n          console.warn('获取场馆数据失败:', venuesResult.reason)\n          // 确保store中有空数组\n          this.venueStore.setPopularVenues([])\n        }\n\n        if (sharingsResult.status === 'rejected') {\n          console.warn('获取拼场数据失败:', sharingsResult.reason)\n          // 确保store中有空数组\n          this.bookingStore.setSharingOrders([])\n        }\n        \n        // 只有当有数据时才缓存\n        if (this.popularVenues.length > 0 || this.latestSharingOrders.length > 0) {\n          const cacheData = {\n            popularVenues: this.popularVenues,\n            latestSharingOrders: this.latestSharingOrders\n          }\n          CacheManager.set(cacheKey, cacheData, 5 * 60 * 1000) // 5分钟缓存\n        }\n        \n        PerformanceMonitor.measure('homeDataLoad')\n        \n      } catch (error) {\n        console.error('加载首页数据失败:', error)\n        // 检查是否是登录过期错误\n        if (error.code === 'LOGIN_EXPIRED') {\n          console.log('登录已过期，但允许继续浏览首页')\n          // 清空数据但不显示错误\n          this.popularVenues = []\n          this.latestSharingOrders = []\n        } else {\n          uni.showToast({\n            title: '数据加载失败',\n            icon: 'none'\n          })\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n     \n     // 图片加载成功处理\n     onImageLoad(e) {\n       console.log('图片加载成功')\n     },\n     \n     // 图片加载失败处理\n     onImageError(e) {\n       console.log('图片加载失败:', e)\n       // 可以设置默认图片\n     },\n     \n     // 下拉刷新（清除缓存）\n     async refreshData() {\n       try {\n         // 清除缓存\n         CacheManager.remove('homePageData')\n         // 重新加载数据\n         await this.loadHomeDataWithCache()\n       } catch (error) {\n         console.error('刷新数据失败:', error)\n         uni.showToast({\n           title: '刷新失败',\n           icon: 'none'\n         })\n       } finally {\n         uni.stopPullDownRefresh()\n       }\n     },\n     \n     // 初始化数据\n    async initData() {\n      try {\n        await Promise.all([\n          this.venueStore.getPopularVenues(),\n          this.bookingStore.getSharingOrdersList({ page: 1, pageSize: 3 })\n        ])\n      } catch (error) {\n        console.error('初始化数据失败:', error)\n      }\n    },\n    \n    // 刷新数据\n    async refreshData() {\n      try {\n        await this.initData()\n        uni.stopPullDownRefresh()\n      } catch (error) {\n        uni.stopPullDownRefresh()\n        console.error('刷新数据失败:', error)\n      }\n    },\n    \n    // 页面跳转\n    navigateTo(url) {\n      smartNavigate(url)\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      return formatDate(date, 'MM-DD')\n    },\n    \n    // 获取状态样式类\n    getStatusClass(status) {\n      const statusMap = {\n        'AVAILABLE': 'status-available',\n        'MAINTENANCE': 'status-maintenance',\n        'OCCUPIED': 'status-occupied'\n      }\n      return statusMap[status] || 'status-available'\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'AVAILABLE': '可用',\n        'MAINTENANCE': '维护中',\n        'OCCUPIED': '已占用'\n      }\n      return statusMap[status] || '可用'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n// 开发者工具栏\n.dev-toolbar {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20rpx 30rpx;\n  margin-bottom: 20rpx;\n\n  .dev-title {\n    color: white;\n    font-size: 28rpx;\n    font-weight: bold;\n    margin-bottom: 15rpx;\n    display: block;\n  }\n\n  .dev-actions {\n    display: flex;\n    gap: 15rpx;\n    flex-wrap: wrap;\n\n    .dev-btn {\n      background: rgba(255, 255, 255, 0.2);\n      color: white;\n      border: 1rpx solid rgba(255, 255, 255, 0.3);\n      border-radius: 20rpx;\n      padding: 8rpx 16rpx;\n      font-size: 24rpx;\n      backdrop-filter: blur(10rpx);\n\n      &:active {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    }\n  }\n}\n\n// 轮播图\n.banner-section {\n  height: 400rpx;\n  \n  .banner-swiper {\n    height: 100%;\n    \n    .banner-image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n}\n\n// 快捷功能\n.quick-actions {\n  display: flex;\n  flex-wrap: wrap;\n  background-color: #ffffff;\n  padding: 40rpx 30rpx;\n  margin-bottom: 20rpx;\n  gap: 20rpx;\n\n  .action-item {\n    width: calc(25% - 15rpx);\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    \n    .action-icon {\n      width: 80rpx;\n      height: 80rpx;\n      background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 16rpx;\n      font-size: 36rpx;\n    }\n    \n    .action-text {\n      font-size: 24rpx;\n      color: #333333;\n    }\n\n    // Pinia测试按钮特殊样式\n    &.pinia-test-item {\n      .pinia-test-icon {\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\n        animation: pulse 2s infinite;\n      }\n\n      .action-text {\n        color: #667eea;\n        font-weight: bold;\n      }\n    }\n  }\n}\n\n// 脉动动画\n@keyframes pulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);\n  }\n  70% {\n    box-shadow: 0 0 0 10rpx rgba(102, 126, 234, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);\n  }\n}\n\n// 浮动测试按钮\n.floating-test-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 150rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);\n  z-index: 999;\n  animation: pulse 2s infinite;\n\n  .floating-btn-text {\n    color: white;\n    font-size: 40rpx;\n    font-weight: bold;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n// 通用区块\n.section {\n  background-color: #ffffff;\n  margin-bottom: 20rpx;\n  padding: 30rpx;\n  \n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 30rpx;\n    \n    .section-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n    \n    .section-more {\n      font-size: 24rpx;\n      color: #ff6b35;\n    }\n  }\n}\n\n// 场馆列表\n.venue-list {\n  .venue-card {\n    display: flex;\n    margin-bottom: 24rpx;\n    padding: 20rpx;\n    background-color: #f8f8f8;\n    border-radius: 12rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .venue-image {\n      width: 120rpx;\n      height: 120rpx;\n      border-radius: 8rpx;\n      margin-right: 20rpx;\n    }\n    \n    .venue-info {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n      \n      .venue-name {\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #333333;\n        margin-bottom: 8rpx;\n      }\n      \n      .venue-location {\n        font-size: 24rpx;\n        color: #666666;\n        margin-bottom: 16rpx;\n      }\n      \n      .venue-price {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        \n        .price-text {\n          font-size: 26rpx;\n          color: #ff6b35;\n          font-weight: 600;\n        }\n        \n        .venue-status {\n          font-size: 20rpx;\n          padding: 4rpx 12rpx;\n          border-radius: 12rpx;\n          \n          &.status-available {\n            background-color: #e6f7ff;\n            color: #1890ff;\n          }\n          \n          &.status-maintenance {\n            background-color: #fff7e6;\n            color: #fa8c16;\n          }\n          \n          &.status-occupied {\n            background-color: #fff2f0;\n            color: #ff4d4f;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 测试按钮\n.test-button {\n  position: fixed;\n  bottom: 120rpx;\n  right: 30rpx;\n  background-color: rgba(0, 0, 0, 0.6);\n  color: white;\n  padding: 16rpx 24rpx;\n  border-radius: 30rpx;\n  font-size: 24rpx;\n  z-index: 999;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);\n}\n\n// 拼场列表\n.sharing-list {\n  .sharing-card {\n    padding: 24rpx;\n    background-color: #f8f8f8;\n    border-radius: 12rpx;\n    margin-bottom: 20rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .sharing-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16rpx;\n      \n      .sharing-venue {\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #333333;\n      }\n      \n      .sharing-time {\n        font-size: 24rpx;\n        color: #666666;\n      }\n    }\n    \n    .sharing-info {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16rpx;\n      \n      .sharing-team {\n        font-size: 26rpx;\n        color: #333333;\n      }\n      \n      .sharing-participants {\n        font-size: 24rpx;\n        color: #666666;\n      }\n    }\n    \n    .sharing-price {\n      text-align: right;\n      \n      .price-per-person {\n        font-size: 26rpx;\n        color: #ff6b35;\n        font-weight: 600;\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useVenueStore", "useBookingStore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PerformanceMonitor", "uni", "smartNavigate", "formatDate"], "mappings": ";;;;;;;AAoLA,MAAK,iBAAkB,MAAW;AAElC,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EAED,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,QACP;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,gBAAgB;;AACd,eAAO,UAAK,eAAL,mBAAiB,kBAAiB,CAAC;AAAA,IAC3C;AAAA,IAED,yBAAyB;;AACvB,eAAO,UAAK,iBAAL,mBAAmB,qBAAoB,CAAC;AAAA,IAChD;AAAA,IAED,sBAAsB;AAEpB,UAAI,CAAC,KAAK,0BAA0B,CAAC,MAAM,QAAQ,KAAK,sBAAsB,GAAG;AAC/E,eAAO,CAAC;AAAA,MACV;AACA,aAAO,KAAK,uBAAuB,MAAM,GAAG,CAAC;AAAA,IAC9C;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,CAAC,KAAK,iBAAiB,CAAC,MAAM,QAAQ,KAAK,aAAa,GAAG;AAC7D,eAAO,CAAC;AAAA,MACV;AACA,aAAO,KAAK;AAAA,IACd;AAAA,EACD;AAAA,EAED,SAAS;AAEP,SAAK,aAAaA,2BAAc;AAChC,SAAK,eAAeC,+BAAgB;AAGpC,SAAK,sBAAsB;AAAA,EAC5B;AAAA,EAED,oBAAoB;AAClB,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAEP,UAAM,WAAW;AACjB,UAAM,SAASC,kBAAAA,aAAa,IAAI,QAAQ;AAGxC,QAAI,CAAC,QAAQ;AACX,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAGP,MAAM,wBAAwB;AAC5B,YAAM,WAAW;AAEjB,UAAI;AAEFC,0BAAkB,mBAAC,KAAK,cAAc;AAGtC,cAAM,SAASD,kBAAAA,aAAa,IAAI,QAAQ;AACxC,YAAI,QAAQ;AAEV,eAAK,WAAW,iBAAiB,OAAO,iBAAiB,CAAA,CAAE;AAC3D,eAAK,aAAa,iBAAiB,OAAO,uBAAuB,CAAA,CAAE;AACnEE,wBAAAA,MAAY,MAAA,OAAA,gCAAA,QAAQ;AACpBD,4BAAkB,mBAAC,QAAQ,cAAc;AACzC;AAAA,QACF;AAEA,aAAK,UAAU;AAIfC,sBAAAA,MAAY,MAAA,OAAA,gCAAA,eAAe;AAG3B,cAAM,UAAU,IAAI,QAAQ,CAAC,GAAG,WAAW;AACzC,qBAAW,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,GAAG,GAAI;AAAA,SACjD;AAGD,cAAM,cAAc,QAAQ,WAAW;AAAA,UACrC,KAAK,WAAW,iBAAkB;AAAA,UAClC,KAAK,aAAa,qBAAqB,EAAE,MAAM,GAAG,UAAU,GAAG;AAAA,SAChE;AAGD,cAAM,UAAU,MAAM,QAAQ,KAAK;AAAA,UACjC;AAAA,UACA,QAAQ,KAAK,MAAM;AAEjBA,0BAAAA,MAAA,MAAA,QAAA,gCAAa,aAAa;AAC1B,mBAAO;AAAA,cACL,EAAE,QAAQ,YAAY,QAAQ,OAAQ;AAAA,cACtC,EAAE,QAAQ,YAAY,QAAQ,OAAO;AAAA,YACvC;AAAA,WACD;AAAA,SACF;AAGD,cAAM,CAAC,cAAc,cAAc,IAAI;AAEvC,YAAI,aAAa,WAAW,YAAY;AACtCA,4EAAa,aAAa,aAAa,MAAM;AAE7C,eAAK,WAAW,iBAAiB,EAAE;AAAA,QACrC;AAEA,YAAI,eAAe,WAAW,YAAY;AACxCA,wBAAA,MAAA,MAAA,QAAA,gCAAa,aAAa,eAAe,MAAM;AAE/C,eAAK,aAAa,iBAAiB,EAAE;AAAA,QACvC;AAGA,YAAI,KAAK,cAAc,SAAS,KAAK,KAAK,oBAAoB,SAAS,GAAG;AACxE,gBAAM,YAAY;AAAA,YAChB,eAAe,KAAK;AAAA,YACpB,qBAAqB,KAAK;AAAA,UAC5B;AACAF,4BAAY,aAAC,IAAI,UAAU,WAAW,IAAI,KAAK,GAAI;AAAA,QACrD;AAEAC,0BAAkB,mBAAC,QAAQ,cAAc;AAAA,MAEzC,SAAO,OAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAEhC,YAAI,MAAM,SAAS,iBAAiB;AAClCA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,iBAAiB;AAE7B,eAAK,gBAAgB,CAAC;AACtB,eAAK,sBAAsB,CAAC;AAAA,eACvB;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACF,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGA,YAAY,GAAG;AACbA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,QAAQ;AAAA,IACrB;AAAA;AAAA,IAGD,aAAa,GAAG;AACdA,oBAAAA,mDAAY,WAAW,CAAC;AAAA,IAEzB;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI;AAEFF,0BAAY,aAAC,OAAO,cAAc;AAElC,cAAM,KAAK,sBAAsB;AAAA,MACjC,SAAO,OAAO;AACdE,sBAAAA,MAAc,MAAA,SAAA,gCAAA,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACRA,sBAAAA,MAAI,oBAAoB;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAGF,MAAM,WAAW;AACf,UAAI;AACF,cAAM,QAAQ,IAAI;AAAA,UAChB,KAAK,WAAW,iBAAkB;AAAA,UAClC,KAAK,aAAa,qBAAqB,EAAE,MAAM,GAAG,UAAU,GAAG;AAAA,SAChE;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI;AACF,cAAM,KAAK,SAAS;AACpBA,sBAAAA,MAAI,oBAAoB;AAAA,MACxB,SAAO,OAAO;AACdA,sBAAAA,MAAI,oBAAoB;AACxBA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,KAAK;AACdC,uBAAAA,cAAc,GAAG;AAAA,IAClB;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,aAAOC,cAAU,WAAC,MAAM,OAAO;AAAA,IAChC;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,YAAY;AAAA,QAChB,aAAa;AAAA,QACb,eAAe;AAAA,QACf,YAAY;AAAA,MACd;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,aAAa;AAAA,QACb,eAAe;AAAA,QACf,YAAY;AAAA,MACd;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtbA,GAAG,WAAW,eAAe;"}