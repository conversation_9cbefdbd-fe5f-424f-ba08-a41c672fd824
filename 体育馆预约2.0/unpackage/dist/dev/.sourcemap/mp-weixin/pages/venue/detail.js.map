{"version": 3, "file": "detail.js", "sources": ["pages/venue/detail.vue", "pages/venue/detail.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <text>加载中...</text>\n    </view>\n    \n    <!-- 场馆详情内容 -->\n    <view v-else-if=\"venueDetail\" class=\"detail-content\">\n      <!-- 场馆图片轮播 -->\n      <view class=\"image-section\">\n        <swiper class=\"venue-swiper\" indicator-dots circular>\n          <swiper-item v-for=\"(image, index) in venueImages\" :key=\"index\">\n            <image :src=\"image\" class=\"venue-image\" mode=\"aspectFill\" />\n          </swiper-item>\n        </swiper>\n        <view class=\"back-btn\" @click=\"goBack\">\n          <text>←</text>\n        </view>\n      </view>\n    \n    <!-- 场馆基本信息 -->\n    <view class=\"info-section\">\n      <view class=\"venue-header\">\n        <text class=\"venue-name\">{{ venueDetail.name }}</text>\n        <view class=\"venue-rating\">\n          <text class=\"rating-score\">{{ venueDetail.rating || '暂无评分' }}</text>\n          <text class=\"rating-star\" v-if=\"venueDetail.rating\">⭐</text>\n          <text class=\"rating-count\" v-if=\"venueDetail.reviewCount\">({{ venueDetail.reviewCount }}条评价)</text>\n        </view>\n      </view>\n\n      <view class=\"venue-location\">\n        <text class=\"location-icon\">📍</text>\n        <text class=\"location-text\">{{ venueDetail.location }}</text>\n        <text class=\"distance-text\" v-if=\"venueDetail.distance\">距离{{ venueDetail.distance }}km</text>\n      </view>\n      \n      <view class=\"venue-price\">\n        <text class=\"price-label\">价格：</text>\n        <text class=\"price-value\">¥{{ venueDetail.price }}</text>\n        <text class=\"price-unit\">/小时</text>\n      </view>\n      \n      <view class=\"venue-tags\">\n        <text class=\"venue-tag\">{{ venueDetail.type }}</text>\n        <text v-if=\"venueDetail.supportSharing\" class=\"venue-tag\">支持拼场</text>\n        <text class=\"venue-tag\">{{ venueDetail.status === 'ACTIVE' ? '营业中' : '暂停营业' }}</text>\n      </view>\n    </view>\n    \n    <!-- 场馆描述 -->\n    <view class=\"description-section\">\n      <view class=\"section-title\">场馆介绍</view>\n      <text class=\"description-text\">{{ venueDetail.description }}</text>\n    </view>\n    \n    <!-- 设施信息 -->\n    <view class=\"facilities-section\">\n      <view class=\"section-title\">设施服务</view>\n      <view class=\"facilities-grid\">\n        <view\n          v-for=\"facility in facilitiesList\"\n          :key=\"facility\"\n          class=\"facility-item\"\n        >\n          <text class=\"facility-icon\">🏃</text>\n          <text class=\"facility-name\">{{ facility }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 营业时间 -->\n    <view class=\"hours-section\">\n      <view class=\"section-title\">营业时间</view>\n      <view class=\"hours-info\">\n        <text class=\"hours-text\">{{ formatOpeningHours }}</text>\n      </view>\n    </view>\n    \n      <!-- 预约类型选择 -->\n      <view class=\"booking-type-section\" v-if=\"venueDetail.supportSharing\">\n        <view class=\"section-title\">预约类型</view>\n        <view class=\"booking-type-options\">\n          <label class=\"radio-item\" @click=\"onBookingTypeChange('EXCLUSIVE')\">\n            <view class=\"radio-wrapper\">\n              <view class=\"radio-circle\" :class=\"{ active: bookingType === 'EXCLUSIVE' }\">\n                <view class=\"radio-dot\" v-if=\"bookingType === 'EXCLUSIVE'\"></view>\n              </view>\n              <view class=\"radio-content\">\n                <text class=\"radio-title\">独享预约</text>\n                <text class=\"radio-desc\">包场使用，享受完整场地</text>\n              </view>\n            </view>\n          </label>\n          \n          <label class=\"radio-item\" @click=\"onBookingTypeChange('SHARED')\">\n            <view class=\"radio-wrapper\">\n              <view class=\"radio-circle\" :class=\"{ active: bookingType === 'SHARED' }\">\n                <view class=\"radio-dot\" v-if=\"bookingType === 'SHARED'\"></view>\n              </view>\n              <view class=\"radio-content\">\n                <text class=\"radio-title\">拼场预约</text>\n                <text class=\"radio-desc\">与他人共享场地，费用更优惠</text>\n              </view>\n            </view>\n          </label>\n        </view>\n        \n        <!-- 拼场提示信息 -->\n        <view class=\"shared-notice\" v-if=\"bookingType === 'SHARED'\">\n          <!-- 拼场说明 -->\n          <view class=\"sharing-notice\">\n            <text class=\"notice-title\">拼场说明：</text>\n            <text class=\"notice-text\">• 拼场预约需要等待其他用户加入</text>\n            <text class=\"notice-text\">• 如果预约时间前2小时内无人加入，系统将自动退款</text>\n            <text class=\"notice-text\">• 请确保联系方式准确，便于其他用户联系</text>\n          </view>\n          \n          <!-- 时间限制提示 -->\n          <view class=\"time-notice\">\n            <text class=\"notice-text\">⏰ 拼场预约需要提前3小时预约</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 时间段选择 -->\n      <view class=\"timeslot-section\">\n        <view class=\"section-title\">选择时间</view>\n      \n      <!-- 日期选择 -->\n      <view class=\"date-selector\">\n        <scroll-view class=\"date-scroll\" scroll-x>\n          <view \n            v-for=\"(date, index) in availableDates\" \n            :key=\"index\" \n            class=\"date-item\"\n            :class=\"{ active: selectedDate === date.value }\"\n            @click=\"selectDate(date.value)\"\n          >\n            <text class=\"date-day\">{{ date.day }}</text>\n            <text class=\"date-date\">{{ date.date }}</text>\n          </view>\n        </scroll-view>\n      </view>\n      \n      <!-- 时间段列表 -->\n      <view class=\"timeslot-list\">\n        <view \n          v-for=\"slot in filteredTimeSlots\" \n          :key=\"slot.id\" \n          :class=\"getSlotClass(slot)\"\n          @click=\"selectTimeSlot(slot)\"\n        >\n          <view class=\"slot-time\">\n            <text>{{ slot.startTime }} - {{ slot.endTime }}</text>\n          </view>\n          <view class=\"slot-price\">\n            <text>¥{{ slot.price }}</text>\n          </view>\n          <view class=\"slot-status\">\n            <text>{{ getSlotStatusText(slot.status) }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-actions\">\n      <view class=\"action-left\">\n        <view class=\"contact-btn\" @click=\"contactVenue\">\n          <text class=\"contact-icon\">📞</text>\n          <text class=\"contact-text\">联系场馆</text>\n        </view>\n      </view>\n      <view class=\"action-right\">\n        <button \n          class=\"book-btn\" \n          :disabled=\"selectedTimeSlots.length === 0\"\n          @click=\"bookVenue\"\n        >\n          {{ getBookButtonText() }}\n        </button>\n      </view>\n    </view>\n    \n    <!-- 预约确认弹窗 -->\n    <uni-popup ref=\"bookingPopup\" type=\"center\">\n      <view class=\"booking-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">确认预约</text>\n        </view>\n        \n        <view class=\"booking-info\">\n          <!-- 基本信息 -->\n          <view class=\"info-section\">\n            <view class=\"section-title\">预约信息</view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">场馆：</text>\n              <text class=\"info-value\">{{ venueDetail.name }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">地址：</text>\n              <text class=\"info-value\">{{ venueDetail.location }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">日期：</text>\n              <text class=\"info-value\">{{ formatSelectedDate() }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">时间：</text>\n              <text class=\"info-value\" v-if=\"selectedTimeSlots.length === 1\">{{ selectedTimeSlots[0]?.startTime }} - {{ selectedTimeSlots[0]?.endTime }}</text>\n              <text class=\"info-value\" v-else-if=\"selectedTimeSlots.length > 1\">{{ getFirstTimeSlot()?.startTime }} - {{ getLastTimeSlot()?.endTime }}</text>\n              <text class=\"info-value\" v-else>未选择</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">时长：</text>\n              <text class=\"info-value\">{{ getBookingDuration() }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">预约类型：</text>\n              <text class=\"info-value\">{{ bookingType === 'EXCLUSIVE' ? '独享预约' : '拼场预约' }}</text>\n            </view>\n          </view>\n          \n          <!-- 拼场信息 -->\n          <view class=\"info-section\" v-if=\"bookingType === 'SHARED'\">\n            <view class=\"section-title\">拼场信息</view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">队伍名称：</text>\n              <text class=\"info-value\">{{ teamName }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">联系方式：</text>\n              <text class=\"info-value\">{{ contactInfo }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">最大人数：</text>\n              <text class=\"info-value\">{{ maxParticipants }}人</text>\n            </view>\n          </view>\n          \n          <!-- 费用明细 -->\n          <view class=\"info-section\">\n            <view class=\"section-title\">费用明细</view>\n            <view class=\"info-item\" v-for=\"(slot, index) in selectedTimeSlots\" :key=\"index\">\n              <text class=\"info-label\">{{ slot.startTime }}-{{ slot.endTime }}：</text>\n              <text class=\"info-value\">¥{{ slot.price }}</text>\n            </view>\n            <view class=\"info-item total-price\">\n              <text class=\"info-label\">总计：</text>\n              <text class=\"info-value price\">¥{{ getTotalPrice() }}</text>\n            </view>\n          </view>\n          \n          <!-- 联系信息 -->\n          <view class=\"info-section\" v-if=\"venueDetail.phone\">\n            <view class=\"section-title\">联系方式</view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">场馆电话：</text>\n              <text class=\"info-value\">{{ venueDetail.phone }}</text>\n            </view>\n          </view>\n          \n          <!-- 预约须知 -->\n          <view class=\"info-section\">\n            <view class=\"section-title\">预约须知</view>\n            <view class=\"notice-list\">\n              <text class=\"notice-item\">• 请提前15分钟到达场馆</text>\n              <text class=\"notice-item\">• 请携带有效身份证件</text>\n              <text class=\"notice-item\">• 如需取消请提前2小时联系场馆</text>\n              <text class=\"notice-item\">• 请遵守场馆相关规定</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"modal-actions\">\n          <button class=\"cancel-btn\" @click=\"closeBookingModal\">取消</button>\n          <button class=\"confirm-btn\" @click=\"confirmBooking\">确认预约</button>\n        </view>\n      </view>\n      </uni-popup>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else class=\"error-container\">\n      <text>加载失败，请重试</text>\n      <button @click=\"initData\" class=\"retry-btn\">重试</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useVenueStore } from '@/stores/venue.js'\nimport { useBookingStore } from '@/stores/booking.js'\nimport { formatDate } from '@/utils/helpers.js'\n\nexport default {\n  name: 'VenueDetail',\n\n  data() {\n    return {\n      venueStore: null,\n      bookingStore: null,\n      venueId: '',\n      selectedDate: '',\n      selectedTimeSlots: [], // 改为数组以支持多时间段选择\n      availableDates: [],\n      bookingType: 'EXCLUSIVE', // 预约类型：EXCLUSIVE(独享) 或 SHARED(拼场)\n      teamName: '', // 拼场队伍名称\n      contactInfo: '', // 拼场联系方式\n      maxParticipants: 4 // 最大参与人数\n    }\n  },\n  \n  computed: {\n    venueDetail() {\n      return this.venueStore?.venueDetailGetter || {}\n    },\n\n    timeSlots() {\n      return this.venueStore?.timeSlotsGetter || []\n    },\n\n    loading() {\n      return this.venueStore?.isLoading || false\n    },\n\n    // 处理场馆图片\n    venueImages() {\n      if (this.venueDetail.image) {\n        // 如果image是字符串，转换为数组\n        if (typeof this.venueDetail.image === 'string') {\n          return [this.venueDetail.image]\n        }\n        // 如果已经是数组，直接返回\n        if (Array.isArray(this.venueDetail.image)) {\n          return this.venueDetail.image\n        }\n      }\n      // 默认图片\n      return ['/static/default-venue.jpg']\n    },\n\n    // 处理设施列表\n    facilitiesList() {\n      if (this.venueDetail.facilities) {\n        // 如果facilities是字符串，按逗号分割\n        if (typeof this.venueDetail.facilities === 'string') {\n          return this.venueDetail.facilities.split(',').map(f => f.trim()).filter(f => f)\n        }\n        // 如果已经是数组，直接返回\n        if (Array.isArray(this.venueDetail.facilities)) {\n          return this.venueDetail.facilities\n        }\n      }\n      return []\n    },\n\n    // 格式化营业时间\n    formatOpeningHours() {\n      if (this.venueDetail.openTime && this.venueDetail.closeTime) {\n        return `${this.venueDetail.openTime} - ${this.venueDetail.closeTime}`\n      }\n      return '营业时间待更新'\n    },\n\n    // 过滤掉已过期的时间段\n    filteredTimeSlots() {\n      console.log('原始时间段数据:', this.timeSlots)\n\n      // 确保timeSlots是数组\n      if (!Array.isArray(this.timeSlots)) {\n        console.warn('timeSlots不是数组:', this.timeSlots)\n        return []\n      }\n\n      const now = new Date()\n      const currentTime = now.getHours() * 60 + now.getMinutes() // 当前时间转换为分钟\n      const selectedDate = this.selectedDate\n      const today = new Date().toISOString().split('T')[0] // 今天的日期 YYYY-MM-DD\n\n      const slots = this.timeSlots.filter(slot => {\n        // 确保slot是对象且有必要的属性\n        if (!slot || typeof slot !== 'object') {\n          console.warn('无效的时间段对象:', slot)\n          return false\n        }\n\n        // 过滤掉状态为EXPIRED的时间段\n        if (slot.status === 'EXPIRED') {\n          return false\n        }\n\n        // 如果选择的是今天，过滤掉已经过去的时间段\n        if (selectedDate === today && slot.startTime) {\n          const slotStartTime = this.getMinutesFromTimeString(slot.startTime)\n          if (slotStartTime <= currentTime) {\n            return false // 已经过去的时间段不显示\n          }\n        }\n\n        return true\n      })\n\n      console.log('过滤后的时间段数据:', slots)\n      return slots\n    }\n  },\n  \n  onLoad(options) {\n    // 初始化Pinia stores\n    this.venueStore = useVenueStore()\n    this.bookingStore = useBookingStore()\n\n    this.venueId = options.id\n    this.initData()\n  },\n  \n  onShow() {\n    // 页面显示时刷新时间段数据，确保取消预约后状态能及时更新\n    if (this.venueId && this.selectedDate) {\n      // 添加延迟确保数据同步\n      setTimeout(() => {\n        this.loadTimeSlots()\n      }, 200)\n    }\n  },\n  \n  onPullDownRefresh() {\n    this.refreshData()\n  },\n  \n  methods: {\n\n    // 将时间字符串转换为分钟数（用于比较）\n    getMinutesFromTimeString(timeStr) {\n      if (!timeStr || typeof timeStr !== 'string') {\n        console.warn('getMinutesFromTimeString: 无效的时间字符串:', timeStr)\n        return 0\n      }\n\n      try {\n        const [hours, minutes] = timeStr.split(':').map(Number)\n        if (isNaN(hours) || isNaN(minutes)) {\n          console.warn('getMinutesFromTimeString: 时间格式错误:', timeStr)\n          return 0\n        }\n        return hours * 60 + minutes\n      } catch (error) {\n        console.error('getMinutesFromTimeString: 解析时间失败:', timeStr, error)\n        return 0\n      }\n    },\n\n    // 初始化数据\n    async initData() {\n      try {\n        console.log('开始初始化数据，场馆ID:', this.venueId)\n        if (!this.venueId) {\n          console.error('场馆ID为空')\n          uni.showToast({\n            title: '参数错误',\n            icon: 'error'\n          })\n          return\n        }\n        \n        console.log('调用getVenueDetail，参数:', this.venueId)\n        await this.venueStore.getVenueDetail(this.venueId)\n        console.log('获取场馆详情成功:', this.venueDetail)\n        \n        this.initDates()\n        if (this.selectedDate) {\n          await this.loadTimeSlots()\n        }\n      } catch (error) {\n        console.error('初始化数据失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 刷新数据\n    async refreshData() {\n      try {\n        await this.initData()\n        uni.stopPullDownRefresh()\n      } catch (error) {\n        uni.stopPullDownRefresh()\n        console.error('刷新数据失败:', error)\n      }\n    },\n    \n    // 初始化可选日期\n    initDates() {\n      const dates = []\n      const today = new Date()\n      \n      for (let i = 0; i < 7; i++) {\n        const date = new Date(today)\n        date.setDate(today.getDate() + i)\n        \n        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\n        const day = i === 0 ? '今天' : i === 1 ? '明天' : dayNames[date.getDay()]\n        \n        dates.push({\n          value: formatDate(date, 'YYYY-MM-DD'),\n          day: day,\n          date: formatDate(date, 'MM/DD')\n        })\n      }\n      \n      this.availableDates = dates\n      this.selectedDate = dates[0].value\n    },\n    \n    // 选择日期\n    async selectDate(date) {\n      this.selectedDate = date\n      this.selectedTimeSlots = []\n      await this.loadTimeSlots()\n    },\n    \n    // 预约类型变化\n    onBookingTypeChange(type) {\n      this.bookingType = type\n      \n      // 清空已选择的时间段，因为过滤条件可能发生变化\n      this.selectedTimeSlots = []\n    },\n    \n    // 加载时间段\n    async loadTimeSlots() {\n      try {\n        await this.venueStore.getVenueTimeSlots({\n          venueId: this.venueId,\n          date: this.selectedDate\n        })\n      } catch (error) {\n        console.error('加载时间段失败:', error)\n      }\n    },\n    \n    // 选择时间段\n    selectTimeSlot(slot) {\n      console.log('点击时间段:', slot)\n      console.log('时间段状态:', slot.status)\n      \n      // 首先检查时间段状态\n      if (slot.status === 'OCCUPIED' || slot.status === 'RESERVED') {\n        uni.showToast({\n          title: '该时间段已被预约',\n          icon: 'none',\n          duration: 2000\n        })\n        return\n      } else if (slot.status === 'MAINTENANCE') {\n        uni.showToast({\n          title: '该时间段维护中',\n          icon: 'none',\n          duration: 2000\n        })\n        return\n      } else if (slot.status === 'EXPIRED') {\n        uni.showToast({\n          title: '该时间段已过期，无法预约',\n          icon: 'none',\n          duration: 2000\n        })\n        return\n      } else if (slot.status === 'AVAILABLE') {\n        // 如果是拼场预约，检查时间限制\n        if (this.bookingType === 'SHARED') {\n          if (!this.isTimeSlotValidForSharing(slot)) {\n            uni.showToast({\n              title: '拼场预约请选择三个小时以后的时间段',\n              icon: 'none',\n              duration: 3000\n            })\n            return\n          }\n        }\n        // 检查时间段是否已被选中\n        const existingIndex = this.selectedTimeSlots.findIndex(item => \n          (item.id && item.id === slot.id) || \n          (item.startTime === slot.startTime && item.endTime === slot.endTime)\n        )\n        \n        // 如果已选中，则取消选择\n        if (existingIndex !== -1) {\n          this.selectedTimeSlots.splice(existingIndex, 1)\n          console.log('取消选择时间段:', slot)\n          uni.showToast({\n            title: '已取消选择',\n            icon: 'success',\n            duration: 1000\n          })\n          return\n        }\n        \n        // 如果已有选择的时间段，检查是否连续\n        if (this.selectedTimeSlots.length > 0) {\n          // 检查与已选时间段是否有连续的\n          const hasConsecutive = this.selectedTimeSlots.some(selectedSlot => \n            this.isConsecutiveTimeSlot(selectedSlot, slot)\n          )\n          \n          if (!hasConsecutive) {\n            // 禁止选择不连续的时间段\n            uni.showToast({\n              title: '只能选择连续的时间段',\n              icon: 'none',\n              duration: 2000\n            })\n            return\n          }\n        }\n        \n        // 添加到已选时间段\n        this.selectedTimeSlots.push(slot)\n        console.log('已选择时间段:', this.selectedTimeSlots)\n        uni.showToast({\n          title: '已选择时间段',\n          icon: 'success',\n          duration: 1000\n        })\n      } else {\n        // 处理未知状态\n        uni.showToast({\n          title: '该时间段不可用',\n          icon: 'none',\n          duration: 2000\n        })\n      }\n    },\n    \n    // 检查两个时间段是否连续\n    isConsecutiveTimeSlot(slot1, slot2) {\n      // 使用已定义的方法将时间转换为分钟数\n      const slot1End = this.getMinutesFromTimeString(slot1.endTime)\n      const slot2Start = this.getMinutesFromTimeString(slot2.startTime)\n      const slot1Start = this.getMinutesFromTimeString(slot1.startTime)\n      const slot2End = this.getMinutesFromTimeString(slot2.endTime)\n      \n      // 检查两个时间段是否相邻\n      return slot1End === slot2Start || slot2End === slot1Start\n    },\n    \n    // 获取时间段样式类\n    getSlotClass(slot) {\n      const classes = ['timeslot-item']\n      \n      if (slot.status === 'OCCUPIED') {\n        classes.push('occupied')\n        classes.push('disabled') // 已预约的时间段添加禁用样式\n      } else if (slot.status === 'RESERVED') {\n        classes.push('occupied')\n        classes.push('disabled') // 已预约的时间段添加禁用样式\n      } else if (slot.status === 'MAINTENANCE') {\n        classes.push('maintenance')\n        classes.push('disabled') // 维护中的时间段添加禁用样式\n      } else if (slot.status === 'EXPIRED') {\n        classes.push('expired')\n        classes.push('disabled') // 已过期的时间段添加禁用样式\n      } \n      \n      // 检查是否是选中的时间段\n      const isSelected = this.selectedTimeSlots.some(selectedSlot => \n        (slot.id && selectedSlot.id === slot.id) || \n        (slot.startTime === selectedSlot.startTime && slot.endTime === selectedSlot.endTime)\n      )\n      \n      if (isSelected) {\n        classes.push('selected')\n        console.log('添加选中样式:', slot)\n      }\n      \n      return classes.join(' ')\n    },\n    \n    // 获取第一个时间段（按开始时间排序）\n    getFirstTimeSlot() {\n      if (this.selectedTimeSlots.length === 0) return null\n      \n      return this.selectedTimeSlots.reduce((earliest, current) => {\n        const earliestTime = this.getMinutesFromTimeString(earliest.startTime)\n        const currentTime = this.getMinutesFromTimeString(current.startTime)\n        return currentTime < earliestTime ? current : earliest\n      }, this.selectedTimeSlots[0])\n    },\n    \n    // 获取最后一个时间段（按结束时间排序）\n    getLastTimeSlot() {\n      if (this.selectedTimeSlots.length === 0) return null\n      \n      return this.selectedTimeSlots.reduce((latest, current) => {\n        const latestTime = this.getMinutesFromTimeString(latest.endTime)\n        const currentTime = this.getMinutesFromTimeString(current.endTime)\n        return currentTime > latestTime ? current : latest\n      }, this.selectedTimeSlots[0])\n    },\n\n    \n    // 检查时间段是否满足拼场预约的时间限制（需要提前3小时）\n    isTimeSlotValidForSharing(slot) {\n      const now = new Date()\n      const selectedDateTime = new Date(`${this.selectedDate} ${slot.startTime}`)\n      \n      // 计算时间差（毫秒）\n      const timeDiff = selectedDateTime.getTime() - now.getTime()\n      \n      // 转换为小时\n      const hoursDiff = timeDiff / (1000 * 60 * 60)\n      \n      // 需要提前3小时以上\n      return hoursDiff >= 3\n    },\n    \n    // 计算总价格\n    getTotalPrice() {\n      if (this.selectedTimeSlots.length === 0) return 0\n      \n      return this.selectedTimeSlots.reduce((total, slot) => {\n        return total + (slot.price || 0)\n      }, 0)\n    },\n    \n    // 获取时间段状态文本\n    getSlotStatusText(status) {\n      const statusMap = {\n        'AVAILABLE': '可预约',\n        'OCCUPIED': '已预约',\n        'RESERVED': '已预约',\n        'MAINTENANCE': '维护中',\n        'EXPIRED': '已过期'\n      }\n      return statusMap[status] || '可预约'\n    },\n    \n    // 获取预约按钮文本\n    getBookButtonText() {\n      if (this.selectedTimeSlots.length === 0) {\n        return '请选择时间段'\n      }\n      \n      return `预约 ${this.selectedTimeSlots.length} 个时间段`\n    },\n    \n    // 预约场馆\n    bookVenue() {\n      if (this.selectedTimeSlots.length === 0) {\n        uni.showToast({\n          title: '请选择时间段',\n          icon: 'none'\n        })\n        return\n      }\n      \n      // 传递所有选中的时间段信息\n      const selectedSlotsData = JSON.stringify(this.selectedTimeSlots)\n      \n      // 跳转到预约页面，传递预约类型参数和所有选中的时间段\n      uni.navigateTo({\n        url: `/pages/booking/create?venueId=${this.venueDetail.id}&date=${this.selectedDate}&bookingType=${this.bookingType}&selectedSlots=${encodeURIComponent(selectedSlotsData)}`\n      })\n    },\n    \n    // 关闭预约弹窗\n    closeBookingModal() {\n      this.$refs.bookingPopup.close()\n    },\n    \n    // 确认预约\n    async confirmBooking() {\n      try {\n        if (this.selectedTimeSlots.length === 0) {\n          uni.showToast({\n            title: '请选择时间段',\n            icon: 'none'\n          })\n          return\n        }\n        \n\n        \n        uni.showLoading({ title: '预约中...' })\n        \n        // 创建单个预约，包含所有选中的时间段\n        // 按时间顺序排序选中的时间段\n        const sortedSlots = [...this.selectedTimeSlots].sort((a, b) => {\n          const aTime = this.getMinutesFromTimeString(a.startTime)\n          const bTime = this.getMinutesFromTimeString(b.startTime)\n          return aTime - bTime\n        })\n        \n        const startTime = sortedSlots[0].startTime\n        const endTime = sortedSlots[sortedSlots.length - 1].endTime\n        const slotIds = this.selectedTimeSlots.map(slot => slot.id)\n        \n        // 构建预约数据\n        const bookingData = {\n          venueId: this.venueId,\n          date: this.selectedDate,\n          startTime: startTime,\n          endTime: endTime,\n          slotIds: slotIds, // 传递所有时间段ID\n          remark: this.selectedTimeSlots.length > 1 ? `连续预约${this.selectedTimeSlots.length}个时间段` : '',\n          bookingType: this.bookingType\n        }\n        \n\n        \n        await this.bookingStore.createBooking(bookingData)\n        \n        // 重新获取时间段数据以刷新状态\n        await this.venueStore.getVenueTimeSlots({\n          venueId: this.venueId,\n          date: this.selectedDate\n        })\n        \n        uni.hideLoading()\n        this.closeBookingModal()\n        \n        uni.showToast({\n          title: '预约成功',\n          icon: 'success'\n        })\n        \n        // 清空选中的时间段\n        this.selectedTimeSlots = []\n        \n        // 跳转到我的预约页面\n        setTimeout(() => {\n          uni.switchTab({\n            url: '/pages/booking/list'\n          })\n        }, 1500)\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('预约失败:', error)\n        uni.showToast({\n          title: error.message || '预约失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 联系场馆\n    contactVenue() {\n      if (this.venueDetail.phone) {\n        uni.makePhoneCall({\n          phoneNumber: this.venueDetail.phone\n        })\n      } else {\n        uni.showToast({\n          title: '暂无联系方式',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 返回上一页\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    // 格式化选中日期\n    formatSelectedDate() {\n      const selectedDateObj = this.availableDates.find(d => d.value === this.selectedDate)\n      return selectedDateObj ? `${selectedDateObj.day} ${selectedDateObj.date}` : this.selectedDate\n    },\n    \n    // 计算预约时长\n    getBookingDuration() {\n      if (this.selectedTimeSlots.length === 0) {\n        return '0小时'\n      }\n      \n      if (this.selectedTimeSlots.length === 1) {\n        const slot = this.selectedTimeSlots[0]\n        const startMinutes = this.getMinutesFromTimeString(slot.startTime)\n        const endMinutes = this.getMinutesFromTimeString(slot.endTime)\n        const durationMinutes = endMinutes - startMinutes\n        const hours = Math.floor(durationMinutes / 60)\n        const minutes = durationMinutes % 60\n        \n        if (minutes === 0) {\n          return `${hours}小时`\n        } else {\n          return `${hours}小时${minutes}分钟`\n        }\n      } else {\n        // 多个时间段的情况\n        const firstSlot = this.getFirstTimeSlot()\n        const lastSlot = this.getLastTimeSlot()\n        \n        if (firstSlot && lastSlot) {\n          const startMinutes = this.getMinutesFromTimeString(firstSlot.startTime)\n          const endMinutes = this.getMinutesFromTimeString(lastSlot.endTime)\n          const durationMinutes = endMinutes - startMinutes\n          const hours = Math.floor(durationMinutes / 60)\n          const minutes = durationMinutes % 60\n          \n          if (minutes === 0) {\n            return `${hours}小时`\n          } else {\n            return `${hours}小时${minutes}分钟`\n          }\n        }\n        \n        return '0小时'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n// 图片轮播\n.image-section {\n  position: relative;\n  height: 500rpx;\n  \n  .venue-swiper {\n    height: 100%;\n    \n    .venue-image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  \n  .back-btn {\n    position: absolute;\n    top: 60rpx;\n    left: 30rpx;\n    width: 60rpx;\n    height: 60rpx;\n    background-color: rgba(0, 0, 0, 0.5);\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #ffffff;\n    font-size: 32rpx;\n    z-index: 10;\n  }\n}\n\n// 基本信息\n.info-section {\n  background-color: #ffffff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .venue-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    margin-bottom: 20rpx;\n    \n    .venue-name {\n      flex: 1;\n      font-size: 36rpx;\n      font-weight: 600;\n      color: #333333;\n      margin-right: 20rpx;\n    }\n    \n    .venue-rating {\n      display: flex;\n      align-items: center;\n      \n      .rating-score {\n        font-size: 28rpx;\n        color: #ff6b35;\n        margin-right: 8rpx;\n      }\n      \n      .rating-star {\n        font-size: 24rpx;\n        margin-right: 8rpx;\n      }\n      \n      .rating-count {\n        font-size: 24rpx;\n        color: #999999;\n      }\n    }\n  }\n  \n  .venue-location {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20rpx;\n    \n    .location-icon {\n      font-size: 24rpx;\n      margin-right: 8rpx;\n    }\n    \n    .location-text {\n      flex: 1;\n      font-size: 28rpx;\n      color: #666666;\n    }\n    \n    .distance-text {\n      font-size: 24rpx;\n      color: #999999;\n    }\n  }\n  \n  .venue-price {\n    display: flex;\n    align-items: baseline;\n    margin-bottom: 20rpx;\n    \n    .price-label {\n      font-size: 28rpx;\n      color: #333333;\n    }\n    \n    .price-value {\n      font-size: 36rpx;\n      font-weight: 600;\n      color: #ff6b35;\n      margin: 0 8rpx;\n    }\n    \n    .price-unit {\n      font-size: 24rpx;\n      color: #999999;\n    }\n  }\n  \n  .venue-tags {\n    display: flex;\n    flex-wrap: wrap;\n    \n    .venue-tag {\n      font-size: 22rpx;\n      color: #666666;\n      background-color: #f0f0f0;\n      padding: 8rpx 16rpx;\n      border-radius: 16rpx;\n      margin-right: 16rpx;\n      margin-bottom: 12rpx;\n    }\n  }\n}\n\n// 通用区块样式\n.description-section,\n.facilities-section,\n.hours-section {\n  background-color: #ffffff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333333;\n    margin-bottom: 20rpx;\n  }\n}\n\n// 描述\n.description-text {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.6;\n}\n\n// 设施\n.facilities-grid {\n  display: flex;\n  flex-wrap: wrap;\n  \n  .facility-item {\n    width: 25%;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    margin-bottom: 30rpx;\n    \n    .facility-icon {\n      font-size: 40rpx;\n      margin-bottom: 12rpx;\n    }\n    \n    .facility-name {\n      font-size: 24rpx;\n      color: #666666;\n      text-align: center;\n    }\n  }\n}\n\n// 营业时间\n.hours-info {\n  .hours-text {\n    font-size: 28rpx;\n    color: #666666;\n  }\n}\n\n// 时间段选择\n.timeslot-section {\n  background-color: #ffffff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333333;\n    margin-bottom: 30rpx;\n  }\n  \n  // 日期选择\n  .date-selector {\n    margin-bottom: 30rpx;\n    \n    .date-scroll {\n      white-space: nowrap;\n      \n      .date-item {\n        display: inline-block;\n        text-align: center;\n        padding: 20rpx 30rpx;\n        margin-right: 20rpx;\n        background-color: #f5f5f5;\n        border-radius: 12rpx;\n        min-width: 120rpx;\n        \n        &.active {\n          background-color: #ff6b35;\n          color: #ffffff;\n        }\n        \n        .date-day {\n          display: block;\n          font-size: 24rpx;\n          margin-bottom: 8rpx;\n        }\n        \n        .date-date {\n          display: block;\n          font-size: 28rpx;\n          font-weight: 600;\n        }\n      }\n    }\n  }\n  \n  // 时间段列表\n  .timeslot-list {\n    .timeslot-item {\n      display: flex;\n      align-items: center;\n      padding: 24rpx;\n      margin-bottom: 16rpx;\n      background-color: #f8f8f8;\n      border-radius: 12rpx;\n      border: 2rpx solid transparent;\n      position: relative; // 为禁用遮罩层提供定位基准\n      \n      &.selected {\n        background-color: #fff7f0;\n        border-color: #ff6b35;\n      }\n      \n      &.occupied {\n        background-color: #f5f5f5;\n        opacity: 0.6;\n        cursor: not-allowed;\n        \n        .slot-status {\n          color: #999999;\n          font-weight: 500;\n        }\n        \n        .slot-time {\n          color: #999999;\n        }\n        \n        .slot-price {\n          color: #cccccc;\n        }\n      }\n      \n      &.maintenance {\n        background-color: #fff7e6;\n        opacity: 0.8;\n        cursor: not-allowed;\n        \n        .slot-status {\n          color: #ff9500;\n          font-weight: 500;\n        }\n      }\n      \n      &.expired {\n        background-color: #f0f0f0;\n        opacity: 0.5;\n        cursor: not-allowed;\n        \n        .slot-status {\n          color: #999999;\n          font-weight: 500;\n        }\n        \n        .slot-time {\n          color: #cccccc;\n          text-decoration: line-through;\n        }\n        \n        .slot-price {\n          color: #cccccc;\n          text-decoration: line-through;\n        }\n      }\n      \n      &.disabled {\n        pointer-events: none; // 禁用点击事件\n        \n        &::after {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background-color: rgba(255, 255, 255, 0.3);\n          border-radius: 12rpx;\n        }\n      }\n      \n      .slot-time {\n        flex: 1;\n        font-size: 28rpx;\n        color: #333333;\n        font-weight: 500;\n      }\n      \n      .slot-price {\n        margin-right: 30rpx;\n        font-size: 28rpx;\n        color: #ff6b35;\n        font-weight: 600;\n      }\n      \n      .slot-status {\n        font-size: 24rpx;\n        color: #666666;\n      }\n    }\n  }\n  \n  // 预约类型选择\n  .booking-type-section {\n    margin-bottom: 32rpx;\n    \n    .booking-type-options {\n      margin-top: 24rpx;\n    }\n    \n    .radio-item {\n      display: block;\n      margin-bottom: 24rpx;\n      \n      .radio-wrapper {\n        display: flex;\n        align-items: flex-start;\n        padding: 24rpx;\n        background-color: #f8f8f8;\n        border-radius: 12rpx;\n        border: 2rpx solid transparent;\n        transition: all 0.3s ease;\n        \n        &:active {\n          background-color: #f0f0f0;\n        }\n      }\n      \n      .radio-circle {\n        width: 40rpx;\n        height: 40rpx;\n        border: 2rpx solid #ddd;\n        border-radius: 50%;\n        margin-right: 24rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        margin-top: 4rpx;\n        \n        &.active {\n          border-color: #ff6b35;\n          background-color: #ff6b35;\n        }\n        \n        .radio-dot {\n          width: 16rpx;\n          height: 16rpx;\n          background-color: white;\n          border-radius: 50%;\n        }\n      }\n      \n      .radio-content {\n        flex: 1;\n        \n        .radio-title {\n          display: block;\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #333;\n          margin-bottom: 8rpx;\n        }\n        \n        .radio-desc {\n          display: block;\n          font-size: 26rpx;\n          color: #666;\n          line-height: 1.4;\n        }\n      }\n    }\n    \n    // 拼场表单\n    .shared-form {\n      margin-top: 32rpx;\n      padding: 32rpx;\n      background-color: #f8f9fa;\n      border-radius: 16rpx;\n      \n      .form-item {\n        margin-bottom: 32rpx;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n        \n        .item-label {\n          display: block;\n          font-size: 28rpx;\n          color: #333;\n          margin-bottom: 16rpx;\n          font-weight: 500;\n        }\n        \n        .form-input {\n          width: 100%;\n          padding: 24rpx;\n          background-color: white;\n          border: 2rpx solid #e0e0e0;\n          border-radius: 12rpx;\n          font-size: 28rpx;\n          color: #333;\n          \n          &:focus {\n            border-color: #ff6b35;\n          }\n        }\n        \n        .picker-text {\n          padding: 24rpx;\n          background-color: white;\n          border: 2rpx solid #e0e0e0;\n          border-radius: 12rpx;\n          font-size: 28rpx;\n          color: #333;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          \n          &::after {\n            content: '>';\n            color: #999;\n            font-size: 24rpx;\n          }\n        }\n      }\n      \n      // 拼场说明\n      .sharing-notice {\n        background-color: #fff3e0;\n        border: 2rpx solid #ffcc80;\n        border-radius: 12rpx;\n        padding: 24rpx;\n        margin-bottom: 24rpx;\n        \n        .notice-title {\n          display: block;\n          font-size: 28rpx;\n          font-weight: 600;\n          color: #e65100;\n          margin-bottom: 16rpx;\n        }\n        \n        .notice-text {\n          display: block;\n          font-size: 26rpx;\n          color: #bf360c;\n          line-height: 1.5;\n          margin-bottom: 8rpx;\n          \n          &:last-child {\n            margin-bottom: 0;\n          }\n        }\n      }\n      \n      // 时间限制提示\n      .time-notice {\n        background-color: #e3f2fd;\n        border: 2rpx solid #90caf9;\n        border-radius: 12rpx;\n        padding: 20rpx 24rpx;\n        \n        .notice-text {\n          font-size: 26rpx;\n          color: #1565c0;\n          line-height: 1.4;\n        }\n      }\n    }\n  }\n}\n</style>\n// 底部操作栏\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n  z-index: 100;\n  \n  .action-left {\n    margin-right: 20rpx;\n    \n    .contact-btn {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      padding: 16rpx;\n      \n      .contact-icon {\n        font-size: 32rpx;\n        margin-bottom: 8rpx;\n      }\n      \n      .contact-text {\n        font-size: 20rpx;\n        color: #666666;\n      }\n    }\n  }\n  \n  .action-right {\n    flex: 1;\n    \n    .book-btn {\n      width: 100%;\n      height: 80rpx;\n      background-color: #ff6b35;\n      color: #ffffff;\n      border: none;\n      border-radius: 8rpx;\n      font-size: 32rpx;\n      font-weight: 600;\n      \n      &:disabled {\n        background-color: #cccccc;\n        color: #999999;\n      }\n    }\n  }\n}\n\n// 预约弹窗\n.booking-modal {\n  width: 600rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  \n  .modal-header {\n    padding: 30rpx;\n    text-align: center;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n  }\n  \n  .booking-info {\n    padding: 30rpx;\n    \n    .info-item {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 20rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .info-label {\n        font-size: 28rpx;\n        color: #666666;\n      }\n      \n      .info-value {\n        font-size: 28rpx;\n        color: #333333;\n        \n        &.price {\n          color: #ff6b35;\n          font-weight: 600;\n        }\n      }\n    }\n  }\n  \n  .modal-actions {\n    display: flex;\n    border-top: 1rpx solid #f0f0f0;\n    \n    .cancel-btn,\n    .confirm-btn {\n      flex: 1;\n      height: 100rpx;\n      border: none;\n      font-size: 28rpx;\n    }\n    \n    .cancel-btn {\n      background-color: #f5f5f5;\n      color: #666666;\n    }\n    \n    .confirm-btn {\n      background-color: #ff6b35;\n      color: #ffffff;\n    }\n  }\n}\n\n/* 加载状态样式 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n  font-size: 32rpx;\n  color: #666666;\n}\n\n/* 错误状态样式 */\n.error-container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n  \n  text {\n    font-size: 32rpx;\n    color: #666666;\n    margin-bottom: 40rpx;\n  }\n  \n  .retry-btn {\n    background-color: #ff6b35;\n    color: white;\n    border: none;\n    border-radius: 10rpx;\n    padding: 20rpx 40rpx;\n    font-size: 28rpx;\n  }\n}\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/venue/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "useVenueStore", "useBookingStore", "formatDate"], "mappings": ";;;;;AAySA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,SAAS;AAAA,MACT,cAAc;AAAA,MACd,mBAAmB,CAAE;AAAA;AAAA,MACrB,gBAAgB,CAAE;AAAA,MAClB,aAAa;AAAA;AAAA,MACb,UAAU;AAAA;AAAA,MACV,aAAa;AAAA;AAAA,MACb,iBAAiB;AAAA;AAAA,IACnB;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,cAAc;;AACZ,eAAO,UAAK,eAAL,mBAAiB,sBAAqB,CAAC;AAAA,IAC/C;AAAA,IAED,YAAY;;AACV,eAAO,UAAK,eAAL,mBAAiB,oBAAmB,CAAC;AAAA,IAC7C;AAAA,IAED,UAAU;;AACR,eAAO,UAAK,eAAL,mBAAiB,cAAa;AAAA,IACtC;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,KAAK,YAAY,OAAO;AAE1B,YAAI,OAAO,KAAK,YAAY,UAAU,UAAU;AAC9C,iBAAO,CAAC,KAAK,YAAY,KAAK;AAAA,QAChC;AAEA,YAAI,MAAM,QAAQ,KAAK,YAAY,KAAK,GAAG;AACzC,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAAA,MACF;AAEA,aAAO,CAAC,2BAA2B;AAAA,IACpC;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,KAAK,YAAY,YAAY;AAE/B,YAAI,OAAO,KAAK,YAAY,eAAe,UAAU;AACnD,iBAAO,KAAK,YAAY,WAAW,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,KAAM,CAAA,EAAE,OAAO,OAAK,CAAC;AAAA,QAChF;AAEA,YAAI,MAAM,QAAQ,KAAK,YAAY,UAAU,GAAG;AAC9C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACT;AAAA;AAAA,IAGD,qBAAqB;AACnB,UAAI,KAAK,YAAY,YAAY,KAAK,YAAY,WAAW;AAC3D,eAAO,GAAG,KAAK,YAAY,QAAQ,MAAM,KAAK,YAAY,SAAS;AAAA,MACrE;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,oBAAoB;AAClBA,oBAAY,MAAA,MAAA,OAAA,iCAAA,YAAY,KAAK,SAAS;AAGtC,UAAI,CAAC,MAAM,QAAQ,KAAK,SAAS,GAAG;AAClCA,sBAAa,MAAA,MAAA,QAAA,iCAAA,kBAAkB,KAAK,SAAS;AAC7C,eAAO,CAAC;AAAA,MACV;AAEA,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,cAAc,IAAI,SAAW,IAAE,KAAK,IAAI,WAAa;AAC3D,YAAM,eAAe,KAAK;AAC1B,YAAM,SAAQ,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAEnD,YAAM,QAAQ,KAAK,UAAU,OAAO,UAAQ;AAE1C,YAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrCA,wBAAAA,MAAa,MAAA,QAAA,iCAAA,aAAa,IAAI;AAC9B,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,WAAW,WAAW;AAC7B,iBAAO;AAAA,QACT;AAGA,YAAI,iBAAiB,SAAS,KAAK,WAAW;AAC5C,gBAAM,gBAAgB,KAAK,yBAAyB,KAAK,SAAS;AAClE,cAAI,iBAAiB,aAAa;AAChC,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,eAAO;AAAA,OACR;AAEDA,oBAAAA,oDAAY,cAAc,KAAK;AAC/B,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AAEd,SAAK,aAAaC,2BAAc;AAChC,SAAK,eAAeC,+BAAgB;AAEpC,SAAK,UAAU,QAAQ;AACvB,SAAK,SAAS;AAAA,EACf;AAAA,EAED,SAAS;AAEP,QAAI,KAAK,WAAW,KAAK,cAAc;AAErC,iBAAW,MAAM;AACf,aAAK,cAAc;AAAA,MACpB,GAAE,GAAG;AAAA,IACR;AAAA,EACD;AAAA,EAED,oBAAoB;AAClB,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA;AAAA,IAGP,yBAAyB,SAAS;AAChC,UAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3CF,sBAAAA,MAAA,MAAA,QAAA,iCAAa,uCAAuC,OAAO;AAC3D,eAAO;AAAA,MACT;AAEA,UAAI;AACF,cAAM,CAAC,OAAO,OAAO,IAAI,QAAQ,MAAM,GAAG,EAAE,IAAI,MAAM;AACtD,YAAI,MAAM,KAAK,KAAK,MAAM,OAAO,GAAG;AAClCA,wBAAAA,MAAA,MAAA,QAAA,iCAAa,qCAAqC,OAAO;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,QAAQ,KAAK;AAAA,MACpB,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,iCAAA,qCAAqC,SAAS,KAAK;AACjE,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,WAAW;AACf,UAAI;AACFA,sBAAA,MAAA,MAAA,OAAA,iCAAY,iBAAiB,KAAK,OAAO;AACzC,YAAI,CAAC,KAAK,SAAS;AACjBA,wBAAAA,MAAc,MAAA,SAAA,iCAAA,QAAQ;AACtBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD;AAAA,QACF;AAEAA,0EAAY,wBAAwB,KAAK,OAAO;AAChD,cAAM,KAAK,WAAW,eAAe,KAAK,OAAO;AACjDA,sBAAA,MAAA,MAAA,OAAA,iCAAY,aAAa,KAAK,WAAW;AAEzC,aAAK,UAAU;AACf,YAAI,KAAK,cAAc;AACrB,gBAAM,KAAK,cAAc;AAAA,QAC3B;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI;AACF,cAAM,KAAK,SAAS;AACpBA,sBAAAA,MAAI,oBAAoB;AAAA,MACxB,SAAO,OAAO;AACdA,sBAAAA,MAAI,oBAAoB;AACxBA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACV,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ,oBAAI,KAAK;AAEvB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,OAAO,IAAI,KAAK,KAAK;AAC3B,aAAK,QAAQ,MAAM,QAAO,IAAK,CAAC;AAEhC,cAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,cAAM,MAAM,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,SAAS,KAAK,OAAM,CAAE;AAEpE,cAAM,KAAK;AAAA,UACT,OAAOG,cAAAA,WAAW,MAAM,YAAY;AAAA,UACpC;AAAA,UACA,MAAMA,cAAAA,WAAW,MAAM,OAAO;AAAA,SAC/B;AAAA,MACH;AAEA,WAAK,iBAAiB;AACtB,WAAK,eAAe,MAAM,CAAC,EAAE;AAAA,IAC9B;AAAA;AAAA,IAGD,MAAM,WAAW,MAAM;AACrB,WAAK,eAAe;AACpB,WAAK,oBAAoB,CAAC;AAC1B,YAAM,KAAK,cAAc;AAAA,IAC1B;AAAA;AAAA,IAGD,oBAAoB,MAAM;AACxB,WAAK,cAAc;AAGnB,WAAK,oBAAoB,CAAC;AAAA,IAC3B;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AACF,cAAM,KAAK,WAAW,kBAAkB;AAAA,UACtC,SAAS,KAAK;AAAA,UACd,MAAM,KAAK;AAAA,SACZ;AAAA,MACD,SAAO,OAAO;AACdH,sBAAAA,MAAA,MAAA,SAAA,iCAAc,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,MAAM;AACnBA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,UAAU,IAAI;AAC1BA,oBAAY,MAAA,MAAA,OAAA,iCAAA,UAAU,KAAK,MAAM;AAGjC,UAAI,KAAK,WAAW,cAAc,KAAK,WAAW,YAAY;AAC5DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD;AAAA,MACF,WAAW,KAAK,WAAW,eAAe;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD;AAAA,MACF,WAAW,KAAK,WAAW,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD;AAAA,MACF,WAAW,KAAK,WAAW,aAAa;AAEtC,YAAI,KAAK,gBAAgB,UAAU;AACjC,cAAI,CAAC,KAAK,0BAA0B,IAAI,GAAG;AACzCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,aACX;AACD;AAAA,UACF;AAAA,QACF;AAEA,cAAM,gBAAgB,KAAK,kBAAkB;AAAA,UAAU,UACpD,KAAK,MAAM,KAAK,OAAO,KAAK,MAC5B,KAAK,cAAc,KAAK,aAAa,KAAK,YAAY,KAAK;AAAA,QAC9D;AAGA,YAAI,kBAAkB,IAAI;AACxB,eAAK,kBAAkB,OAAO,eAAe,CAAC;AAC9CA,wBAAAA,MAAY,MAAA,OAAA,iCAAA,YAAY,IAAI;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AACD;AAAA,QACF;AAGA,YAAI,KAAK,kBAAkB,SAAS,GAAG;AAErC,gBAAM,iBAAiB,KAAK,kBAAkB;AAAA,YAAK,kBACjD,KAAK,sBAAsB,cAAc,IAAI;AAAA,UAC/C;AAEA,cAAI,CAAC,gBAAgB;AAEnBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,aACX;AACD;AAAA,UACF;AAAA,QACF;AAGA,aAAK,kBAAkB,KAAK,IAAI;AAChCA,sBAAY,MAAA,MAAA,OAAA,iCAAA,WAAW,KAAK,iBAAiB;AAC7CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,aACI;AAELA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB,OAAO,OAAO;AAElC,YAAM,WAAW,KAAK,yBAAyB,MAAM,OAAO;AAC5D,YAAM,aAAa,KAAK,yBAAyB,MAAM,SAAS;AAChE,YAAM,aAAa,KAAK,yBAAyB,MAAM,SAAS;AAChE,YAAM,WAAW,KAAK,yBAAyB,MAAM,OAAO;AAG5D,aAAO,aAAa,cAAc,aAAa;AAAA,IAChD;AAAA;AAAA,IAGD,aAAa,MAAM;AACjB,YAAM,UAAU,CAAC,eAAe;AAEhC,UAAI,KAAK,WAAW,YAAY;AAC9B,gBAAQ,KAAK,UAAU;AACvB,gBAAQ,KAAK,UAAU;AAAA,MACzB,WAAW,KAAK,WAAW,YAAY;AACrC,gBAAQ,KAAK,UAAU;AACvB,gBAAQ,KAAK,UAAU;AAAA,MACzB,WAAW,KAAK,WAAW,eAAe;AACxC,gBAAQ,KAAK,aAAa;AAC1B,gBAAQ,KAAK,UAAU;AAAA,MACzB,WAAW,KAAK,WAAW,WAAW;AACpC,gBAAQ,KAAK,SAAS;AACtB,gBAAQ,KAAK,UAAU;AAAA,MACzB;AAGA,YAAM,aAAa,KAAK,kBAAkB;AAAA,QAAK,kBAC5C,KAAK,MAAM,aAAa,OAAO,KAAK,MACpC,KAAK,cAAc,aAAa,aAAa,KAAK,YAAY,aAAa;AAAA,MAC9E;AAEA,UAAI,YAAY;AACd,gBAAQ,KAAK,UAAU;AACvBA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,WAAW,IAAI;AAAA,MAC7B;AAEA,aAAO,QAAQ,KAAK,GAAG;AAAA,IACxB;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,KAAK,kBAAkB,WAAW;AAAG,eAAO;AAEhD,aAAO,KAAK,kBAAkB,OAAO,CAAC,UAAU,YAAY;AAC1D,cAAM,eAAe,KAAK,yBAAyB,SAAS,SAAS;AACrE,cAAM,cAAc,KAAK,yBAAyB,QAAQ,SAAS;AACnE,eAAO,cAAc,eAAe,UAAU;AAAA,SAC7C,KAAK,kBAAkB,CAAC,CAAC;AAAA,IAC7B;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,KAAK,kBAAkB,WAAW;AAAG,eAAO;AAEhD,aAAO,KAAK,kBAAkB,OAAO,CAAC,QAAQ,YAAY;AACxD,cAAM,aAAa,KAAK,yBAAyB,OAAO,OAAO;AAC/D,cAAM,cAAc,KAAK,yBAAyB,QAAQ,OAAO;AACjE,eAAO,cAAc,aAAa,UAAU;AAAA,SAC3C,KAAK,kBAAkB,CAAC,CAAC;AAAA,IAC7B;AAAA;AAAA,IAID,0BAA0B,MAAM;AAC9B,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,mBAAmB,oBAAI,KAAK,GAAG,KAAK,YAAY,IAAI,KAAK,SAAS,EAAE;AAG1E,YAAM,WAAW,iBAAiB,QAAO,IAAK,IAAI,QAAQ;AAG1D,YAAM,YAAY,YAAY,MAAO,KAAK;AAG1C,aAAO,aAAa;AAAA,IACrB;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,KAAK,kBAAkB,WAAW;AAAG,eAAO;AAEhD,aAAO,KAAK,kBAAkB,OAAO,CAAC,OAAO,SAAS;AACpD,eAAO,SAAS,KAAK,SAAS;AAAA,MAC/B,GAAE,CAAC;AAAA,IACL;AAAA;AAAA,IAGD,kBAAkB,QAAQ;AACxB,YAAM,YAAY;AAAA,QAChB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,KAAK,kBAAkB,MAAM;AAAA,IAC3C;AAAA;AAAA,IAGD,YAAY;AACV,UAAI,KAAK,kBAAkB,WAAW,GAAG;AACvCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAGA,YAAM,oBAAoB,KAAK,UAAU,KAAK,iBAAiB;AAG/DA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iCAAiC,KAAK,YAAY,EAAE,SAAS,KAAK,YAAY,gBAAgB,KAAK,WAAW,kBAAkB,mBAAmB,iBAAiB,CAAC;AAAA,OAC3K;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,MAAM,aAAa,MAAM;AAAA,IAC/B;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrB,UAAI;AACF,YAAI,KAAK,kBAAkB,WAAW,GAAG;AACvCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD;AAAA,QACF;AAIAA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAInC,cAAM,cAAc,CAAC,GAAG,KAAK,iBAAiB,EAAE,KAAK,CAAC,GAAG,MAAM;AAC7D,gBAAM,QAAQ,KAAK,yBAAyB,EAAE,SAAS;AACvD,gBAAM,QAAQ,KAAK,yBAAyB,EAAE,SAAS;AACvD,iBAAO,QAAQ;AAAA,SAChB;AAED,cAAM,YAAY,YAAY,CAAC,EAAE;AACjC,cAAM,UAAU,YAAY,YAAY,SAAS,CAAC,EAAE;AACpD,cAAM,UAAU,KAAK,kBAAkB,IAAI,UAAQ,KAAK,EAAE;AAG1D,cAAM,cAAc;AAAA,UAClB,SAAS,KAAK;AAAA,UACd,MAAM,KAAK;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UACA,QAAQ,KAAK,kBAAkB,SAAS,IAAI,OAAO,KAAK,kBAAkB,MAAM,SAAS;AAAA,UACzF,aAAa,KAAK;AAAA,QACpB;AAIA,cAAM,KAAK,aAAa,cAAc,WAAW;AAGjD,cAAM,KAAK,WAAW,kBAAkB;AAAA,UACtC,SAAS,KAAK;AAAA,UACd,MAAM,KAAK;AAAA,SACZ;AAEDA,sBAAAA,MAAI,YAAY;AAChB,aAAK,kBAAkB;AAEvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,aAAK,oBAAoB,CAAC;AAG1B,mBAAW,MAAM;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,WACN;AAAA,QACF,GAAE,IAAI;AAAA,MAEP,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAc,MAAA,SAAA,iCAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,KAAK,YAAY,OAAO;AAC1BA,sBAAAA,MAAI,cAAc;AAAA,UAChB,aAAa,KAAK,YAAY;AAAA,SAC/B;AAAA,aACI;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,qBAAqB;AACnB,YAAM,kBAAkB,KAAK,eAAe,KAAK,OAAK,EAAE,UAAU,KAAK,YAAY;AACnF,aAAO,kBAAkB,GAAG,gBAAgB,GAAG,IAAI,gBAAgB,IAAI,KAAK,KAAK;AAAA,IAClF;AAAA;AAAA,IAGD,qBAAqB;AACnB,UAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,cAAM,OAAO,KAAK,kBAAkB,CAAC;AACrC,cAAM,eAAe,KAAK,yBAAyB,KAAK,SAAS;AACjE,cAAM,aAAa,KAAK,yBAAyB,KAAK,OAAO;AAC7D,cAAM,kBAAkB,aAAa;AACrC,cAAM,QAAQ,KAAK,MAAM,kBAAkB,EAAE;AAC7C,cAAM,UAAU,kBAAkB;AAElC,YAAI,YAAY,GAAG;AACjB,iBAAO,GAAG,KAAK;AAAA,eACV;AACL,iBAAO,GAAG,KAAK,KAAK,OAAO;AAAA,QAC7B;AAAA,aACK;AAEL,cAAM,YAAY,KAAK,iBAAiB;AACxC,cAAM,WAAW,KAAK,gBAAgB;AAEtC,YAAI,aAAa,UAAU;AACzB,gBAAM,eAAe,KAAK,yBAAyB,UAAU,SAAS;AACtE,gBAAM,aAAa,KAAK,yBAAyB,SAAS,OAAO;AACjE,gBAAM,kBAAkB,aAAa;AACrC,gBAAM,QAAQ,KAAK,MAAM,kBAAkB,EAAE;AAC7C,gBAAM,UAAU,kBAAkB;AAElC,cAAI,YAAY,GAAG;AACjB,mBAAO,GAAG,KAAK;AAAA,iBACV;AACL,mBAAO,GAAG,KAAK,KAAK,OAAO;AAAA,UAC7B;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACr5BA,GAAG,WAAW,eAAe;"}