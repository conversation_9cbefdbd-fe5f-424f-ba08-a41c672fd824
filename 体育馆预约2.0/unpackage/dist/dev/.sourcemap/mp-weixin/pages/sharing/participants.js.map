{"version": 3, "file": "participants.js", "sources": ["pages/sharing/participants.vue", "pages/sharing/participants.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">参与者列表</text>\n    </view>\n    \n    <view v-if=\"loading\" class=\"loading\">\n      <text>加载中...</text>\n    </view>\n    \n    <view v-else-if=\"participants.length === 0\" class=\"empty\">\n      <text>暂无参与者</text>\n    </view>\n    \n    <view v-else class=\"participants-list\">\n      <view \n        v-for=\"participant in participants\" \n        :key=\"participant.id\"\n        class=\"participant-item\"\n      >\n        <view class=\"participant-info\">\n          <image \n            :src=\"participant.avatar || '/static/default-avatar.png'\" \n            class=\"avatar\"\n          />\n          <view class=\"info\">\n            <text class=\"name\">{{ participant.nickname || participant.username }}</text>\n            <text class=\"phone\">{{ participant.phone || '未提供' }}</text>\n            <text class=\"join-time\">加入时间: {{ formatDateTime(participant.joinTime) }}</text>\n          </view>\n        </view>\n        \n        <view class=\"participant-status\">\n          <text \n            class=\"status-text\"\n            :class=\"getStatusClass(participant.status)\"\n          >\n            {{ getStatusText(participant.status) }}\n          </text>\n        </view>\n        \n        <view v-if=\"isCreator && participant.status === 'PENDING'\" class=\"actions\">\n          <button \n            class=\"btn btn-approve\" \n            @click=\"approveParticipant(participant.id)\"\n          >\n            同意\n          </button>\n          <button \n            class=\"btn btn-reject\" \n            @click=\"rejectParticipant(participant.id)\"\n          >\n            拒绝\n          </button>\n        </view>\n        \n        <view v-if=\"isCreator && participant.status === 'APPROVED'\" class=\"actions\">\n          <button \n            class=\"btn btn-remove\" \n            @click=\"removeParticipant(participant.id)\"\n          >\n            移除\n          </button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useSharingStore } from '@/stores/sharing.js'\nimport { useUserStore } from '@/stores/user.js'\nimport { formatDateTime } from '@/utils/helpers.js'\n\nexport default {\n  name: 'SharingParticipants',\n  \n  data() {\n    return {\n      orderId: '',\n      participants: [],\n      loading: false,\n      sharingStore: null,\n      userStore: null\n    }\n  },\n  \n  computed: {\n    isCreator() {\n      // 判断当前用户是否为拼场创建者\n      return this.userStore?.userId === this.sharingOrder?.creatorId\n    },\n    \n    sharingOrder() {\n      return this.sharingStore?.getSharingOrderDetail\n    }\n  },\n  \n  onLoad(options) {\n    this.orderId = options.orderId\n    this.sharingStore = useSharingStore()\n    this.userStore = useUserStore()\n    \n    if (this.orderId) {\n      this.loadParticipants()\n    }\n  },\n  \n  methods: {\n    formatDateTime,\n    \n    // 加载参与者列表\n    async loadParticipants() {\n      try {\n        this.loading = true\n        \n        // 获取拼场订单详情\n        await this.sharingStore.getOrderDetail(this.orderId)\n        \n        // 从订单详情中获取参与者信息\n        const order = this.sharingStore.getSharingOrderDetail\n        if (order && order.participants) {\n          this.participants = order.participants\n        }\n        \n      } catch (error) {\n        console.error('加载参与者失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'error'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 同意参与者\n    async approveParticipant(participantId) {\n      try {\n        await this.sharingStore.handleSharingRequest({\n          requestId: participantId,\n          data: { status: 'APPROVED' }\n        })\n        \n        // 重新加载参与者列表\n        await this.loadParticipants()\n        \n      } catch (error) {\n        console.error('同意参与者失败:', error)\n      }\n    },\n    \n    // 拒绝参与者\n    async rejectParticipant(participantId) {\n      try {\n        await this.sharingStore.handleSharingRequest({\n          requestId: participantId,\n          data: { status: 'REJECTED' }\n        })\n        \n        // 重新加载参与者列表\n        await this.loadParticipants()\n        \n      } catch (error) {\n        console.error('拒绝参与者失败:', error)\n      }\n    },\n    \n    // 移除参与者\n    async removeParticipant(participantId) {\n      try {\n        uni.showModal({\n          title: '确认移除',\n          content: '确定要移除这个参与者吗？',\n          success: async (res) => {\n            if (res.confirm) {\n              await this.sharingStore.removeSharingParticipant({\n                sharingId: this.orderId,\n                participantId: participantId\n              })\n              \n              // 重新加载参与者列表\n              await this.loadParticipants()\n            }\n          }\n        })\n      } catch (error) {\n        console.error('移除参与者失败:', error)\n      }\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'PENDING': '待审核',\n        'APPROVED': '已同意',\n        'REJECTED': '已拒绝',\n        'JOINED': '已加入'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    \n    // 获取状态样式类\n    getStatusClass(status) {\n      return `status-${status.toLowerCase()}`\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  margin-bottom: 20px;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n}\n\n.loading, .empty {\n  text-align: center;\n  padding: 40px 0;\n  color: #999;\n}\n\n.participants-list {\n  background-color: #fff;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.participant-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.participant-item:last-child {\n  border-bottom: none;\n}\n\n.participant-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  margin-right: 12px;\n}\n\n.info {\n  flex: 1;\n}\n\n.name {\n  display: block;\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.phone {\n  display: block;\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.join-time {\n  display: block;\n  font-size: 12px;\n  color: #999;\n}\n\n.participant-status {\n  margin-right: 10px;\n}\n\n.status-text {\n  font-size: 12px;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n\n.status-pending {\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n.status-approved {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.status-rejected {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.status-joined {\n  background-color: #d1ecf1;\n  color: #0c5460;\n}\n\n.actions {\n  display: flex;\n  gap: 8px;\n}\n\n.btn {\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  border: none;\n}\n\n.btn-approve {\n  background-color: #28a745;\n  color: white;\n}\n\n.btn-reject {\n  background-color: #dc3545;\n  color: white;\n}\n\n.btn-remove {\n  background-color: #ffc107;\n  color: #212529;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/sharing/participants.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useSharingStore", "useUserStore", "formatDateTime", "uni"], "mappings": ";;;;;AA0EA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,cAAc,CAAE;AAAA,MAChB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,IACb;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,YAAY;;AAEV,eAAO,UAAK,cAAL,mBAAgB,cAAW,UAAK,iBAAL,mBAAmB;AAAA,IACtD;AAAA,IAED,eAAe;;AACb,cAAO,UAAK,iBAAL,mBAAmB;AAAA,IAC5B;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACd,SAAK,UAAU,QAAQ;AACvB,SAAK,eAAeA,+BAAgB;AACpC,SAAK,YAAYC,yBAAa;AAE9B,QAAI,KAAK,SAAS;AAChB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,gBAAAC,cAAc;AAAA;AAAA,IAGd,MAAM,mBAAmB;AACvB,UAAI;AACF,aAAK,UAAU;AAGf,cAAM,KAAK,aAAa,eAAe,KAAK,OAAO;AAGnD,cAAM,QAAQ,KAAK,aAAa;AAChC,YAAI,SAAS,MAAM,cAAc;AAC/B,eAAK,eAAe,MAAM;AAAA,QAC5B;AAAA,MAEA,SAAO,OAAO;AACdC,sBAAAA,MAAA,MAAA,SAAA,yCAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,mBAAmB,eAAe;AACtC,UAAI;AACF,cAAM,KAAK,aAAa,qBAAqB;AAAA,UAC3C,WAAW;AAAA,UACX,MAAM,EAAE,QAAQ,WAAW;AAAA,SAC5B;AAGD,cAAM,KAAK,iBAAiB;AAAA,MAE5B,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yCAAc,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,kBAAkB,eAAe;AACrC,UAAI;AACF,cAAM,KAAK,aAAa,qBAAqB;AAAA,UAC3C,WAAW;AAAA,UACX,MAAM,EAAE,QAAQ,WAAW;AAAA,SAC5B;AAGD,cAAM,KAAK,iBAAiB;AAAA,MAE5B,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yCAAc,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,kBAAkB,eAAe;AACrC,UAAI;AACFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,OAAO,QAAQ;AACtB,gBAAI,IAAI,SAAS;AACf,oBAAM,KAAK,aAAa,yBAAyB;AAAA,gBAC/C,WAAW,KAAK;AAAA,gBAChB;AAAA,eACD;AAGD,oBAAM,KAAK,iBAAiB;AAAA,YAC9B;AAAA,UACF;AAAA,SACD;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yCAAc,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,aAAO,UAAU,OAAO,YAAa,CAAA;AAAA,IACvC;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9MA,GAAG,WAAW,eAAe;"}