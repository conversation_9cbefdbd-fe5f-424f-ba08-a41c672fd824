{"version": 3, "file": "index.js", "sources": ["pages/payment/index.vue", "pages/payment/index.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"payment-container\">\n    <!-- 导航栏 -->\n    <view class=\"nav-bar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <text class=\"nav-icon\">←</text>\n      </view>\n      <view class=\"nav-title\">订单支付</view>\n      <view class=\"nav-right\"></view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n\n    <!-- 订单信息 -->\n    <view v-else-if=\"orderInfo\" class=\"order-section\">\n      <view class=\"order-header\">\n        <text class=\"order-title\">订单信息</text>\n        <text class=\"order-no\">{{ orderInfo.orderNo }}</text>\n      </view>\n\n      <view class=\"order-details\">\n        <view class=\"detail-item\">\n          <text class=\"detail-label\">场馆名称</text>\n          <text class=\"detail-value\">{{ orderInfo.venueName }}</text>\n        </view>\n        <view class=\"detail-item\">\n          <text class=\"detail-label\">预约时间</text>\n          <text class=\"detail-value\">{{ formatOrderDateTime() }}</text>\n        </view>\n        <view class=\"detail-item\">\n          <text class=\"detail-label\">预约类型</text>\n          <text class=\"detail-value\">{{ getBookingTypeText() }}</text>\n        </view>\n        <view v-if=\"orderInfo.bookingType === 'SHARED' || orderInfo.isVirtualOrder\" class=\"detail-item\">\n          <text class=\"detail-label\">队伍名称</text>\n          <text class=\"detail-value\">{{ getTeamName() }}</text>\n        </view>\n        <view class=\"detail-item\">\n          <text class=\"detail-label\">联系方式</text>\n          <text class=\"detail-value\">{{ getContactInfo() }}</text>\n        </view>\n      </view>\n\n      <!-- 价格信息 -->\n      <view class=\"price-section\">\n        <view class=\"price-item\">\n          <text class=\"price-label\">订单金额</text>\n          <text class=\"price-value\">¥{{ getOrderAmount() }}</text>\n        </view>\n        <view v-if=\"orderInfo.bookingType === 'SHARED' || orderInfo.isVirtualOrder\" class=\"price-note\">\n          <text class=\"note-text\">* 拼场订单按队伍收费</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 支付方式选择 -->\n    <view class=\"payment-methods\">\n      <view class=\"method-header\">\n        <text class=\"method-title\">支付方式</text>\n      </view>\n      <view class=\"method-list\">\n        <view \n          class=\"method-item\" \n          :class=\"{ active: selectedMethod === 'wechat' }\"\n          @click=\"selectMethod('wechat')\"\n        >\n          <view class=\"method-info\">\n            <text class=\"method-icon\">💳</text>\n            <text class=\"method-name\">微信支付</text>\n          </view>\n          <view class=\"method-radio\">\n            <text v-if=\"selectedMethod === 'wechat'\" class=\"radio-checked\">✓</text>\n          </view>\n        </view>\n        <view \n          class=\"method-item\" \n          :class=\"{ active: selectedMethod === 'alipay' }\"\n          @click=\"selectMethod('alipay')\"\n        >\n          <view class=\"method-info\">\n            <text class=\"method-icon\">💰</text>\n            <text class=\"method-name\">支付宝</text>\n          </view>\n          <view class=\"method-radio\">\n            <text v-if=\"selectedMethod === 'alipay'\" class=\"radio-checked\">✓</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部支付按钮 -->\n    <view class=\"payment-footer\">\n      <view class=\"footer-info\">\n        <text class=\"footer-label\">应付金额</text>\n        <text class=\"footer-amount\">¥{{ getOrderAmount() }}</text>\n      </view>\n      <button \n        class=\"pay-button\" \n        :class=\"{ disabled: !canPay }\"\n        :disabled=\"!canPay\"\n        @click=\"handlePayment\"\n      >\n        {{ payButtonText }}\n      </button>\n    </view>\n\n    <!-- 支付结果弹窗 -->\n    <uni-popup ref=\"resultPopup\" type=\"center\" :mask-click=\"false\">\n      <view class=\"result-popup\">\n        <view class=\"result-icon\">\n          <text v-if=\"paymentResult.success\" class=\"success-icon\">✓</text>\n          <text v-else class=\"error-icon\">✗</text>\n        </view>\n        <text class=\"result-title\">{{ paymentResult.title }}</text>\n        <text class=\"result-message\">{{ paymentResult.message }}</text>\n        <button class=\"result-button\" @click=\"handleResultAction\">\n          {{ paymentResult.buttonText }}\n        </button>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { payOrder, getPaymentStatus } from '@/api/payment.js'\nimport { getOrderDetail } from '@/api/order.js'\nimport { get } from '@/utils/request.js'\nimport { useBookingStore } from '@/stores/booking.js'\n\nexport default {\n  name: 'PaymentPage',\n  data() {\n    return {\n      bookingStore: null,\n      orderId: null,\n      orderType: 'booking', // booking 或 sharing\n      orderInfo: null,\n      loading: true,\n      selectedMethod: 'wechat',\n      paying: false,\n      fromPage: '', // 记录来源页面\n      paymentResult: {\n        success: false,\n        title: '',\n        message: '',\n        buttonText: '确定'\n      }\n    }\n  },\n  \n  computed: {\n    canPay() {\n      if (!this.orderInfo || !this.selectedMethod || this.paying) return false\n\n      // 虚拟订单的支付状态判断\n      if (this.orderInfo.isVirtualOrder) {\n        // 虚拟订单可支付的状态：PENDING（等待支付）\n        return this.orderInfo.status === 'PENDING'\n      } else {\n        // 普通订单只有PENDING状态可以支付\n        return this.orderInfo.status === 'PENDING'\n      }\n    },\n\n    payButtonText() {\n      if (this.paying) return '支付中...'\n      if (!this.orderInfo) return '加载中...'\n\n      // 虚拟订单的按钮文本逻辑\n      if (this.orderInfo.isVirtualOrder) {\n        if (this.orderInfo.status === 'PENDING') {\n          const amount = this.orderInfo.paymentAmount || this.orderInfo.totalPrice\n          return `立即支付 ¥${amount?.toFixed(2) || '0.00'}`\n        } else {\n          // 根据虚拟订单状态显示不同文本\n          const statusMessages = {\n            'SHARING_SUCCESS': '拼场已成功',\n            'CANCELLED': '申请已取消',\n            'EXPIRED': '申请已过期',\n            'NOT_FOUND': '申请不存在',\n            'ACCESS_DENIED': '无权访问'\n          }\n          return statusMessages[this.orderInfo.status] || '订单状态异常'\n        }\n      } else {\n        // 普通订单的按钮文本逻辑\n        if (this.orderInfo.status === 'PENDING') {\n          return `立即支付 ¥${this.orderInfo.totalPrice?.toFixed(2) || '0.00'}`\n        } else {\n          return '订单状态异常'\n        }\n      }\n    }\n  },\n  \n  onLoad(options) {\n    console.log('支付页面参数:', options)\n\n    // 初始化Pinia store\n    this.bookingStore = useBookingStore()\n\n    if (options.orderId) {\n      this.orderId = options.orderId\n      this.orderType = options.type || 'booking'\n      this.fromPage = options.from || ''  // 记录来源页面\n      this.loadOrderInfo()\n    } else {\n      uni.showToast({\n        title: '订单ID缺失',\n        icon: 'error'\n      })\n      setTimeout(() => {\n        uni.navigateBack()\n      }, 1500)\n    }\n  },\n  \n  methods: {\n    // 加载订单信息\n    async loadOrderInfo() {\n      try {\n        this.loading = true\n\n        // 检查是否是虚拟订单（负数ID）\n        const isVirtualOrder = this.orderId < 0\n        console.log('订单ID:', this.orderId, '是否为虚拟订单:', isVirtualOrder)\n\n        let response\n        if (isVirtualOrder) {\n          // 虚拟订单：使用申请ID调用虚拟订单API\n          const requestId = Math.abs(this.orderId) // 转换为正数\n          console.log('获取虚拟订单详情，申请ID:', requestId)\n          try {\n            // 使用项目的请求工具\n            response = await get(`/users/me/virtual-order/${requestId}`)\n          } catch (error) {\n            console.error('获取虚拟订单失败:', error)\n            // 如果是404或403错误，创建一个错误状态的虚拟订单\n            if (error.status === 404) {\n              response = {\n                data: {\n                  id: this.orderId,\n                  orderNo: `REQ_${requestId}`,\n                  status: 'NOT_FOUND',\n                  isVirtualOrder: true,\n                  venueName: '未知场馆',\n                  totalPrice: 0,\n                  paymentAmount: 0\n                }\n              }\n            } else if (error.status === 403) {\n              response = {\n                data: {\n                  id: this.orderId,\n                  orderNo: `REQ_${requestId}`,\n                  status: 'ACCESS_DENIED',\n                  isVirtualOrder: true,\n                  venueName: '未知场馆',\n                  totalPrice: 0,\n                  paymentAmount: 0\n                }\n              }\n            } else {\n              throw error // 重新抛出其他错误\n            }\n          }\n        } else {\n          // 真实订单：使用Pinia Booking Store\n          console.log('获取真实订单详情，订单ID:', this.orderId)\n          await this.bookingStore.getBookingDetail(this.orderId)\n          console.log('Booking Store调用完成')\n\n          // 从store的getter中获取数据\n          const storeData = this.bookingStore.getBookingDetail\n          console.log('从Store getter获取的数据:', storeData)\n\n          response = { data: storeData }\n        }\n\n        // 处理不同的响应格式\n        this.orderInfo = response.data || response\n        console.log('最终订单信息:', this.orderInfo)\n\n        // 如果是虚拟订单，添加特殊标识和详细调试信息\n        if (isVirtualOrder) {\n          this.orderInfo.isVirtualOrder = true\n          console.log('虚拟订单详细信息:')\n          console.log('- 订单状态:', this.orderInfo.status)\n          console.log('- 申请状态:', this.orderInfo.requestStatus)\n          console.log('- 支付金额:', this.orderInfo.paymentAmount)\n          console.log('- 总价:', this.orderInfo.totalPrice)\n          console.log('- 队伍名称:', this.orderInfo.applicantTeamName)\n          console.log('- 联系方式:', this.orderInfo.applicantContact)\n          console.log('- 预约时间:', this.orderInfo.bookingTime)\n          console.log('- 结束时间:', this.orderInfo.endTime)\n          console.log('- 原始响应:', response)\n\n          // 检查是否有错误状态\n          if (!this.orderInfo.status) {\n            console.error('虚拟订单状态为空！')\n            this.orderInfo.status = 'PENDING' // 设置默认状态\n          }\n\n          // 检查支付金额\n          if (!this.orderInfo.paymentAmount && !this.orderInfo.totalPrice) {\n            console.error('虚拟订单金额为空！')\n          }\n        }\n\n      } catch (error) {\n        console.error('加载订单信息失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'error'\n        })\n        setTimeout(() => {\n          uni.navigateBack()\n        }, 1500)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 选择支付方式\n    selectMethod(method) {\n      this.selectedMethod = method\n    },\n    \n    // 处理支付\n    async handlePayment() {\n      if (!this.canPay) return\n\n      try {\n        this.paying = true\n\n        // 显示支付加载\n        uni.showLoading({ title: '支付中...' })\n\n        // 模拟支付延迟\n        await new Promise(resolve => setTimeout(resolve, 2000))\n\n        // 模拟支付结果（80%成功率）\n        const isSuccess = Math.random() > 0.2\n\n        uni.hideLoading()\n\n        if (isSuccess) {\n          // 调用支付接口\n          const response = await payOrder(this.orderId, this.selectedMethod)\n\n          if (response.success) {\n            // 支付成功，跳转到成功页面\n            let successUrl = `/pages/payment/success?orderId=${this.orderId}`\n            if (this.fromPage) {\n              successUrl += `&from=${this.fromPage}`\n            }\n            uni.redirectTo({\n              url: successUrl\n            })\n          } else {\n            throw new Error(response.message || '支付失败')\n          }\n        } else {\n          // 模拟支付失败\n          throw new Error('支付失败，请检查账户余额或重试')\n        }\n\n      } catch (error) {\n        uni.hideLoading()\n        console.error('支付失败:', error)\n\n        // 支付失败，跳转到失败页面\n        uni.redirectTo({\n          url: `/pages/payment/failed?orderId=${this.orderId}&reason=${encodeURIComponent(error.message)}`\n        })\n      } finally {\n        this.paying = false\n      }\n    },\n\n    // 处理结果操作（保留用于其他用途）\n    handleResultAction() {\n      this.$refs.resultPopup.close()\n\n      if (this.paymentResult.success) {\n        // 支付成功，跳转到订单列表\n        uni.redirectTo({\n          url: '/pages/booking/list'\n        })\n      }\n      // 支付失败，留在当前页面重试\n    },\n    \n    // 格式化日期时间\n    formatDateTime(date, startTime, endTime) {\n      if (!date || !startTime) return '未设置'\n\n      // 处理日期\n      let dateStr = ''\n      if (typeof date === 'string' && date.includes('-')) {\n        // 如果是 YYYY-MM-DD 格式\n        const [year, month, day] = date.split('-')\n        dateStr = `${month}-${day}`\n      } else {\n        // 其他格式\n        const dateObj = new Date(date)\n        dateStr = dateObj.toLocaleDateString('zh-CN', {\n          month: '2-digit',\n          day: '2-digit'\n        })\n      }\n\n      // 处理时间\n      const timeStr = endTime ? `${startTime}-${endTime}` : startTime\n\n      return `${dateStr} ${timeStr}`\n    },\n    \n    // 返回\n    goBack() {\n      uni.navigateBack()\n    },\n\n    // 格式化订单时间（处理虚拟订单和普通订单的差异）\n    formatOrderDateTime() {\n      if (!this.orderInfo) return '未设置'\n\n      if (this.orderInfo.isVirtualOrder) {\n        // 虚拟订单使用 bookingTime 和 endTime (LocalDateTime格式: \"yyyy-MM-dd HH:mm:ss\")\n        const startTime = this.orderInfo.bookingTime\n        const endTime = this.orderInfo.endTime\n\n        console.log('虚拟订单时间格式化 - 原始数据:', { startTime, endTime })\n\n        if (!startTime) {\n          console.warn('虚拟订单开始时间为空')\n          return '未设置'\n        }\n\n        try {\n          // 处理后端返回的时间格式 \"yyyy-MM-dd HH:mm:ss\"，转换为iOS兼容格式\n          let startDateTime, endDateTime\n\n          if (typeof startTime === 'string') {\n            // 后端格式: \"2025-07-16 08:00:00\"\n            // iOS兼容格式: \"2025-07-16T08:00:00\"\n            let isoTime = startTime\n            if (startTime.includes(' ') && !startTime.includes('T')) {\n              isoTime = startTime.replace(' ', 'T')\n            }\n            startDateTime = new Date(isoTime)\n            console.log('支付页面时间转换 - 原始:', startTime, '转换后:', isoTime, '解析结果:', startDateTime)\n          } else {\n            startDateTime = new Date(startTime)\n          }\n\n          if (endTime) {\n            if (typeof endTime === 'string') {\n              let isoEndTime = endTime\n              if (endTime.includes(' ') && !endTime.includes('T')) {\n                isoEndTime = endTime.replace(' ', 'T')\n              }\n              endDateTime = new Date(isoEndTime)\n              console.log('支付页面结束时间转换 - 原始:', endTime, '转换后:', isoEndTime, '解析结果:', endDateTime)\n            } else {\n              endDateTime = new Date(endTime)\n            }\n          }\n\n          // 检查日期是否有效\n          if (isNaN(startDateTime.getTime())) {\n            console.error('无效的开始时间:', startTime)\n            return '时间格式错误'\n          }\n\n          // 格式化日期 (MM-DD)\n          const dateStr = startDateTime.toLocaleDateString('zh-CN', {\n            month: '2-digit',\n            day: '2-digit'\n          })\n\n          // 格式化开始时间 (HH:mm)\n          const startTimeStr = startDateTime.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n          })\n\n          // 格式化结束时间 (HH:mm)\n          let endTimeStr = ''\n          if (endDateTime && !isNaN(endDateTime.getTime())) {\n            endTimeStr = endDateTime.toLocaleTimeString('zh-CN', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false\n            })\n          }\n\n          const result = `${dateStr} ${startTimeStr}${endTimeStr ? '-' + endTimeStr : ''}`\n          console.log('虚拟订单时间格式化结果:', result)\n          return result\n        } catch (error) {\n          console.error('虚拟订单时间格式化错误:', error, '原始数据:', { startTime, endTime })\n          return '时间格式错误'\n        }\n      } else {\n        // 普通订单使用原有逻辑\n        return this.formatDateTime(this.orderInfo.bookingDate, this.orderInfo.startTime, this.orderInfo.endTime)\n      }\n    },\n\n    // 获取队伍名称\n    getTeamName() {\n      if (!this.orderInfo) return '未设置'\n\n      if (this.orderInfo.isVirtualOrder) {\n        return this.orderInfo.applicantTeamName || '未设置'\n      } else {\n        return this.orderInfo.teamName || '未设置'\n      }\n    },\n\n    // 获取联系方式\n    getContactInfo() {\n      if (!this.orderInfo) return '未设置'\n\n      if (this.orderInfo.isVirtualOrder) {\n        return this.orderInfo.applicantContact || '未设置'\n      } else {\n        return this.orderInfo.contactInfo || '未设置'\n      }\n    },\n\n    // 获取订单金额\n    getOrderAmount() {\n      if (!this.orderInfo) return '0.00'\n\n      const amount = this.orderInfo.isVirtualOrder\n        ? this.orderInfo.paymentAmount\n        : this.orderInfo.totalPrice\n\n      return amount?.toFixed(2) || '0.00'\n    },\n\n    // 获取预约类型文本\n    getBookingTypeText() {\n      if (!this.orderInfo) return '未知'\n\n      // 虚拟订单始终是拼场类型\n      if (this.orderInfo.isVirtualOrder) {\n        return '拼场'\n      }\n\n      return this.orderInfo.bookingType === 'SHARED' ? '拼场' : '独享'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.payment-container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n.nav-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 32rpx;\n  background-color: #ffffff;\n  border-bottom: 1rpx solid #e5e5e5;\n  \n  .nav-left, .nav-right {\n    width: 80rpx;\n  }\n  \n  .nav-icon {\n    font-size: 36rpx;\n    color: #333333;\n  }\n  \n  .nav-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333333;\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n  \n  .loading-text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n.order-section {\n  margin: 20rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.order-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  .order-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333333;\n  }\n  \n  .order-no {\n    font-size: 24rpx;\n    color: #999999;\n  }\n}\n\n.order-details {\n  padding: 0 32rpx;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx 0;\n  border-bottom: 1rpx solid #f8f8f8;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  .detail-label {\n    font-size: 28rpx;\n    color: #666666;\n  }\n  \n  .detail-value {\n    font-size: 28rpx;\n    color: #333333;\n    text-align: right;\n    flex: 1;\n    margin-left: 32rpx;\n  }\n}\n\n.price-section {\n  padding: 32rpx;\n  border-top: 1rpx solid #f0f0f0;\n  background-color: #fafafa;\n}\n\n.price-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  \n  .price-label {\n    font-size: 32rpx;\n    color: #333333;\n    font-weight: 600;\n  }\n  \n  .price-value {\n    font-size: 36rpx;\n    color: #ff6b35;\n    font-weight: 700;\n  }\n}\n\n.price-note {\n  margin-top: 16rpx;\n  \n  .note-text {\n    font-size: 24rpx;\n    color: #999999;\n  }\n}\n\n.payment-methods {\n  margin: 20rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.method-header {\n  padding: 32rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  .method-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333333;\n  }\n}\n\n.method-list {\n  padding: 0 32rpx;\n}\n\n.method-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32rpx 0;\n  border-bottom: 1rpx solid #f8f8f8;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  &.active {\n    .method-name {\n      color: #ff6b35;\n    }\n  }\n}\n\n.method-info {\n  display: flex;\n  align-items: center;\n  \n  .method-icon {\n    font-size: 32rpx;\n    margin-right: 16rpx;\n  }\n  \n  .method-name {\n    font-size: 28rpx;\n    color: #333333;\n  }\n}\n\n.method-radio {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  border: 2rpx solid #ddd;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .radio-checked {\n    color: #ff6b35;\n    font-size: 24rpx;\n    font-weight: bold;\n  }\n}\n\n.payment-footer {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  background-color: #ffffff;\n  border-top: 1rpx solid #e5e5e5;\n  z-index: 100;\n}\n\n.footer-info {\n  flex: 1;\n  \n  .footer-label {\n    font-size: 24rpx;\n    color: #666666;\n    display: block;\n  }\n  \n  .footer-amount {\n    font-size: 32rpx;\n    color: #ff6b35;\n    font-weight: 700;\n  }\n}\n\n.pay-button {\n  width: 240rpx;\n  height: 80rpx;\n  background-color: #ff6b35;\n  color: #ffffff;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  \n  &.disabled {\n    background-color: #cccccc;\n    color: #999999;\n  }\n}\n\n.result-popup {\n  width: 560rpx;\n  padding: 60rpx 40rpx 40rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  text-align: center;\n}\n\n.result-icon {\n  margin-bottom: 32rpx;\n  \n  .success-icon {\n    display: inline-block;\n    width: 80rpx;\n    height: 80rpx;\n    line-height: 80rpx;\n    background-color: #52c41a;\n    color: #ffffff;\n    border-radius: 50%;\n    font-size: 48rpx;\n    font-weight: bold;\n  }\n  \n  .error-icon {\n    display: inline-block;\n    width: 80rpx;\n    height: 80rpx;\n    line-height: 80rpx;\n    background-color: #ff4d4f;\n    color: #ffffff;\n    border-radius: 50%;\n    font-size: 48rpx;\n    font-weight: bold;\n  }\n}\n\n.result-title {\n  display: block;\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 16rpx;\n}\n\n.result-message {\n  display: block;\n  font-size: 28rpx;\n  color: #666666;\n  margin-bottom: 40rpx;\n  line-height: 1.5;\n}\n\n.result-button {\n  width: 200rpx;\n  height: 72rpx;\n  background-color: #ff6b35;\n  color: #ffffff;\n  border: none;\n  border-radius: 36rpx;\n  font-size: 28rpx;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/payment/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "useBookingStore", "get", "payOrder"], "mappings": ";;;;;AAoIA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA,MACd,SAAS;AAAA,MACT,WAAW;AAAA;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,UAAU;AAAA;AAAA,MACV,eAAe;AAAA,QACb,SAAS;AAAA,QACT,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,SAAS;AACP,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,kBAAkB,KAAK;AAAQ,eAAO;AAGnE,UAAI,KAAK,UAAU,gBAAgB;AAEjC,eAAO,KAAK,UAAU,WAAW;AAAA,aAC5B;AAEL,eAAO,KAAK,UAAU,WAAW;AAAA,MACnC;AAAA,IACD;AAAA,IAED,gBAAgB;;AACd,UAAI,KAAK;AAAQ,eAAO;AACxB,UAAI,CAAC,KAAK;AAAW,eAAO;AAG5B,UAAI,KAAK,UAAU,gBAAgB;AACjC,YAAI,KAAK,UAAU,WAAW,WAAW;AACvC,gBAAM,SAAS,KAAK,UAAU,iBAAiB,KAAK,UAAU;AAC9D,iBAAO,UAAS,iCAAQ,QAAQ,OAAM,MAAM;AAAA,eACvC;AAEL,gBAAM,iBAAiB;AAAA,YACrB,mBAAmB;AAAA,YACnB,aAAa;AAAA,YACb,WAAW;AAAA,YACX,aAAa;AAAA,YACb,iBAAiB;AAAA,UACnB;AACA,iBAAO,eAAe,KAAK,UAAU,MAAM,KAAK;AAAA,QAClD;AAAA,aACK;AAEL,YAAI,KAAK,UAAU,WAAW,WAAW;AACvC,iBAAO,WAAS,UAAK,UAAU,eAAf,mBAA2B,QAAQ,OAAM,MAAM;AAAA,eAC1D;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACdA,kBAAAA,MAAA,MAAA,OAAA,kCAAY,WAAW,OAAO;AAG9B,SAAK,eAAeC,+BAAgB;AAEpC,QAAI,QAAQ,SAAS;AACnB,WAAK,UAAU,QAAQ;AACvB,WAAK,YAAY,QAAQ,QAAQ;AACjC,WAAK,WAAW,QAAQ,QAAQ;AAChC,WAAK,cAAc;AAAA,WACd;AACLD,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AACD,iBAAW,MAAM;AACfA,sBAAAA,MAAI,aAAa;AAAA,MAClB,GAAE,IAAI;AAAA,IACT;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,gBAAgB;AACpB,UAAI;AACF,aAAK,UAAU;AAGf,cAAM,iBAAiB,KAAK,UAAU;AACtCA,4BAAY,MAAA,OAAA,kCAAA,SAAS,KAAK,SAAS,YAAY,cAAc;AAE7D,YAAI;AACJ,YAAI,gBAAgB;AAElB,gBAAM,YAAY,KAAK,IAAI,KAAK,OAAO;AACvCA,wBAAAA,MAAA,MAAA,OAAA,kCAAY,kBAAkB,SAAS;AACvC,cAAI;AAEF,uBAAW,MAAME,cAAG,IAAC,2BAA2B,SAAS,EAAE;AAAA,UAC3D,SAAO,OAAO;AACdF,0BAAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,KAAK;AAEhC,gBAAI,MAAM,WAAW,KAAK;AACxB,yBAAW;AAAA,gBACT,MAAM;AAAA,kBACJ,IAAI,KAAK;AAAA,kBACT,SAAS,OAAO,SAAS;AAAA,kBACzB,QAAQ;AAAA,kBACR,gBAAgB;AAAA,kBAChB,WAAW;AAAA,kBACX,YAAY;AAAA,kBACZ,eAAe;AAAA,gBACjB;AAAA,cACF;AAAA,uBACS,MAAM,WAAW,KAAK;AAC/B,yBAAW;AAAA,gBACT,MAAM;AAAA,kBACJ,IAAI,KAAK;AAAA,kBACT,SAAS,OAAO,SAAS;AAAA,kBACzB,QAAQ;AAAA,kBACR,gBAAgB;AAAA,kBAChB,WAAW;AAAA,kBACX,YAAY;AAAA,kBACZ,eAAe;AAAA,gBACjB;AAAA,cACF;AAAA,mBACK;AACL,oBAAM;AAAA,YACR;AAAA,UACF;AAAA,eACK;AAELA,wBAAY,MAAA,MAAA,OAAA,kCAAA,kBAAkB,KAAK,OAAO;AAC1C,gBAAM,KAAK,aAAa,iBAAiB,KAAK,OAAO;AACrDA,wBAAAA,MAAA,MAAA,OAAA,kCAAY,mBAAmB;AAG/B,gBAAM,YAAY,KAAK,aAAa;AACpCA,wBAAAA,qDAAY,uBAAuB,SAAS;AAE5C,qBAAW,EAAE,MAAM,UAAU;AAAA,QAC/B;AAGA,aAAK,YAAY,SAAS,QAAQ;AAClCA,sBAAY,MAAA,MAAA,OAAA,kCAAA,WAAW,KAAK,SAAS;AAGrC,YAAI,gBAAgB;AAClB,eAAK,UAAU,iBAAiB;AAChCA,wBAAAA,MAAA,MAAA,OAAA,kCAAY,WAAW;AACvBA,6EAAY,WAAW,KAAK,UAAU,MAAM;AAC5CA,wBAAY,MAAA,MAAA,OAAA,kCAAA,WAAW,KAAK,UAAU,aAAa;AACnDA,wBAAY,MAAA,MAAA,OAAA,kCAAA,WAAW,KAAK,UAAU,aAAa;AACnDA,wBAAA,MAAA,MAAA,OAAA,kCAAY,SAAS,KAAK,UAAU,UAAU;AAC9CA,wBAAA,MAAA,MAAA,OAAA,kCAAY,WAAW,KAAK,UAAU,iBAAiB;AACvDA,wBAAA,MAAA,MAAA,OAAA,kCAAY,WAAW,KAAK,UAAU,gBAAgB;AACtDA,wBAAA,MAAA,MAAA,OAAA,kCAAY,WAAW,KAAK,UAAU,WAAW;AACjDA,6EAAY,WAAW,KAAK,UAAU,OAAO;AAC7CA,wBAAAA,MAAA,MAAA,OAAA,kCAAY,WAAW,QAAQ;AAG/B,cAAI,CAAC,KAAK,UAAU,QAAQ;AAC1BA,0BAAAA,uDAAc,WAAW;AACzB,iBAAK,UAAU,SAAS;AAAA,UAC1B;AAGA,cAAI,CAAC,KAAK,UAAU,iBAAiB,CAAC,KAAK,UAAU,YAAY;AAC/DA,0BAAAA,uDAAc,WAAW;AAAA,UAC3B;AAAA,QACF;AAAA,MAEA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,aAAa;AAAA,QAClB,GAAE,IAAI;AAAA,MACT,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,QAAQ;AACnB,WAAK,iBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI,CAAC,KAAK;AAAQ;AAElB,UAAI;AACF,aAAK,SAAS;AAGdA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAGnC,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAGtD,cAAM,YAAY,KAAK,OAAM,IAAK;AAElCA,sBAAAA,MAAI,YAAY;AAEhB,YAAI,WAAW;AAEb,gBAAM,WAAW,MAAMG,YAAQ,SAAC,KAAK,SAAS,KAAK,cAAc;AAEjE,cAAI,SAAS,SAAS;AAEpB,gBAAI,aAAa,kCAAkC,KAAK,OAAO;AAC/D,gBAAI,KAAK,UAAU;AACjB,4BAAc,SAAS,KAAK,QAAQ;AAAA,YACtC;AACAH,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,aACN;AAAA,iBACI;AACL,kBAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,UAC5C;AAAA,eACK;AAEL,gBAAM,IAAI,MAAM,iBAAiB;AAAA,QACnC;AAAA,MAEA,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,SAAS,KAAK;AAG5BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,iCAAiC,KAAK,OAAO,WAAW,mBAAmB,MAAM,OAAO,CAAC;AAAA,SAC/F;AAAA,MACH,UAAU;AACR,aAAK,SAAS;AAAA,MAChB;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,MAAM,YAAY,MAAM;AAE7B,UAAI,KAAK,cAAc,SAAS;AAE9BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,SACN;AAAA,MACH;AAAA,IAED;AAAA;AAAA,IAGD,eAAe,MAAM,WAAW,SAAS;AACvC,UAAI,CAAC,QAAQ,CAAC;AAAW,eAAO;AAGhC,UAAI,UAAU;AACd,UAAI,OAAO,SAAS,YAAY,KAAK,SAAS,GAAG,GAAG;AAElD,cAAM,CAAC,MAAM,OAAO,GAAG,IAAI,KAAK,MAAM,GAAG;AACzC,kBAAU,GAAG,KAAK,IAAI,GAAG;AAAA,aACpB;AAEL,cAAM,UAAU,IAAI,KAAK,IAAI;AAC7B,kBAAU,QAAQ,mBAAmB,SAAS;AAAA,UAC5C,OAAO;AAAA,UACP,KAAK;AAAA,SACN;AAAA,MACH;AAGA,YAAM,UAAU,UAAU,GAAG,SAAS,IAAI,OAAO,KAAK;AAEtD,aAAO,GAAG,OAAO,IAAI,OAAO;AAAA,IAC7B;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,sBAAsB;AACpB,UAAI,CAAC,KAAK;AAAW,eAAO;AAE5B,UAAI,KAAK,UAAU,gBAAgB;AAEjC,cAAM,YAAY,KAAK,UAAU;AACjC,cAAM,UAAU,KAAK,UAAU;AAE/BA,sBAAY,MAAA,MAAA,OAAA,kCAAA,qBAAqB,EAAE,WAAW,SAAS;AAEvD,YAAI,CAAC,WAAW;AACdA,wBAAAA,MAAa,MAAA,QAAA,kCAAA,YAAY;AACzB,iBAAO;AAAA,QACT;AAEA,YAAI;AAEF,cAAI,eAAe;AAEnB,cAAI,OAAO,cAAc,UAAU;AAGjC,gBAAI,UAAU;AACd,gBAAI,UAAU,SAAS,GAAG,KAAK,CAAC,UAAU,SAAS,GAAG,GAAG;AACvD,wBAAU,UAAU,QAAQ,KAAK,GAAG;AAAA,YACtC;AACA,4BAAgB,IAAI,KAAK,OAAO;AAChCA,gCAAY,MAAA,OAAA,kCAAA,kBAAkB,WAAW,QAAQ,SAAS,SAAS,aAAa;AAAA,iBAC3E;AACL,4BAAgB,IAAI,KAAK,SAAS;AAAA,UACpC;AAEA,cAAI,SAAS;AACX,gBAAI,OAAO,YAAY,UAAU;AAC/B,kBAAI,aAAa;AACjB,kBAAI,QAAQ,SAAS,GAAG,KAAK,CAAC,QAAQ,SAAS,GAAG,GAAG;AACnD,6BAAa,QAAQ,QAAQ,KAAK,GAAG;AAAA,cACvC;AACA,4BAAc,IAAI,KAAK,UAAU;AACjCA,iFAAY,oBAAoB,SAAS,QAAQ,YAAY,SAAS,WAAW;AAAA,mBAC5E;AACL,4BAAc,IAAI,KAAK,OAAO;AAAA,YAChC;AAAA,UACF;AAGA,cAAI,MAAM,cAAc,QAAO,CAAE,GAAG;AAClCA,0BAAAA,MAAc,MAAA,SAAA,kCAAA,YAAY,SAAS;AACnC,mBAAO;AAAA,UACT;AAGA,gBAAM,UAAU,cAAc,mBAAmB,SAAS;AAAA,YACxD,OAAO;AAAA,YACP,KAAK;AAAA,WACN;AAGD,gBAAM,eAAe,cAAc,mBAAmB,SAAS;AAAA,YAC7D,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,WACT;AAGD,cAAI,aAAa;AACjB,cAAI,eAAe,CAAC,MAAM,YAAY,QAAS,CAAA,GAAG;AAChD,yBAAa,YAAY,mBAAmB,SAAS;AAAA,cACnD,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,QAAQ;AAAA,aACT;AAAA,UACH;AAEA,gBAAM,SAAS,GAAG,OAAO,IAAI,YAAY,GAAG,aAAa,MAAM,aAAa,EAAE;AAC9EA,wBAAAA,MAAY,MAAA,OAAA,kCAAA,gBAAgB,MAAM;AAClC,iBAAO;AAAA,QACP,SAAO,OAAO;AACdA,8BAAA,MAAA,SAAA,kCAAc,gBAAgB,OAAO,SAAS,EAAE,WAAW,SAAS;AACpE,iBAAO;AAAA,QACT;AAAA,aACK;AAEL,eAAO,KAAK,eAAe,KAAK,UAAU,aAAa,KAAK,UAAU,WAAW,KAAK,UAAU,OAAO;AAAA,MACzG;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,CAAC,KAAK;AAAW,eAAO;AAE5B,UAAI,KAAK,UAAU,gBAAgB;AACjC,eAAO,KAAK,UAAU,qBAAqB;AAAA,aACtC;AACL,eAAO,KAAK,UAAU,YAAY;AAAA,MACpC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,CAAC,KAAK;AAAW,eAAO;AAE5B,UAAI,KAAK,UAAU,gBAAgB;AACjC,eAAO,KAAK,UAAU,oBAAoB;AAAA,aACrC;AACL,eAAO,KAAK,UAAU,eAAe;AAAA,MACvC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,CAAC,KAAK;AAAW,eAAO;AAE5B,YAAM,SAAS,KAAK,UAAU,iBAC1B,KAAK,UAAU,gBACf,KAAK,UAAU;AAEnB,cAAO,iCAAQ,QAAQ,OAAM;AAAA,IAC9B;AAAA;AAAA,IAGD,qBAAqB;AACnB,UAAI,CAAC,KAAK;AAAW,eAAO;AAG5B,UAAI,KAAK,UAAU,gBAAgB;AACjC,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,UAAU,gBAAgB,WAAW,OAAO;AAAA,IAC1D;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9iBA,GAAG,WAAW,eAAe;"}