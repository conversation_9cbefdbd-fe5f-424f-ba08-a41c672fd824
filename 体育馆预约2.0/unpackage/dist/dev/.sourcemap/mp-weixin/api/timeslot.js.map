{"version": 3, "file": "timeslot.js", "sources": ["api/timeslot.js"], "sourcesContent": ["import { get, post, patch } from '@/utils/request.js'\n\n// 获取场馆指定日期的所有时间段\nexport function getVenueTimeSlots(venueId, date) {\n  return get(`/timeslots/venue/${venueId}/date/${date}`)\n}\n\n// 获取场馆指定日期的可用时间段\nexport function getAvailableTimeSlots(venueId, date) {\n  return get(`/timeslots/venue/${venueId}/date/${date}/available`)\n}\n\n// 检查时间段是否可预约\nexport function checkTimeSlotAvailability(params) {\n  return get('/timeslots/check', params)\n}\n\n// 为场馆生成指定日期的时间段（仅限管理员）\nexport function generateTimeSlots(venueId, date) {\n  return post(`/timeslots/venue/${venueId}/date/${date}/generate`)\n}\n\n// 批量生成未来一周的时间段（仅限管理员）\nexport function generateWeekTimeSlots(venueId) {\n  return post(`/timeslots/venue/${venueId}/generate-week`)\n}\n\n// 更新时间段状态（仅限管理员）\nexport function updateTimeSlotStatus(id, data) {\n  return patch(`/timeslots/${id}/status`, data)\n}"], "names": ["get"], "mappings": ";;AAGO,SAAS,kBAAkB,SAAS,MAAM;AAC/C,SAAOA,cAAAA,IAAI,oBAAoB,OAAO,SAAS,IAAI,EAAE;AACvD;;"}