// Pinia迁移验证器
// 用于验证Vuex到Pinia迁移的正确性

import { useAppStore } from './app.js'
import { useUserStore } from './user.js'
import { useVenueStore } from './venue.js'
import { useBookingStore } from './booking.js'
import { useSharingStore } from './sharing.js'

// 验证结果存储
let validationInterval = null

// 验证单个store的状态同步
function validateStoreSync(storeName, piniaStore, vuexState) {
  const results = {
    storeName,
    passed: true,
    errors: [],
    warnings: []
  }

  try {
    if (!piniaStore) {
      results.passed = false
      results.errors.push(`${storeName} Pinia store未初始化`)
      return results
    }

    if (!vuexState) {
      results.warnings.push(`${storeName} Vuex状态不可用，跳过同步验证`)
      return results
    }

    // 根据不同store进行特定验证
    switch (storeName) {
      case 'app':
        validateAppStore(piniaStore, vuexState, results)
        break
      case 'user':
        validateUserStore(piniaStore, vuexState, results)
        break
      case 'venue':
        validateVenueStore(piniaStore, vuexState, results)
        break
      case 'booking':
        validateBookingStore(piniaStore, vuexState, results)
        break
      case 'sharing':
        validateSharingStore(piniaStore, vuexState, results)
        break
    }
  } catch (error) {
    results.passed = false
    results.errors.push(`验证${storeName}时发生错误: ${error.message}`)
  }

  return results
}

// 验证App store
function validateAppStore(piniaStore, vuexState, results) {
  if (piniaStore.loading !== vuexState.loading) {
    results.errors.push(`loading状态不同步: Pinia=${piniaStore.loading}, Vuex=${vuexState.loading}`)
    results.passed = false
  }
  
  if (piniaStore.networkStatus !== vuexState.networkStatus) {
    results.errors.push(`networkStatus状态不同步: Pinia=${piniaStore.networkStatus}, Vuex=${vuexState.networkStatus}`)
    results.passed = false
  }
}

// 验证User store
function validateUserStore(piniaStore, vuexState, results) {
  if (piniaStore.isLoggedIn !== vuexState.isLoggedIn) {
    results.errors.push(`登录状态不同步: Pinia=${piniaStore.isLoggedIn}, Vuex=${vuexState.isLoggedIn}`)
    results.passed = false
  }
  
  if (piniaStore.token !== vuexState.token) {
    results.warnings.push(`token可能不同步`)
  }
  
  // 验证用户信息对象
  if (JSON.stringify(piniaStore.userInfo) !== JSON.stringify(vuexState.userInfo)) {
    results.warnings.push(`用户信息可能不同步`)
  }
}

// 验证Venue store
function validateVenueStore(piniaStore, vuexState, results) {
  if (piniaStore.loading !== vuexState.loading) {
    results.errors.push(`venue loading状态不同步`)
    results.passed = false
  }
  
  if (piniaStore.venueList.length !== vuexState.venueList.length) {
    results.warnings.push(`场馆列表长度不同: Pinia=${piniaStore.venueList.length}, Vuex=${vuexState.venueList.length}`)
  }
}

// 验证Booking store
function validateBookingStore(piniaStore, vuexState, results) {
  if (piniaStore.loading !== vuexState.loading) {
    results.errors.push(`booking loading状态不同步`)
    results.passed = false
  }
  
  if (piniaStore.bookingList.length !== vuexState.bookingList.length) {
    results.warnings.push(`预订列表长度不同: Pinia=${piniaStore.bookingList.length}, Vuex=${vuexState.bookingList.length}`)
  }
}

// 验证Sharing store
function validateSharingStore(piniaStore, vuexState, results) {
  if (piniaStore.loading !== vuexState.loading) {
    results.errors.push(`sharing loading状态不同步`)
    results.passed = false
  }
  
  if (piniaStore.sharingOrders.length !== vuexState.sharingOrders.length) {
    results.warnings.push(`拼场订单列表长度不同: Pinia=${piniaStore.sharingOrders.length}, Vuex=${vuexState.sharingOrders.length}`)
  }
}

// 主验证函数
export function validateMigration() {
  console.log('[Migration Validator] 开始验证迁移状态')
  
  const results = {
    timestamp: new Date().toISOString(),
    overall: true,
    stores: {},
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      warnings: 0
    }
  }

  try {
    // 获取Vuex store状态（如果可用）
    let vuexStore = null
    try {
      if (typeof getCurrentPages === 'function') {
        const pages = getCurrentPages()
        if (pages.length > 0 && pages[0].$store) {
          vuexStore = pages[0].$store
        }
      }
    } catch (error) {
      console.warn('[Migration Validator] 无法获取Vuex store:', error)
    }

    // 验证各个store
    const stores = [
      { name: 'app', pinia: useAppStore(), vuex: vuexStore?.state },
      { name: 'user', pinia: useUserStore(), vuex: vuexStore?.state?.user },
      { name: 'venue', pinia: useVenueStore(), vuex: vuexStore?.state?.venue },
      { name: 'booking', pinia: useBookingStore(), vuex: vuexStore?.state?.booking },
      { name: 'sharing', pinia: useSharingStore(), vuex: vuexStore?.state?.sharing }
    ]

    stores.forEach(store => {
      const storeResult = validateStoreSync(store.name, store.pinia, store.vuex)
      results.stores[store.name] = storeResult
      results.summary.total++
      
      if (storeResult.passed) {
        results.summary.passed++
      } else {
        results.summary.failed++
        results.overall = false
      }
      
      results.summary.warnings += storeResult.warnings.length
    })

    console.log('[Migration Validator] 验证完成:', results.summary)
    return results
    
  } catch (error) {
    console.error('[Migration Validator] 验证过程中发生错误:', error)
    results.overall = false
    results.error = error.message
    return results
  }
}

// 开始持续验证
export function startValidation(interval = 5000) {
  console.log('[Migration Validator] 开始持续验证，间隔:', interval)
  
  if (validationInterval) {
    clearInterval(validationInterval)
  }
  
  validationInterval = setInterval(() => {
    const results = validateMigration()
    if (!results.overall) {
      console.warn('[Migration Validator] 发现同步问题:', results)
    }
  }, interval)
  
  return validationInterval
}

// 停止持续验证
export function stopValidation(intervalId = null) {
  const targetInterval = intervalId || validationInterval
  
  if (targetInterval) {
    clearInterval(targetInterval)
    console.log('[Migration Validator] 持续验证已停止')
    
    if (targetInterval === validationInterval) {
      validationInterval = null
    }
  }
}

// 获取验证统计
export function getValidationStats() {
  const results = validateMigration()
  return {
    overall: results.overall,
    totalStores: results.summary.total,
    passedStores: results.summary.passed,
    failedStores: results.summary.failed,
    totalWarnings: results.summary.warnings,
    successRate: results.summary.total > 0 ? (results.summary.passed / results.summary.total * 100).toFixed(1) : 0
  }
}

// 清理资源
export function cleanup() {
  stopValidation()
  console.log('[Migration Validator] 资源清理完成')
}
