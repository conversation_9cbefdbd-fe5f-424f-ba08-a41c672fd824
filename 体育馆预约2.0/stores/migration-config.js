// Pinia迁移配置文件
// 用于管理迁移状态和配置

export const migrationConfig = {
  // 迁移阶段配置
  phases: {
    // 阶段1: 全局状态 - 已完成
    phase1: {
      name: "全局状态迁移",
      status: "completed",
      modules: ["app"],
      description: "迁移loading、networkStatus等全局状态"
    },
    // 阶段2: 用户模块 - 已完成
    phase2: {
      name: "用户模块迁移", 
      status: "completed",
      modules: ["user"],
      description: "迁移用户认证、信息管理功能"
    },
    // 阶段3: 场馆模块 - 已完成
    phase3: {
      name: "场馆模块迁移",
      status: "completed", 
      modules: ["venue"],
      description: "迁移场馆列表、详情、搜索功能"
    },
    // 阶段4: 拼场模块 - 已完成
    phase4: {
      name: "拼场模块迁移",
      status: "completed",
      modules: ["sharing"],
      description: "迁移拼场创建、申请、管理功能"
    },
    // 阶段5: 预订模块 - 已完成
    phase5: {
      name: "预订模块迁移",
      status: "completed",
      modules: ["booking"],
      description: "迁移预订管理、订单处理功能"
    },
    // 阶段6: 清理 - 待开始
    phase6: {
      name: "清理Vuex",
      status: "pending",
      modules: [],
      description: "移除Vuex依赖，清理同步代码"
    }
  },
  
  // 当前激活的模块 - 控制哪些模块使用Pinia
  activeModules: ["app", "user", "venue", "sharing", "booking"],
  
  // 特性标志 - 控制是否启用Pinia功能
  features: {
    usePiniaForApp: true,
    usePiniaForUser: true,
    usePiniaForVenue: true,
    usePiniaForBooking: true,
    usePiniaForSharing: true,
    enableSync: true, // 是否启用Vuex-Pinia同步
    enableLogging: true // 是否启用迁移日志
  },
  
  // 测试配置
  testing: {
    enableMigrationTests: true,
    testModules: ["app", "user", "venue", "sharing", "booking"],
    autoValidation: true // 自动验证状态同步
  }
}

// 获取迁移状态
export function getMigrationStatus() {
  const phases = migrationConfig.phases
  const totalPhases = Object.keys(phases).length
  const completedPhases = Object.values(phases).filter(phase => phase.status === 'completed').length
  const inProgressPhases = Object.values(phases).filter(phase => phase.status === 'in-progress').length
  
  return {
    totalPhases,
    completedPhases,
    inProgressPhases,
    completionPercentage: Math.round((completedPhases / totalPhases) * 100),
    phases,
    activeModules: migrationConfig.activeModules,
    features: migrationConfig.features,
    testing: migrationConfig.testing
  }
}

// 更新阶段状态
export function updatePhaseStatus(phaseId, status) {
  if (migrationConfig.phases[phaseId]) {
    migrationConfig.phases[phaseId].status = status
    console.log(`[Migration Config] 阶段 ${phaseId} 状态更新为: ${status}`)
  }
}

// 启用/禁用模块
export function toggleModule(moduleName, enabled) {
  if (enabled) {
    if (!migrationConfig.activeModules.includes(moduleName)) {
      migrationConfig.activeModules.push(moduleName)
    }
  } else {
    const index = migrationConfig.activeModules.indexOf(moduleName)
    if (index > -1) {
      migrationConfig.activeModules.splice(index, 1)
    }
  }
  console.log(`[Migration Config] 模块 ${moduleName} ${enabled ? '启用' : '禁用'}`)
}

// 更新特性标志
export function updateFeature(featureName, enabled) {
  if (migrationConfig.features.hasOwnProperty(featureName)) {
    migrationConfig.features[featureName] = enabled
    console.log(`[Migration Config] 特性 ${featureName} ${enabled ? '启用' : '禁用'}`)
  }
}

// 检查模块是否激活
export function isModuleActive(moduleName) {
  return migrationConfig.activeModules.includes(moduleName)
}

// 检查特性是否启用
export function isFeatureEnabled(featureName) {
  return migrationConfig.features[featureName] === true
}

// 获取当前迁移进度
export function getMigrationProgress() {
  const status = getMigrationStatus()
  return {
    percentage: status.completionPercentage,
    completed: status.completedPhases,
    total: status.totalPhases,
    current: Object.values(status.phases).find(phase => phase.status === 'in-progress')?.name || '迁移完成'
  }
}
