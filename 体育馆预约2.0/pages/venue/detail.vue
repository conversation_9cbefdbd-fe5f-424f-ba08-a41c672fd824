<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text>加载中...</text>
    </view>
    
    <!-- 场馆详情内容 -->
    <view v-else-if="venueDetail" class="detail-content">
      <!-- 场馆图片轮播 -->
      <view class="image-section">
        <swiper class="venue-swiper" indicator-dots circular>
          <swiper-item v-for="(image, index) in venueImages" :key="index">
            <image :src="image" class="venue-image" mode="aspectFill" />
          </swiper-item>
        </swiper>
        <view class="back-btn" @click="goBack">
          <text>←</text>
        </view>
      </view>
    
    <!-- 场馆基本信息 -->
    <view class="info-section">
      <view class="venue-header">
        <text class="venue-name">{{ venueDetail.name }}</text>
        <view class="venue-rating">
          <text class="rating-score">{{ venueDetail.rating || '暂无评分' }}</text>
          <text class="rating-star" v-if="venueDetail.rating">⭐</text>
          <text class="rating-count" v-if="venueDetail.reviewCount">({{ venueDetail.reviewCount }}条评价)</text>
        </view>
      </view>

      <view class="venue-location">
        <text class="location-icon">📍</text>
        <text class="location-text">{{ venueDetail.location }}</text>
        <text class="distance-text" v-if="venueDetail.distance">距离{{ venueDetail.distance }}km</text>
      </view>
      
      <view class="venue-price">
        <text class="price-label">价格：</text>
        <text class="price-value">¥{{ venueDetail.price }}</text>
        <text class="price-unit">/小时</text>
      </view>
      
      <view class="venue-tags">
        <text class="venue-tag">{{ venueDetail.type }}</text>
        <text v-if="venueDetail.supportSharing" class="venue-tag">支持拼场</text>
        <text class="venue-tag">{{ venueDetail.status === 'ACTIVE' ? '营业中' : '暂停营业' }}</text>
      </view>
    </view>
    
    <!-- 场馆描述 -->
    <view class="description-section">
      <view class="section-title">场馆介绍</view>
      <text class="description-text">{{ venueDetail.description }}</text>
    </view>
    
    <!-- 设施信息 -->
    <view class="facilities-section">
      <view class="section-title">设施服务</view>
      <view class="facilities-grid">
        <view
          v-for="facility in facilitiesList"
          :key="facility"
          class="facility-item"
        >
          <text class="facility-icon">🏃</text>
          <text class="facility-name">{{ facility }}</text>
        </view>
      </view>
    </view>

    <!-- 营业时间 -->
    <view class="hours-section">
      <view class="section-title">营业时间</view>
      <view class="hours-info">
        <text class="hours-text">{{ venueDetail.openTime }} - {{ venueDetail.closeTime }}</text>
      </view>
    </view>
    
      <!-- 预约类型选择 -->
      <view class="booking-type-section" v-if="venueDetail.supportSharing">
        <view class="section-title">预约类型</view>
        <view class="booking-type-options">
          <label class="radio-item" @click="onBookingTypeChange('EXCLUSIVE')">
            <view class="radio-wrapper">
              <view class="radio-circle" :class="{ active: bookingType === 'EXCLUSIVE' }">
                <view class="radio-dot" v-if="bookingType === 'EXCLUSIVE'"></view>
              </view>
              <view class="radio-content">
                <text class="radio-title">独享预约</text>
                <text class="radio-desc">包场使用，享受完整场地</text>
              </view>
            </view>
          </label>
          
          <label class="radio-item" @click="onBookingTypeChange('SHARED')">
            <view class="radio-wrapper">
              <view class="radio-circle" :class="{ active: bookingType === 'SHARED' }">
                <view class="radio-dot" v-if="bookingType === 'SHARED'"></view>
              </view>
              <view class="radio-content">
                <text class="radio-title">拼场预约</text>
                <text class="radio-desc">与他人共享场地，费用更优惠</text>
              </view>
            </view>
          </label>
        </view>
        
        <!-- 拼场提示信息 -->
        <view class="shared-notice" v-if="bookingType === 'SHARED'">
          <!-- 拼场说明 -->
          <view class="sharing-notice">
            <text class="notice-title">拼场说明：</text>
            <text class="notice-text">• 拼场预约需要等待其他用户加入</text>
            <text class="notice-text">• 如果预约时间前2小时内无人加入，系统将自动退款</text>
            <text class="notice-text">• 请确保联系方式准确，便于其他用户联系</text>
          </view>
          
          <!-- 时间限制提示 -->
          <view class="time-notice">
            <text class="notice-text">⏰ 拼场预约需要提前3小时预约</text>
          </view>
        </view>
      </view>
      
      <!-- 时间段选择 -->
      <view class="timeslot-section">
        <view class="section-title">选择时间</view>
      
      <!-- 日期选择 -->
      <view class="date-selector">
        <scroll-view class="date-scroll" scroll-x>
          <view 
            v-for="(date, index) in availableDates" 
            :key="index" 
            class="date-item"
            :class="{ active: selectedDate === date.value }"
            @click="selectDate(date.value)"
          >
            <text class="date-day">{{ date.day }}</text>
            <text class="date-date">{{ date.date }}</text>
          </view>
        </scroll-view>
      </view>
      
      <!-- 时间段列表 -->
      <view class="timeslot-list">
        <view 
          v-for="slot in filteredTimeSlots" 
          :key="slot.id" 
          :class="getSlotClass(slot)"
          @click="selectTimeSlot(slot)"
        >
          <view class="slot-time">
            <text>{{ slot.startTime }} - {{ slot.endTime }}</text>
          </view>
          <view class="slot-price">
            <text>¥{{ slot.price }}</text>
          </view>
          <view class="slot-status">
            <text>{{ getSlotStatusText(slot.status) }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="contact-btn" @click="contactVenue">
          <text class="contact-icon">📞</text>
          <text class="contact-text">联系场馆</text>
        </view>
      </view>
      <view class="action-right">
        <button 
          class="book-btn" 
          :disabled="selectedTimeSlots.length === 0"
          @click="bookVenue"
        >
          {{ getBookButtonText() }}
        </button>
      </view>
    </view>
    
    <!-- 预约确认弹窗 -->
    <uni-popup ref="bookingPopup" type="center">
      <view class="booking-modal">
        <view class="modal-header">
          <text class="modal-title">确认预约</text>
        </view>
        
        <view class="booking-info">
          <!-- 基本信息 -->
          <view class="info-section">
            <view class="section-title">预约信息</view>
            <view class="info-item">
              <text class="info-label">场馆：</text>
              <text class="info-value">{{ venueDetail.name }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">地址：</text>
              <text class="info-value">{{ venueDetail.location }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">日期：</text>
              <text class="info-value">{{ formatSelectedDate() }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">时间：</text>
              <text class="info-value" v-if="selectedTimeSlots.length === 1">{{ selectedTimeSlots[0]?.startTime }} - {{ selectedTimeSlots[0]?.endTime }}</text>
              <text class="info-value" v-else-if="selectedTimeSlots.length > 1">{{ getFirstTimeSlot()?.startTime }} - {{ getLastTimeSlot()?.endTime }}</text>
              <text class="info-value" v-else>未选择</text>
            </view>
            <view class="info-item">
              <text class="info-label">时长：</text>
              <text class="info-value">{{ getBookingDuration() }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">预约类型：</text>
              <text class="info-value">{{ bookingType === 'EXCLUSIVE' ? '独享预约' : '拼场预约' }}</text>
            </view>
          </view>
          
          <!-- 拼场信息 -->
          <view class="info-section" v-if="bookingType === 'SHARED'">
            <view class="section-title">拼场信息</view>
            <view class="info-item">
              <text class="info-label">队伍名称：</text>
              <text class="info-value">{{ teamName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">联系方式：</text>
              <text class="info-value">{{ contactInfo }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">最大人数：</text>
              <text class="info-value">{{ maxParticipants }}人</text>
            </view>
          </view>
          
          <!-- 费用明细 -->
          <view class="info-section">
            <view class="section-title">费用明细</view>
            <view class="info-item" v-for="(slot, index) in selectedTimeSlots" :key="index">
              <text class="info-label">{{ slot.startTime }}-{{ slot.endTime }}：</text>
              <text class="info-value">¥{{ slot.price }}</text>
            </view>
            <view class="info-item total-price">
              <text class="info-label">总计：</text>
              <text class="info-value price">¥{{ getTotalPrice() }}</text>
            </view>
          </view>
          
          <!-- 联系信息 -->
          <view class="info-section" v-if="venueDetail.phone">
            <view class="section-title">联系方式</view>
            <view class="info-item">
              <text class="info-label">场馆电话：</text>
              <text class="info-value">{{ venueDetail.phone }}</text>
            </view>
          </view>
          
          <!-- 预约须知 -->
          <view class="info-section">
            <view class="section-title">预约须知</view>
            <view class="notice-list">
              <text class="notice-item">• 请提前15分钟到达场馆</text>
              <text class="notice-item">• 请携带有效身份证件</text>
              <text class="notice-item">• 如需取消请提前2小时联系场馆</text>
              <text class="notice-item">• 请遵守场馆相关规定</text>
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="cancel-btn" @click="closeBookingModal">取消</button>
          <button class="confirm-btn" @click="confirmBooking">确认预约</button>
        </view>
      </view>
      </uni-popup>
    </view>
    
    <!-- 错误状态 -->
    <view v-else class="error-container">
      <text>加载失败，请重试</text>
      <button @click="initData" class="retry-btn">重试</button>
    </view>
  </view>
</template>

<script>
import { useVenueStore } from '@/stores/venue.js'
import { useBookingStore } from '@/stores/booking.js'
import { formatDate } from '@/utils/helpers.js'

export default {
  name: 'VenueDetail',

  data() {
    return {
      venueStore: null,
      bookingStore: null,
      venueId: '',
      selectedDate: '',
      selectedTimeSlots: [], // 改为数组以支持多时间段选择
      availableDates: [],
      bookingType: 'EXCLUSIVE', // 预约类型：EXCLUSIVE(独享) 或 SHARED(拼场)
      teamName: '', // 拼场队伍名称
      contactInfo: '', // 拼场联系方式
      maxParticipants: 4 // 最大参与人数
    }
  },
  
  computed: {
    venueDetail() {
      return this.venueStore?.venueDetailGetter || {}
    },

    timeSlots() {
      return this.venueStore?.timeSlotsGetter || []
    },

    loading() {
      return this.venueStore?.isLoading || false
    },

    // 处理场馆图片
    venueImages() {
      if (this.venueDetail.image) {
        // 如果image是字符串，转换为数组
        if (typeof this.venueDetail.image === 'string') {
          return [this.venueDetail.image]
        }
        // 如果已经是数组，直接返回
        if (Array.isArray(this.venueDetail.image)) {
          return this.venueDetail.image
        }
      }
      // 默认图片
      return ['/static/default-venue.jpg']
    },

    // 处理设施列表
    facilitiesList() {
      if (this.venueDetail.facilities) {
        // 如果facilities是字符串，按逗号分割
        if (typeof this.venueDetail.facilities === 'string') {
          return this.venueDetail.facilities.split(',').map(f => f.trim()).filter(f => f)
        }
        // 如果已经是数组，直接返回
        if (Array.isArray(this.venueDetail.facilities)) {
          return this.venueDetail.facilities
        }
      }
      return []
    },
    
    // 过滤掉已过期的时间段
    filteredTimeSlots() {
      console.log('原始时间段数据:', this.timeSlots)
      const slots = this.timeSlots.filter(slot => slot.status !== 'EXPIRED')
      console.log('过滤后的时间段数据:', slots)
      return slots
    }
  },
  
  onLoad(options) {
    // 初始化Pinia stores
    this.venueStore = useVenueStore()
    this.bookingStore = useBookingStore()

    this.venueId = options.id
    this.initData()
  },
  
  onShow() {
    // 页面显示时刷新时间段数据，确保取消预约后状态能及时更新
    if (this.venueId && this.selectedDate) {
      // 添加延迟确保数据同步
      setTimeout(() => {
        this.loadTimeSlots()
      }, 200)
    }
  },
  
  onPullDownRefresh() {
    this.refreshData()
  },
  
  methods: {
    
    // 初始化数据
    async initData() {
      try {
        console.log('开始初始化数据，场馆ID:', this.venueId)
        if (!this.venueId) {
          console.error('场馆ID为空')
          uni.showToast({
            title: '参数错误',
            icon: 'error'
          })
          return
        }
        
        console.log('调用getVenueDetail，参数:', this.venueId)
        await this.venueStore.getVenueDetail(this.venueId)
        console.log('获取场馆详情成功:', this.venueDetail)
        
        this.initDates()
        if (this.selectedDate) {
          await this.loadTimeSlots()
        }
      } catch (error) {
        console.error('初始化数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },
    
    // 刷新数据
    async refreshData() {
      try {
        await this.initData()
        uni.stopPullDownRefresh()
      } catch (error) {
        uni.stopPullDownRefresh()
        console.error('刷新数据失败:', error)
      }
    },
    
    // 初始化可选日期
    initDates() {
      const dates = []
      const today = new Date()
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() + i)
        
        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        const day = i === 0 ? '今天' : i === 1 ? '明天' : dayNames[date.getDay()]
        
        dates.push({
          value: formatDate(date, 'YYYY-MM-DD'),
          day: day,
          date: formatDate(date, 'MM/DD')
        })
      }
      
      this.availableDates = dates
      this.selectedDate = dates[0].value
    },
    
    // 选择日期
    async selectDate(date) {
      this.selectedDate = date
      this.selectedTimeSlots = []
      await this.loadTimeSlots()
    },
    
    // 预约类型变化
    onBookingTypeChange(type) {
      this.bookingType = type
      
      // 清空已选择的时间段，因为过滤条件可能发生变化
      this.selectedTimeSlots = []
    },
    
    // 加载时间段
    async loadTimeSlots() {
      try {
        await this.venueStore.getVenueTimeSlots({
          venueId: this.venueId,
          date: this.selectedDate
        })
      } catch (error) {
        console.error('加载时间段失败:', error)
      }
    },
    
    // 选择时间段
    selectTimeSlot(slot) {
      console.log('点击时间段:', slot)
      console.log('时间段状态:', slot.status)
      
      // 首先检查时间段状态
      if (slot.status === 'OCCUPIED' || slot.status === 'RESERVED') {
        uni.showToast({
          title: '该时间段已被预约',
          icon: 'none',
          duration: 2000
        })
        return
      } else if (slot.status === 'MAINTENANCE') {
        uni.showToast({
          title: '该时间段维护中',
          icon: 'none',
          duration: 2000
        })
        return
      } else if (slot.status === 'EXPIRED') {
        uni.showToast({
          title: '该时间段已过期，无法预约',
          icon: 'none',
          duration: 2000
        })
        return
      } else if (slot.status === 'AVAILABLE') {
        // 如果是拼场预约，检查时间限制
        if (this.bookingType === 'SHARED') {
          if (!this.isTimeSlotValidForSharing(slot)) {
            uni.showToast({
              title: '拼场预约请选择三个小时以后的时间段',
              icon: 'none',
              duration: 3000
            })
            return
          }
        }
        // 检查时间段是否已被选中
        const existingIndex = this.selectedTimeSlots.findIndex(item => 
          (item.id && item.id === slot.id) || 
          (item.startTime === slot.startTime && item.endTime === slot.endTime)
        )
        
        // 如果已选中，则取消选择
        if (existingIndex !== -1) {
          this.selectedTimeSlots.splice(existingIndex, 1)
          console.log('取消选择时间段:', slot)
          uni.showToast({
            title: '已取消选择',
            icon: 'success',
            duration: 1000
          })
          return
        }
        
        // 如果已有选择的时间段，检查是否连续
        if (this.selectedTimeSlots.length > 0) {
          // 检查与已选时间段是否有连续的
          const hasConsecutive = this.selectedTimeSlots.some(selectedSlot => 
            this.isConsecutiveTimeSlot(selectedSlot, slot)
          )
          
          if (!hasConsecutive) {
            // 禁止选择不连续的时间段
            uni.showToast({
              title: '只能选择连续的时间段',
              icon: 'none',
              duration: 2000
            })
            return
          }
        }
        
        // 添加到已选时间段
        this.selectedTimeSlots.push(slot)
        console.log('已选择时间段:', this.selectedTimeSlots)
        uni.showToast({
          title: '已选择时间段',
          icon: 'success',
          duration: 1000
        })
      } else {
        // 处理未知状态
        uni.showToast({
          title: '该时间段不可用',
          icon: 'none',
          duration: 2000
        })
      }
    },
    
    // 检查两个时间段是否连续
    isConsecutiveTimeSlot(slot1, slot2) {
      // 使用已定义的方法将时间转换为分钟数
      const slot1End = this.getMinutesFromTimeString(slot1.endTime)
      const slot2Start = this.getMinutesFromTimeString(slot2.startTime)
      const slot1Start = this.getMinutesFromTimeString(slot1.startTime)
      const slot2End = this.getMinutesFromTimeString(slot2.endTime)
      
      // 检查两个时间段是否相邻
      return slot1End === slot2Start || slot2End === slot1Start
    },
    
    // 获取时间段样式类
    getSlotClass(slot) {
      const classes = ['timeslot-item']
      
      if (slot.status === 'OCCUPIED') {
        classes.push('occupied')
        classes.push('disabled') // 已预约的时间段添加禁用样式
      } else if (slot.status === 'RESERVED') {
        classes.push('occupied')
        classes.push('disabled') // 已预约的时间段添加禁用样式
      } else if (slot.status === 'MAINTENANCE') {
        classes.push('maintenance')
        classes.push('disabled') // 维护中的时间段添加禁用样式
      } else if (slot.status === 'EXPIRED') {
        classes.push('expired')
        classes.push('disabled') // 已过期的时间段添加禁用样式
      } 
      
      // 检查是否是选中的时间段
      const isSelected = this.selectedTimeSlots.some(selectedSlot => 
        (slot.id && selectedSlot.id === slot.id) || 
        (slot.startTime === selectedSlot.startTime && slot.endTime === selectedSlot.endTime)
      )
      
      if (isSelected) {
        classes.push('selected')
        console.log('添加选中样式:', slot)
      }
      
      return classes.join(' ')
    },
    
    // 获取第一个时间段（按开始时间排序）
    getFirstTimeSlot() {
      if (this.selectedTimeSlots.length === 0) return null
      
      return this.selectedTimeSlots.reduce((earliest, current) => {
        const earliestTime = this.getMinutesFromTimeString(earliest.startTime)
        const currentTime = this.getMinutesFromTimeString(current.startTime)
        return currentTime < earliestTime ? current : earliest
      }, this.selectedTimeSlots[0])
    },
    
    // 获取最后一个时间段（按结束时间排序）
    getLastTimeSlot() {
      if (this.selectedTimeSlots.length === 0) return null
      
      return this.selectedTimeSlots.reduce((latest, current) => {
        const latestTime = this.getMinutesFromTimeString(latest.endTime)
        const currentTime = this.getMinutesFromTimeString(current.endTime)
        return currentTime > latestTime ? current : latest
      }, this.selectedTimeSlots[0])
    },
    
    // 将时间字符串转换为分钟数
    getMinutesFromTimeString(timeStr) {
      const [hours, minutes] = timeStr.split(':').map(Number)
      return hours * 60 + minutes
    },
    
    // 检查时间段是否满足拼场预约的时间限制（需要提前3小时）
    isTimeSlotValidForSharing(slot) {
      const now = new Date()
      const selectedDateTime = new Date(`${this.selectedDate} ${slot.startTime}`)
      
      // 计算时间差（毫秒）
      const timeDiff = selectedDateTime.getTime() - now.getTime()
      
      // 转换为小时
      const hoursDiff = timeDiff / (1000 * 60 * 60)
      
      // 需要提前3小时以上
      return hoursDiff >= 3
    },
    
    // 计算总价格
    getTotalPrice() {
      if (this.selectedTimeSlots.length === 0) return 0
      
      return this.selectedTimeSlots.reduce((total, slot) => {
        return total + (slot.price || 0)
      }, 0)
    },
    
    // 获取时间段状态文本
    getSlotStatusText(status) {
      const statusMap = {
        'AVAILABLE': '可预约',
        'OCCUPIED': '已预约',
        'RESERVED': '已预约',
        'MAINTENANCE': '维护中',
        'EXPIRED': '已过期'
      }
      return statusMap[status] || '可预约'
    },
    
    // 获取预约按钮文本
    getBookButtonText() {
      if (this.selectedTimeSlots.length === 0) {
        return '请选择时间段'
      }
      
      return `预约 ${this.selectedTimeSlots.length} 个时间段`
    },
    
    // 预约场馆
    bookVenue() {
      if (this.selectedTimeSlots.length === 0) {
        uni.showToast({
          title: '请选择时间段',
          icon: 'none'
        })
        return
      }
      
      // 传递所有选中的时间段信息
      const selectedSlotsData = JSON.stringify(this.selectedTimeSlots)
      
      // 跳转到预约页面，传递预约类型参数和所有选中的时间段
      uni.navigateTo({
        url: `/pages/booking/create?venueId=${this.venueDetail.id}&date=${this.selectedDate}&bookingType=${this.bookingType}&selectedSlots=${encodeURIComponent(selectedSlotsData)}`
      })
    },
    
    // 关闭预约弹窗
    closeBookingModal() {
      this.$refs.bookingPopup.close()
    },
    
    // 确认预约
    async confirmBooking() {
      try {
        if (this.selectedTimeSlots.length === 0) {
          uni.showToast({
            title: '请选择时间段',
            icon: 'none'
          })
          return
        }
        

        
        uni.showLoading({ title: '预约中...' })
        
        // 创建单个预约，包含所有选中的时间段
        // 按时间顺序排序选中的时间段
        const sortedSlots = [...this.selectedTimeSlots].sort((a, b) => {
          const aTime = this.getMinutesFromTimeString(a.startTime)
          const bTime = this.getMinutesFromTimeString(b.startTime)
          return aTime - bTime
        })
        
        const startTime = sortedSlots[0].startTime
        const endTime = sortedSlots[sortedSlots.length - 1].endTime
        const slotIds = this.selectedTimeSlots.map(slot => slot.id)
        
        // 构建预约数据
        const bookingData = {
          venueId: this.venueId,
          date: this.selectedDate,
          startTime: startTime,
          endTime: endTime,
          slotIds: slotIds, // 传递所有时间段ID
          remark: this.selectedTimeSlots.length > 1 ? `连续预约${this.selectedTimeSlots.length}个时间段` : '',
          bookingType: this.bookingType
        }
        

        
        await this.bookingStore.createBooking(bookingData)
        
        // 重新获取时间段数据以刷新状态
        await this.venueStore.getVenueTimeSlots({
          venueId: this.venueId,
          date: this.selectedDate
        })
        
        uni.hideLoading()
        this.closeBookingModal()
        
        uni.showToast({
          title: '预约成功',
          icon: 'success'
        })
        
        // 清空选中的时间段
        this.selectedTimeSlots = []
        
        // 跳转到我的预约页面
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/booking/list'
          })
        }, 1500)
        
      } catch (error) {
        uni.hideLoading()
        console.error('预约失败:', error)
        uni.showToast({
          title: error.message || '预约失败',
          icon: 'error'
        })
      }
    },
    
    // 联系场馆
    contactVenue() {
      if (this.venueDetail.phone) {
        uni.makePhoneCall({
          phoneNumber: this.venueDetail.phone
        })
      } else {
        uni.showToast({
          title: '暂无联系方式',
          icon: 'none'
        })
      }
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 格式化选中日期
    formatSelectedDate() {
      const selectedDateObj = this.availableDates.find(d => d.value === this.selectedDate)
      return selectedDateObj ? `${selectedDateObj.day} ${selectedDateObj.date}` : this.selectedDate
    },
    
    // 计算预约时长
    getBookingDuration() {
      if (this.selectedTimeSlots.length === 0) {
        return '0小时'
      }
      
      if (this.selectedTimeSlots.length === 1) {
        const slot = this.selectedTimeSlots[0]
        const startMinutes = this.getMinutesFromTimeString(slot.startTime)
        const endMinutes = this.getMinutesFromTimeString(slot.endTime)
        const durationMinutes = endMinutes - startMinutes
        const hours = Math.floor(durationMinutes / 60)
        const minutes = durationMinutes % 60
        
        if (minutes === 0) {
          return `${hours}小时`
        } else {
          return `${hours}小时${minutes}分钟`
        }
      } else {
        // 多个时间段的情况
        const firstSlot = this.getFirstTimeSlot()
        const lastSlot = this.getLastTimeSlot()
        
        if (firstSlot && lastSlot) {
          const startMinutes = this.getMinutesFromTimeString(firstSlot.startTime)
          const endMinutes = this.getMinutesFromTimeString(lastSlot.endTime)
          const durationMinutes = endMinutes - startMinutes
          const hours = Math.floor(durationMinutes / 60)
          const minutes = durationMinutes % 60
          
          if (minutes === 0) {
            return `${hours}小时`
          } else {
            return `${hours}小时${minutes}分钟`
          }
        }
        
        return '0小时'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

// 图片轮播
.image-section {
  position: relative;
  height: 500rpx;
  
  .venue-swiper {
    height: 100%;
    
    .venue-image {
      width: 100%;
      height: 100%;
    }
  }
  
  .back-btn {
    position: absolute;
    top: 60rpx;
    left: 30rpx;
    width: 60rpx;
    height: 60rpx;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 32rpx;
    z-index: 10;
  }
}

// 基本信息
.info-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .venue-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;
    
    .venue-name {
      flex: 1;
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
      margin-right: 20rpx;
    }
    
    .venue-rating {
      display: flex;
      align-items: center;
      
      .rating-score {
        font-size: 28rpx;
        color: #ff6b35;
        margin-right: 8rpx;
      }
      
      .rating-star {
        font-size: 24rpx;
        margin-right: 8rpx;
      }
      
      .rating-count {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
  
  .venue-location {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .location-icon {
      font-size: 24rpx;
      margin-right: 8rpx;
    }
    
    .location-text {
      flex: 1;
      font-size: 28rpx;
      color: #666666;
    }
    
    .distance-text {
      font-size: 24rpx;
      color: #999999;
    }
  }
  
  .venue-price {
    display: flex;
    align-items: baseline;
    margin-bottom: 20rpx;
    
    .price-label {
      font-size: 28rpx;
      color: #333333;
    }
    
    .price-value {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff6b35;
      margin: 0 8rpx;
    }
    
    .price-unit {
      font-size: 24rpx;
      color: #999999;
    }
  }
  
  .venue-tags {
    display: flex;
    flex-wrap: wrap;
    
    .venue-tag {
      font-size: 22rpx;
      color: #666666;
      background-color: #f0f0f0;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      margin-right: 16rpx;
      margin-bottom: 12rpx;
    }
  }
}

// 通用区块样式
.description-section,
.facilities-section,
.hours-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20rpx;
  }
}

// 描述
.description-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

// 设施
.facilities-grid {
  display: flex;
  flex-wrap: wrap;
  
  .facility-item {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30rpx;
    
    .facility-icon {
      font-size: 40rpx;
      margin-bottom: 12rpx;
    }
    
    .facility-name {
      font-size: 24rpx;
      color: #666666;
      text-align: center;
    }
  }
}

// 营业时间
.hours-info {
  .hours-text {
    font-size: 28rpx;
    color: #666666;
  }
}

// 时间段选择
.timeslot-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 30rpx;
  }
  
  // 日期选择
  .date-selector {
    margin-bottom: 30rpx;
    
    .date-scroll {
      white-space: nowrap;
      
      .date-item {
        display: inline-block;
        text-align: center;
        padding: 20rpx 30rpx;
        margin-right: 20rpx;
        background-color: #f5f5f5;
        border-radius: 12rpx;
        min-width: 120rpx;
        
        &.active {
          background-color: #ff6b35;
          color: #ffffff;
        }
        
        .date-day {
          display: block;
          font-size: 24rpx;
          margin-bottom: 8rpx;
        }
        
        .date-date {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
        }
      }
    }
  }
  
  // 时间段列表
  .timeslot-list {
    .timeslot-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 16rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      border: 2rpx solid transparent;
      position: relative; // 为禁用遮罩层提供定位基准
      
      &.selected {
        background-color: #fff7f0;
        border-color: #ff6b35;
      }
      
      &.occupied {
        background-color: #f5f5f5;
        opacity: 0.6;
        cursor: not-allowed;
        
        .slot-status {
          color: #999999;
          font-weight: 500;
        }
        
        .slot-time {
          color: #999999;
        }
        
        .slot-price {
          color: #cccccc;
        }
      }
      
      &.maintenance {
        background-color: #fff7e6;
        opacity: 0.8;
        cursor: not-allowed;
        
        .slot-status {
          color: #ff9500;
          font-weight: 500;
        }
      }
      
      &.expired {
        background-color: #f0f0f0;
        opacity: 0.5;
        cursor: not-allowed;
        
        .slot-status {
          color: #999999;
          font-weight: 500;
        }
        
        .slot-time {
          color: #cccccc;
          text-decoration: line-through;
        }
        
        .slot-price {
          color: #cccccc;
          text-decoration: line-through;
        }
      }
      
      &.disabled {
        pointer-events: none; // 禁用点击事件
        
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: 12rpx;
        }
      }
      
      .slot-time {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;
      }
      
      .slot-price {
        margin-right: 30rpx;
        font-size: 28rpx;
        color: #ff6b35;
        font-weight: 600;
      }
      
      .slot-status {
        font-size: 24rpx;
        color: #666666;
      }
    }
  }
  
  // 预约类型选择
  .booking-type-section {
    margin-bottom: 32rpx;
    
    .booking-type-options {
      margin-top: 24rpx;
    }
    
    .radio-item {
      display: block;
      margin-bottom: 24rpx;
      
      .radio-wrapper {
        display: flex;
        align-items: flex-start;
        padding: 24rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        border: 2rpx solid transparent;
        transition: all 0.3s ease;
        
        &:active {
          background-color: #f0f0f0;
        }
      }
      
      .radio-circle {
        width: 40rpx;
        height: 40rpx;
        border: 2rpx solid #ddd;
        border-radius: 50%;
        margin-right: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        margin-top: 4rpx;
        
        &.active {
          border-color: #ff6b35;
          background-color: #ff6b35;
        }
        
        .radio-dot {
          width: 16rpx;
          height: 16rpx;
          background-color: white;
          border-radius: 50%;
        }
      }
      
      .radio-content {
        flex: 1;
        
        .radio-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .radio-desc {
          display: block;
          font-size: 26rpx;
          color: #666;
          line-height: 1.4;
        }
      }
    }
    
    // 拼场表单
    .shared-form {
      margin-top: 32rpx;
      padding: 32rpx;
      background-color: #f8f9fa;
      border-radius: 16rpx;
      
      .form-item {
        margin-bottom: 32rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .item-label {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 16rpx;
          font-weight: 500;
        }
        
        .form-input {
          width: 100%;
          padding: 24rpx;
          background-color: white;
          border: 2rpx solid #e0e0e0;
          border-radius: 12rpx;
          font-size: 28rpx;
          color: #333;
          
          &:focus {
            border-color: #ff6b35;
          }
        }
        
        .picker-text {
          padding: 24rpx;
          background-color: white;
          border: 2rpx solid #e0e0e0;
          border-radius: 12rpx;
          font-size: 28rpx;
          color: #333;
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          &::after {
            content: '>';
            color: #999;
            font-size: 24rpx;
          }
        }
      }
      
      // 拼场说明
      .sharing-notice {
        background-color: #fff3e0;
        border: 2rpx solid #ffcc80;
        border-radius: 12rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
        
        .notice-title {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #e65100;
          margin-bottom: 16rpx;
        }
        
        .notice-text {
          display: block;
          font-size: 26rpx;
          color: #bf360c;
          line-height: 1.5;
          margin-bottom: 8rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      
      // 时间限制提示
      .time-notice {
        background-color: #e3f2fd;
        border: 2rpx solid #90caf9;
        border-radius: 12rpx;
        padding: 20rpx 24rpx;
        
        .notice-text {
          font-size: 26rpx;
          color: #1565c0;
          line-height: 1.4;
        }
      }
    }
  }
}
</style>
// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
  
  .action-left {
    margin-right: 20rpx;
    
    .contact-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16rpx;
      
      .contact-icon {
        font-size: 32rpx;
        margin-bottom: 8rpx;
      }
      
      .contact-text {
        font-size: 20rpx;
        color: #666666;
      }
    }
  }
  
  .action-right {
    flex: 1;
    
    .book-btn {
      width: 100%;
      height: 80rpx;
      background-color: #ff6b35;
      color: #ffffff;
      border: none;
      border-radius: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      
      &:disabled {
        background-color: #cccccc;
        color: #999999;
      }
    }
  }
}

// 预约弹窗
.booking-modal {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  
  .modal-header {
    padding: 30rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }
  
  .booking-info {
    padding: 30rpx;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        font-size: 28rpx;
        color: #666666;
      }
      
      .info-value {
        font-size: 28rpx;
        color: #333333;
        
        &.price {
          color: #ff6b35;
          font-weight: 600;
        }
      }
    }
  }
  
  .modal-actions {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    
    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 100rpx;
      border: none;
      font-size: 28rpx;
    }
    
    .cancel-btn {
      background-color: #f5f5f5;
      color: #666666;
    }
    
    .confirm-btn {
      background-color: #ff6b35;
      color: #ffffff;
    }
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  font-size: 32rpx;
  color: #666666;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  
  text {
    font-size: 32rpx;
    color: #666666;
    margin-bottom: 40rpx;
  }
  
  .retry-btn {
    background-color: #ff6b35;
    color: white;
    border: none;
    border-radius: 10rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
  }
}
