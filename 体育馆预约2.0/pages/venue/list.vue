<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <input 
          v-model="searchKeyword" 
          placeholder="搜索场馆名称或地址" 
          class="search-input"
          @input="onSearchInput"
          @confirm="handleSearch"
          confirm-type="search"
        />
        <view class="search-icon" @click="handleSearch">
          <text>🔍</text>
        </view>
      </view>
    </view>
    
    <!-- 筛选栏 -->
    <view class="filter-section">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-item" 
              :class="{ active: selectedType === '' }" 
              @click="selectType('')">
          全部
        </view>
        <view 
          v-for="type in (venueTypes && Array.isArray(venueTypes) ? venueTypes : [])" 
          :key="type.id" 
          class="filter-item"
          :class="{ active: selectedType === type.id }"
          @click="selectType(type.id)"
        >
          {{ type.name }}
        </view>
      </scroll-view>
      <view class="filter-more" @click="showFilterModal">
        <text>筛选</text>
      </view>
    </view>
    
    <!-- 场馆列表 -->
    <view class="venue-list">
      <view 
        v-for="venue in (filteredVenues && Array.isArray(filteredVenues) ? filteredVenues : [])" 
        :key="venue.id" 
        class="venue-card"
        @click="navigateToDetail(venue.id)"
      >
        <image :src="(venue.image) || '/static/default-venue.jpg'" class="venue-image" mode="aspectFill" />
        <view class="venue-info">
          <view class="venue-header">
            <text class="venue-name">{{ venue.name || '未知场馆' }}</text>
            <view class="venue-rating">
              <text class="rating-score">{{ venue.rating || '4.5' }}</text>
              <text class="rating-star">⭐</text>
            </view>
          </view>
          <text class="venue-location">📍 {{ venue.location || '位置未知' }}</text>
          <view class="venue-tags">
            <text class="venue-tag">{{ venue.type || '运动场馆' }}</text>
            <text v-if="venue.supportSharing" class="venue-tag sharing-tag">支持拼场</text>
          </view>
          <view class="venue-footer">
            <view class="venue-price">
              <text class="price-text">¥{{ venue.price || 0 }}</text>
              <text class="price-unit">/小时</text>
            </view>
            <view class="venue-status" :class="getStatusClass(venue.status)">
              {{ getStatusText(venue.status) }}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 无数据提示 -->
      <view v-if="(!filteredVenues || !Array.isArray(filteredVenues) || filteredVenues.length === 0) && !loading" class="no-data">
        <text class="no-data-text">{{ searchKeyword ? '未找到相关场馆' : '暂无场馆数据' }}</text>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view v-if="hasMore" class="load-more" @click="loadMore">
      <text>{{ loading ? '加载中...' : '加载更多' }}</text>
    </view>
    
    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <text class="modal-close" @click="closeFilterModal">✕</text>
        </view>
        
        <view class="filter-content">
          <!-- 价格筛选 -->
          <view class="filter-group">
            <text class="group-title">价格范围</text>
            <view class="price-range">
              <input 
                v-model="filterOptions.minPrice" 
                type="number" 
                placeholder="最低价格" 
                class="price-input"
              />
              <text class="price-separator">-</text>
              <input 
                v-model="filterOptions.maxPrice" 
                type="number" 
                placeholder="最高价格" 
                class="price-input"
              />
            </view>
          </view>
          
          <!-- 距离筛选 -->
          <view class="filter-group">
            <text class="group-title">距离范围</text>
            <view class="distance-options">
              <view 
                v-for="distance in (distanceOptions && Array.isArray(distanceOptions) ? distanceOptions : [])" 
                :key="distance.value" 
                class="distance-item"
                :class="{ active: filterOptions.distance === distance.value }"
                @click="selectDistance(distance.value)"
              >
                {{ distance.label }}
              </view>
            </view>
          </view>
          
          <!-- 设施筛选 -->
          <view class="filter-group">
            <text class="group-title">设施要求</text>
            <view class="facility-options">
              <view 
                v-for="facility in (facilityOptions && Array.isArray(facilityOptions) ? facilityOptions : [])" 
                :key="facility.value" 
                class="facility-item"
                :class="{ active: (filterOptions.facilities && Array.isArray(filterOptions.facilities) ? filterOptions.facilities : []).includes(facility.value) }"
                @click="toggleFacility(facility.value)"
              >
                {{ facility.label }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="reset-btn" @click="resetFilter">重置</button>
          <button class="confirm-btn" @click="applyFilter">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { useVenueStore } from '@/stores/venue.js'
import { debounce } from '@/utils/helpers.js'
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'

export default {
  name: 'VenueList',
  
  components: {
    uniPopup
  },
  
  data() {
    return {
      venueStore: null,
      searchKeyword: '',
      selectedType: '',
      
      // 筛选选项
      filterOptions: {
        minPrice: '',
        maxPrice: '',
        distance: '',
        facilities: []
      },
      
      // 距离选项
      distanceOptions: [
        { label: '1km内', value: 1 },
        { label: '3km内', value: 3 },
        { label: '5km内', value: 5 },
        { label: '10km内', value: 10 }
      ],
      
      // 设施选项
      facilityOptions: [
        { label: '停车场', value: 'parking' },
        { label: '淋浴间', value: 'shower' },
        { label: '更衣室', value: 'locker' },
        { label: 'WiFi', value: 'wifi' },
        { label: '空调', value: 'ac' },
        { label: '器材租赁', value: 'equipment' }
      ]
    }
  },
  
  computed: {
    venueList() {
      return this.venueStore?.venueListGetter || []
    },

    venueTypes() {
      return this.venueStore?.venueTypesGetter || []
    },

    searchResults() {
      return this.venueStore?.getSearchResults || []
    },

    loading() {
      return this.venueStore?.isLoading || false
    },

    pagination() {
      return this.venueStore?.getPagination || { current: 0, totalPages: 0 }
    },
    
    filteredVenues() {
      if (this.searchKeyword && this.searchKeyword.trim()) {
        return Array.isArray(this.searchResults) ? this.searchResults : []
      }
      return Array.isArray(this.venueList) ? this.venueList : []
    },
    
    hasMore() {
      return this.pagination && this.pagination.current < this.pagination.totalPages
    }
  },
  
  onLoad() {
    // 初始化Pinia store
    this.venueStore = useVenueStore()

    this.initData()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },
  
  onPullDownRefresh() {
    this.refreshData()
  },
  
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  
  methods: {
    
    // 初始化数据
    async initData() {
      try {
        console.log('开始初始化场馆数据...')
        const results = await Promise.all([
          this.venueStore.getVenueList({ page: 1, pageSize: 50 }),
          this.venueStore.getVenueTypes()
        ])
        console.log('场馆数据获取结果:', results)
        console.log('当前场馆列表:', this.venueList)
        console.log('当前场馆类型:', this.venueTypes)
        console.log('场馆类型数据类型:', typeof this.venueTypes, Array.isArray(this.venueTypes))
        this.updatePagination()
      } catch (error) {
        console.error('初始化数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      }
    },
    
    // 刷新数据
    async refreshData() {
      try {
        await this.venueStore.getVenueList({ page: 1, pageSize: 50, refresh: true })
        this.updatePagination()
        uni.stopPullDownRefresh()
      } catch (error) {
        uni.stopPullDownRefresh()
        console.error('刷新数据失败:', error)
      }
    },
    
    // 加载更多
    async loadMore() {
      if (this.loading || !this.hasMore) return

      try {
        const nextPage = this.pagination.current + 1

        // 清理参数，移除空值
        const params = {
          page: nextPage,
          pageSize: 50
        }

        // 只添加有值的参数
        if (this.selectedType) {
          params.type = this.selectedType
        }
        if (this.filterOptions.minPrice) {
          params.minPrice = Number(this.filterOptions.minPrice)
        }
        if (this.filterOptions.maxPrice) {
          params.maxPrice = Number(this.filterOptions.maxPrice)
        }
        if (this.filterOptions.distance) {
          params.distance = this.filterOptions.distance
        }
        if (this.filterOptions.facilities && Array.isArray(this.filterOptions.facilities) && this.filterOptions.facilities.length > 0) {
          params.facilities = this.filterOptions.facilities.join(',')
        }

        await this.venueStore.getVenueList(params)
        this.updatePagination()
      } catch (error) {
        console.error('加载更多失败:', error)
      }
    },
    
    // 更新分页状态
    updatePagination() {
      // hasMore已经通过computed属性计算，这里不需要重复赋值
    },
    
    // 搜索输入处理
    onSearchInput: debounce(function() {
      // 输入时不自动搜索，避免显示上次结果
      // 只有用户主动点击搜索按钮或按回车时才搜索
    }, 500),
    
    // 处理搜索
    async handleSearch() {
      const trimmedKeyword = this.searchKeyword ? this.searchKeyword.trim() : ''
      
      if (!trimmedKeyword) {
        // 如果搜索关键词为空，清空搜索结果并重新加载场馆列表
        this.venueStore.setSearchResults([])
        await this.venueStore.getVenueList({ page: 1, pageSize: 50, refresh: true })
        this.updatePagination()
        return
      }
      
      try {
        uni.showLoading({ title: '搜索中...' })
        
        // 立即清空旧的搜索结果，确保UI立即更新
        this.venueStore.setSearchResults([])
        
        // 强制更新视图
        this.$forceUpdate()
        
        const params = {
          keyword: trimmedKeyword,
          page: 1,
          pageSize: 50
        }
        
        // 添加类型筛选
        if (this.selectedType) {
          params.type = this.selectedType
        }
        
        // 添加价格筛选
        if (this.filterOptions.minPrice) {
          params.minPrice = Number(this.filterOptions.minPrice)
        }
        if (this.filterOptions.maxPrice) {
          params.maxPrice = Number(this.filterOptions.maxPrice)
        }
        
        // 添加其他筛选参数
        if (this.filterOptions.distance) {
          params.distance = this.filterOptions.distance
        }
        if (this.filterOptions.facilities && Array.isArray(this.filterOptions.facilities) && this.filterOptions.facilities.length > 0) {
          params.facilities = this.filterOptions.facilities.join(',')
        }
        
        console.log('执行搜索，参数:', params)
        await this.venueStore.searchVenues(params)
        console.log('搜索完成，结果:', this.searchResults)

        // 如果搜索结果为空，尝试获取所有场馆作为对比
        if (this.searchResults.length === 0) {
          console.log('搜索结果为空，尝试获取所有场馆...')
          await this.venueStore.getVenueList({ page: 1, pageSize: 10 })
          console.log('所有场馆列表:', this.venueStore.venueListGetter)
        }
        
        uni.hideLoading()
      } catch (error) {
        console.error('搜索失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'error'
        })
      }
    },
    
    // 选择场馆类型
    async selectType(typeId) {
      this.selectedType = typeId
      
      // 如果选择"全部"，清空搜索关键词和重置筛选条件
      if (typeId === '') {
        this.searchKeyword = ''
        this.resetFilter()
        // 清空搜索结果
        this.venueStore.setSearchResults([])
      }
      
      try {
        const params = { 
          page: 1, 
          pageSize: 50, 
          refresh: true,
          ...this.filterOptions
        }
        
        // 只有当typeId不为空时才添加type参数
        if (typeId) {
          params.type = typeId
        }
        
        console.log('选择场馆类型，参数:', params)
        await this.venueStore.getVenueList(params)
        this.updatePagination()
      } catch (error) {
        console.error('筛选失败:', error)
      }
    },
    
    // 跳转到详情页
    navigateToDetail(venueId) {
      uni.navigateTo({
        url: `/pages/venue/detail?id=${venueId}`
      })
    },
    
    // 显示筛选弹窗
    showFilterModal() {
      this.$refs.filterPopup.open()
    },
    
    // 关闭筛选弹窗
    closeFilterModal() {
      this.$refs.filterPopup.close()
    },
    
    // 选择距离
    selectDistance(distance) {
      this.filterOptions.distance = distance
    },
    
    // 切换设施选择
    toggleFacility(facility) {
      if (!Array.isArray(this.filterOptions.facilities)) {
        this.filterOptions.facilities = []
      }
      const index = this.filterOptions.facilities.indexOf(facility)
      if (index > -1) {
        this.filterOptions.facilities.splice(index, 1)
      } else {
        this.filterOptions.facilities.push(facility)
      }
    },
    
    // 重置筛选
    resetFilter() {
      this.filterOptions = {
        minPrice: '',
        maxPrice: '',
        distance: '',
        facilities: []
      }
    },
    
    // 应用筛选
    async applyFilter() {
      this.closeFilterModal()
      try {
        // 如果有搜索关键词，使用搜索接口
        if (this.searchKeyword && this.searchKeyword.trim()) {
          const params = {
            keyword: this.searchKeyword.trim()
          }
          
          // 添加类型筛选
          if (this.selectedType) {
            params.type = this.selectedType
          }
          
          // 添加价格筛选参数
          if (this.filterOptions.minPrice) {
            params.minPrice = Number(this.filterOptions.minPrice)
          }
          if (this.filterOptions.maxPrice) {
            params.maxPrice = Number(this.filterOptions.maxPrice)
          }
          
          // 添加其他筛选参数
          if (this.filterOptions.distance) {
            params.distance = this.filterOptions.distance
          }
          if (this.filterOptions.facilities && this.filterOptions.facilities.length > 0) {
            params.facilities = this.filterOptions.facilities.join(',')
          }
          
          console.log('应用筛选（搜索模式），参数:', params)
          await this.venueStore.searchVenues(params)
        } else {
          // 没有搜索关键词，使用场馆列表接口
          const params = { 
            page: 1, 
            pageSize: 50,
            refresh: true
          }
          
          // 只有当selectedType不为空时才添加type参数
          if (this.selectedType) {
            params.type = this.selectedType
          }
          
          // 添加价格筛选参数
          if (this.filterOptions.minPrice) {
            params.minPrice = Number(this.filterOptions.minPrice)
          }
          if (this.filterOptions.maxPrice) {
            params.maxPrice = Number(this.filterOptions.maxPrice)
          }
          
          // 添加其他筛选参数
          if (this.filterOptions.distance) {
            params.distance = this.filterOptions.distance
          }
          if (this.filterOptions.facilities && this.filterOptions.facilities.length > 0) {
            params.facilities = this.filterOptions.facilities.join(',')
          }
          
          console.log('应用筛选（列表模式），参数:', params)
          await this.venueStore.getVenueList(params)
          this.updatePagination()
        }
      } catch (error) {
        console.error('应用筛选失败:', error)
        uni.showToast({
          title: '筛选失败，请重试',
          icon: 'error'
        })
      }
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        'AVAILABLE': 'status-available',
        'MAINTENANCE': 'status-maintenance',
        'OCCUPIED': 'status-occupied'
      }
      return statusMap[status] || 'status-available'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'AVAILABLE': '可预约',
        'MAINTENANCE': '维护中',
        'OCCUPIED': '已满'
      }
      return statusMap[status] || '可预约'
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 搜索栏
.search-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  
  .search-bar {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 50rpx;
    padding: 0 30rpx;
    
    .search-input {
      flex: 1;
      height: 80rpx;
      font-size: 28rpx;
      border: none;
      background: transparent;
    }
    
    .search-icon {
      font-size: 32rpx;
      color: #666666;
    }
  }
}

// 筛选栏
.filter-section {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .filter-scroll {
    flex: 1;
    white-space: nowrap;
    
    .filter-item {
      display: inline-block;
      padding: 12rpx 24rpx;
      margin-right: 20rpx;
      background-color: #f5f5f5;
      border-radius: 30rpx;
      font-size: 24rpx;
      color: #666666;
      
      &.active {
        background-color: #ff6b35;
        color: #ffffff;
      }
    }
  }
  
  .filter-more {
    padding: 12rpx 24rpx;
    background-color: #f5f5f5;
    border-radius: 30rpx;
    font-size: 24rpx;
    color: #666666;
  }
}

// 场馆列表
.venue-list {
  padding: 20rpx 30rpx;
  
  .venue-card {
    display: flex;
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    
    .venue-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 12rpx;
      margin-right: 24rpx;
    }
    
    .venue-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .venue-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12rpx;
        
        .venue-name {
          flex: 1;
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin-right: 20rpx;
        }
        
        .venue-rating {
          display: flex;
          align-items: center;
          
          .rating-score {
            font-size: 24rpx;
            color: #ff6b35;
            margin-right: 4rpx;
          }
          
          .rating-star {
            font-size: 20rpx;
          }
        }
      }
      
      .venue-location {
        font-size: 24rpx;
        color: #666666;
        margin-bottom: 16rpx;
      }
      
      .venue-tags {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 16rpx;
        
        .venue-tag {
          font-size: 20rpx;
          color: #999999;
          background-color: #f0f0f0;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
          margin-right: 12rpx;
          margin-bottom: 8rpx;
        }
        
        .sharing-tag {
          background-color: #e8f5e8;
          color: #52c41a;
        }
      }
      
      .venue-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .venue-price {
          display: flex;
          align-items: baseline;
          
          .price-text {
            font-size: 32rpx;
            font-weight: 600;
            color: #ff6b35;
          }
          
          .price-unit {
            font-size: 24rpx;
            color: #999999;
            margin-left: 4rpx;
          }
        }
        
        .venue-status {
          font-size: 20rpx;
          padding: 6rpx 16rpx;
          border-radius: 16rpx;
          
          &.status-available {
            background-color: #e6f7ff;
            color: #1890ff;
          }
          
          &.status-maintenance {
            background-color: #fff7e6;
            color: #fa8c16;
          }
          
          &.status-occupied {
            background-color: #fff2f0;
            color: #ff4d4f;
          }
        }
      }
    }
  }
}

// 加载更多
.load-more {
  text-align: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #666666;
}

// 筛选弹窗
.filter-modal {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .modal-close {
      font-size: 32rpx;
      color: #999999;
    }
  }
  
  .filter-content {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;
    
    .filter-group {
      margin-bottom: 40rpx;
      
      .group-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 20rpx;
        display: block;
      }
      
      // 价格范围
      .price-range {
        display: flex;
        align-items: center;
        
        .price-input {
          flex: 1;
          height: 80rpx;
          border: 1rpx solid #e0e0e0;
          border-radius: 8rpx;
          padding: 0 20rpx;
          font-size: 28rpx;
        }
        
        .price-separator {
          margin: 0 20rpx;
          color: #999999;
        }
      }
      
      // 距离选项
      .distance-options {
        display: flex;
        flex-wrap: wrap;
        
        .distance-item {
          padding: 16rpx 32rpx;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          background-color: #f5f5f5;
          border-radius: 30rpx;
          font-size: 24rpx;
          color: #666666;
          
          &.active {
            background-color: #ff6b35;
            color: #ffffff;
          }
        }
      }
      
      // 设施选项
      .facility-options {
        display: flex;
        flex-wrap: wrap;
        
        .facility-item {
          padding: 16rpx 32rpx;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          background-color: #f5f5f5;
          border-radius: 30rpx;
          font-size: 24rpx;
          color: #666666;
          
          &.active {
            background-color: #ff6b35;
            color: #ffffff;
          }
        }
      }
    }
  }
  
  .modal-footer {
    display: flex;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    
    .reset-btn {
      flex: 1;
      height: 80rpx;
      background-color: #f5f5f5;
      color: #666666;
      border: none;
      border-radius: 8rpx;
      margin-right: 20rpx;
      font-size: 28rpx;
    }
    
    .confirm-btn {
      flex: 2;
      height: 80rpx;
      background-color: #ff6b35;
      color: #ffffff;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
    }
  }
}
</style>