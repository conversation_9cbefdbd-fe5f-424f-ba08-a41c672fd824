2025-07-19T15:12:03.050+08:00 DEBUG 64081 --- [scheduling-1] org.hibernate.orm.sql.ast.tree           : SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (so1 : com.example.gymbooking.model.SharingOrder(75)) {
          primaryTableReference : sharing_orders as so1_0
        }
      }
    }

2025-07-19T15:12:03.054+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 insertions, 0 updates, 0 deletions to 13 objects
2025-07-19T15:12:03.054+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-07-19T15:12:03.054+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : Listing entities:
2025-07-19T15:12:03.054+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=加入拼场, bookingTime=2025-07-14T16:00, fieldName=社区足球场默认场地, orderNo=ORD1752410499715958, contactInfo=, originalEndTime=null, totalPrice=200.0, currentParticipants=1, description=加入拼场订单, originalStartTime=null, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-13T20:41:39.712393, bookingType=SHARED, venueId=29, id=342, allowSharing=true, endTime=2025-07-14T18:00, fieldId=29, status=OPEN, updatedAt=2025-07-13T21:04:51.041066, username=13402838503}
2025-07-19T15:12:03.054+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=12312312, bookingTime=2025-07-16T07:00, fieldName=梦想足球场默认场地, orderNo=ORD1752482034442342, contactInfo=12312312312, originalEndTime=null, totalPrice=100.0, currentParticipants=2, description=12312312312, originalStartTime=null, maxParticipants=2, venueName=梦想足球场, createdAt=2025-07-14T16:33:54.440723, bookingType=SHARED, venueId=40, id=348, allowSharing=true, endTime=2025-07-16T09:00, fieldId=40, status=OPEN, updatedAt=2025-07-14T16:43:46.605570, username=13402838501}
2025-07-19T15:12:03.055+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=213213, bookingTime=2025-07-16T06:00, fieldName=绿茵足球场默认场地, orderNo=ORD1752480947703533, contactInfo=1233122, originalEndTime=null, totalPrice=120.0, currentParticipants=2, description=123123123123, originalStartTime=null, maxParticipants=2, venueName=绿茵足球场, createdAt=2025-07-14T16:15:47.701510, bookingType=SHARED, venueId=34, id=347, allowSharing=true, endTime=2025-07-16T08:00, fieldId=34, status=OPEN, updatedAt=2025-07-14T16:20:45.565676, username=13402838501}
2025-07-19T15:12:03.055+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=1231231, bookingTime=2025-07-16T08:00, fieldName=阳光体育馆默认场地, orderNo=ORD1752480713255467, contactInfo=123123123123, originalEndTime=null, totalPrice=80.0, currentParticipants=2, description=1232313, originalStartTime=null, maxParticipants=2, venueName=阳光体育馆, createdAt=2025-07-14T16:11:53.252052, bookingType=SHARED, venueId=33, id=346, allowSharing=true, endTime=2025-07-16T10:00, fieldId=33, status=OPEN, updatedAt=2025-07-14T16:17:46.534684, username=13402838501}
2025-07-19T15:12:03.055+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=12312313, bookingTime=2025-07-15T09:00, fieldName=社区足球场默认场地, orderNo=ORD175242086205312, contactInfo=123123123, originalEndTime=null, totalPrice=200.0, currentParticipants=2, description=213123213, originalStartTime=null, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-13T23:34:22.051617, bookingType=SHARED, venueId=29, id=345, allowSharing=true, endTime=2025-07-15T11:00, fieldId=29, status=OPEN, updatedAt=2025-07-14T15:53:31.048746, username=13402838501}
2025-07-19T15:12:03.055+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=1231312, bookingTime=2025-07-16T09:00, fieldName=社区足球场默认场地, orderNo=ORD1752483293311430, contactInfo=2312312312, originalEndTime=null, totalPrice=200.0, currentParticipants=2, description=132132312, originalStartTime=null, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-14T16:54:53.310295, bookingType=SHARED, venueId=29, id=350, allowSharing=true, endTime=2025-07-16T11:00, fieldId=29, status=OPEN, updatedAt=2025-07-14T16:56:42.018414, username=13402838501}
2025-07-19T15:12:03.056+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=12312313, allowExit=true, orderNo=S17524208620662136, contactInfo=123123123, orderId=345, totalPrice=400.0, pricePerTeam=200.0, currentParticipants=2, description=213123213, autoApprove=true, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-13T23:34:22.068856, creatorUsername=13402838501, venueId=29, bookingDate=2025-07-15, startTime=09:00, id=104, endTime=11:00, status=FULL, updatedAt=2025-07-14T15:53:31.003002}
2025-07-19T15:12:03.056+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=3123123, bookingTime=2025-07-16T06:00, fieldName=星光篮球场默认场地, orderNo=ORD1752482452180140, contactInfo=12312313132, originalEndTime=null, totalPrice=60.0, currentParticipants=2, description=12312312312312, originalStartTime=null, maxParticipants=2, venueName=星光篮球场, createdAt=2025-07-14T16:40:52.178454, bookingType=SHARED, venueId=39, id=349, allowSharing=true, endTime=2025-07-16T08:00, fieldId=39, status=OPEN, updatedAt=2025-07-14T17:05:24.475038, username=13402838501}
2025-07-19T15:12:03.056+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=213213, allowExit=false, orderNo=S17524809477136164, contactInfo=1233122, orderId=347, totalPrice=240.0, pricePerTeam=120.0, currentParticipants=2, description=123123123123, autoApprove=true, maxParticipants=2, venueName=绿茵足球场, createdAt=2025-07-14T16:15:47.715990, creatorUsername=13402838501, venueId=34, bookingDate=2025-07-16, startTime=06:00, id=106, endTime=08:00, status=FULL, updatedAt=2025-07-14T16:20:45.550760}
2025-07-19T15:12:03.056+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=1231231, allowExit=false, orderNo=S17524807133023130, contactInfo=123123123123, orderId=346, totalPrice=160.0, pricePerTeam=80.0, currentParticipants=2, description=1232313, autoApprove=true, maxParticipants=2, venueName=阳光体育馆, createdAt=2025-07-14T16:11:53.332563, creatorUsername=13402838501, venueId=33, bookingDate=2025-07-16, startTime=08:00, id=105, endTime=10:00, status=FULL, updatedAt=2025-07-14T16:17:46.523573}
2025-07-19T15:12:03.056+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=3123123, allowExit=false, orderNo=S17524824521909980, contactInfo=12312313132, orderId=349, totalPrice=120.0, pricePerTeam=60.0, currentParticipants=2, description=12312312312312, autoApprove=true, maxParticipants=2, venueName=星光篮球场, createdAt=2025-07-14T16:40:52.193132, creatorUsername=13402838501, venueId=39, bookingDate=2025-07-16, startTime=06:00, id=108, endTime=08:00, status=FULL, updatedAt=2025-07-14T17:05:24.460494}
2025-07-19T15:12:03.056+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=12312312, allowExit=false, orderNo=S17524820344535785, contactInfo=12312312312, orderId=348, totalPrice=200.0, pricePerTeam=100.0, currentParticipants=2, description=12312312312, autoApprove=true, maxParticipants=2, venueName=梦想足球场, createdAt=2025-07-14T16:33:54.456873, creatorUsername=13402838501, venueId=40, bookingDate=2025-07-16, startTime=07:00, id=107, endTime=09:00, status=FULL, updatedAt=2025-07-14T16:43:46.595355}
2025-07-19T15:12:03.056+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=测试队伍1, bookingTime=2025-07-14T18:08:53.411063, fieldName=场地A, orderNo=ORD_1752401333, contactInfo=联系方式1, originalEndTime=null, totalPrice=100.0, currentParticipants=1, description=null, originalStartTime=null, maxParticipants=2, venueName=体育馆A, createdAt=2025-07-13T18:08:53.411104, bookingType=SHARED, venueId=1, id=339, allowSharing=true, endTime=2025-07-14T20:08:53.411063, fieldId=1, status=OPEN, updatedAt=2025-07-13T18:08:53.411105, username=13800138000}
2025-07-19T15:12:03.057+08:00 DEBUG 64081 --- [scheduling-1] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T15:12:03.057+08:00 DEBUG 64081 --- [scheduling-1] org.hibernate.SQL                        : 
    select
        so1_0.id,
        so1_0.allow_exit,
        so1_0.auto_approve,
        so1_0.booking_date,
        so1_0.contact_info,
        so1_0.created_at,
        so1_0.creator_username,
        so1_0.current_participants,
        so1_0.description,
        so1_0.end_time,
        so1_0.max_participants,
        so1_0.order_id,
        so1_0.order_no,
        so1_0.price_per_team,
        so1_0.start_time,
        so1_0.status,
        so1_0.team_name,
        so1_0.total_price,
        so1_0.updated_at,
        so1_0.venue_id,
        so1_0.venue_name 
    from
        sharing_orders so1_0 
    where
        so1_0.order_id=?
2025-07-19T15:12:03.059+08:00 DEBUG 64081 --- [scheduling-1] org.hibernate.orm.results                : Initializer list:
	  com.example.gymbooking.model.SharingOrder(75) -> EntityJoinedFetchInitializer(com.example.gymbooking.model.SharingOrder(75))@225008959 (SingleTableEntityPersister(com.example.gymbooking.model.SharingOrder))

2025-07-19T15:12:03.060+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T15:12:03.060+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(1995924999<open>)]
2025-07-19T15:12:03.060+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T15:12:03.060+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.i.AbstractFlushingEventListener    : Processing flush-time cascades
2025-07-19T15:12:03.061+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.i.AbstractFlushingEventListener    : Dirty checking collections
2025-07-19T15:12:03.061+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 insertions, 0 updates, 0 deletions to 14 objects
2025-07-19T15:12:03.062+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-07-19T15:12:03.062+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : Listing entities:
2025-07-19T15:12:03.062+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=加入拼场, bookingTime=2025-07-14T16:00, fieldName=社区足球场默认场地, orderNo=ORD1752410499715958, contactInfo=, originalEndTime=null, totalPrice=200.0, currentParticipants=1, description=加入拼场订单, originalStartTime=null, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-13T20:41:39.712393, bookingType=SHARED, venueId=29, id=342, allowSharing=true, endTime=2025-07-14T18:00, fieldId=29, status=OPEN, updatedAt=2025-07-13T21:04:51.041066, username=13402838503}
2025-07-19T15:12:03.062+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=12312312, bookingTime=2025-07-16T07:00, fieldName=梦想足球场默认场地, orderNo=ORD1752482034442342, contactInfo=12312312312, originalEndTime=null, totalPrice=100.0, currentParticipants=2, description=12312312312, originalStartTime=null, maxParticipants=2, venueName=梦想足球场, createdAt=2025-07-14T16:33:54.440723, bookingType=SHARED, venueId=40, id=348, allowSharing=true, endTime=2025-07-16T09:00, fieldId=40, status=OPEN, updatedAt=2025-07-14T16:43:46.605570, username=13402838501}
2025-07-19T15:12:03.062+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=213213, bookingTime=2025-07-16T06:00, fieldName=绿茵足球场默认场地, orderNo=ORD1752480947703533, contactInfo=1233122, originalEndTime=null, totalPrice=120.0, currentParticipants=2, description=123123123123, originalStartTime=null, maxParticipants=2, venueName=绿茵足球场, createdAt=2025-07-14T16:15:47.701510, bookingType=SHARED, venueId=34, id=347, allowSharing=true, endTime=2025-07-16T08:00, fieldId=34, status=OPEN, updatedAt=2025-07-14T16:20:45.565676, username=13402838501}
2025-07-19T15:12:03.062+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=1231231, bookingTime=2025-07-16T08:00, fieldName=阳光体育馆默认场地, orderNo=ORD1752480713255467, contactInfo=123123123123, originalEndTime=null, totalPrice=80.0, currentParticipants=2, description=1232313, originalStartTime=null, maxParticipants=2, venueName=阳光体育馆, createdAt=2025-07-14T16:11:53.252052, bookingType=SHARED, venueId=33, id=346, allowSharing=true, endTime=2025-07-16T10:00, fieldId=33, status=OPEN, updatedAt=2025-07-14T16:17:46.534684, username=13402838501}
2025-07-19T15:12:03.062+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=12312313, bookingTime=2025-07-15T09:00, fieldName=社区足球场默认场地, orderNo=ORD175242086205312, contactInfo=123123123, originalEndTime=null, totalPrice=200.0, currentParticipants=2, description=213123213, originalStartTime=null, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-13T23:34:22.051617, bookingType=SHARED, venueId=29, id=345, allowSharing=true, endTime=2025-07-15T11:00, fieldId=29, status=OPEN, updatedAt=2025-07-14T15:53:31.048746, username=13402838501}
2025-07-19T15:12:03.062+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=1231312, bookingTime=2025-07-16T09:00, fieldName=社区足球场默认场地, orderNo=ORD1752483293311430, contactInfo=2312312312, originalEndTime=null, totalPrice=200.0, currentParticipants=2, description=132132312, originalStartTime=null, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-14T16:54:53.310295, bookingType=SHARED, venueId=29, id=350, allowSharing=true, endTime=2025-07-16T11:00, fieldId=29, status=OPEN, updatedAt=2025-07-14T16:56:42.018414, username=13402838501}
2025-07-19T15:12:03.063+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=12312313, allowExit=true, orderNo=S17524208620662136, contactInfo=123123123, orderId=345, totalPrice=400.0, pricePerTeam=200.0, currentParticipants=2, description=213123213, autoApprove=true, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-13T23:34:22.068856, creatorUsername=13402838501, venueId=29, bookingDate=2025-07-15, startTime=09:00, id=104, endTime=11:00, status=FULL, updatedAt=2025-07-14T15:53:31.003002}
2025-07-19T15:12:03.063+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=3123123, bookingTime=2025-07-16T06:00, fieldName=星光篮球场默认场地, orderNo=ORD1752482452180140, contactInfo=12312313132, originalEndTime=null, totalPrice=60.0, currentParticipants=2, description=12312312312312, originalStartTime=null, maxParticipants=2, venueName=星光篮球场, createdAt=2025-07-14T16:40:52.178454, bookingType=SHARED, venueId=39, id=349, allowSharing=true, endTime=2025-07-16T08:00, fieldId=39, status=OPEN, updatedAt=2025-07-14T17:05:24.475038, username=13402838501}
2025-07-19T15:12:03.063+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=1231312, allowExit=false, orderNo=S17524832933469648, contactInfo=2312312312, orderId=350, totalPrice=400.0, pricePerTeam=200.0, currentParticipants=2, description=132132312, autoApprove=true, maxParticipants=2, venueName=社区足球场, createdAt=2025-07-14T16:54:53.350171, creatorUsername=13402838501, venueId=29, bookingDate=2025-07-16, startTime=09:00, id=109, endTime=11:00, status=FULL, updatedAt=2025-07-14T16:56:42.000054}
2025-07-19T15:12:03.063+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=213213, allowExit=false, orderNo=S17524809477136164, contactInfo=1233122, orderId=347, totalPrice=240.0, pricePerTeam=120.0, currentParticipants=2, description=123123123123, autoApprove=true, maxParticipants=2, venueName=绿茵足球场, createdAt=2025-07-14T16:15:47.715990, creatorUsername=13402838501, venueId=34, bookingDate=2025-07-16, startTime=06:00, id=106, endTime=08:00, status=FULL, updatedAt=2025-07-14T16:20:45.550760}
2025-07-19T15:12:03.063+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=1231231, allowExit=false, orderNo=S17524807133023130, contactInfo=123123123123, orderId=346, totalPrice=160.0, pricePerTeam=80.0, currentParticipants=2, description=1232313, autoApprove=true, maxParticipants=2, venueName=阳光体育馆, createdAt=2025-07-14T16:11:53.332563, creatorUsername=13402838501, venueId=33, bookingDate=2025-07-16, startTime=08:00, id=105, endTime=10:00, status=FULL, updatedAt=2025-07-14T16:17:46.523573}
2025-07-19T15:12:03.063+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=3123123, allowExit=false, orderNo=S17524824521909980, contactInfo=12312313132, orderId=349, totalPrice=120.0, pricePerTeam=60.0, currentParticipants=2, description=12312312312312, autoApprove=true, maxParticipants=2, venueName=星光篮球场, createdAt=2025-07-14T16:40:52.193132, creatorUsername=13402838501, venueId=39, bookingDate=2025-07-16, startTime=06:00, id=108, endTime=08:00, status=FULL, updatedAt=2025-07-14T17:05:24.460494}
2025-07-19T15:12:03.063+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.SharingOrder{teamName=12312312, allowExit=false, orderNo=S17524820344535785, contactInfo=12312312312, orderId=348, totalPrice=200.0, pricePerTeam=100.0, currentParticipants=2, description=12312312312, autoApprove=true, maxParticipants=2, venueName=梦想足球场, createdAt=2025-07-14T16:33:54.456873, creatorUsername=13402838501, venueId=40, bookingDate=2025-07-16, startTime=07:00, id=107, endTime=09:00, status=FULL, updatedAt=2025-07-14T16:43:46.595355}
2025-07-19T15:12:03.064+08:00 DEBUG 64081 --- [scheduling-1] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Order{teamName=测试队伍1, bookingTime=2025-07-14T18:08:53.411063, fieldName=场地A, orderNo=ORD_1752401333, contactInfo=联系方式1, originalEndTime=null, totalPrice=100.0, currentParticipants=1, description=null, originalStartTime=null, maxParticipants=2, venueName=体育馆A, createdAt=2025-07-13T18:08:53.411104, bookingType=SHARED, venueId=1, id=339, allowSharing=true, endTime=2025-07-14T20:08:53.411063, fieldId=1, status=OPEN, updatedAt=2025-07-13T18:08:53.411105, username=13800138000}
2025-07-19T15:12:03.065+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(1995924999<open>)] after transaction
2025-07-19T15:12:03.065+08:00  INFO 64081 --- [scheduling-1] c.e.g.scheduler.OrderStatusScheduler     : 拼场超时检查任务完成，处理了 0 个订单
2025-07-19T15:12:03.067+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [com.example.gymbooking.service.SharingOrderScheduleService.cancelUnfilledSharingOrders]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT
2025-07-19T15:12:03.067+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(120013382<open>)] for JPA transaction
2025-07-19T15:12:03.067+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-19T15:12:03.067+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.t.internal.TransactionImpl         : begin
2025-07-19T15:12:03.068+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@40e07738]
2025-07-19T15:12:03.069+08:00 DEBUG 64081 --- [scheduling-1] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T15:12:03.069+08:00 DEBUG 64081 --- [scheduling-1] org.hibernate.SQL                        : 
    select
        so1_0.id,
        so1_0.allow_exit,
        so1_0.auto_approve,
        so1_0.booking_date,
        so1_0.contact_info,
        so1_0.created_at,
        so1_0.creator_username,
        so1_0.current_participants,
        so1_0.description,
        so1_0.end_time,
        so1_0.max_participants,
        so1_0.order_id,
        so1_0.order_no,
        so1_0.price_per_team,
        so1_0.start_time,
        so1_0.status,
        so1_0.team_name,
        so1_0.total_price,
        so1_0.updated_at,
        so1_0.venue_id,
        so1_0.venue_name 
    from
        sharing_orders so1_0 
    where
        so1_0.status='OPEN' 
        and timestamp(so1_0.booking_date, so1_0.start_time)<=?
2025-07-19T15:12:03.071+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T15:12:03.071+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(120013382<open>)]
2025-07-19T15:12:03.071+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T15:12:03.072+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(120013382<open>)] after transaction
2025-07-19T15:12:03.072+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [com.example.gymbooking.service.SharingOrderScheduleService.cancelUnfilledSharingOrders]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT
2025-07-19T15:12:03.072+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(1721300749<open>)] for JPA transaction
2025-07-19T15:12:03.072+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-19T15:12:03.072+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.t.internal.TransactionImpl         : begin
2025-07-19T15:12:03.073+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@4a2be399]
2025-07-19T15:12:03.073+08:00 DEBUG 64081 --- [scheduling-1] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T15:12:03.074+08:00 DEBUG 64081 --- [scheduling-1] org.hibernate.SQL                        : 
    select
        so1_0.id,
        so1_0.allow_exit,
        so1_0.auto_approve,
        so1_0.booking_date,
        so1_0.contact_info,
        so1_0.created_at,
        so1_0.creator_username,
        so1_0.current_participants,
        so1_0.description,
        so1_0.end_time,
        so1_0.max_participants,
        so1_0.order_id,
        so1_0.order_no,
        so1_0.price_per_team,
        so1_0.start_time,
        so1_0.status,
        so1_0.team_name,
        so1_0.total_price,
        so1_0.updated_at,
        so1_0.venue_id,
        so1_0.venue_name 
    from
        sharing_orders so1_0 
    where
        so1_0.status='OPEN' 
        and timestamp(so1_0.booking_date, so1_0.start_time)<=?
2025-07-19T15:12:03.076+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T15:12:03.076+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(1721300749<open>)]
2025-07-19T15:12:03.077+08:00 DEBUG 64081 --- [scheduling-1] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T15:12:03.077+08:00 DEBUG 64081 --- [scheduling-1] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(1721300749<open>)] after transaction
