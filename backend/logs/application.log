2025-07-19T14:49:36.674+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.gymbooking.controller.TimeSlotController#getTimeSlotsByVenueAndDate(Long, LocalDate)
2025-07-19T14:49:36.683+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-1] tor$SharedEntityManagerInvocationHandler : Creating new EntityManager for shared EntityManager invocation
2025-07-19T14:49:36.683+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-1] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T14:49:36.683+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-1] org.hibernate.SQL                        : 
    select
        ts1_0.id,
        ts1_0.created_at,
        ts1_0.date,
        ts1_0.end_time,
        ts1_0.order_id,
        ts1_0.price,
        ts1_0.start_time,
        ts1_0.status,
        ts1_0.updated_at,
        ts1_0.venue_id 
    from
        time_slots ts1_0 
    where
        ts1_0.venue_id=? 
        and ts1_0.date=? 
    order by
        ts1_0.start_time
2025-07-19T14:49:36.684+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-07-19T14:49:36.685+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [[]]
2025-07-19T14:49:36.685+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/timeslots/venue/33/date/2025-07-20/generate
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       : 处理请求: POST /api/timeslots/venue/33/date/2025-07-20/generate
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       : 请求头信息:
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   host: localhost:8080
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   connection: keep-alive
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   content-length: 2
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMzQwMjgzODUwMSIsImlhdCI6MTc1MjkwNzY4NywiZXhwIjoxNzUyOTk0MDg3fQ.uwbcz6VZSH54gie35vMJ4wdYxOCPmMwQTpu41R-ZZkk
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/43
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   content-type: application/json
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   accept: */*
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-site: cross-site
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-mode: cors
2025-07-19T14:49:36.736+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-dest: empty
2025-07-19T14:49:36.737+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   referer: https://servicewechat.com/wx2e811ad126ad0e40/devtools/page-frame.html
2025-07-19T14:49:36.737+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   accept-encoding: gzip, deflate, br
2025-07-19T14:49:36.737+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   accept-language: zh-CN,zh;q=0.9
2025-07-19T14:49:36.737+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       : 从Authorization头解析到JWT令牌，长度: 139
2025-07-19T14:49:36.739+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [com.example.gymbooking.security.services.UserDetailsServiceImpl.loadUserByUsername]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT
2025-07-19T14:49:36.740+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(671116606<open>)] for JPA transaction
2025-07-19T14:49:36.740+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-19T14:49:36.740+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : begin
2025-07-19T14:49:36.740+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@5410975d]
2025-07-19T14:49:36.740+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.s.services.UserDetailsServiceImpl  : 正在加载用户: 13402838501
2025-07-19T14:49:36.740+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.create         : Created new SQL alias : u1_0
2025-07-19T14:49:36.740+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.create         : Registration of TableGroup [StandardTableGroup(com.example.gymbooking.model.User(2))] with identifierForTableGroup [com.example.gymbooking.model.User] for NavigablePath [com.example.gymbooking.model.User] 
2025-07-19T14:49:36.741+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@263585df
2025-07-19T14:49:36.741+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.example.gymbooking.model.User(2).username) 
2025-07-19T14:49:36.741+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results.graph.AST      : DomainResult Graph:
 \-EntityResultImpl [com.example.gymbooking.model.User(2)]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).active]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).avatar]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).createdAt]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).email]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).nickname]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).password]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).phone]
 |  +-SelectEagerCollectionFetch [com.example.gymbooking.model.User(2).roles]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).updatedAt]
 |  \-BasicFetch [com.example.gymbooking.model.User(2).username]

2025-07-19T14:49:36.741+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.tree           : SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.example.gymbooking.model.User(2)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-07-19T14:49:36.741+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T14:49:36.741+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    select
        u1_0.id,
        u1_0.active,
        u1_0.avatar,
        u1_0.created_at,
        u1_0.email,
        u1_0.nickname,
        u1_0.password,
        u1_0.phone,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-19T14:49:36.742+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list:
	  com.example.gymbooking.model.User(2) -> EntityJoinedFetchInitializer(com.example.gymbooking.model.User(2))@424605776 (SingleTableEntityPersister(com.example.gymbooking.model.User))
	  com.example.gymbooking.model.User(2).roles -> SelectEagerCollectionInitializer(com.example.gymbooking.model.User(2).roles)@1284383729 (PluralAttribute(com.example.gymbooking.model.User.roles))

2025-07-19T14:49:36.743+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = IGNORE
2025-07-19T14:49:36.743+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    select
        r1_0.user_id,
        r1_0.role 
    from
        user_roles r1_0 
    where
        r1_0.user_id=?
2025-07-19T14:49:36.744+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.sql.results.internal.ResultsHelper   : Collection fully initialized: [com.example.gymbooking.model.User.roles#17]
2025-07-19T14:49:36.744+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.s.services.UserDetailsServiceImpl  : 成功加载用户: 13402838501, 角色: [ROLE_USER]
2025-07-19T14:49:36.744+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T14:49:36.744+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(671116606<open>)]
2025-07-19T14:49:36.744+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T14:49:36.745+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Processing flush-time cascades
2025-07-19T14:49:36.745+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Dirty checking collections
2025-07-19T14:49:36.745+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.engine.internal.Collections  : Collection found: [com.example.gymbooking.model.User.roles#17], was: [com.example.gymbooking.model.User.roles#17] (initialized)
2025-07-19T14:49:36.745+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-07-19T14:49:36.745+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 (re)creations, 0 updates, 0 removals to 1 collections
2025-07-19T14:49:36.745+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : Listing entities:
2025-07-19T14:49:36.745+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.User{createdAt=2025-06-22T12:46:54.618255, password=$2a$10$v6ApAukPPgUlLAhqPLPGT.Jm.5u6/ib2tlQEpMpb0Wn8m0aFGxfZ6, phone=13402838501, roles=[ROLE_USER], nickname=test1, active=true, id=17, avatar=null, email=null, updatedAt=2025-06-22T12:46:54.618293, username=13402838501}
2025-07-19T14:49:36.746+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(671116606<open>)] after transaction
2025-07-19T14:49:36.746+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       : 用户 13402838501 认证成功
2025-07-19T14:49:36.746+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/timeslots/venue/33/date/2025-07-20/generate
2025-07-19T14:49:36.746+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.web.servlet.DispatcherServlet        : POST "/api/timeslots/venue/33/date/2025-07-20/generate", parameters={}
2025-07-19T14:49:36.747+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.gymbooking.controller.TimeSlotController#generateTimeSlotsForVenue(Long, LocalDate)
2025-07-19T14:49:36.747+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [com.example.gymbooking.service.TimeSlotService.generateTimeSlotsForVenue]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT
2025-07-19T14:49:36.748+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(2001896597<open>)] for JPA transaction
2025-07-19T14:49:36.748+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-19T14:49:36.748+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : begin
2025-07-19T14:49:36.748+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@11442f51]
2025-07-19T14:49:36.748+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Found thread-bound EntityManager [SessionImpl(2001896597<open>)] for JPA transaction
2025-07-19T14:49:36.748+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Participating in existing transaction
2025-07-19T14:49:36.748+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = IGNORE
2025-07-19T14:49:36.748+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    select
        v1_0.id,
        v1_0.close_time,
        v1_0.created_at,
        v1_0.description,
        v1_0.facilities,
        v1_0.image,
        v1_0.location,
        v1_0.manager_id,
        v1_0.name,
        v1_0.open_time,
        v1_0.price,
        v1_0.status,
        v1_0.support_sharing,
        v1_0.type,
        v1_0.updated_at 
    from
        venues v1_0 
    where
        v1_0.id=?
2025-07-19T14:49:36.750+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Processing flush-time cascades
2025-07-19T14:49:36.750+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Dirty checking collections
2025-07-19T14:49:36.750+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.create         : Created new SQL alias : ts1_0
2025-07-19T14:49:36.750+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.create         : Registration of TableGroup [StandardTableGroup(com.example.gymbooking.model.TimeSlot(60))] with identifierForTableGroup [com.example.gymbooking.model.TimeSlot] for NavigablePath [com.example.gymbooking.model.TimeSlot] 
2025-07-19T14:49:36.751+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@3f34f5c1
2025-07-19T14:49:36.751+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.example.gymbooking.model.TimeSlot(60).venueId) 
2025-07-19T14:49:36.751+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@56fecef0
2025-07-19T14:49:36.751+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.example.gymbooking.model.TimeSlot(60).date) 
2025-07-19T14:49:36.751+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results.graph.AST      : DomainResult Graph:
 \-EntityResultImpl [com.example.gymbooking.model.TimeSlot(60)]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(60).createdAt]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(60).date]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(60).endTime]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(60).orderId]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(60).price]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(60).startTime]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(60).status]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(60).updatedAt]
 |  \-BasicFetch [com.example.gymbooking.model.TimeSlot(60).venueId]

2025-07-19T14:49:36.752+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.tree           : SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (ts1 : com.example.gymbooking.model.TimeSlot(60)) {
          primaryTableReference : time_slots as ts1_0
        }
      }
    }

2025-07-19T14:49:36.752+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.engine.internal.Collections  : Collection found: [com.example.gymbooking.model.Venue.features#33], was: [com.example.gymbooking.model.Venue.features#33] (uninitialized)
2025-07-19T14:49:36.752+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.engine.internal.Collections  : Collection found: [com.example.gymbooking.model.Venue.photos#33], was: [com.example.gymbooking.model.Venue.photos#33] (uninitialized)
2025-07-19T14:49:36.752+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-07-19T14:49:36.752+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 (re)creations, 0 updates, 0 removals to 2 collections
2025-07-19T14:49:36.752+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : Listing entities:
2025-07-19T14:49:36.752+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Venue{supportSharing=true, image=https://example.com/basketball1.jpg, description=专业篮球场地，设施齐全, managerId=1, type=篮球, photos=<uninitialized>, createdAt=2025-06-22T22:28:12, features=<uninitialized>, price=80.0, closeTime=22:00, name=阳光体育馆, location=北京市朝阳区体育路1号, id=33, facilities=篮球架,更衣室,淋浴间,停车场, openTime=08:00, status=OPEN, updatedAt=2025-06-22T22:28:12}
2025-07-19T14:49:36.752+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T14:49:36.753+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    select
        ts1_0.id,
        ts1_0.created_at,
        ts1_0.date,
        ts1_0.end_time,
        ts1_0.order_id,
        ts1_0.price,
        ts1_0.start_time,
        ts1_0.status,
        ts1_0.updated_at,
        ts1_0.venue_id 
    from
        time_slots ts1_0 
    where
        ts1_0.venue_id=? 
        and ts1_0.date=?
2025-07-19T14:49:36.754+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list:
	  com.example.gymbooking.model.TimeSlot(60) -> EntityJoinedFetchInitializer(com.example.gymbooking.model.TimeSlot(60))@975480379 (SingleTableEntityPersister(com.example.gymbooking.model.TimeSlot))

2025-07-19T14:49:36.754+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Found thread-bound EntityManager [SessionImpl(2001896597<open>)] for JPA transaction
2025-07-19T14:49:36.755+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Participating in existing transaction
2025-07-19T14:49:36.755+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.755+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.755+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.760+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.761+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@7ad47021
2025-07-19T14:49:36.761+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.761+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.762+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.763+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.763+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@4df828e6
2025-07-19T14:49:36.764+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.764+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.765+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.766+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.767+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@5d828e9d
2025-07-19T14:49:36.767+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.767+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.768+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.769+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.769+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@2bcf1e0c
2025-07-19T14:49:36.769+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.769+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.769+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.770+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.771+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@2fbf1a93
2025-07-19T14:49:36.771+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.771+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.771+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.772+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.772+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@38af3709
2025-07-19T14:49:36.772+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.772+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.773+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.774+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.774+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@3fd0d921
2025-07-19T14:49:36.774+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.774+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.774+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.775+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.775+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@dc2ce2b
2025-07-19T14:49:36.775+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.775+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.775+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.776+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.776+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@732c1174
2025-07-19T14:49:36.776+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.776+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.777+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.778+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.778+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@69d82d19
2025-07-19T14:49:36.778+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.778+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.779+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.779+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 82
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=OperatingSystem, attribute=ProcessCpuLoad
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@5673c9dc
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T14:49:36.780+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-19T14:49:36.781+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.781+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T14:49:36.781+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T14:49:36.781+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.781+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-19T14:49:36.783+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.783+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@210d58db
2025-07-19T14:49:36.783+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.783+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.784+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.785+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.785+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@1617e3a6
2025-07-19T14:49:36.785+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.785+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.786+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.786+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.787+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@6e90cc1b
2025-07-19T14:49:36.787+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.787+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.787+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.788+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.788+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@73f7c28
2025-07-19T14:49:36.789+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.789+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.789+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.790+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.790+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@47f5fafd
2025-07-19T14:49:36.790+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.790+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.791+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.792+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.792+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@48beead
2025-07-19T14:49:36.792+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.793+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.793+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.795+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.795+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@2662cc1a
2025-07-19T14:49:36.795+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.795+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.796+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.798+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.798+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@77e4cae3
2025-07-19T14:49:36.798+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.798+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.799+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.801+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.801+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@3f8c5a34
2025-07-19T14:49:36.802+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.802+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.802+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.803+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.803+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@2a640d9d
2025-07-19T14:49:36.803+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.803+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.804+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.804+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.805+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@105dda95
2025-07-19T14:49:36.805+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.805+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.805+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.807+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.807+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@4343a488
2025-07-19T14:49:36.807+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.809+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.809+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.810+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.810+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@3d4353e4
2025-07-19T14:49:36.811+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.811+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.811+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.813+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.813+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@1d51da9c
2025-07-19T14:49:36.813+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.813+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.814+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.815+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.815+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@1b3b7f00
2025-07-19T14:49:36.816+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.816+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.816+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.818+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.818+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@3779bd4c
2025-07-19T14:49:36.818+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.j.internal.PersistenceUnitUtilImpl   : jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-07-19T14:49:36.819+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.engine.spi.ActionQueue     : Executing identity-insert immediately
2025-07-19T14:49:36.819+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    insert 
    into
        time_slots
        (created_at, date, end_time, order_id, price, start_time, status, updated_at, venue_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-19T14:49:36.820+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list is empty
2025-07-19T14:49:36.820+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.id.IdentifierGeneratorHelper         : Extracted generated values [com.example.gymbooking.model.TimeSlot]: [Ljava.lang.Object;@2e05e74a
2025-07-19T14:49:36.820+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T14:49:36.820+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(2001896597<open>)]
2025-07-19T14:49:36.820+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T14:49:36.820+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Processing flush-time cascades
2025-07-19T14:49:36.820+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Dirty checking collections
2025-07-19T14:49:36.820+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.engine.internal.Collections  : Collection found: [com.example.gymbooking.model.Venue.features#33], was: [com.example.gymbooking.model.Venue.features#33] (uninitialized)
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.engine.internal.Collections  : Collection found: [com.example.gymbooking.model.Venue.photos#33], was: [com.example.gymbooking.model.Venue.photos#33] (uninitialized)
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 insertions, 0 updates, 0 deletions to 29 objects
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 (re)creations, 0 updates, 0 removals to 2 collections
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : Listing entities:
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.804022, orderId=null, price=40.0, venueId=33, startTime=18:30, id=4498, endTime=19:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.804030}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.805651, orderId=null, price=40.0, venueId=33, startTime=19:00, id=4499, endTime=19:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.805659}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.799302, orderId=null, price=40.0, venueId=33, startTime=17:30, id=4496, endTime=18:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.799319}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.802519, orderId=null, price=40.0, venueId=33, startTime=18:00, id=4497, endTime=18:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.802527}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.793254, orderId=null, price=40.0, venueId=33, startTime=16:30, id=4494, endTime=17:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.793262}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.796815, orderId=null, price=40.0, venueId=33, startTime=17:00, id=4495, endTime=17:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.796829}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.789452, orderId=null, price=40.0, venueId=33, startTime=15:30, id=4492, endTime=16:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.789459}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.791003, orderId=null, price=40.0, venueId=33, startTime=16:00, id=4493, endTime=16:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.791010}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.819362, orderId=null, price=40.0, venueId=33, startTime=21:30, id=4504, endTime=22:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.819371}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.813987, orderId=null, price=40.0, venueId=33, startTime=20:30, id=4502, endTime=21:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.813999}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.816749, orderId=null, price=40.0, venueId=33, startTime=21:00, id=4503, endTime=21:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.816762}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.809334, orderId=null, price=40.0, venueId=33, startTime=19:30, id=4500, endTime=20:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.809343}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.811441, orderId=null, price=40.0, venueId=33, startTime=20:00, id=4501, endTime=20:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.811450}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.771539, orderId=null, price=40.0, venueId=33, startTime=10:30, id=4482, endTime=11:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.771548}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.Venue{supportSharing=true, image=https://example.com/basketball1.jpg, description=专业篮球场地，设施齐全, managerId=1, type=篮球, photos=<uninitialized>, createdAt=2025-06-22T22:28:12, features=<uninitialized>, price=80.0, closeTime=22:00, name=阳光体育馆, location=北京市朝阳区体育路1号, id=33, facilities=篮球架,更衣室,淋浴间,停车场, openTime=08:00, status=OPEN, updatedAt=2025-06-22T22:28:12}
2025-07-19T14:49:36.821+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.773080, orderId=null, price=40.0, venueId=33, startTime=11:00, id=4483, endTime=11:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.773090}
2025-07-19T14:49:36.822+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.767913, orderId=null, price=40.0, venueId=33, startTime=09:30, id=4480, endTime=10:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.767926}
2025-07-19T14:49:36.822+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.769875, orderId=null, price=40.0, venueId=33, startTime=10:00, id=4481, endTime=10:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.769883}
2025-07-19T14:49:36.822+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.762011, orderId=null, price=40.0, venueId=33, startTime=08:30, id=4478, endTime=09:00, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.762025}
2025-07-19T14:49:36.822+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.764936, orderId=null, price=40.0, venueId=33, startTime=09:00, id=4479, endTime=09:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.764945}
2025-07-19T14:49:36.822+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.TimeSlot{date=2025-07-20, createdAt=2025-07-19T14:49:36.755644, orderId=null, price=40.0, venueId=33, startTime=08:00, id=4477, endTime=08:30, status=AVAILABLE, updatedAt=2025-07-19T14:49:36.755653}
2025-07-19T14:49:36.822+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : More......
2025-07-19T14:49:36.823+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(2001896597<open>)] after transaction
2025-07-19T14:49:36.823+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-07-19T14:49:36.823+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [[com.example.gymbooking.model.TimeSlot@545f22c3, com.example.gymbooking.model.TimeSlot@5baecea7, com (truncated)...]
2025-07-19T14:49:36.824+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.web.servlet.DispatcherServlet        : Completed 201 CREATED
