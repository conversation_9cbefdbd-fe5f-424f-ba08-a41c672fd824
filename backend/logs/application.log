2025-07-19T13:51:20.966+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-07-19T13:51:20.968+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/venues/popular?limit=5
2025-07-19T13:51:20.968+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.web.servlet.DispatcherServlet        : GET "/api/venues/popular?limit=5", parameters={masked}
2025-07-19T13:51:20.969+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.gymbooking.controller.VenueController#getPopularVenues(int)
2025-07-19T13:51:20.969+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly
2025-07-19T13:51:20.970+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(1294309219<open>)] for JPA transaction
2025-07-19T13:51:20.970+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.jdbc.datasource.DataSourceUtils      : Setting JDBC Connection [HikariProxyConnection@446669393 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2bccfd] read-only
2025-07-19T13:51:20.970+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-19T13:51:20.970+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.h.e.t.internal.TransactionImpl         : begin
2025-07-19T13:51:20.971+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@78fb1df4]
2025-07-19T13:51:20.972+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] org.hibernate.orm.sql.ast.create         : Created new SQL alias : v1_0
2025-07-19T13:51:20.972+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] org.hibernate.orm.sql.ast.create         : Registration of TableGroup [StandardTableGroup(com.example.gymbooking.model.Venue(139))] with identifierForTableGroup [com.example.gymbooking.model.Venue] for NavigablePath [com.example.gymbooking.model.Venue] 
2025-07-19T13:51:20.972+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] org.hibernate.orm.results.graph.AST      : DomainResult Graph:
 \-EntityResultImpl [com.example.gymbooking.model.Venue(139)]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).closeTime]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).createdAt]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).description]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).facilities]
 |  +-DelayedCollectionFetch [com.example.gymbooking.model.Venue(139).features]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).image]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).location]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).managerId]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).name]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).openTime]
 |  +-DelayedCollectionFetch [com.example.gymbooking.model.Venue(139).photos]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).price]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).status]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).supportSharing]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(139).type]
 |  \-BasicFetch [com.example.gymbooking.model.Venue(139).updatedAt]

2025-07-19T13:51:20.973+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] org.hibernate.orm.sql.ast.tree           : SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (v1 : com.example.gymbooking.model.Venue(139)) {
          primaryTableReference : venues as v1_0
        }
      }
    }

2025-07-19T13:51:20.973+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T13:51:20.973+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] org.hibernate.SQL                        : 
    select
        v1_0.id,
        v1_0.close_time,
        v1_0.created_at,
        v1_0.description,
        v1_0.facilities,
        v1_0.image,
        v1_0.location,
        v1_0.manager_id,
        v1_0.name,
        v1_0.open_time,
        v1_0.price,
        v1_0.status,
        v1_0.support_sharing,
        v1_0.type,
        v1_0.updated_at 
    from
        venues v1_0
2025-07-19T13:51:20.974+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] org.hibernate.orm.results                : Initializer list:
	  com.example.gymbooking.model.Venue(139).photos -> DelayedCollectionInitializer(com.example.gymbooking.model.Venue(139).photos)@432381028 (PluralAttribute(com.example.gymbooking.model.Venue.photos))
	  com.example.gymbooking.model.Venue(139).features -> DelayedCollectionInitializer(com.example.gymbooking.model.Venue(139).features)@2060725060 (PluralAttribute(com.example.gymbooking.model.Venue.features))
	  com.example.gymbooking.model.Venue(139) -> EntityJoinedFetchInitializer(com.example.gymbooking.model.Venue(139))@1768822857 (SingleTableEntityPersister(com.example.gymbooking.model.Venue))

2025-07-19T13:51:20.975+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T13:51:20.975+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(1294309219<open>)]
2025-07-19T13:51:20.975+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T13:51:20.976+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.jdbc.datasource.DataSourceUtils      : Resetting read-only flag of JDBC Connection [HikariProxyConnection@446669393 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2bccfd]
2025-07-19T13:51:20.976+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(1294309219<open>)] after transaction
2025-07-19T13:51:20.977+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-07-19T13:51:20.977+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [[com.example.gymbooking.model.Venue@5c381024, com.example.gymbooking.model.Venue@934c053, com.exampl (truncated)...]
2025-07-19T13:51:20.978+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-6] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-07-19T13:51:20.987+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/sharing-orders/joinable?page=1&pageSize=3
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       : 处理请求: GET /api/sharing-orders/joinable
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       : 请求头信息:
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   host: localhost:8080
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   connection: keep-alive
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMzQwMjgzODUwMSIsImlhdCI6MTc1MjkwNDA2OCwiZXhwIjoxNzUyOTkwNDY4fQ.mhJbUAku1QS0FXn3KlTxWdoh61pwpZpiYA8CpMChtSc
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/11
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   content-type: application/json
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   accept: */*
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-site: cross-site
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-mode: cors
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-dest: empty
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   referer: https://servicewechat.com/wx2e811ad126ad0e40/devtools/page-frame.html
2025-07-19T13:51:20.988+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   accept-encoding: gzip, deflate, br
2025-07-19T13:51:20.989+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       :   accept-language: zh-CN,zh;q=0.9
2025-07-19T13:51:20.989+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       : 从Authorization头解析到JWT令牌，长度: 139
2025-07-19T13:51:21.004+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [com.example.gymbooking.security.services.UserDetailsServiceImpl.loadUserByUsername]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT
2025-07-19T13:51:21.004+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(164797690<open>)] for JPA transaction
2025-07-19T13:51:21.004+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-19T13:51:21.004+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.e.t.internal.TransactionImpl         : begin
2025-07-19T13:51:21.005+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@69e872c2]
2025-07-19T13:51:21.006+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.s.services.UserDetailsServiceImpl  : 正在加载用户: 13402838501
2025-07-19T13:51:21.008+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.orm.sql.ast.create         : Created new SQL alias : u1_0
2025-07-19T13:51:21.009+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.orm.sql.ast.create         : Registration of TableGroup [StandardTableGroup(com.example.gymbooking.model.User(2))] with identifierForTableGroup [com.example.gymbooking.model.User] for NavigablePath [com.example.gymbooking.model.User] 
2025-07-19T13:51:21.009+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@3d1315a4
2025-07-19T13:51:21.009+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.example.gymbooking.model.User(2).username) 
2025-07-19T13:51:21.010+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.orm.results.graph.AST      : DomainResult Graph:
 \-EntityResultImpl [com.example.gymbooking.model.User(2)]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).active]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).avatar]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).createdAt]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).email]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).nickname]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).password]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).phone]
 |  +-SelectEagerCollectionFetch [com.example.gymbooking.model.User(2).roles]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).updatedAt]
 |  \-BasicFetch [com.example.gymbooking.model.User(2).username]

2025-07-19T13:51:21.010+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.orm.sql.ast.tree           : SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.example.gymbooking.model.User(2)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-07-19T13:51:21.010+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T13:51:21.011+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.SQL                        : 
    select
        u1_0.id,
        u1_0.active,
        u1_0.avatar,
        u1_0.created_at,
        u1_0.email,
        u1_0.nickname,
        u1_0.password,
        u1_0.phone,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-19T13:51:21.013+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.orm.results                : Initializer list:
	  com.example.gymbooking.model.User(2) -> EntityJoinedFetchInitializer(com.example.gymbooking.model.User(2))@1207519349 (SingleTableEntityPersister(com.example.gymbooking.model.User))
	  com.example.gymbooking.model.User(2).roles -> SelectEagerCollectionInitializer(com.example.gymbooking.model.User(2).roles)@1684984932 (PluralAttribute(com.example.gymbooking.model.User.roles))

2025-07-19T13:51:21.013+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = IGNORE
2025-07-19T13:51:21.013+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.SQL                        : 
    select
        r1_0.user_id,
        r1_0.role 
    from
        user_roles r1_0 
    where
        r1_0.user_id=?
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.sql.results.internal.ResultsHelper   : Collection fully initialized: [com.example.gymbooking.model.User.roles#17]
2025-07-19T13:51:21.015+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.s.services.UserDetailsServiceImpl  : 成功加载用户: 13402838501, 角色: [ROLE_USER]
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(164797690<open>)]
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.e.i.AbstractFlushingEventListener    : Processing flush-time cascades
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.e.i.AbstractFlushingEventListener    : Dirty checking collections
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.hibernate.engine.internal.Collections  : Collection found: [com.example.gymbooking.model.User.roles#17], was: [com.example.gymbooking.model.User.roles#17] (initialized)
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 (re)creations, 0 updates, 0 removals to 1 collections
2025-07-19T13:51:21.015+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.hibernate.internal.util.EntityPrinter  : Listing entities:
2025-07-19T13:51:21.016+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.User{createdAt=2025-06-22T12:46:54.618255, password=$2a$10$v6ApAukPPgUlLAhqPLPGT.Jm.5u6/ib2tlQEpMpb0Wn8m0aFGxfZ6, phone=13402838501, roles=[ROLE_USER], nickname=test1, active=true, id=17, avatar=null, email=null, updatedAt=2025-06-22T12:46:54.618293, username=13402838501}
2025-07-19T13:51:21.016+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(164797690<open>)] after transaction
2025-07-19T13:51:21.016+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.security.jwt.AuthTokenFilter       : 用户 13402838501 认证成功
2025-07-19T13:51:21.017+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/sharing-orders/joinable?page=1&pageSize=3
2025-07-19T13:51:21.017+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.web.servlet.DispatcherServlet        : GET "/api/sharing-orders/joinable?page=1&pageSize=3", parameters={masked}
2025-07-19T13:51:21.017+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.gymbooking.controller.SharingOrderController#getJoinableSharingOrders(int, int)
2025-07-19T13:51:21.018+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.controller.SharingOrderController  : 获取可加入的拼场订单列表，页码: 1, 页大小: 3
2025-07-19T13:51:21.018+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] tor$SharedEntityManagerInvocationHandler : Creating new EntityManager for shared EntityManager invocation
2025-07-19T13:51:21.018+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T13:51:21.019+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] org.hibernate.SQL                        : 
    select
        so1_0.id,
        so1_0.allow_exit,
        so1_0.auto_approve,
        so1_0.booking_date,
        so1_0.contact_info,
        so1_0.created_at,
        so1_0.creator_username,
        so1_0.current_participants,
        so1_0.description,
        so1_0.end_time,
        so1_0.max_participants,
        so1_0.order_id,
        so1_0.order_no,
        so1_0.price_per_team,
        so1_0.start_time,
        so1_0.status,
        so1_0.team_name,
        so1_0.total_price,
        so1_0.updated_at,
        so1_0.venue_id,
        so1_0.venue_name 
    from
        sharing_orders so1_0 
    where
        so1_0.status='OPEN' 
        and (
            so1_0.max_participants is null 
            or so1_0.current_participants<so1_0.max_participants
        ) 
    order by
        so1_0.created_at desc
2025-07-19T13:51:21.020+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-7] c.e.g.controller.SharingOrderController  : 返回拼场订单数量: 0, 总数: 0, 当前页: 1/0
2025-07-19T13:51:21.021+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-07-19T13:51:21.022+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [{pagination={totalPages=0, pageSize=3, hasPrevious=false, hasNext=false, currentPage=1, totalElement (truncated)...]
2025-07-19T13:51:21.023+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-7] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-07-19T13:51:21.026+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/venues/popular?limit=5
2025-07-19T13:51:21.026+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] c.e.g.security.jwt.AuthTokenFilter       : 处理请求: GET /api/venues/popular
2025-07-19T13:51:21.026+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] c.e.g.security.jwt.AuthTokenFilter       : 公开接口，跳过JWT验证: GET /api/venues/popular
2025-07-19T13:51:21.026+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-07-19T13:51:21.026+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/venues/popular?limit=5
2025-07-19T13:51:21.027+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.web.servlet.DispatcherServlet        : GET "/api/venues/popular?limit=5", parameters={masked}
2025-07-19T13:51:21.027+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.gymbooking.controller.VenueController#getPopularVenues(int)
2025-07-19T13:51:21.027+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly
2025-07-19T13:51:21.028+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(1567985256<open>)] for JPA transaction
2025-07-19T13:51:21.028+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.jdbc.datasource.DataSourceUtils      : Setting JDBC Connection [HikariProxyConnection@327807586 wrapping com.mysql.cj.jdbc.ConnectionImpl@4b16c6c6] read-only
2025-07-19T13:51:21.028+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-19T13:51:21.028+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.h.e.t.internal.TransactionImpl         : begin
2025-07-19T13:51:21.028+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@77b0b639]
2025-07-19T13:51:21.029+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] org.hibernate.orm.sql.ast.create         : Created new SQL alias : v1_0
2025-07-19T13:51:21.029+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] org.hibernate.orm.sql.ast.create         : Registration of TableGroup [StandardTableGroup(com.example.gymbooking.model.Venue(140))] with identifierForTableGroup [com.example.gymbooking.model.Venue] for NavigablePath [com.example.gymbooking.model.Venue] 
2025-07-19T13:51:21.029+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] org.hibernate.orm.results.graph.AST      : DomainResult Graph:
 \-EntityResultImpl [com.example.gymbooking.model.Venue(140)]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).closeTime]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).createdAt]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).description]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).facilities]
 |  +-DelayedCollectionFetch [com.example.gymbooking.model.Venue(140).features]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).image]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).location]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).managerId]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).name]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).openTime]
 |  +-DelayedCollectionFetch [com.example.gymbooking.model.Venue(140).photos]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).price]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).status]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).supportSharing]
 |  +-BasicFetch [com.example.gymbooking.model.Venue(140).type]
 |  \-BasicFetch [com.example.gymbooking.model.Venue(140).updatedAt]

2025-07-19T13:51:21.030+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] org.hibernate.orm.sql.ast.tree           : SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (v1 : com.example.gymbooking.model.Venue(140)) {
          primaryTableReference : venues as v1_0
        }
      }
    }

2025-07-19T13:51:21.030+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T13:51:21.030+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] org.hibernate.SQL                        : 
    select
        v1_0.id,
        v1_0.close_time,
        v1_0.created_at,
        v1_0.description,
        v1_0.facilities,
        v1_0.image,
        v1_0.location,
        v1_0.manager_id,
        v1_0.name,
        v1_0.open_time,
        v1_0.price,
        v1_0.status,
        v1_0.support_sharing,
        v1_0.type,
        v1_0.updated_at 
    from
        venues v1_0
2025-07-19T13:51:21.031+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] org.hibernate.orm.results                : Initializer list:
	  com.example.gymbooking.model.Venue(140) -> EntityJoinedFetchInitializer(com.example.gymbooking.model.Venue(140))@385192077 (SingleTableEntityPersister(com.example.gymbooking.model.Venue))
	  com.example.gymbooking.model.Venue(140).features -> DelayedCollectionInitializer(com.example.gymbooking.model.Venue(140).features)@132547266 (PluralAttribute(com.example.gymbooking.model.Venue.features))
	  com.example.gymbooking.model.Venue(140).photos -> DelayedCollectionInitializer(com.example.gymbooking.model.Venue(140).photos)@1646535464 (PluralAttribute(com.example.gymbooking.model.Venue.photos))

2025-07-19T13:51:21.033+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T13:51:21.033+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(1567985256<open>)]
2025-07-19T13:51:21.033+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T13:51:21.033+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.jdbc.datasource.DataSourceUtils      : Resetting read-only flag of JDBC Connection [HikariProxyConnection@327807586 wrapping com.mysql.cj.jdbc.ConnectionImpl@4b16c6c6]
2025-07-19T13:51:21.034+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(1567985256<open>)] after transaction
2025-07-19T13:51:21.034+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-07-19T13:51:21.034+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [[com.example.gymbooking.model.Venue@2cee3cf2, com.example.gymbooking.model.Venue@1be7a151, com.examp (truncated)...]
2025-07-19T13:51:21.035+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-8] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-07-19T13:51:21.091+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/sharing-orders/joinable?page=1&pageSize=3
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       : 处理请求: GET /api/sharing-orders/joinable
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       : 请求头信息:
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   host: localhost:8080
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   connection: keep-alive
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMzQwMjgzODUwMSIsImlhdCI6MTc1MjkwNDA2OCwiZXhwIjoxNzUyOTkwNDY4fQ.mhJbUAku1QS0FXn3KlTxWdoh61pwpZpiYA8CpMChtSc
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/11
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   content-type: application/json
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   accept: */*
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-site: cross-site
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-mode: cors
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   sec-fetch-dest: empty
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   referer: https://servicewechat.com/wx2e811ad126ad0e40/devtools/page-frame.html
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   accept-encoding: gzip, deflate, br
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       :   accept-language: zh-CN,zh;q=0.9
2025-07-19T13:51:21.092+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       : 从Authorization头解析到JWT令牌，长度: 139
2025-07-19T13:51:21.101+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [com.example.gymbooking.security.services.UserDetailsServiceImpl.loadUserByUsername]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT
2025-07-19T13:51:21.102+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(1486535224<open>)] for JPA transaction
2025-07-19T13:51:21.102+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-19T13:51:21.102+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : begin
2025-07-19T13:51:21.102+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@3abb98da]
2025-07-19T13:51:21.102+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.s.services.UserDetailsServiceImpl  : 正在加载用户: 13402838501
2025-07-19T13:51:21.103+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.create         : Created new SQL alias : u1_0
2025-07-19T13:51:21.103+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.create         : Registration of TableGroup [StandardTableGroup(com.example.gymbooking.model.User(2))] with identifierForTableGroup [com.example.gymbooking.model.User] for NavigablePath [com.example.gymbooking.model.User] 
2025-07-19T13:51:21.104+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@6ff86a17
2025-07-19T13:51:21.104+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.example.gymbooking.model.User(2).username) 
2025-07-19T13:51:21.104+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results.graph.AST      : DomainResult Graph:
 \-EntityResultImpl [com.example.gymbooking.model.User(2)]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).active]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).avatar]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).createdAt]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).email]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).nickname]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).password]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).phone]
 |  +-SelectEagerCollectionFetch [com.example.gymbooking.model.User(2).roles]
 |  +-BasicFetch [com.example.gymbooking.model.User(2).updatedAt]
 |  \-BasicFetch [com.example.gymbooking.model.User(2).username]

2025-07-19T13:51:21.104+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.ast.tree           : SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.example.gymbooking.model.User(2)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-07-19T13:51:21.105+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T13:51:21.105+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    select
        u1_0.id,
        u1_0.active,
        u1_0.avatar,
        u1_0.created_at,
        u1_0.email,
        u1_0.nickname,
        u1_0.password,
        u1_0.phone,
        u1_0.updated_at,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-07-19T13:51:21.106+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.results                : Initializer list:
	  com.example.gymbooking.model.User(2) -> EntityJoinedFetchInitializer(com.example.gymbooking.model.User(2))@566129872 (SingleTableEntityPersister(com.example.gymbooking.model.User))
	  com.example.gymbooking.model.User(2).roles -> SelectEagerCollectionInitializer(com.example.gymbooking.model.User(2).roles)@1343156432 (PluralAttribute(com.example.gymbooking.model.User.roles))

2025-07-19T13:51:21.106+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = IGNORE
2025-07-19T13:51:21.107+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    select
        r1_0.user_id,
        r1_0.role 
    from
        user_roles r1_0 
    where
        r1_0.user_id=?
2025-07-19T13:51:21.108+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.sql.results.internal.ResultsHelper   : Collection fully initialized: [com.example.gymbooking.model.User.roles#17]
2025-07-19T13:51:21.108+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.s.services.UserDetailsServiceImpl  : 成功加载用户: 13402838501, 角色: [ROLE_USER]
2025-07-19T13:51:21.108+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-19T13:51:21.108+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(1486535224<open>)]
2025-07-19T13:51:21.108+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.t.internal.TransactionImpl         : committing
2025-07-19T13:51:21.108+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Processing flush-time cascades
2025-07-19T13:51:21.109+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Dirty checking collections
2025-07-19T13:51:21.109+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.engine.internal.Collections  : Collection found: [com.example.gymbooking.model.User.roles#17], was: [com.example.gymbooking.model.User.roles#17] (initialized)
2025-07-19T13:51:21.109+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-07-19T13:51:21.109+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 (re)creations, 0 updates, 0 removals to 1 collections
2025-07-19T13:51:21.109+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : Listing entities:
2025-07-19T13:51:21.109+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.User{createdAt=2025-06-22T12:46:54.618255, password=$2a$10$v6ApAukPPgUlLAhqPLPGT.Jm.5u6/ib2tlQEpMpb0Wn8m0aFGxfZ6, phone=13402838501, roles=[ROLE_USER], nickname=test1, active=true, id=17, avatar=null, email=null, updatedAt=2025-06-22T12:46:54.618293, username=13402838501}
2025-07-19T13:51:21.109+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(1486535224<open>)] after transaction
2025-07-19T13:51:21.109+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.security.jwt.AuthTokenFilter       : 用户 13402838501 认证成功
2025-07-19T13:51:21.111+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/sharing-orders/joinable?page=1&pageSize=3
2025-07-19T13:51:21.111+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.web.servlet.DispatcherServlet        : GET "/api/sharing-orders/joinable?page=1&pageSize=3", parameters={masked}
2025-07-19T13:51:21.112+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.gymbooking.controller.SharingOrderController#getJoinableSharingOrders(int, int)
2025-07-19T13:51:21.113+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.controller.SharingOrderController  : 获取可加入的拼场订单列表，页码: 1, 页大小: 3
2025-07-19T13:51:21.113+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] tor$SharedEntityManagerInvocationHandler : Creating new EntityManager for shared EntityManager invocation
2025-07-19T13:51:21.113+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-19T13:51:21.113+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] org.hibernate.SQL                        : 
    select
        so1_0.id,
        so1_0.allow_exit,
        so1_0.auto_approve,
        so1_0.booking_date,
        so1_0.contact_info,
        so1_0.created_at,
        so1_0.creator_username,
        so1_0.current_participants,
        so1_0.description,
        so1_0.end_time,
        so1_0.max_participants,
        so1_0.order_id,
        so1_0.order_no,
        so1_0.price_per_team,
        so1_0.start_time,
        so1_0.status,
        so1_0.team_name,
        so1_0.total_price,
        so1_0.updated_at,
        so1_0.venue_id,
        so1_0.venue_name 
    from
        sharing_orders so1_0 
    where
        so1_0.status='OPEN' 
        and (
            so1_0.max_participants is null 
            or so1_0.current_participants<so1_0.max_participants
        ) 
    order by
        so1_0.created_at desc
2025-07-19T13:51:21.114+08:00  INFO 64081 --- [http-nio-0.0.0.0-8080-exec-5] c.e.g.controller.SharingOrderController  : 返回拼场订单数量: 0, 总数: 0, 当前页: 1/0
2025-07-19T13:51:21.115+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-07-19T13:51:21.115+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [{pagination={totalPages=0, pageSize=3, hasPrevious=false, hasNext=false, currentPage=1, totalElement (truncated)...]
2025-07-19T13:51:21.116+08:00 DEBUG 64081 --- [http-nio-0.0.0.0-8080-exec-5] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-07-19T13:51:21.948+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 82
2025-07-19T13:51:21.948+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T13:51:21.948+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T13:51:21.948+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=OperatingSystem, attribute=ProcessCpuLoad
2025-07-19T13:51:21.949+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 82
2025-07-19T13:51:21.949+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T13:51:21.949+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T13:51:21.949+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-19T13:51:21.950+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 82
2025-07-19T13:51:21.950+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T13:51:21.950+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T13:51:21.950+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-19T13:51:22.955+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 82
2025-07-19T13:51:22.955+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T13:51:22.956+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T13:51:22.956+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=OperatingSystem, attribute=ProcessCpuLoad
2025-07-19T13:51:22.956+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 82
2025-07-19T13:51:22.956+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T13:51:22.956+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T13:51:22.956+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-19T13:51:22.957+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 82
2025-07-19T13:51:22.957+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(4)-127.0.0.1: (port 60146) op = 80
2025-07-19T13:51:22.957+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(4)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-19T13:51:22.957+08:00 DEBUG 64081 --- [RMI TCP Connection(4)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
