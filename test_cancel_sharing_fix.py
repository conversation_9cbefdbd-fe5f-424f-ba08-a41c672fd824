#!/usr/bin/env python3
"""
测试取消拼场订单的修复效果
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8080/api"

def test_sharing_orders_before_cancel(token):
    """测试取消前的拼场订单状态"""
    
    print("\n🌐 测试1: 取消前的拼场订单状态")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # 获取我创建的拼场订单
        response = requests.get(f"{BASE_URL}/sharing-orders/my-created", headers=headers)
        print(f"📡 我创建的拼场订单 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            orders = response.json()
            if isinstance(orders, list) and len(orders) > 0:
                print(f"📊 找到 {len(orders)} 个拼场订单")
                
                for i, order in enumerate(orders[:2]):
                    print(f"\n📋 拼场订单 {i+1}:")
                    print(f"  ID: {order.get('id')}")
                    print(f"  状态: {order.get('status')}")
                    print(f"  关联订单ID: {order.get('orderId')}")
                    print(f"  场馆: {order.get('venueName')}")
                    print(f"  时间: {order.get('startTime')} - {order.get('endTime')}")
                    
                    # 检查关联的原始订单状态
                    if order.get('orderId'):
                        order_response = requests.get(f"{BASE_URL}/bookings/{order.get('orderId')}", headers=headers)
                        if order_response.status_code == 200:
                            original_order = order_response.json()
                            print(f"  关联原始订单状态: {original_order.get('status')}")
                        else:
                            print(f"  ❌ 无法获取关联原始订单")
                    
                    return order.get('id')  # 返回第一个订单ID用于测试
            else:
                print("📊 没有找到拼场订单")
                return None
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_cancel_sharing_order(token, sharing_order_id):
    """测试取消拼场订单"""
    
    print(f"\n🌐 测试2: 取消拼场订单 (ID: {sharing_order_id})")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/sharing-orders/{sharing_order_id}/cancel", headers=headers)
        print(f"📡 取消拼场订单 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 取消成功: {result.get('message')}")
            print(f"📊 更新后的拼场订单状态: {result.get('order', {}).get('status')}")
            return True
        else:
            print(f"❌ 取消失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"❌ 错误信息: {error_data.get('message')}")
            except:
                print(f"❌ 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_orders_after_cancel(token):
    """测试取消后的订单状态"""
    
    print("\n🌐 测试3: 取消后的订单状态验证")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # 1. 检查拼场订单状态
        print("\n📋 检查拼场订单状态:")
        response = requests.get(f"{BASE_URL}/sharing-orders/my-created", headers=headers)
        if response.status_code == 200:
            orders = response.json()
            cancelled_orders = [o for o in orders if o.get('status') == 'CANCELLED']
            print(f"  已取消的拼场订单数量: {len(cancelled_orders)}")
            
            for order in cancelled_orders:
                print(f"  - 订单ID: {order.get('id')}, 状态: {order.get('status')}")
        
        # 2. 检查原始预约订单状态
        print("\n📋 检查原始预约订单状态:")
        response = requests.get(f"{BASE_URL}/bookings/my-bookings", headers=headers)
        if response.status_code == 200:
            bookings_data = response.json()
            bookings = bookings_data.get('data', []) if isinstance(bookings_data, dict) else bookings_data
            
            cancelled_bookings = [b for b in bookings if b.get('status') == 'CANCELLED']
            print(f"  已取消的预约订单数量: {len(cancelled_bookings)}")
            
            for booking in cancelled_bookings:
                print(f"  - 订单ID: {booking.get('id')}, 状态: {booking.get('status')}")
                print(f"    场馆: {booking.get('venueName')}")
                print(f"    时间: {booking.get('bookingTime')}")
        
        # 3. 检查时间段释放情况
        print("\n📋 检查时间段释放情况:")
        print("  (时间段释放需要通过预约新订单来验证)")
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🚀 开始测试取消拼场订单的修复效果")
    
    # 提示用户输入token
    print("\n请从调试页面获取最新的JWT token:")
    print("1. 在浏览器中打开调试页面")
    print("2. 点击'登录测试'获取新token")
    print("3. 复制accessToken的值")
    
    token = input("\n请输入JWT token: ").strip()
    
    if not token:
        print("❌ 未提供token，测试终止")
        return
    
    print("=" * 60)
    
    # 测试流程
    sharing_order_id = test_sharing_orders_before_cancel(token)
    
    if sharing_order_id:
        print(f"\n🎯 将测试取消拼场订单: {sharing_order_id}")
        
        # 询问用户是否继续
        confirm = input("\n是否继续取消这个拼场订单? (y/N): ").strip().lower()
        if confirm == 'y':
            success = test_cancel_sharing_order(token, sharing_order_id)
            
            if success:
                print("\n⏳ 等待3秒让数据同步...")
                import time
                time.sleep(3)
                
                test_orders_after_cancel(token)
            else:
                print("❌ 取消失败，跳过后续测试")
        else:
            print("🚫 用户取消测试")
    else:
        print("❌ 没有找到可测试的拼场订单")
    
    print("\n" + "=" * 60)
    print("🎉 取消拼场订单测试完成")
    print("\n💡 本次修复的关键功能:")
    print("1. ✅ 取消拼场订单时同步取消关联的原始预约订单")
    print("2. ✅ 释放被占用的时间段，使其重新可用")
    print("3. ✅ 处理相关的拼场申请，将待处理申请标记为已取消")
    print("4. ✅ 前端取消成功后自动跳转回上一页")
    print("5. ✅ 预约列表页面自动刷新显示最新状态")
    print("\n🔍 验证要点:")
    print("- 拼场订单状态应变为CANCELLED")
    print("- 关联的原始预约订单状态应变为CANCELLED")
    print("- 时间段应被释放，可以重新预约")
    print("- 我的预约界面应显示订单已取消")

if __name__ == "__main__":
    main()
