<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">订单详情API测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试订单ID: 238</text>
      <button @click="testBookingDetail" class="test-btn">测试获取订单详情</button>
    </view>
    
    <view class="test-section">
      <text class="section-title">清除缓存</text>
      <button @click="clearBookingCache" class="test-btn">清除订单缓存</button>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试结果</text>
      <view class="result-container">
        <text class="result-text">{{ testResult }}</text>
      </view>
    </view>
    
    <view class="test-section" v-if="bookingData">
      <text class="section-title">订单数据</text>
      <view class="data-container">
        <text class="data-text">{{ JSON.stringify(bookingData, null, 2) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import { clearCache } from '@/utils/request.js'

export default {
  name: 'BookingDetailTest',
  data() {
    return {
      testResult: '等待测试...',
      bookingData: null
    }
  },
  computed: {
    ...mapState('booking', ['bookingDetail', 'loading'])
  },
  methods: {
    ...mapActions('booking', ['getBookingDetail']),
    
    async testBookingDetail() {
      try {
        this.testResult = '开始测试...'
        this.bookingData = null
        
        console.log('🧪 开始测试订单详情API')
        
        // 清除缓存
        clearCache('/bookings/238')
        console.log('🗑️ 已清除缓存')
        
        // 调用API
        const response = await this.getBookingDetail(238)
        console.log('🧪 测试响应:', response)
        
        // 检查store中的数据
        console.log('🧪 Store中的数据:', this.bookingDetail)
        
        this.bookingData = this.bookingDetail
        
        if (this.bookingDetail && this.bookingDetail.orderNo) {
          this.testResult = '✅ 测试成功！获取到有效的订单数据'
        } else {
          this.testResult = '❌ 测试失败：获取到的数据无效'
        }
        
      } catch (error) {
        console.error('🧪 测试失败:', error)
        this.testResult = `❌ 测试失败: ${error.message}`
      }
    },
    
    clearBookingCache() {
      clearCache('/bookings/238')
      this.testResult = '🗑️ 缓存已清除'
      console.log('🗑️ 订单缓存已清除')
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.test-btn {
  background-color: #007aff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 14px;
}

.result-container,
.data-container {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
}

.result-text,
.data-text {
  font-size: 12px;
  color: #666;
  word-break: break-all;
  white-space: pre-wrap;
}
</style>