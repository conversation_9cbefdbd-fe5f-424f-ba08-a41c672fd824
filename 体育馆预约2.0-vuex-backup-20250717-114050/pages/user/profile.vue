<template>
  <view class="container">
    <!-- 头部用户信息 -->
    <view class="header">
      <view class="user-info">
        <view class="avatar-wrapper" @click="changeAvatar">
          <image 
            :src="userInfo?.avatar || '/static/images/default-avatar.svg'" 
            class="avatar"
            mode="aspectFill"
          />
          <view class="avatar-edit">
            <text class="edit-icon">📷</text>
          </view>
        </view>
        
        <view class="user-details">
          <text class="nickname">{{ userInfo?.nickname || userInfo?.username || '未设置昵称' }}</text>
          <text class="username">用户名: {{ userInfo?.username || '未设置用户名' }}</text>
          <text class="phone">{{ formatPhone(userInfo?.phone || '') }}</text>
        </view>
        
        <view class="edit-btn" @click="editProfile">
          <text class="edit-text">编辑</text>
        </view>
      </view>
      
      <!-- 统计信息 -->
      <view class="stats">
        <view class="stat-item" @click="navigateTo('/pages/booking/list')">
          <text class="stat-number">{{ userStats.totalBookings || 0 }}</text>
          <text class="stat-label">总预约</text>
        </view>
        <view class="stat-item" @click="navigateTo('/pages/sharing/list?tab=my')">
          <text class="stat-number">{{ userStats.totalSharings || 0 }}</text>
          <text class="stat-label">拼场次数</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 我的订单 -->
      <view class="menu-group">
        <view class="group-title">
          <text class="title-text">我的订单</text>
        </view>
        
        <view class="menu-item" @click="navigateTo('/pages/booking/list')">
          <view class="item-left">
            <text class="item-icon">📅</text>
            <text class="item-text">我的预约</text>
          </view>
          <view class="item-right">
            <text v-if="pendingBookings > 0" class="badge">{{ pendingBookings }}</text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('/pages/sharing/list?tab=my')">
          <view class="item-left">
            <text class="item-icon">👥</text>
            <text class="item-text">我的拼场</text>
          </view>
          <view class="item-right">
            <text v-if="pendingSharings > 0" class="badge">{{ pendingSharings }}</text>
            <text class="arrow">></text>
          </view>
        </view>
      </view>
      
      <!-- 拼场申请 -->
      <view class="menu-group">
        <view class="group-title">
          <text class="title-text">拼场申请</text>
        </view>
        
        <view class="menu-item" @click="navigateTo('/pages/sharing/requests')">
          <view class="item-left">
            <text class="item-icon">📝</text>
            <text class="item-text">我的申请</text>
          </view>
          <view class="item-right">
            <text v-if="pendingRequests > 0" class="badge">{{ pendingRequests }}</text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('/pages/sharing/received')">
          <view class="item-left">
            <text class="item-icon">📬</text>
            <text class="item-text">收到的申请</text>
          </view>
          <view class="item-right">
            <text v-if="receivedRequests > 0" class="badge">{{ receivedRequests }}</text>
            <text class="arrow">></text>
          </view>
        </view>
      </view>
      
      <!-- 设置 -->
      <view class="menu-group">
        <view class="group-title">
          <text class="title-text">设置</text>
        </view>
        
        <view class="menu-item" @click="showLogoutConfirm">
          <view class="item-left">
            <text class="item-icon">🚪</text>
            <text class="item-text">退出登录</text>
          </view>
          <view class="item-right">
            <text class="arrow">></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 退出登录确认弹窗 -->
    <uni-popup ref="logoutPopup" type="dialog">
      <uni-popup-dialog 
        type="warn"
        title="确认退出"
        content="确定要退出登录吗？"
        :before-close="true"
        @close="handleLogoutCancel"
        @confirm="handleLogoutConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { useUserStore } from '@/stores/user.js'

export default {
  name: 'UserProfile',

  data() {
    return {
      pendingBookings: 0,
      pendingSharings: 0,
      pendingRequests: 0,
      receivedRequests: 0,
      userStore: null
    }
  },

  computed: {
    userInfo() {
      return this.userStore?.getCurrentUserInfo || {}
    },

    userStats() {
      return this.userStore?.userStats || {}
    },

    isLoggedIn() {
      return this.userStore?.isLoggedIn || false
    }
  },

  onLoad() {
    // 初始化Pinia store
    this.userStore = useUserStore()

    // 加载用户数据
    this.loadUserData();
  },
  
  onShow() {
    // 页面显示时刷新用户数据
    this.loadUserData();
  },
  
  methods: {
    
    // 加载用户数据
    async loadUserData() {
        try {
          console.log('[Profile] 开始加载用户数据')
          // 获取用户信息
          await this.userStore.getUserInfo()
          console.log('[Profile] 用户信息:', this.userInfo)

          // 获取用户统计数据
          try {
            await this.userStore.getUserStats()
          } catch (statsError) {
            console.error('获取用户统计失败:', statsError)
            // 统计数据获取失败时，设置默认值
            this.userStats = {
              totalBookings: 0,
              pendingBookings: 0,
              completedBookings: 0,
              totalSpent: 0
            }
          }
          
          // 获取待处理的预约和拼场数量
          await this.loadPendingCounts()
          
        } catch (error) {
          console.error('加载用户数据失败:', error)
          uni.showToast({
            title: '加载用户信息失败',
            icon: 'none'
          })
        }
      },
    
    // 加载待处理数量
    async loadPendingCounts() {
      try {
        // 获取待确认的预约
        const bookingResult = await this.getUserBookings({
          status: 'pending',
          page: 1,
          pageSize: 1
        })
        this.pendingBookings = bookingResult.total || 0
        
        // 获取待处理的拼场
        const sharingResult = await this.getUserSharingOrders({
          status: 'pending',
          page: 1,
          pageSize: 1
        })
        // 处理拼场数据，可能是数组格式
        if (Array.isArray(sharingResult.data)) {
          this.pendingSharings = sharingResult.data.length
        } else {
          this.pendingSharings = sharingResult.total || 0
        }
        
        // 获取我发出的待处理申请
        const myRequestsResult = await this.getMySharingRequests()
        if (Array.isArray(myRequestsResult)) {
          this.pendingRequests = myRequestsResult.filter(req => req.status === 'pending').length
        } else {
          this.pendingRequests = 0
        }
        
        // 获取收到的待处理申请
        const receivedRequestsResult = await this.getReceivedSharingRequests()
        if (Array.isArray(receivedRequestsResult)) {
          this.receivedRequests = receivedRequestsResult.filter(req => req.status === 'pending').length
        } else {
          this.receivedRequests = 0
        }
        
      } catch (error) {
        console.error('加载待处理数量失败:', error)
        // 设置默认值
        this.pendingBookings = 0
        this.pendingSharings = 0
        this.pendingRequests = 0
        this.receivedRequests = 0
      }
    },
    
    // 格式化手机号
    formatPhone(phone) {
      if (!phone) return ''
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
    },
    
    // 更换头像
    changeAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          this.uploadAvatar(tempFilePath)
        }
      })
    },
    
    // 上传头像
    async uploadAvatar(filePath) {
      try {
        uni.showLoading({ title: '上传中...' })
        
        // 这里应该调用上传头像的API
        // const result = await this.uploadUserAvatar(filePath)
        
        uni.hideLoading()
        uni.showToast({
          title: '头像更新成功',
          icon: 'success'
        })
        
        // 重新获取用户信息
        await this.getUserInfo()
        
      } catch (error) {
        uni.hideLoading()
        console.error('上传头像失败:', error)
        uni.showToast({
          title: '上传失败',
          icon: 'error'
        })
      }
    },
    
    // 编辑资料
    editProfile() {
      // 确保用户信息已加载
      if (!this.userInfo) {
        this.loadUserData()
      }
      uni.navigateTo({
        url: '/pages/user/edit-profile'
      })
    },
    
    // 页面跳转
    navigateTo(url) {
      // 检查是否为tabbar页面
      const tabbarPages = [
        '/pages/index/index',
        '/pages/venue/list', 
        '/pages/sharing/list',
        '/pages/booking/list',
        '/pages/user/profile'
      ]
      
      // 提取页面路径（去掉查询参数）
      const pagePath = url.split('?')[0]
      
      if (tabbarPages.includes(pagePath)) {
        // 如果是tabbar页面，使用switchTab
        uni.switchTab({ url: pagePath })
      } else {
        // 普通页面使用navigateTo
        uni.navigateTo({ url })
      }
    },
    
    // 显示退出登录确认
    showLogoutConfirm() {
      this.$refs.logoutPopup.open()
    },
    
    // 取消退出登录
    handleLogoutCancel() {
      this.$refs.logoutPopup.close()
    },
    
    // 确认退出登录
    async handleLogoutConfirm() {
      try {
        await this.userStore.logout()
        
        this.$refs.logoutPopup.close()
        
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })
        
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/user/login'
          })
        }, 1000)
        
      } catch (error) {
        console.error('退出登录失败:', error)
        uni.showToast({
          title: '退出失败',
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 头部
.header {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f65 100%);
  padding: 40rpx 40rpx 30rpx;
  
  // 用户信息
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
    
    .avatar-wrapper {
      position: relative;
      margin-right: 24rpx;
      
      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
      }
      
      .avatar-edit {
        position: absolute;
        bottom: -8rpx;
        right: -8rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: #ffffff;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        
        .edit-icon {
          font-size: 20rpx;
        }
      }
    }
    
    .user-details {
      flex: 1;
      
      .nickname {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8rpx;
      }
      
      .username {
        display: block;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 8rpx;
      }
      
      .phone {
        display: block;
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 12rpx;
      }
      

    }
    
    .edit-btn {
      padding: 12rpx 24rpx;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 20rpx;
      
      .edit-text {
        font-size: 24rpx;
        color: #ffffff;
      }
    }
  }
  
  // 统计信息
  .stats {
    display: flex;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 16rpx;
    padding: 20rpx 0;
    
    .stat-item {
      flex: 1;
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

// 菜单区域
.menu-section {
  padding: 20rpx;
  
  .menu-group {
    background-color: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    
    .group-title {
      padding: 24rpx 32rpx 16rpx;
      
      .title-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
      }
    }
    
    .menu-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 32rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-left {
        display: flex;
        align-items: center;
        
        .item-icon {
          font-size: 32rpx;
          margin-right: 20rpx;
        }
        
        .item-text {
          font-size: 28rpx;
          color: #333333;
        }
      }
      
      .item-right {
        display: flex;
        align-items: center;
        
        .badge {
          background-color: #ff4d4f;
          color: #ffffff;
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 10rpx;
          margin-right: 12rpx;
          min-width: 32rpx;
          text-align: center;
        }
        

        
        .arrow {
          font-size: 24rpx;
          color: #cccccc;
        }
      }
    }
  }
}
</style>