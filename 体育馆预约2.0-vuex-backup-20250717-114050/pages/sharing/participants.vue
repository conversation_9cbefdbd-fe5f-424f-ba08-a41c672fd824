<template>
  <view class="container">
    <view class="header">
      <text class="title">参与者列表</text>
    </view>
    
    <view v-if="loading" class="loading">
      <text>加载中...</text>
    </view>
    
    <view v-else-if="participants.length === 0" class="empty">
      <text>暂无参与者</text>
    </view>
    
    <view v-else class="participants-list">
      <view 
        v-for="participant in participants" 
        :key="participant.id"
        class="participant-item"
      >
        <view class="participant-info">
          <image 
            :src="participant.avatar || '/static/default-avatar.png'" 
            class="avatar"
          />
          <view class="info">
            <text class="name">{{ participant.nickname || participant.username }}</text>
            <text class="phone">{{ participant.phone || '未提供' }}</text>
            <text class="join-time">加入时间: {{ formatDateTime(participant.joinTime) }}</text>
          </view>
        </view>
        
        <view class="participant-status">
          <text 
            class="status-text"
            :class="getStatusClass(participant.status)"
          >
            {{ getStatusText(participant.status) }}
          </text>
        </view>
        
        <view v-if="isCreator && participant.status === 'PENDING'" class="actions">
          <button 
            class="btn btn-approve" 
            @click="approveParticipant(participant.id)"
          >
            同意
          </button>
          <button 
            class="btn btn-reject" 
            @click="rejectParticipant(participant.id)"
          >
            拒绝
          </button>
        </view>
        
        <view v-if="isCreator && participant.status === 'APPROVED'" class="actions">
          <button 
            class="btn btn-remove" 
            @click="removeParticipant(participant.id)"
          >
            移除
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useSharingStore } from '@/stores/sharing.js'
import { useUserStore } from '@/stores/user.js'
import { formatDateTime } from '@/utils/helpers.js'

export default {
  name: 'SharingParticipants',
  
  data() {
    return {
      orderId: '',
      participants: [],
      loading: false,
      sharingStore: null,
      userStore: null
    }
  },
  
  computed: {
    isCreator() {
      // 判断当前用户是否为拼场创建者
      return this.userStore?.userId === this.sharingOrder?.creatorId
    },
    
    sharingOrder() {
      return this.sharingStore?.getSharingOrderDetail
    }
  },
  
  onLoad(options) {
    this.orderId = options.orderId
    this.sharingStore = useSharingStore()
    this.userStore = useUserStore()
    
    if (this.orderId) {
      this.loadParticipants()
    }
  },
  
  methods: {
    formatDateTime,
    
    // 加载参与者列表
    async loadParticipants() {
      try {
        this.loading = true
        
        // 获取拼场订单详情
        await this.sharingStore.getOrderDetail(this.orderId)
        
        // 从订单详情中获取参与者信息
        const order = this.sharingStore.getSharingOrderDetail
        if (order && order.participants) {
          this.participants = order.participants
        }
        
      } catch (error) {
        console.error('加载参与者失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 同意参与者
    async approveParticipant(participantId) {
      try {
        await this.sharingStore.handleSharingRequest({
          requestId: participantId,
          data: { status: 'APPROVED' }
        })
        
        // 重新加载参与者列表
        await this.loadParticipants()
        
      } catch (error) {
        console.error('同意参与者失败:', error)
      }
    },
    
    // 拒绝参与者
    async rejectParticipant(participantId) {
      try {
        await this.sharingStore.handleSharingRequest({
          requestId: participantId,
          data: { status: 'REJECTED' }
        })
        
        // 重新加载参与者列表
        await this.loadParticipants()
        
      } catch (error) {
        console.error('拒绝参与者失败:', error)
      }
    },
    
    // 移除参与者
    async removeParticipant(participantId) {
      try {
        uni.showModal({
          title: '确认移除',
          content: '确定要移除这个参与者吗？',
          success: async (res) => {
            if (res.confirm) {
              await this.sharingStore.removeSharingParticipant({
                sharingId: this.orderId,
                participantId: participantId
              })
              
              // 重新加载参与者列表
              await this.loadParticipants()
            }
          }
        })
      } catch (error) {
        console.error('移除参与者失败:', error)
      }
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待审核',
        'APPROVED': '已同意',
        'REJECTED': '已拒绝',
        'JOINED': '已加入'
      }
      return statusMap[status] || '未知状态'
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      return `status-${status.toLowerCase()}`
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.loading, .empty {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

.participants-list {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.participant-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.participant-item:last-child {
  border-bottom: none;
}

.participant-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
}

.info {
  flex: 1;
}

.name {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.phone {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.join-time {
  display: block;
  font-size: 12px;
  color: #999;
}

.participant-status {
  margin-right: 10px;
}

.status-text {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-joined {
  background-color: #d1ecf1;
  color: #0c5460;
}

.actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  border: none;
}

.btn-approve {
  background-color: #28a745;
  color: white;
}

.btn-reject {
  background-color: #dc3545;
  color: white;
}

.btn-remove {
  background-color: #ffc107;
  color: #212529;
}
</style>
