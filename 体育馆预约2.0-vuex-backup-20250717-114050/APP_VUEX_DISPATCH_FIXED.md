# 🔧 App.vue Vuex dispatch错误修复

## 🚨 问题描述

**错误信息**:
```
TypeError: Cannot read property 'dispatch' of undefined
at Proxy.mappedAction (vuex.esm-bundler.js:1294)
at Proxy.onLaunch (App.vue:13)
```

**根本原因**: App.vue中仍在使用Vuex的`mapActions`，但Vuex store已被移除，导致dispatch方法不存在。

## ✅ 修复内容

### 1. 更新import语句
```javascript
// 修复前
import { setupRouterGuard } from '@/utils/router-guard-new.js'
import { mapActions } from 'vuex'

// 修复后  
import { setupRouterGuard } from '@/utils/router-guard-new.js'
import { useUserStore } from '@/stores/user.js'
import { useAppStore } from '@/stores/app.js'
```

### 2. 添加data属性存储store实例
```javascript
data() {
  return {
    userStore: null,
    appStore: null
  }
},
```

### 3. 在onLaunch中初始化stores
```javascript
onLaunch: function() {
  console.log('[App] 应用启动')
  
  // 初始化stores
  this.userStore = useUserStore()
  this.appStore = useAppStore()
  
  // 1. 立即设置新的路由守卫
  setupRouterGuard()
  
  // 2. 初始化用户状态（从本地存储恢复）
  this.userStore.initUserState()
  
  // 3. 立即检查登录状态，未登录则跳转到登录页
  this.checkAndRedirectToLogin()
  
  // 4. 延迟执行非关键操作，提升启动速度
  this.$nextTick(() => {
    this.setupNetworkListener()
  })
},
```

### 4. 移除mapActions并更新方法调用
```javascript
// 修复前
methods: {
  ...mapActions('user', ['checkLoginStatus', 'initUserState']),
  ...mapActions(['setNetworkStatus']),
  
  setupNetworkListener() {
    uni.onNetworkStatusChange((res) => {
      this.setNetworkStatus(res.isConnected)  // ❌ 会报错
    })
  }
}

// 修复后
methods: {
  setupNetworkListener() {
    uni.onNetworkStatusChange((res) => {
      this.appStore.setNetworkStatus(res.isConnected)  // ✅ 正确
    })
  }
}
```

### 5. 在user store中添加initUserState方法
```javascript
// stores/user.js 中添加
// 初始化用户状态 - 从本地存储恢复
initUserState() {
  try {
    console.log('[UserStore] 初始化用户状态')
    const token = getToken()
    const userInfo = getUserInfo()
    
    if (token && userInfo) {
      this.token = token
      this.userInfo = userInfo
      this.isLoggedIn = true
      console.log('[UserStore] 用户状态恢复成功')
    } else {
      this.clearUserData()
      console.log('[UserStore] 无有效用户状态，已清空')
    }
  } catch (error) {
    console.error('[UserStore] 初始化用户状态失败:', error)
    this.clearUserData()
  }
},
```

## 🔍 相关问题分析

### 401错误问题
从错误日志中看到大量的401错误，这表明：

1. **登录状态丢失**: 用户token可能已过期或无效
2. **初始化时序问题**: App启动时可能没有正确恢复登录状态
3. **API调用时机**: 在用户未登录时就开始调用需要认证的API

### 建议的解决方案

1. **检查token有效性**:
```javascript
// 在App.vue的checkAndRedirectToLogin方法中
checkAndRedirectToLogin() {
  try {
    console.log('[App] 检查登录状态')
    const token = uni.getStorageSync('token')
    const userInfo = uni.getStorageSync('userInfo')
    
    if (!token || !userInfo) {
      console.log('[App] 无有效登录信息，跳转到登录页')
      uni.reLaunch({
        url: '/pages/user/login'
      })
      return
    }
    
    // 验证token是否有效
    this.userStore.checkLoginStatus()
  } catch (error) {
    console.warn('[App] 登录状态检查失败:', error.message)
    uni.reLaunch({
      url: '/pages/user/login'
    })
  }
}
```

2. **延迟API调用**: 确保在用户登录状态确认后再调用API

3. **添加错误处理**: 在API调用失败时提供更好的用户体验

## 🧪 测试验证

### 测试步骤
1. **清除缓存**: 微信开发者工具 → 工具 → 清缓存 → 清除全部缓存
2. **重新编译**: `npm run dev`
3. **测试应用启动**: 查看控制台是否还有dispatch错误
4. **测试登录流程**: 确认登录后API调用正常

### 预期结果
- ✅ 应用启动时不再出现dispatch错误
- ✅ 用户状态正确初始化
- ✅ 网络状态监听正常工作
- ✅ 登录状态检查正常

## 📋 修复清单

- [x] 修复App.vue中的Vuex mapActions调用
- [x] 添加Pinia store初始化
- [x] 更新网络状态监听方法
- [x] 在user store中添加initUserState方法
- [x] 确保所有store方法都存在并正常工作

## 🚀 下一步

1. **测试应用启动** - 确认dispatch错误已解决
2. **解决401错误** - 检查登录状态和token有效性
3. **全面测试** - 验证所有功能正常工作

---

**修复时间**: 2025-07-16 19:15
**状态**: ✅ App.vue Vuex dispatch错误已修复
**下一步**: 解决登录状态和401错误问题
