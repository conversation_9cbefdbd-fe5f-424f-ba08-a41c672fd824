import request, { get, post, put } from '../utils/request'

// 创建拼场订单
export function createSharingOrder(data) {
  return post('/sharing-orders', data)
}

// 获取拼场订单详情
export function getSharingOrderById(id) {
  console.log('[SharingAPI] 请求拼场订单详情，ID:', id)
  return get(`/sharing-orders/${id}`, {}, { cache: false })
}

// 通过主订单ID获取拼场订单详情
export function getSharingOrderByMainOrderId(orderId) {
  console.log('[SharingAPI] 通过主订单ID请求拼场订单详情，主订单ID:', orderId)
  return get(`/sharing-orders/by-order/${orderId}`, {}, { cache: false })
}

// 获取可加入的拼场订单列表
export function getJoinableSharingOrders(params) {
  return get('/sharing-orders/joinable', params)
}

// 获取所有拼场订单列表（包括所有状态）
export function getAllSharingOrders(params) {
  // 移除all=true参数，使用正常分页
  return get('/sharing-orders', params, {
    cache: false // 确保不使用缓存
  })
}

// 旧的API方法，保留兼容性
export function getAvailableSharedBookings(params) {
  return get('/bookings/shared/available', params)
}

// 根据场馆ID获取可加入的拼场订单
export function getJoinableSharingOrdersByVenueId(venueId) {
  return get(`/sharing-orders/joinable/venue/${venueId}`)
}

// 根据日期获取可加入的拼场订单
export function getJoinableSharingOrdersByDate(date) {
  return get(`/sharing-orders/joinable/date/${date}`)
}

// 根据场馆ID和日期获取可加入的拼场订单
export function getJoinableSharingOrdersByVenueIdAndDate(venueId, date) {
  return get(`/sharing-orders/joinable/venue/${venueId}/date/${date}`)
}

// 获取我创建的拼场订单
export function getMyCreatedSharingOrders() {
  return get('/sharing-orders/my-created', {}, { cache: false })
}

// 加入拼场订单（申请拼场）
export function joinSharingOrder(id, data = {}) {
  // 使用正确的后端接口路径
  return post(`/bookings/shared/${id}/apply`, {
    participantsCount: data.participantsCount || 1,
    teamName: data.teamName || '',
    contactInfo: data.contactInfo || '',
    message: data.message || ''
  })
}

// 申请加入拼场订单（需要支付）
export function applyJoinSharingOrder(id) {
  return post(`/sharing-orders/${id}/apply-join`)
}

// 取消加入拼场订单
export function cancelJoinSharingOrder(id) {
  return post(`/sharing-orders/${id}/cancel-join`)
}

// 确认拼场订单
export function confirmSharingOrder(id) {
  return post(`/sharing-orders/${id}/confirm`)
}

// 取消拼场订单
export function cancelSharingOrder(id) {
  return post(`/sharing-orders/${id}/cancel`)
}

// 移除拼场参与者
export function removeSharingParticipant(sharingId, participantId) {
  return request({
    url: `/sharing-orders/${sharingId}/participants/${participantId}/remove`,
    method: 'post'
  })
}

// 更新拼场设置
export function updateSharingSettings(sharingId, settings) {
  return request({
    url: `/sharing-orders/${sharingId}/settings`,
    method: 'put',
    data: settings
  })
}

// 根据订单号获取拼场订单
export function getSharingOrderByOrderNo(orderNo) {
  return get(`/sharing-orders/order-no/${orderNo}`)
}

// 申请拼场（原有的拼场申请功能，用于普通订单的拼场申请）
export function applySharedBooking(orderId, data) {
  return post(`/bookings/shared/${orderId}/apply`, data)
}

// 处理拼场申请
export function handleSharedRequest(requestId, data) {
  return put(`/shared/requests/${requestId}`, data)
}

// 获取我发出的拼场申请
export function getMySharedRequests(params) {
  return get('/shared/my-requests', params, { cache: false })
}

// 获取我收到的拼场申请
export function getReceivedSharedRequests(params) {
  return get('/shared/received-requests', params)
}

// 取消拼场申请
export function cancelSharingRequest(requestId) {
  return request({
    url: `/shared/requests/${requestId}/cancel`,
    method: 'post'
  })
}



// 默认导出所有API方法
export default {
  createSharingOrder,
  getSharingOrderById,
  getSharingOrderByMainOrderId,
  getJoinableSharingOrders,
  getAllSharingOrders,
  getAvailableSharedBookings,
  getJoinableSharingOrdersByVenueId,
  getJoinableSharingOrdersByDate,
  getJoinableSharingOrdersByVenueIdAndDate,
  getMyCreatedSharingOrders,
  joinSharingOrder,
  cancelJoinSharingOrder,
  confirmSharingOrder,
  cancelSharingOrder,
  removeSharingParticipant,
  updateSharingSettings,
  getSharingOrderByOrderNo,
  applySharedBooking,
  handleSharedRequest,
  getMySharedRequests,
  getReceivedSharedRequests,
  cancelSharingRequest,
  applyJoinSharingOrder
}