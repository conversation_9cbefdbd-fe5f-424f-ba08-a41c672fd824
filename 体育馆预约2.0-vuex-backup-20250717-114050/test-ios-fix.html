<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS 日期格式兼容性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .sharing-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            position: relative;
        }
        .my-sharing-badge {
            display: inline-block;
            padding: 4px 8px;
            background-color: #ff6b35;
            color: white;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        .sharing-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .status-open {
            background-color: #e8f5e8;
            color: #52c41a;
        }
    </style>
</head>
<body>
    <h1>iOS 兼容性修复测试</h1>
    
    <div class="test-section">
        <h2>1. 日期格式兼容性测试</h2>
        <div id="dateTest"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 拼场订单标识测试</h2>
        <div id="sharingTest"></div>
    </div>
    
    <script>
        // 修复后的 formatDateTime 函数
        function formatDateTime(datetime) {
            if (!datetime) return '--'
            
            try {
                let dateStr = datetime
                
                // 将 "YYYY-MM-DD HH:mm:ss" 或 "YYYY-MM-DD HH:mm" 格式转换为 iOS 兼容格式
                if (typeof dateStr === 'string') {
                    // 检查是否为 "YYYY-MM-DD HH:mm:ss" 或 "YYYY-MM-DD HH:mm" 格式
                    if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(dateStr)) {
                        // 将空格替换为 T，使其符合 ISO 8601 标准
                        dateStr = dateStr.replace(' ', 'T')
                    }
                }
                
                const date = new Date(dateStr)
                
                if (isNaN(date.getTime())) {
                    console.warn('Invalid date:', datetime)
                    return '--'
                }
                
                const year = date.getFullYear()
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                const hours = String(date.getHours()).padStart(2, '0')
                const minutes = String(date.getMinutes()).padStart(2, '0')
                
                return `${year}-${month}-${day} ${hours}:${minutes}`
            } catch (error) {
                console.error('Date formatting error:', error, datetime)
                return '--'
            }
        }
        
        // 测试日期格式
        function testDateFormats() {
            const testDates = [
                '2025-06-28 14:54:59',
                '2025-06-27 22:33:26',
                '2025-01-15 09:30:00',
                '2025-12-31 23:59:59',
                '2025-06-15 08:00',
                'invalid-date',
                null,
                undefined
            ]
            
            const container = document.getElementById('dateTest')
            
            testDates.forEach(testDate => {
                const result = document.createElement('div')
                result.className = 'test-result'
                
                try {
                    const formatted = formatDateTime(testDate)
                    result.className += ' success'
                    result.innerHTML = `
                        <strong>输入:</strong> ${testDate || 'null/undefined'}<br>
                        <strong>输出:</strong> ${formatted}
                    `
                } catch (error) {
                    result.className += ' error'
                    result.innerHTML = `
                        <strong>输入:</strong> ${testDate || 'null/undefined'}<br>
                        <strong>错误:</strong> ${error.message}
                    `
                }
                
                container.appendChild(result)
            })
        }
        
        // 测试拼场订单标识
        function testSharingIdentification() {
            const mockUser = { username: 'testuser' }
            const mockSharings = [
                {
                    id: 1,
                    venueName: '体育馆A',
                    creatorUsername: 'testuser',
                    status: 'OPEN',
                    currentParticipants: 2,
                    maxParticipants: 4
                },
                {
                    id: 2,
                    venueName: '体育馆B',
                    creatorUsername: 'otheruser',
                    status: 'OPEN',
                    currentParticipants: 1,
                    maxParticipants: 6
                },
                {
                    id: 3,
                    venueName: '体育馆C',
                    creatorUsername: 'testuser',
                    status: 'FULL',
                    currentParticipants: 4,
                    maxParticipants: 4
                }
            ]
            
            function isMySharing(sharing) {
                return mockUser && sharing.creatorUsername === mockUser.username
            }
            
            function canJoinSharing(sharing) {
                if (mockUser && sharing.creatorUsername === mockUser.username) {
                    return false
                }
                return sharing.status === 'OPEN' && 
                       (sharing.currentParticipants || 0) < (sharing.maxParticipants || 0)
            }
            
            function getJoinButtonText(sharing) {
                if (isMySharing(sharing)) {
                    return '我的拼场'
                }
                if (sharing.status === 'FULL') {
                    return '已满员'
                }
                return '申请拼场'
            }
            
            const container = document.getElementById('sharingTest')
            
            mockSharings.forEach(sharing => {
                const card = document.createElement('div')
                card.className = 'sharing-card'
                
                const isMy = isMySharing(sharing)
                const canJoin = canJoinSharing(sharing)
                const buttonText = getJoinButtonText(sharing)
                
                card.innerHTML = `
                    <div>
                        <strong>${sharing.venueName}</strong>
                        ${isMy ? '<span class="my-sharing-badge">我的</span>' : ''}
                        <span class="sharing-status status-${sharing.status.toLowerCase()}">${sharing.status}</span>
                    </div>
                    <div style="margin-top: 10px;">
                        <strong>创建者:</strong> ${sharing.creatorUsername}<br>
                        <strong>人数:</strong> ${sharing.currentParticipants}/${sharing.maxParticipants}<br>
                        <strong>是否为我的拼场:</strong> ${isMy ? '是' : '否'}<br>
                        <strong>是否可申请:</strong> ${canJoin ? '是' : '否'}<br>
                        <strong>按钮文本:</strong> ${buttonText}
                    </div>
                `
                
                container.appendChild(card)
            })
        }
        
        // 运行测试
        testDateFormats()
        testSharingIdentification()
    </script>
</body>
</html>