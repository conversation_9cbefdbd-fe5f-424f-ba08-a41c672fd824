import { defineStore } from 'pinia'
import * as authApi from '@/api/auth.js'
import * as userApi from '@/api/user.js'
import { setToken, removeToken, setUserInfo, removeUserInfo, getUserInfo, getToken } from '@/utils/auth.js'
import { showSuccess, showError } from '@/utils/ui.js'
import { clearAuthCache, updateAuthCache } from '@/utils/router-guard-new.js'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    userInfo: getUserInfo(),
    userStats: {
      totalBookings: 0,
      totalSharings: 0
    },
    isLoggedIn: !!(getToken() && getUserInfo()),
    loginChecking: false // 是否正在检查登录状态
  }),

  getters: {
    // 用户信息相关
    getCurrentUserInfo: (state) => state.userInfo,
    isAuthenticated: (state) => state.isLoggedIn,
    userId: (state) => state.userInfo?.id,
    username: (state) => state.userInfo?.username,
    nickname: (state) => state.userInfo?.nickname || state.userInfo?.username,
    avatar: (state) => state.userInfo?.avatar,
    phone: (state) => state.userInfo?.phone,
    email: (state) => state.userInfo?.email,
    
    // 统计信息
    totalBookings: (state) => state.userStats.totalBookings,
    totalSharings: (state) => state.userStats.totalSharings,
    
    // 状态检查
    isLoginChecking: (state) => state.loginChecking
  },

  actions: {
    // 设置token
    setToken(token) {
      this.token = token
      setToken(token)
    },
    
    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      setUserInfo(userInfo)
    },
    
    // 设置登录状态
    setLoginStatus(status) {
      console.log('[UserStore] 设置登录状态:', status)
      this.isLoggedIn = status
      // 同步更新路由守卫缓存
      updateAuthCache(status)
    },
    
    // 设置登录检查状态
    setLoginChecking(checking) {
      this.loginChecking = checking
    },
    
    // 设置用户统计
    setUserStats(stats) {
      this.userStats = stats
    },
    
    // 清除用户数据
    clearUserData() {
      console.log('[UserStore] 清除用户数据')
      try {
        this.token = ''
        this.userInfo = {}
        this.userStats = {
          totalBookings: 0,
          totalSharings: 0
        }
        this.isLoggedIn = false
        this.loginChecking = false
        removeToken()
        removeUserInfo()
        // 清除路由守卫缓存
        clearAuthCache()
        console.log('[UserStore] 用户数据清除成功')
      } catch (error) {
        console.error('[UserStore] 清除用户数据失败:', error)
      }
    },

    // 用户登录
    async login(loginData) {
      try {
        console.log('[UserStore] 开始登录')
        const response = await authApi.login(loginData)
        console.log('[UserStore] 登录响应:', response)
        
        if (!response) {
          throw new Error('登录响应为空')
        }
        
        const responseData = response.data || response
        const token = responseData.accessToken || responseData.token
        
        if (!token) {
          console.error('[UserStore] 响应数据:', responseData)
          throw new Error('未获取到登录令牌')
        }
        
        const user = {
          id: responseData.id,
          username: responseData.username,
          email: responseData.email,
          phone: responseData.phone,
          nickname: responseData.nickname,
          avatar: responseData.avatar,
          roles: responseData.roles
        }
        
        // 更新状态
        this.setToken(token)
        this.setUserInfo(user)
        this.setLoginStatus(true)
        
        console.log('[UserStore] 登录成功，用户信息:', user)
        return response
      } catch (error) {
        console.error('[UserStore] 登录错误:', error)
        this.setLoginStatus(false)
        throw error
      }
    },

    // 用户注册
    async register(registerData) {
      try {
        console.log('[UserStore] 开始注册')
        const response = await authApi.register(registerData)
        console.log('[UserStore] 注册响应:', response)
        
        showSuccess('注册成功')
        return response
      } catch (error) {
        console.error('[UserStore] 注册错误:', error)
        showError(error.message || '注册失败')
        throw error
      }
    },

    // 用户退出
    async logout() {
      try {
        console.log('[UserStore] 开始退出登录')
        
        // 调用后端退出接口
        try {
          await authApi.logout()
        } catch (error) {
          console.warn('[UserStore] 后端退出接口调用失败:', error)
          // 即使后端接口失败，也继续清除本地数据
        }
        
        // 清除本地数据
        this.clearUserData()
        
        console.log('[UserStore] 退出登录成功')
        showSuccess('退出成功')
        
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/user/login'
        })
        
      } catch (error) {
        console.error('[UserStore] 退出登录错误:', error)
        // 即使出错也要清除本地数据
        this.clearUserData()
        throw error
      }
    },

    // 检查登录状态
    async checkLoginStatus() {
      if (this.loginChecking) {
        console.log('[UserStore] 正在检查登录状态，跳过重复检查')
        return this.isLoggedIn
      }
      
      this.setLoginChecking(true)
      
      try {
        console.log('[UserStore] 开始检查登录状态')
        
        const token = getToken()
        const userInfo = getUserInfo()
        
        if (!token || !userInfo) {
          console.log('[UserStore] 本地无登录信息')
          this.setLoginStatus(false)
          return false
        }
        
        // 验证token有效性
        try {
          const response = await userApi.getCurrentUser()
          console.log('[UserStore] 用户信息验证成功:', response)
          
          // 更新用户信息
          if (response && response.data) {
            this.setUserInfo(response.data)
          }
          
          this.setLoginStatus(true)
          return true
        } catch (error) {
          console.error('[UserStore] Token验证失败:', error)
          // Token无效，清除本地数据
          this.clearUserData()
          return false
        }
        
      } catch (error) {
        console.error('[UserStore] 检查登录状态错误:', error)
        this.setLoginStatus(false)
        return false
      } finally {
        this.setLoginChecking(false)
      }
    },

    // 初始化用户状态 - 从本地存储恢复
    initUserState() {
      try {
        console.log('[UserStore] 初始化用户状态')
        const token = getToken()
        const userInfo = getUserInfo()

        if (token && userInfo) {
          this.token = token
          this.userInfo = userInfo
          this.isLoggedIn = true
          console.log('[UserStore] 用户状态恢复成功')
        } else {
          this.clearUserData()
          console.log('[UserStore] 无有效用户状态，已清空')
        }
      } catch (error) {
        console.error('[UserStore] 初始化用户状态失败:', error)
        this.clearUserData()
      }
    },

    // 获取当前用户信息 - 兼容原Vuex的getUserInfo方法
    async getUserInfo() {
      try {
        console.log('[UserStore] 获取当前用户信息')
        const response = await userApi.getUserInfo()

        if (response && response.data) {
          this.setUserInfo(response.data)
          console.log('[UserStore] 用户信息获取成功:', response.data)
        }

        return response
      } catch (error) {
        console.error('[UserStore] 获取用户信息失败:', error)
        throw error
      }
    },

    // 获取用户统计信息
    async getUserStats() {
      try {
        console.log('[UserStore] 获取用户统计信息')
        const response = await userApi.getUserStats()

        if (response && response.data) {
          this.setUserStats(response.data)
          console.log('[UserStore] 用户统计信息更新成功:', response.data)
        }

        return response
      } catch (error) {
        console.error('[UserStore] 获取用户统计信息失败:', error)
        throw error
      }
    },

    // 更新用户信息
    async updateProfile(profileData) {
      try {
        console.log('[UserStore] 更新用户信息')
        const response = await userApi.updateProfile(profileData)
        
        if (response && response.data) {
          // 更新本地用户信息
          this.setUserInfo({
            ...this.userInfo,
            ...response.data
          })
          console.log('[UserStore] 用户信息更新成功')
          showSuccess('信息更新成功')
        }
        
        return response
      } catch (error) {
        console.error('[UserStore] 更新用户信息失败:', error)
        showError(error.message || '更新失败')
        throw error
      }
    },

    // 修改密码
    async changePassword(passwordData) {
      try {
        console.log('[UserStore] 修改密码')
        const response = await userApi.changePassword(passwordData)
        
        console.log('[UserStore] 密码修改成功')
        showSuccess('密码修改成功')
        
        return response
      } catch (error) {
        console.error('[UserStore] 修改密码失败:', error)
        showError(error.message || '密码修改失败')
        throw error
      }
    }
  }
})
