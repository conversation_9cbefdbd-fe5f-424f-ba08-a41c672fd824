<script>
	import { setupRouterGuard } from '@/utils/router-guard-new.js'
	import { useUserStore } from '@/stores/user.js'
	import { useAppStore } from '@/stores/app.js'
	
	export default {
		onLaunch: function() {
		console.log('[App] 应用启动')

		// 初始化stores
		this.userStore = useUserStore()
		this.appStore = useAppStore()

		// 1. 立即设置新的路由守卫
		setupRouterGuard()

		// 2. 初始化用户状态（从本地存储恢复）
		this.userStore.initUserState()

		// 3. 立即检查登录状态，未登录则跳转到登录页
		this.checkAndRedirectToLogin()

		// 4. 延迟执行非关键操作，提升启动速度
		this.$nextTick(() => {
			this.setupNetworkListener()
		})
	},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		data() {
			return {
				userStore: null,
				appStore: null
			}
		},

		methods: {
			
			// 检查登录状态并跳转到登录页
			checkAndRedirectToLogin() {
				try {
					console.log('[App] 检查登录状态')
					const token = uni.getStorageSync('token')
					const userInfo = uni.getStorageSync('userInfo')
					
					if (!token || !userInfo) {
						console.log('[App] 未登录，跳转到登录页')
						uni.reLaunch({
							url: '/pages/user/login'
						})
						return
					}
					
					console.log('[App] 已登录，继续正常流程')
				} catch (error) {
					console.warn('[App] 登录状态检查失败:', error.message)
					// 检查失败时也跳转到登录页
					uni.reLaunch({
						url: '/pages/user/login'
					})
				}
			},
			
			// 设置网络监听
			setupNetworkListener() {
				uni.onNetworkStatusChange((res) => {
					this.appStore.setNetworkStatus(res.isConnected)
					if (!res.isConnected) {
						uni.showToast({
							title: '网络连接已断开',
							icon: 'none'
						})
					}
				})
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
</style>
