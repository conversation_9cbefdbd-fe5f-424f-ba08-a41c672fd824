# 🏷️ 预订列表订单类型标识优化

## 🎯 需求描述

在 `pages/booking/list` 中的预订订单能根据后端的 `bookingType` 字段来标识订单类型：
- **拼场订单** - 在场馆名右边显示"拼场"标识
- **包场订单** - 在场馆名右边显示"包场"标识

## ✅ 实现方案

### 1. 布局结构优化

#### 修改前（垂直布局）
```html
<view class="venue-info">
  <text class="venue-name">场馆名称</text>
  <text class="booking-date">预订日期</text>
  <view class="booking-type-tag">
    <text class="tag-text">类型标识</text>
  </view>
</view>
```

#### 修改后（水平布局）
```html
<view class="venue-info">
  <!-- 场馆名和类型标识在同一行 -->
  <view class="venue-name-row">
    <text class="venue-name">场馆名称</text>
    <!-- 订单类型标识显示在场馆名右边 -->
    <view class="booking-type-tag" v-if="booking.bookingType">
      <text class="tag-text" :class="getBookingTypeClass(booking.bookingType)">
        {{ getBookingTypeText(booking.bookingType) }}
      </text>
    </view>
  </view>
  <text class="booking-date">预订日期</text>
</view>
```

### 2. CSS样式优化

#### 新增样式结构
```scss
.venue-info {
  flex: 1;
  
  .venue-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
    
    .venue-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-right: 12rpx;  // 与标识保持间距
    }
    
    .booking-type-tag {
      display: inline-block;
      
      .tag-text {
        font-size: 20rpx;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        border: 1rpx solid;
        
        // 拼场样式
        &.tag-shared {
          color: #ff6b35;
          background-color: #fff7e6;
          border-color: #ff6b35;
        }
        
        // 包场样式
        &.tag-exclusive {
          color: #1890ff;
          background-color: #e6f7ff;
          border-color: #1890ff;
        }
        
        // 默认样式
        &.tag-default {
          color: #666666;
          background-color: #f5f5f5;
          border-color: #d9d9d9;
        }
      }
    }
  }
  
  .booking-date {
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 8rpx;
  }
}
```

### 3. 类型映射逻辑

#### 文本映射
```javascript
const getBookingTypeText = (bookingType?: string): string => {
  const typeMap: Record<string, string> = {
    'EXCLUSIVE': '包场',
    'SHARED': '拼场'
  };
  return bookingType ? (typeMap[bookingType] || '普通') : '普通';
};
```

#### 样式类映射
```javascript
const getBookingTypeClass = (bookingType?: string): string => {
  const classMap: Record<string, string> = {
    'EXCLUSIVE': 'tag-exclusive',
    'SHARED': 'tag-shared'
  };
  return bookingType ? (classMap[bookingType] || 'tag-default') : 'tag-default';
};
```

## 🎨 视觉效果

### 拼场订单显示
```
[场馆名称] [拼场]  [状态]
预订日期
时间段
```

### 包场订单显示
```
[场馆名称] [包场]  [状态]
预订日期  
时间段
```

### 样式特点
- **拼场标识**: 橙色背景 (#fff7e6)，橙色边框和文字 (#ff6b35)
- **包场标识**: 蓝色背景 (#e6f7ff)，蓝色边框和文字 (#1890ff)
- **默认标识**: 灰色背景 (#f5f5f5)，灰色边框和文字 (#666666)

## 📊 技术特点

### 1. 响应式布局
- 使用 `flex` 布局确保标识与场馆名在同一行
- 自适应不同长度的场馆名称
- 保持整体布局的协调性

### 2. 条件渲染
- 只有当 `booking.bookingType` 存在时才显示标识
- 避免空标识的显示问题

### 3. 类型安全
- 使用 TypeScript 类型定义
- 完善的类型映射和默认值处理

### 4. 样式一致性
- 统一的标识样式规范
- 与整体设计风格保持一致

## 🔍 数据字段要求

### 后端数据结构
```javascript
{
  id: string,
  venueName: string,
  bookingDate: string,
  bookingType: 'EXCLUSIVE' | 'SHARED' | string,  // 关键字段
  status: string,
  // ... 其他字段
}
```

### 支持的类型值
- `'EXCLUSIVE'` - 包场订单
- `'SHARED'` - 拼场订单
- 其他值或空值 - 显示为"普通"

## 🧪 测试验证

### 测试场景
1. **拼场订单** - 验证显示"拼场"标识，橙色样式
2. **包场订单** - 验证显示"包场"标识，蓝色样式
3. **普通订单** - 验证显示"普通"标识，灰色样式
4. **无类型订单** - 验证不显示标识或显示默认标识
5. **长场馆名** - 验证布局不会错乱

### 预期效果
- ✅ 标识紧贴场馆名右侧显示
- ✅ 不同类型有明显的视觉区分
- ✅ 布局整齐，不影响其他信息显示
- ✅ 响应式适配不同屏幕尺寸

## 🎉 优化完成

现在预订列表页面具备了：
- 🏷️ **清晰的类型标识** - 一眼就能区分订单类型
- 🎨 **美观的视觉设计** - 不同颜色区分不同类型
- 📱 **响应式布局** - 适配各种屏幕尺寸
- 🔧 **完善的错误处理** - 兼容各种数据情况

用户现在可以快速识别每个订单的类型，提升了用户体验！🚀
