<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>拼场列表测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .card { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 8px; }
        .status { padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; }
        .status.open { background-color: #52c41a; }
        .status.full { background-color: #faad14; }
        .status.confirmed { background-color: #1890ff; }
        .price { color: #ff4d4f; font-weight: bold; }
        .note { color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <h1>拼场列表测试页面</h1>
    <p>这个页面用于测试拼场数据的显示效果</p>
    
    <div id="sharing-list"></div>
    
    <script>
        // 模拟拼场数据
        const mockData = [
            {
                id: 1,
                venueName: '奥体中心篮球馆',
                venueLocation: '朝阳区奥林匹克公园',
                teamName: '雄鹰队',
                status: 'OPEN',
                maxParticipants: 8,
                currentParticipants: 5,
                totalPrice: 600,
                bookingDate: '2025-06-30',
                startTime: '14:00',
                endTime: '16:00',
                creatorUsername: '用户1',
                description: '欢迎加入雄鹰队，一起享受运动的乐趣！'
            },
            {
                id: 2,
                venueName: '工体足球场',
                venueLocation: '朝阳区工人体育场',
                teamName: '猛虎队',
                status: 'FULL',
                maxParticipants: 10,
                currentParticipants: 10,
                totalPrice: 800,
                bookingDate: '2025-07-01',
                startTime: '10:00',
                endTime: '12:00',
                creatorUsername: '用户2',
                description: null
            },
            {
                id: 3,
                venueName: '五棵松体育馆',
                venueLocation: '海淀区五棵松',
                teamName: '飞龙队',
                status: 'CONFIRMED',
                maxParticipants: 6,
                currentParticipants: 6,
                totalPrice: 450,
                bookingDate: '2025-07-02',
                startTime: '18:00',
                endTime: '20:00',
                creatorUsername: '用户3',
                description: '期待与大家一起比赛！'
            }
        ];
        
        function getStatusText(status) {
            switch(status) {
                case 'OPEN': return '开放中';
                case 'FULL': return '已满员';
                case 'CONFIRMED': return '已确认';
                default: return '未知状态';
            }
        }
        
        function getStatusClass(status) {
            switch(status) {
                case 'OPEN': return 'open';
                case 'FULL': return 'full';
                case 'CONFIRMED': return 'confirmed';
                default: return '';
            }
        }
        
        function renderSharingList() {
            const container = document.getElementById('sharing-list');
            
            mockData.forEach(sharing => {
                const card = document.createElement('div');
                card.className = 'card';
                
                card.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h3 style="margin: 0;">${sharing.venueName}</h3>
                        <span class="status ${getStatusClass(sharing.status)}">${getStatusText(sharing.status)}</span>
                    </div>
                    
                    <p style="margin: 5px 0; color: #666;">${sharing.venueLocation}</p>
                    
                    <div style="margin: 10px 0;">
                        <p><strong>队伍：</strong>${sharing.teamName}</p>
                        <p><strong>时间：</strong>${sharing.bookingDate} ${sharing.startTime}-${sharing.endTime}</p>
                        <p><strong>人数：</strong>${sharing.currentParticipants}/${sharing.maxParticipants}人</p>
                        <p class="price"><strong>总费用：</strong>¥${sharing.totalPrice} <span class="note">（两队平分）</span></p>
                        <p><strong>组织者：</strong>${sharing.creatorUsername}</p>
                        ${sharing.description ? `<p><strong>描述：</strong>${sharing.description}</p>` : ''}
                    </div>
                `;
                
                container.appendChild(card);
            });
        }
        
        // 页面加载完成后渲染列表
        document.addEventListener('DOMContentLoaded', renderSharingList);
    </script>
</body>
</html>