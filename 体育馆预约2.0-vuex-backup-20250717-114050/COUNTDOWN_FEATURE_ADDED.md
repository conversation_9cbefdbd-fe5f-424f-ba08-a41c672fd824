# ⏰ 拼场订单倒计时功能完成

## 🎯 功能需求

为拼场订单添加倒计时功能，显示距离自动取消还有多长时间：
- **自动取消规则**：距离预约时间2小时自动取消
- **显示位置**：拼场界面和我的预约界面
- **实时更新**：每秒更新倒计时显示

## ✅ 实现方案

### 1. 倒计时工具函数 (`utils/countdown.js`)

#### 核心功能
- **计算自动取消时间**：`getAutoCancelTime(bookingDate, startTime)`
- **计算倒计时**：`calculateCountdown(targetTime)`
- **获取拼场订单倒计时**：`getSharingOrderCountdown(sharingOrder)`
- **判断是否显示倒计时**：`shouldShowCountdown(order)`

#### 时间计算逻辑
```javascript
// 自动取消时间 = 预约时间 - 2小时
const bookingDateTime = new Date(`${bookingDate} ${timeStr}`)
const cancelTime = new Date(bookingDateTime.getTime() - 2 * 60 * 60 * 1000)
```

#### 倒计时格式化
- **超过1天**：`2天3小时`
- **超过1小时**：`3小时25分钟`
- **小于1小时**：`25分钟30秒`
- **已过期**：`已过期`

### 2. 倒计时组件 (`components/CountdownTimer.vue`)

#### 组件特性
- **自动更新**：每秒刷新倒计时
- **状态样式**：根据剩余时间显示不同颜色
- **过期处理**：自动停止定时器并触发事件
- **简化模式**：支持列表页面的简化显示

#### 样式状态
- **正常状态**（绿色）：剩余时间 > 2小时
- **警告状态**（橙色）：剩余时间 < 2小时
- **紧急状态**（红色）：剩余时间 < 30分钟，带闪烁效果
- **已过期**（灰色）：倒计时结束

#### 使用方式
```html
<CountdownTimer 
  :order="sharingOrder" 
  label="自动取消" 
  :short="true"
  @expired="onCountdownExpired"
/>
```

### 3. 页面集成

#### 拼场列表页面 (`pages/sharing/list.vue`)
- **显示位置**：参与者信息下方
- **显示条件**：仅拼场订单且状态为开放中
- **样式**：简化模式，紧凑显示

```html
<CountdownTimer 
  v-if="shouldShowCountdown(sharing)"
  :order="sharing" 
  label="自动取消" 
  :short="true"
  class="simple"
  @expired="onCountdownExpired"
/>
```

#### 拼场详情页面 (`pages/sharing/detail.vue`)
- **显示位置**：状态标识下方
- **显示条件**：拼场订单且状态为开放中
- **样式**：完整模式，详细显示

```html
<CountdownTimer 
  v-if="shouldShowCountdown(sharingOrderDetail)"
  :order="sharingOrderDetail" 
  label="距离自动取消" 
  @expired="onCountdownExpired"
/>
```

#### 我的预约页面 (`pages/booking/list.vue`)
- **显示位置**：价格信息下方
- **显示条件**：拼场类型订单且状态为开放中
- **样式**：简化模式，紧凑显示

```html
<CountdownTimer 
  v-if="shouldShowCountdown(booking)"
  :order="booking" 
  label="自动取消" 
  :short="true"
  class="simple"
  @expired="onCountdownExpired"
/>
```

## 📊 技术实现

### 1. 数据字段要求
拼场订单需要包含以下字段：
- `bookingDate` - 预约日期 (YYYY-MM-DD)
- `startTime` - 开始时间 (HH:mm:ss 或 HH:mm)
- `status` - 订单状态 ('OPEN' 才显示倒计时)
- `bookingType` - 订单类型 ('SHARED' 为拼场订单)

### 2. 显示条件判断
```javascript
function shouldShowCountdown(order) {
  // 只有拼场订单且状态为开放中才显示倒计时
  const isSharingOrder = order.bookingType === 'SHARED' || 
                        order.status === 'OPEN' || 
                        order.maxParticipants > 0
  
  if (!isSharingOrder) return false
  if (order.status !== 'OPEN' && order.status !== 'SHARING') return false
  if (!order.bookingDate || !order.startTime) return false
  
  return true
}
```

### 3. 定时器管理
- **创建定时器**：组件mounted时创建
- **清理定时器**：组件销毁时清理
- **自动停止**：倒计时过期时自动停止

### 4. 过期处理
```javascript
onCountdownExpired(order) {
  console.log('拼场订单倒计时过期:', order.orderNo)
  // 刷新数据，更新订单状态
  this.refreshData()
}
```

## 🎨 视觉效果

### 列表页面显示
```
[场馆名称] [拼场]
参与球队：2/4支
进度条 ████████░░ 50%
⏰ 自动取消 2时30分
```

### 详情页面显示
```
[开放中]
⏰ 距离自动取消 2小时30分钟

队伍信息
...
```

### 我的预约页面显示
```
[场馆名称] [拼场]  [开放中]
时间：14:00-16:00
费用：¥120.00
⏰ 自动取消 2时30分
```

## 🔄 状态变化

### 倒计时颜色变化
1. **绿色**（正常）：剩余时间充足
2. **橙色**（警告）：剩余时间 < 2小时
3. **红色闪烁**（紧急）：剩余时间 < 30分钟
4. **灰色**（过期）：倒计时结束

### 自动刷新机制
- **定时更新**：每秒更新倒计时显示
- **过期处理**：倒计时结束时刷新页面数据
- **状态同步**：确保显示状态与后端一致

## 🧪 测试场景

### 1. 正常倒计时
- 创建拼场订单，预约时间为3小时后
- 验证倒计时显示为"1小时"（3小时-2小时）
- 验证颜色为绿色（正常状态）

### 2. 警告状态
- 倒计时剩余1小时时
- 验证颜色变为橙色（警告状态）

### 3. 紧急状态
- 倒计时剩余20分钟时
- 验证颜色变为红色并闪烁（紧急状态）

### 4. 过期处理
- 倒计时结束时
- 验证显示"已过期"
- 验证页面数据自动刷新

### 5. 不同页面显示
- 验证列表页面简化显示
- 验证详情页面完整显示
- 验证我的预约页面正确显示

## 🎉 功能完成

拼场订单倒计时功能已完全实现：
- ⏰ **倒计时工具函数** - 完整的时间计算逻辑
- 🎨 **倒计时组件** - 可复用的UI组件
- 📱 **页面集成** - 三个页面都已集成
- 🎯 **状态管理** - 完善的状态变化处理
- 🔄 **自动刷新** - 实时更新和过期处理

现在用户可以清楚地看到拼场订单距离自动取消还有多长时间，提升了用户体验！🚀
