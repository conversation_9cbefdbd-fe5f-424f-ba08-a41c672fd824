import App from './App.vue'

// #ifndef VUE3
import Vue from 'vue'
import store from './store/index.js'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { pinia } from './stores/index.js'

export function createApp() {
  const app = createSSRApp(App)
  app.use(pinia) // 只使用Pinia
  return {
    app
  }
}
// #endif