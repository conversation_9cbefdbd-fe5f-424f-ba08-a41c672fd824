<!DOCTYPE html>
<html>
<head>
    <title>拼场数据修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .data { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>拼场数据修复测试</h1>
    
    <div class="test-section">
        <h2>1. 测试API数据结构</h2>
        <button onclick="testApiStructure()">测试API返回结构</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 模拟Store数据处理</h2>
        <button onclick="testStoreProcessing()">测试Store数据处理逻辑</button>
        <div id="store-result"></div>
    </div>
    
    <script>
        async function testApiStructure() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p>正在测试...</p>';
            
            try {
                const response = await fetch('http://localhost:8080/api/sharing-orders/joinable?page=1&pageSize=10');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ API调用成功</div>
                    <div class="data">
                        <strong>返回数据结构:</strong>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                    <div>
                        <strong>数据分析:</strong><br>
                        - 是否有list字段: ${data.list ? '✅ 是' : '❌ 否'}<br>
                        - 是否有data字段: ${data.data ? '✅ 是' : '❌ 否'}<br>
                        - 是否有pagination字段: ${data.pagination ? '✅ 是' : '❌ 否'}<br>
                        - list数组长度: ${data.list ? data.list.length : 'N/A'}<br>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ API调用失败: ${error.message}</div>`;
            }
        }
        
        function testStoreProcessing() {
            const resultDiv = document.getElementById('store-result');
            
            // 模拟API返回的数据
            const mockApiResponse = {
                list: [
                    { id: 1, venueName: '测试场馆1', status: 'OPEN' },
                    { id: 2, venueName: '测试场馆2', status: 'FULL' }
                ],
                pagination: {
                    current: 1,
                    pageSize: 10,
                    total: 2
                }
            };
            
            // 模拟Store的数据处理逻辑
            const orders = mockApiResponse.list || mockApiResponse.data || [];
            
            resultDiv.innerHTML = `
                <div class="success">✅ Store数据处理测试</div>
                <div class="data">
                    <strong>模拟API响应:</strong>
                    <pre>${JSON.stringify(mockApiResponse, null, 2)}</pre>
                </div>
                <div class="data">
                    <strong>提取的订单数据:</strong>
                    <pre>${JSON.stringify(orders, null, 2)}</pre>
                </div>
                <div>
                    <strong>处理结果:</strong><br>
                    - 提取的订单数量: ${orders.length}<br>
                    - 数据提取逻辑: response.list || response.data || []<br>
                    - 结果: ${orders.length > 0 ? '✅ 成功提取数据' : '❌ 未提取到数据'}
                </div>
            `;
        }
    </script>
</body>
</html>