#!/usr/bin/env node

// Vuex到Pinia页面迁移脚本
// 用于批量迁移剩余的页面文件

const fs = require('fs')
const path = require('path')

// 需要迁移的页面列表
const pagesToMigrate = [
  'pages/booking/create.vue',
  'pages/sharing/list.vue', 
  'pages/user/profile.vue',
  'pages/booking/detail.vue',
  'pages/venue/detail.vue',
  'pages/sharing/create.vue',
  'pages/sharing/detail.vue',
  'pages/sharing/manage.vue',
  'pages/sharing/my-orders.vue',
  'pages/sharing/received.vue',
  'pages/sharing/requests.vue',
  'pages/user/register.vue',
  'pages/user/edit-profile.vue'
]

// Store映射关系
const storeMapping = {
  'user': 'useUserStore',
  'venue': 'useVenueStore', 
  'booking': 'useBookingStore',
  'sharing': 'useSharingStore',
  'app': 'useAppStore'
}

// 迁移单个页面
function migratePage(filePath) {
  console.log(`🔄 开始迁移: ${filePath}`)
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`)
    return false
  }
  
  let content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  
  // 1. 替换import语句
  if (content.includes("import { mapState, mapActions, mapGetters") || 
      content.includes("import { mapActions, mapGetters") ||
      content.includes("import { mapState, mapGetters") ||
      content.includes("import { mapGetters")) {
    
    // 检测使用了哪些store
    const usedStores = []
    Object.keys(storeMapping).forEach(storeName => {
      if (content.includes(`'${storeName}'`) || content.includes(`"${storeName}"`)) {
        usedStores.push(storeName)
      }
    })
    
    // 生成新的import语句
    const newImports = usedStores.map(store => 
      `import { ${storeMapping[store]} } from '@/stores/${store}.js'`
    ).join('\n')
    
    // 替换旧的import
    content = content.replace(
      /import\s+{\s*[^}]*map[^}]*}\s+from\s+['"]vuex['"]/g,
      newImports
    )
    modified = true
  }
  
  // 2. 在data中添加store实例
  if (content.includes('data()')) {
    const usedStores = []
    Object.keys(storeMapping).forEach(storeName => {
      if (content.includes(`'${storeName}'`) || content.includes(`"${storeName}"`)) {
        usedStores.push(storeName)
      }
    })
    
    if (usedStores.length > 0) {
      const storeVars = usedStores.map(store => `${store}Store: null`).join(',\n      ')
      
      content = content.replace(
        /(data\(\)\s*{\s*return\s*{)/,
        `$1\n      ${storeVars},`
      )
      modified = true
    }
  }
  
  // 3. 在onLoad中初始化stores
  if (content.includes('onLoad(')) {
    const usedStores = []
    Object.keys(storeMapping).forEach(storeName => {
      if (content.includes(`'${storeName}'`) || content.includes(`"${storeName}"`)) {
        usedStores.push(storeName)
      }
    })
    
    if (usedStores.length > 0) {
      const storeInits = usedStores.map(store => 
        `this.${store}Store = ${storeMapping[store]}()`
      ).join('\n    ')
      
      content = content.replace(
        /(onLoad\([^)]*\)\s*{)/,
        `$1\n    // 初始化Pinia stores\n    ${storeInits}\n`
      )
      modified = true
    }
  }
  
  // 4. 移除mapGetters, mapActions等
  content = content.replace(/\s*\.\.\.mapGetters\([^)]+\),?\s*/g, '')
  content = content.replace(/\s*\.\.\.mapActions\([^)]+\),?\s*/g, '')
  content = content.replace(/\s*\.\.\.mapState\([^)]+\),?\s*/g, '')
  content = content.replace(/\s*\.\.\.mapMutations\([^)]+\),?\s*/g, '')
  
  if (modified) {
    // 创建备份
    const backupPath = filePath + '.backup'
    fs.writeFileSync(backupPath, fs.readFileSync(filePath))
    
    // 写入修改后的内容
    fs.writeFileSync(filePath, content)
    console.log(`✅ 迁移完成: ${filePath}`)
    console.log(`📁 备份文件: ${backupPath}`)
    return true
  } else {
    console.log(`ℹ️  无需修改: ${filePath}`)
    return false
  }
}

// 主函数
function main() {
  console.log('🚀 开始批量迁移Vuex到Pinia...')
  console.log(`📋 待迁移页面数量: ${pagesToMigrate.length}`)
  
  let successCount = 0
  let failCount = 0
  
  pagesToMigrate.forEach(pagePath => {
    try {
      const fullPath = path.join(__dirname, pagePath)
      if (migratePage(fullPath)) {
        successCount++
      }
    } catch (error) {
      console.error(`❌ 迁移失败: ${pagePath}`, error.message)
      failCount++
    }
  })
  
  console.log('\n📊 迁移统计:')
  console.log(`✅ 成功: ${successCount}`)
  console.log(`❌ 失败: ${failCount}`)
  console.log(`ℹ️  无需修改: ${pagesToMigrate.length - successCount - failCount}`)
  
  console.log('\n⚠️  注意事项:')
  console.log('1. 脚本只完成了基础的语法替换')
  console.log('2. 需要手动检查computed属性和methods中的store调用')
  console.log('3. 需要手动测试每个页面的功能')
  console.log('4. 备份文件已创建，如有问题可以恢复')
}

// 运行脚本
if (require.main === module) {
  main()
}

module.exports = { migratePage, storeMapping }
