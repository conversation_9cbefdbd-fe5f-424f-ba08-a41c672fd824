# 🚨 紧急Vuex迁移计划

## 📊 当前状态

**已完成**: 4个页面 (31%)
**剩余**: 9个页面 (69%)

### ✅ 已完成的页面
1. pages/index/index.vue ✅
2. pages/booking/detail.vue ✅  
3. pages/sharing/list.vue ✅
4. pages/booking/create.vue ✅

### 🔄 剩余需要修复的页面
5. pages/venue/detail.vue
6. pages/venue/list.vue
7. pages/sharing/create.vue
8. pages/sharing/detail.vue
9. pages/sharing/my-orders.vue
10. pages/sharing/requests.vue
11. pages/sharing/received.vue
12. pages/user/register.vue
13. pages/user/edit-profile.vue

## 🚀 快速修复策略

### 立即行动计划

1. **清除缓存并测试当前修复**
   - 微信开发者工具 → 清缓存
   - 删除unpackage目录
   - 重新编译测试

2. **批量修复剩余页面**
   - 使用标准化修复模式
   - 优先修复高频使用页面

## 📋 标准修复模板

每个页面都按以下模板修复：

### 1. 导入语句
```javascript
// 删除
import { mapState, mapActions, mapGetters } from 'vuex'

// 添加
import { useVenueStore } from '@/stores/venue.js'
import { useBookingStore } from '@/stores/booking.js'
import { useUserStore } from '@/stores/user.js'
import { useSharingStore } from '@/stores/sharing.js'
```

### 2. data中添加store实例
```javascript
data() {
  return {
    venueStore: null,
    bookingStore: null,
    userStore: null,
    sharingStore: null,
    // ... 其他数据
  }
}
```

### 3. onLoad中初始化
```javascript
onLoad() {
  this.venueStore = useVenueStore()
  this.bookingStore = useBookingStore()
  this.userStore = useUserStore()
  this.sharingStore = useSharingStore()
  // ... 其他初始化
}
```

### 4. 替换computed
```javascript
// 删除
computed: {
  ...mapGetters('venue', ['venues']),
  ...mapState('user', ['userInfo'])
}

// 替换为
computed: {
  venues() {
    return this.venueStore?.venues || []
  },
  userInfo() {
    return this.userStore?.getCurrentUserInfo || {}
  }
}
```

### 5. 删除mapActions并更新方法调用
```javascript
// 删除
methods: {
  ...mapActions('venue', ['getVenues']),
}

// 方法调用更新
// 从: await this.getVenues()
// 到: await this.venueStore.getVenues()
```

### 6. 替换$store调用
```javascript
// 从: this.$store.commit('venue/SET_VENUES', data)
// 到: this.venueStore.setVenues(data)

// 从: this.$store.dispatch('venue/getVenues')
// 到: await this.venueStore.getVenues()
```

## ⚡ 快速修复命令

### 批量查找需要修复的调用
```bash
# 查找mapActions调用
grep -n "mapActions" pages/venue/detail.vue

# 查找$store调用
grep -n "\$store" pages/venue/detail.vue

# 查找this.方法调用（需要更新为store调用）
grep -n "this\.\(get\|create\|update\|delete\)" pages/venue/detail.vue
```

## 🎯 优先级修复顺序

### 第一批（高优先级）
1. pages/venue/detail.vue - 场馆详情
2. pages/venue/list.vue - 场馆列表

### 第二批（中优先级）
3. pages/sharing/create.vue - 创建拼场
4. pages/sharing/detail.vue - 拼场详情

### 第三批（低优先级）
5. pages/sharing/my-orders.vue
6. pages/sharing/requests.vue
7. pages/sharing/received.vue
8. pages/user/register.vue
9. pages/user/edit-profile.vue

## 🧪 测试验证

每修复一个页面后：

1. **编译检查**
   ```bash
   npm run dev
   ```

2. **功能测试**
   - 页面能正常加载
   - 数据能正常显示
   - 交互功能正常

3. **错误检查**
   - 控制台无Vuex相关错误
   - 无"dispatch undefined"错误
   - 无"mapActions"相关错误

## ⚠️ 常见问题解决

### 1. 方法不存在错误
- 检查store中是否有对应方法
- 确认方法名拼写正确
- 确保store已正确初始化

### 2. getter名称冲突
- 使用更明确的getter名称
- 避免与state属性同名

### 3. 缓存问题
- 清除微信开发者工具缓存
- 删除unpackage目录
- 重启开发工具

## 🚀 立即执行

**现在就开始**：
1. 清除缓存测试当前修复效果
2. 如果当前修复的页面工作正常，继续修复venue/detail.vue
3. 使用标准模板快速修复剩余页面

**目标**：在接下来的1-2小时内完成所有页面的迁移！

---

**创建时间**: 2025-07-16 20:35
**状态**: 🚨 紧急执行中
**目标**: 100%完成Vuex到Pinia迁移
