{"id": "uni-transition", "displayName": "uni-transition 过渡动画", "version": "1.3.5", "description": "元素的简单过渡动画", "keywords": ["uni-ui", "uniui", "动画", "过渡", "过渡动画"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": "", "uni-app": "^4.01", "uni-app-x": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": ["uni-scss"], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "x"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": "√", "ios": "√", "harmony": "-"}, "mp": {"weixin": {"extVersion": "1.0.2", "minVersion": ""}, "alipay": {"extVersion": "1.0.2", "minVersion": ""}, "toutiao": {"extVersion": "1.0.2", "minVersion": ""}, "baidu": {"extVersion": "1.0.2", "minVersion": ""}, "kuaishou": {"extVersion": "1.1.0", "minVersion": ""}, "jd": {"extVersion": "1.0.2", "minVersion": ""}, "harmony": "x", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}}