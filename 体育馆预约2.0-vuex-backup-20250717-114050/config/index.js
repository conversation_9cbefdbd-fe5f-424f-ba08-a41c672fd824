// 环境配置
const config = {
  development: {
    baseURL: 'http://localhost:8080/api',
    timeout: 15000, // 增加到15秒超时，适应复杂查询
    debug: true,
    cache: true, // 启用缓存
    retryTimes: 2, // 重试次数
    retryDelay: 1000 // 重试延迟
  },
  
  production: {
    baseURL: 'https://api.example.com/api',
    timeout: 20000, // 生产环境增加到20秒超时
    debug: false,
    cache: true,
    retryTimes: 3,
    retryDelay: 1000
  }
}

export default config[process.env.NODE_ENV || 'development']