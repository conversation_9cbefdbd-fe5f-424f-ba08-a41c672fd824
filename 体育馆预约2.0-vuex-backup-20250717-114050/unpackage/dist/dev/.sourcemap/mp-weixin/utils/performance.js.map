{"version": 3, "file": "performance.js", "sources": ["utils/performance.js"], "sourcesContent": ["// 性能优化工具类\n\n/**\n * 图片预加载\n * @param {Array} imageUrls 图片URL数组\n * @returns {Promise} 预加载完成的Promise\n */\nexport function preloadImages(imageUrls) {\n  return Promise.allSettled(\n    imageUrls.map(url => {\n      return new Promise((resolve, reject) => {\n        const img = new Image()\n        img.onload = () => resolve(url)\n        img.onerror = () => reject(url)\n        img.src = url\n      })\n    })\n  )\n}\n\n/**\n * 缓存管理器\n */\nexport class CacheManager {\n  /**\n   * 设置缓存\n   * @param {string} key 缓存键\n   * @param {any} data 缓存数据\n   * @param {number} expireTime 过期时间（毫秒）\n   */\n  static set(key, data, expireTime = 5 * 60 * 1000) {\n    const cacheData = {\n      data,\n      timestamp: Date.now(),\n      expireTime\n    }\n    uni.setStorageSync(key, cacheData)\n  }\n\n  /**\n   * 获取缓存\n   * @param {string} key 缓存键\n   * @returns {any|null} 缓存数据或null\n   */\n  static get(key) {\n    try {\n      const cached = uni.getStorageSync(key)\n      if (!cached) return null\n\n      const { data, timestamp, expireTime } = cached\n      if (Date.now() - timestamp > expireTime) {\n        this.remove(key)\n        return null\n      }\n\n      return data\n    } catch (error) {\n      console.error('获取缓存失败:', error)\n      return null\n    }\n  }\n\n  /**\n   * 删除缓存\n   * @param {string} key 缓存键\n   */\n  static remove(key) {\n    uni.removeStorageSync(key)\n  }\n\n  /**\n   * 清空所有缓存\n   */\n  static clear() {\n    uni.clearStorageSync()\n  }\n\n  /**\n   * 获取缓存大小\n   * @returns {Object} 缓存信息\n   */\n  static getInfo() {\n    return uni.getStorageInfoSync()\n  }\n}\n\n/**\n * 防抖函数\n * @param {Function} func 要防抖的函数\n * @param {number} delay 延迟时间\n * @returns {Function} 防抖后的函数\n */\nexport function debounce(func, delay = 300) {\n  let timeoutId\n  return function (...args) {\n    clearTimeout(timeoutId)\n    timeoutId = setTimeout(() => func.apply(this, args), delay)\n  }\n}\n\n/**\n * 节流函数\n * @param {Function} func 要节流的函数\n * @param {number} delay 延迟时间\n * @returns {Function} 节流后的函数\n */\nexport function throttle(func, delay = 300) {\n  let lastTime = 0\n  return function (...args) {\n    const now = Date.now()\n    if (now - lastTime >= delay) {\n      lastTime = now\n      func.apply(this, args)\n    }\n  }\n}\n\n/**\n * 延迟执行\n * @param {number} ms 延迟毫秒数\n * @returns {Promise} Promise对象\n */\nexport function delay(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n/**\n * 批量请求管理器\n */\nexport class BatchRequestManager {\n  constructor() {\n    this.requestQueue = []\n    this.isProcessing = false\n    this.maxConcurrent = 3 // 最大并发数\n  }\n\n  /**\n   * 添加请求到队列\n   * @param {Function} requestFn 请求函数\n   * @returns {Promise} 请求结果\n   */\n  add(requestFn) {\n    return new Promise((resolve, reject) => {\n      this.requestQueue.push({\n        requestFn,\n        resolve,\n        reject\n      })\n      this.process()\n    })\n  }\n\n  /**\n   * 处理请求队列\n   */\n  async process() {\n    if (this.isProcessing) return\n    this.isProcessing = true\n\n    while (this.requestQueue.length > 0) {\n      const batch = this.requestQueue.splice(0, this.maxConcurrent)\n      const promises = batch.map(async ({ requestFn, resolve, reject }) => {\n        try {\n          const result = await requestFn()\n          resolve(result)\n        } catch (error) {\n          reject(error)\n        }\n      })\n\n      await Promise.allSettled(promises)\n    }\n\n    this.isProcessing = false\n  }\n}\n\n/**\n * 性能监控\n */\nexport class PerformanceMonitor {\n  static marks = new Map()\n\n  /**\n   * 开始计时\n   * @param {string} name 计时名称\n   */\n  static mark(name) {\n    this.marks.set(name, Date.now())\n  }\n\n  /**\n   * 结束计时并输出结果\n   * @param {string} name 计时名称\n   * @returns {number} 耗时（毫秒）\n   */\n  static measure(name) {\n    const startTime = this.marks.get(name)\n    if (!startTime) {\n      console.warn(`未找到计时标记: ${name}`)\n      return 0\n    }\n\n    const duration = Date.now() - startTime\n    console.log(`${name} 耗时: ${duration}ms`)\n    this.marks.delete(name)\n    return duration\n  }\n\n  /**\n   * 监控页面加载性能\n   */\n  static monitorPageLoad() {\n    const pages = getCurrentPages()\n    const currentPage = pages[pages.length - 1]\n    const route = currentPage.route\n    \n    console.log(`页面 ${route} 加载完成`)\n    console.log(`当前页面栈深度: ${pages.length}`)\n  }\n}\n\n/**\n * 内存优化\n */\nexport class MemoryOptimizer {\n  /**\n   * 清理页面栈\n   * @param {number} maxPages 最大页面数\n   */\n  static cleanPageStack(maxPages = 5) {\n    const pages = getCurrentPages()\n    if (pages.length > maxPages) {\n      // 可以考虑使用 uni.reLaunch 重新加载页面栈\n      console.warn(`页面栈过深 (${pages.length}), 建议优化导航逻辑`)\n    }\n  }\n\n  /**\n   * 清理无用的缓存\n   */\n  static cleanCache() {\n    const info = CacheManager.getInfo()\n    console.log('当前缓存信息:', info)\n    \n    // 如果缓存过大，清理部分缓存\n    if (info.currentSize > 10 * 1024 * 1024) { // 10MB\n      console.log('缓存过大，开始清理...')\n      // 可以实现更精细的缓存清理策略\n    }\n  }\n}"], "names": ["uni"], "mappings": ";;;;;;;;AAuBO,MAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,OAAO,IAAI,KAAK,MAAM,aAAa,IAAI,KAAK,KAAM;AAChD,UAAM,YAAY;AAAA,MAChB;AAAA,MACA,WAAW,KAAK,IAAK;AAAA,MACrB;AAAA,IACD;AACDA,wBAAI,eAAe,KAAK,SAAS;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,IAAI,KAAK;AACd,QAAI;AACF,YAAM,SAASA,cAAAA,MAAI,eAAe,GAAG;AACrC,UAAI,CAAC;AAAQ,eAAO;AAEpB,YAAM,EAAE,MAAM,WAAW,WAAY,IAAG;AACxC,UAAI,KAAK,QAAQ,YAAY,YAAY;AACvC,aAAK,OAAO,GAAG;AACf,eAAO;AAAA,MACR;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,8BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,OAAO,KAAK;AACjBA,kBAAG,MAAC,kBAAkB,GAAG;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,QAAQ;AACbA,kBAAAA,MAAI,iBAAkB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,UAAU;AACf,WAAOA,cAAAA,MAAI,mBAAoB;AAAA,EAChC;AACH;AAgGO,MAAM,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAO9B,OAAO,KAAK,MAAM;AAChB,SAAK,MAAM,IAAI,MAAM,KAAK,IAAG,CAAE;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,QAAQ,MAAM;AACnB,UAAM,YAAY,KAAK,MAAM,IAAI,IAAI;AACrC,QAAI,CAAC,WAAW;AACdA,uEAAa,YAAY,IAAI,EAAE;AAC/B,aAAO;AAAA,IACR;AAED,UAAM,WAAW,KAAK,IAAG,IAAK;AAC9BA,wBAAA,MAAA,OAAA,+BAAY,GAAG,IAAI,QAAQ,QAAQ,IAAI;AACvC,SAAK,MAAM,OAAO,IAAI;AACtB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAkB;AACvB,UAAM,QAAQ,gBAAiB;AAC/B,UAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,UAAM,QAAQ,YAAY;AAE1BA,kBAAA,MAAA,MAAA,OAAA,+BAAY,MAAM,KAAK,OAAO;AAC9BA,wBAAA,MAAA,OAAA,+BAAY,YAAY,MAAM,MAAM,EAAE;AAAA,EACvC;AACH;AAvCE,cADW,oBACJ,SAAQ,oBAAI,IAAK;;;"}