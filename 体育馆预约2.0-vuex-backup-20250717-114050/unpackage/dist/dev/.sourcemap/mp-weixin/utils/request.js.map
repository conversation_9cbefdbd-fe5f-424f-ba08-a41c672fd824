{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["import { getToken, removeToken, removeUserInfo } from './auth.js'\nimport { showLoading, hideLoading, showToast } from './ui.js'\nimport config from '@/config/index.js'\nimport { guestPages } from './router-guard.js'\n\n// 简单的请求缓存管理器\nconst cacheManager = {\n  cache: new Map(),\n  \n  // 生成缓存键\n  generateKey(options) {\n    const { url, method = 'GET', data = {} } = options\n    return `${method}:${url}:${JSON.stringify(data)}`\n  },\n  \n  // 获取缓存\n  get(options) {\n    if (!config.cache || options.cache === false) return null\n    \n    // 只缓存GET请求\n    if (options.method && options.method !== 'GET') return null\n    \n    const key = this.generateKey(options)\n    const cached = this.cache.get(key)\n    \n    if (!cached) return null\n    \n    // 检查缓存是否过期\n    const now = Date.now()\n    if (cached.expiry && now > cached.expiry) {\n      this.cache.delete(key)\n      return null\n    }\n    \n    console.log('使用缓存数据:', key)\n    return cached.data\n  },\n  \n  // 设置缓存\n  set(options, data, ttl = 60000) { // 默认缓存1分钟\n    if (!config.cache || options.cache === false) return\n    \n    // 只缓存GET请求\n    if (options.method && options.method !== 'GET') return\n    \n    const key = this.generateKey(options)\n    const expiry = ttl ? Date.now() + ttl : null\n    \n    this.cache.set(key, { data, expiry })\n    console.log('缓存数据:', key)\n  },\n  \n  // 清除缓存\n  clear() {\n    this.cache.clear()\n    console.log('清除所有缓存')\n  },\n  \n  // 清除指定URL的缓存\n  clearUrl(url) {\n    for (const key of this.cache.keys()) {\n      if (key.includes(url)) {\n        this.cache.delete(key)\n        console.log('清除缓存:', key)\n      }\n    }\n  }\n}\n\n// 请求拦截器\nfunction requestInterceptor(options) {\n  // 添加基础URL\n  if (!options.url.startsWith('http')) {\n    options.url = config.baseURL + options.url\n  }\n  \n  // 添加认证头\n  const token = getToken()\n  if (token) {\n    options.header = {\n      ...options.header,\n      'Authorization': `Bearer ${token}`\n    }\n  }\n  \n  // 设置默认请求头\n  options.header = {\n    'Content-Type': 'application/json',\n    ...options.header\n  }\n  \n  // 设置超时时间\n  options.timeout = options.timeout || config.timeout\n  \n  return options\n}\n\n// 判断是否应该重试请求\nfunction shouldRetry(error) {\n  // 网络错误、超时错误应该重试\n  if (!error.statusCode || error.statusCode === 0) {\n    return true\n  }\n  \n  // 服务器错误（5xx）应该重试\n  if (error.statusCode >= 500 && error.statusCode < 600) {\n    return true\n  }\n  \n  // 429 Too Many Requests 应该重试\n  if (error.statusCode === 429) {\n    return true\n  }\n  \n  // 其他错误不重试\n  return false\n}\n\n// 响应拦截器\nfunction responseInterceptor(response, options) {\n  const { data, statusCode } = response\n\n  // 添加调试日志（仅对拼场订单详情接口）\n  if (options.url && options.url.includes('/sharing-orders/')) {\n    console.log('[Request] 拼场订单API响应:', {\n      url: options.url,\n      statusCode,\n      data,\n      hasCodeField: data.hasOwnProperty('code'),\n      dataKeys: data ? Object.keys(data) : []\n    })\n  }\n\n  // HTTP状态码检查\n  if (statusCode >= 200 && statusCode < 300) {\n    // 业务状态码检查\n    if (data.code === 200 || data.success === true || !data.hasOwnProperty('code')) {\n      // 成功响应：明确成功标识 或 没有code字段（如JWT响应）\n      return data\n    } else {\n      // 业务错误\n      const error = new Error(data.message || '请求失败')\n      error.code = data.code\n      error.statusCode = statusCode\n      throw error\n    }\n  } else if (statusCode === 401) {\n    // 未授权，根据具体错误信息区分是登录过期还是账号密码错误\n    const errorMessage = data && data.message ? data.message : '认证失败'\n    \n    // 如果是账号密码错误，不清除token，不跳转\n    if (errorMessage.includes('用户名或密码错误') || \n        errorMessage.includes('账号或密码错误') || \n        errorMessage.includes('密码错误') || \n        errorMessage.includes('用户不存在') ||\n        errorMessage.includes('Invalid credentials') ||\n        errorMessage.includes('Bad credentials')) {\n      throw new Error(errorMessage)\n    } else {\n      // 其他401错误认为是登录过期\n      removeToken()\n      removeUserInfo() // 同时清除用户信息\n      \n      // 检查当前页面是否是游客页面\n      const pages = getCurrentPages()\n      const currentPage = pages.length > 0 ? '/' + pages[pages.length - 1].route : ''\n      const isGuestPage = guestPages.some(page => currentPage.includes(page))\n      \n      // 创建一个特殊的错误对象，表示登录过期\n      const error = new Error('登录已过期')\n      error.code = 'LOGIN_EXPIRED'\n      \n      // 如果是游客页面，只返回错误，不处理跳转\n      // 如果不是游客页面，由路由守卫处理跳转\n      throw error\n    }\n  } else if (statusCode === 403) {\n    throw new Error('权限不足')\n  } else if (statusCode === 409) {\n    // 冲突错误，显示后端返回的具体错误信息\n    const errorMessage = data && data.message ? data.message : '资源冲突'\n    throw new Error(errorMessage)\n  } else {\n    // 其他错误，尝试从响应中获取错误信息\n    const errorMessage = data && data.message ? data.message : `请求失败 (${statusCode})`\n    const error = new Error(errorMessage)\n\n    // 保留后端返回的完整数据，特别是needPayment、orderId等字段\n    if (data) {\n      Object.keys(data).forEach(key => {\n        if (key !== 'message') {\n          error[key] = data[key]\n        }\n      })\n    }\n\n    throw error\n  }\n}\n\n// 基础请求方法\nfunction request(options, retryCount = 0) {\n  return new Promise((resolve, reject) => {\n    // 请求拦截\n    options = requestInterceptor(options)\n    \n    // 检查缓存\n    const cachedData = cacheManager.get(options)\n    if (cachedData) {\n      // 使用缓存数据，不显示加载状态\n      resolve(cachedData)\n      return\n    }\n    \n    // 显示加载状态\n    if (options.loading !== false) {\n      showLoading(options.loadingText)\n    }\n    \n    uni.request({\n      ...options,\n      success: (response) => {\n        try {\n          const result = responseInterceptor(response, options)\n          \n          // 缓存成功的响应数据\n          if (result && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE' && options.method !== 'PATCH') {\n            cacheManager.set(options, result, options.cacheTTL)\n          }\n          \n          resolve(result)\n        } catch (error) {\n          // 检查是否需要重试\n          if (retryCount < config.retryTimes && shouldRetry(error)) {\n            // 隐藏当前加载状态\n            if (options.loading !== false) {\n              hideLoading()\n            }\n            \n            // 延迟后重试\n            setTimeout(() => {\n              request(options, retryCount + 1)\n                .then(resolve)\n                .catch(reject)\n            }, config.retryDelay)\n            return\n          }\n          \n          // 显示错误提示\n          if (options.showError !== false) {\n            showToast(error.message || '请求失败')\n          }\n          reject(error)\n        }\n      },\n      fail: (error) => {\n        console.error('请求失败:', error)\n        const errorMsg = '网络请求失败，请检查网络连接'\n        \n        // 检查是否需要重试\n        if (retryCount < config.retryTimes) {\n          // 隐藏当前加载状态\n          if (options.loading !== false) {\n            hideLoading()\n          }\n          \n          // 延迟后重试\n          setTimeout(() => {\n            request(options, retryCount + 1)\n              .then(resolve)\n              .catch(reject)\n          }, config.retryDelay)\n          return\n        }\n        \n        if (options.showError !== false) {\n          showToast(errorMsg)\n        }\n        reject(new Error(errorMsg))\n      },\n      complete: () => {\n        // 隐藏加载状态（仅在不重试的情况下）\n        if (options.loading !== false) {\n          hideLoading()\n        }\n      }\n    })\n  })\n}\n\n// GET请求\nexport function get(url, params = {}, options = {}) {\n  // 缓存控制选项\n  // options.cache: 是否使用缓存，默认跟随全局配置\n  // options.cacheTTL: 缓存有效期（毫秒），默认60000（1分钟）\n  return request({\n    url,\n    method: 'GET',\n    data: params,\n    ...options\n  })\n}\n\n// POST请求\nexport function post(url, data = {}, options = {}) {\n  return request({\n    url,\n    method: 'POST',\n    data,\n    ...options\n  })\n}\n\n// PUT请求\nexport function put(url, data = {}, options = {}) {\n  return request({\n    url,\n    method: 'PUT',\n    data,\n    ...options\n  })\n}\n\n// DELETE请求\nexport function del(url, params = {}, options = {}) {\n  return request({\n    url,\n    method: 'DELETE',\n    data: params,\n    ...options\n  })\n}\n\n// PATCH请求\nexport function patch(url, data = {}, options = {}) {\n  return request({\n    url,\n    method: 'PATCH',\n    data,\n    ...options\n  })\n}\n\n// 清除缓存\nexport function clearCache(url) {\n  if (url) {\n    cacheManager.clearUrl(url)\n  } else {\n    cacheManager.clear()\n  }\n}\n\n// 文件上传\nexport function upload(url, filePath, formData = {}, options = {}) {\n  return new Promise((resolve, reject) => {\n    const token = getToken()\n    \n    uni.uploadFile({\n      url: config.baseURL + url,\n      filePath,\n      name: 'file',\n      formData,\n      header: {\n        'Authorization': token ? `Bearer ${token}` : ''\n      },\n      success: (response) => {\n        try {\n          const data = JSON.parse(response.data)\n          if (data.code === 200 || data.success === true) {\n            resolve(data)\n          } else {\n            reject(new Error(data.message || '上传失败'))\n          }\n        } catch (error) {\n          reject(new Error('上传响应解析失败'))\n        }\n      },\n      fail: (error) => {\n        console.error('上传失败:', error)\n        reject(new Error('文件上传失败'))\n      }\n    })\n  })\n}\n\n// 将缓存管理器暴露到全局\nif (typeof window !== 'undefined') {\n  window.cacheManager = cacheManager\n}\n\nexport default request"], "names": ["uni", "config", "getToken", "removeToken", "removeUserInfo", "guestPages", "showLoading", "hideLoading", "showToast"], "mappings": ";;;;;;AAMA,MAAM,eAAe;AAAA,EACnB,OAAO,oBAAI,IAAK;AAAA;AAAA,EAGhB,YAAY,SAAS;AACnB,UAAM,EAAE,KAAK,SAAS,OAAO,OAAO,CAAE,EAAA,IAAK;AAC3C,WAAO,GAAG,MAAM,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,CAAC;AAAA,EAChD;AAAA;AAAA,EAGD,IAAI,SAAS;AACX,QAAqB,QAAQ,UAAU;AAAO,aAAO;AAGrD,QAAI,QAAQ,UAAU,QAAQ,WAAW;AAAO,aAAO;AAEvD,UAAM,MAAM,KAAK,YAAY,OAAO;AACpC,UAAM,SAAS,KAAK,MAAM,IAAI,GAAG;AAEjC,QAAI,CAAC;AAAQ,aAAO;AAGpB,UAAM,MAAM,KAAK,IAAK;AACtB,QAAI,OAAO,UAAU,MAAM,OAAO,QAAQ;AACxC,WAAK,MAAM,OAAO,GAAG;AACrB,aAAO;AAAA,IACR;AAEDA,kBAAAA,6CAAY,WAAW,GAAG;AAC1B,WAAO,OAAO;AAAA,EACf;AAAA;AAAA,EAGD,IAAI,SAAS,MAAM,MAAM,KAAO;AAC9B,QAAqB,QAAQ,UAAU;AAAO;AAG9C,QAAI,QAAQ,UAAU,QAAQ,WAAW;AAAO;AAEhD,UAAM,MAAM,KAAK,YAAY,OAAO;AACpC,UAAM,SAAS,MAAM,KAAK,IAAK,IAAG,MAAM;AAExC,SAAK,MAAM,IAAI,KAAK,EAAE,MAAM,QAAQ;AACpCA,kBAAAA,MAAY,MAAA,OAAA,0BAAA,SAAS,GAAG;AAAA,EACzB;AAAA;AAAA,EAGD,QAAQ;AACN,SAAK,MAAM,MAAO;AAClBA,kBAAAA,MAAA,MAAA,OAAA,0BAAY,QAAQ;AAAA,EACrB;AAAA;AAAA,EAGD,SAAS,KAAK;AACZ,eAAW,OAAO,KAAK,MAAM,KAAI,GAAI;AACnC,UAAI,IAAI,SAAS,GAAG,GAAG;AACrB,aAAK,MAAM,OAAO,GAAG;AACrBA,sBAAAA,6CAAY,SAAS,GAAG;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACH;AAGA,SAAS,mBAAmB,SAAS;AAEnC,MAAI,CAAC,QAAQ,IAAI,WAAW,MAAM,GAAG;AACnC,YAAQ,MAAMC,aAAAA,OAAO,UAAU,QAAQ;AAAA,EACxC;AAGD,QAAM,QAAQC,WAAAA,SAAU;AACxB,MAAI,OAAO;AACT,YAAQ,SAAS;AAAA,MACf,GAAG,QAAQ;AAAA,MACX,iBAAiB,UAAU,KAAK;AAAA,IACjC;AAAA,EACF;AAGD,UAAQ,SAAS;AAAA,IACf,gBAAgB;AAAA,IAChB,GAAG,QAAQ;AAAA,EACZ;AAGD,UAAQ,UAAU,QAAQ,WAAWD,aAAM,OAAC;AAE5C,SAAO;AACT;AAGA,SAAS,YAAY,OAAO;AAE1B,MAAI,CAAC,MAAM,cAAc,MAAM,eAAe,GAAG;AAC/C,WAAO;AAAA,EACR;AAGD,MAAI,MAAM,cAAc,OAAO,MAAM,aAAa,KAAK;AACrD,WAAO;AAAA,EACR;AAGD,MAAI,MAAM,eAAe,KAAK;AAC5B,WAAO;AAAA,EACR;AAGD,SAAO;AACT;AAGA,SAAS,oBAAoB,UAAU,SAAS;AAC9C,QAAM,EAAE,MAAM,WAAU,IAAK;AAG7B,MAAI,QAAQ,OAAO,QAAQ,IAAI,SAAS,kBAAkB,GAAG;AAC3DD,kBAAAA,MAAA,MAAA,OAAA,2BAAY,wBAAwB;AAAA,MAClC,KAAK,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA,cAAc,KAAK,eAAe,MAAM;AAAA,MACxC,UAAU,OAAO,OAAO,KAAK,IAAI,IAAI,CAAE;AAAA,IAC7C,CAAK;AAAA,EACF;AAGD,MAAI,cAAc,OAAO,aAAa,KAAK;AAEzC,QAAI,KAAK,SAAS,OAAO,KAAK,YAAY,QAAQ,CAAC,KAAK,eAAe,MAAM,GAAG;AAE9E,aAAO;AAAA,IACb,OAAW;AAEL,YAAM,QAAQ,IAAI,MAAM,KAAK,WAAW,MAAM;AAC9C,YAAM,OAAO,KAAK;AAClB,YAAM,aAAa;AACnB,YAAM;AAAA,IACP;AAAA,EACL,WAAa,eAAe,KAAK;AAE7B,UAAM,eAAe,QAAQ,KAAK,UAAU,KAAK,UAAU;AAG3D,QAAI,aAAa,SAAS,UAAU,KAChC,aAAa,SAAS,SAAS,KAC/B,aAAa,SAAS,MAAM,KAC5B,aAAa,SAAS,OAAO,KAC7B,aAAa,SAAS,qBAAqB,KAC3C,aAAa,SAAS,iBAAiB,GAAG;AAC5C,YAAM,IAAI,MAAM,YAAY;AAAA,IAClC,OAAW;AAELG,6BAAa;AACbC,gCAAgB;AAGhB,YAAM,QAAQ,gBAAiB;AAC/B,YAAM,cAAc,MAAM,SAAS,IAAI,MAAM,MAAM,MAAM,SAAS,CAAC,EAAE,QAAQ;AACzDC,wBAAU,WAAC,KAAK,UAAQ,YAAY,SAAS,IAAI,CAAC;AAGtE,YAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,YAAM,OAAO;AAIb,YAAM;AAAA,IACP;AAAA,EACL,WAAa,eAAe,KAAK;AAC7B,UAAM,IAAI,MAAM,MAAM;AAAA,EAC1B,WAAa,eAAe,KAAK;AAE7B,UAAM,eAAe,QAAQ,KAAK,UAAU,KAAK,UAAU;AAC3D,UAAM,IAAI,MAAM,YAAY;AAAA,EAChC,OAAS;AAEL,UAAM,eAAe,QAAQ,KAAK,UAAU,KAAK,UAAU,SAAS,UAAU;AAC9E,UAAM,QAAQ,IAAI,MAAM,YAAY;AAGpC,QAAI,MAAM;AACR,aAAO,KAAK,IAAI,EAAE,QAAQ,SAAO;AAC/B,YAAI,QAAQ,WAAW;AACrB,gBAAM,GAAG,IAAI,KAAK,GAAG;AAAA,QACtB;AAAA,MACT,CAAO;AAAA,IACF;AAED,UAAM;AAAA,EACP;AACH;AAGA,SAAS,QAAQ,SAAS,aAAa,GAAG;AACxC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,cAAU,mBAAmB,OAAO;AAGpC,UAAM,aAAa,aAAa,IAAI,OAAO;AAC3C,QAAI,YAAY;AAEd,cAAQ,UAAU;AAClB;AAAA,IACD;AAGD,QAAI,QAAQ,YAAY,OAAO;AAC7BC,eAAW,YAAC,QAAQ,WAAW;AAAA,IAChC;AAEDN,kBAAAA,MAAI,QAAQ;AAAA,MACV,GAAG;AAAA,MACH,SAAS,CAAC,aAAa;AACrB,YAAI;AACF,gBAAM,SAAS,oBAAoB,UAAU,OAAO;AAGpD,cAAI,UAAU,QAAQ,WAAW,UAAU,QAAQ,WAAW,SAAS,QAAQ,WAAW,YAAY,QAAQ,WAAW,SAAS;AAChI,yBAAa,IAAI,SAAS,QAAQ,QAAQ,QAAQ;AAAA,UACnD;AAED,kBAAQ,MAAM;AAAA,QACf,SAAQ,OAAO;AAEd,cAAI,aAAaC,aAAM,OAAC,cAAc,YAAY,KAAK,GAAG;AAExD,gBAAI,QAAQ,YAAY,OAAO;AAC7BM,mCAAa;AAAA,YACd;AAGD,uBAAW,MAAM;AACf,sBAAQ,SAAS,aAAa,CAAC,EAC5B,KAAK,OAAO,EACZ,MAAM,MAAM;AAAA,YAC7B,GAAeN,aAAAA,OAAO,UAAU;AACpB;AAAA,UACD;AAGD,cAAI,QAAQ,cAAc,OAAO;AAC/BO,+BAAU,MAAM,WAAW,MAAM;AAAA,UAClC;AACD,iBAAO,KAAK;AAAA,QACb;AAAA,MACF;AAAA,MACD,MAAM,CAAC,UAAU;AACfR,sBAAAA,MAAc,MAAA,SAAA,2BAAA,SAAS,KAAK;AAC5B,cAAM,WAAW;AAGjB,YAAI,aAAaC,aAAM,OAAC,YAAY;AAElC,cAAI,QAAQ,YAAY,OAAO;AAC7BM,iCAAa;AAAA,UACd;AAGD,qBAAW,MAAM;AACf,oBAAQ,SAAS,aAAa,CAAC,EAC5B,KAAK,OAAO,EACZ,MAAM,MAAM;AAAA,UAC3B,GAAaN,aAAAA,OAAO,UAAU;AACpB;AAAA,QACD;AAED,YAAI,QAAQ,cAAc,OAAO;AAC/BO,mBAAAA,UAAU,QAAQ;AAAA,QACnB;AACD,eAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,MAC3B;AAAA,MACD,UAAU,MAAM;AAEd,YAAI,QAAQ,YAAY,OAAO;AAC7BD,+BAAa;AAAA,QACd;AAAA,MACF;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAGO,SAAS,IAAI,KAAK,SAAS,CAAA,GAAI,UAAU,CAAA,GAAI;AAIlD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACP,CAAG;AACH;AAGO,SAAS,KAAK,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,GAAI;AACjD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACP,CAAG;AACH;AAGO,SAAS,IAAI,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,GAAI;AAChD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACP,CAAG;AACH;AAuBO,SAAS,WAAW,KAAK;AAC9B,MAAI,KAAK;AACP,iBAAa,SAAS,GAAG;AAAA,EAC7B,OAAS;AACL,iBAAa,MAAO;AAAA,EACrB;AACH;AAoCA,IAAI,OAAO,WAAW,aAAa;AACjC,SAAO,eAAe;AACxB;;;;;;"}