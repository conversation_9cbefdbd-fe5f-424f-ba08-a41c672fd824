{"version": 3, "file": "auth.js", "sources": ["utils/auth.js"], "sourcesContent": ["const TOKEN_KEY = 'access_token'\nconst USER_INFO_KEY = 'user_info'\n\n// 获取token\nexport function getToken() {\n  return uni.getStorageSync(TOKEN_KEY)\n}\n\n// 设置token\nexport function setToken(token) {\n  return uni.setStorageSync(TOKEN_KEY, token)\n}\n\n// 移除token\nexport function removeToken() {\n  return uni.removeStorageSync(TOKEN_KEY)\n}\n\n// 获取用户信息\nexport function getUserInfo() {\n  const userInfo = uni.getStorageSync(USER_INFO_KEY)\n  return userInfo ? JSON.parse(userInfo) : null\n}\n\n// 设置用户信息\nexport function setUserInfo(userInfo) {\n  return uni.setStorageSync(USER_INFO_KEY, JSON.stringify(userInfo))\n}\n\n// 移除用户信息\nexport function removeUserInfo() {\n  return uni.removeStorageSync(USER_INFO_KEY)\n}\n\n// 检查是否已登录\nexport function isLoggedIn() {\n  return !!getToken()\n}\n\n// 清除所有认证信息\nexport function clearAuth() {\n  removeToken()\n  removeUserInfo()\n}"], "names": ["uni"], "mappings": ";;AAAA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AAGf,SAAS,WAAW;AACzB,SAAOA,cAAG,MAAC,eAAe,SAAS;AACrC;AAGO,SAAS,SAAS,OAAO;AAC9B,SAAOA,oBAAI,eAAe,WAAW,KAAK;AAC5C;AAGO,SAAS,cAAc;AAC5B,SAAOA,cAAG,MAAC,kBAAkB,SAAS;AACxC;AAGO,SAAS,cAAc;AAC5B,QAAM,WAAWA,cAAAA,MAAI,eAAe,aAAa;AACjD,SAAO,WAAW,KAAK,MAAM,QAAQ,IAAI;AAC3C;AAGO,SAAS,YAAY,UAAU;AACpC,SAAOA,cAAAA,MAAI,eAAe,eAAe,KAAK,UAAU,QAAQ,CAAC;AACnE;AAGO,SAAS,iBAAiB;AAC/B,SAAOA,cAAG,MAAC,kBAAkB,aAAa;AAC5C;AAGO,SAAS,aAAa;AAC3B,SAAO,CAAC,CAAC,SAAU;AACrB;AAGO,SAAS,YAAY;AAC1B,cAAa;AACb,iBAAgB;AAClB;;;;;;;;;"}