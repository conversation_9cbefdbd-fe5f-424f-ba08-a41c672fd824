{"version": 3, "file": "helpers.js", "sources": ["utils/helpers.js"], "sourcesContent": ["// 日期格式化\nexport function formatDate(date, format = 'YYYY-MM-DD') {\n  if (!date) return '--'\n\n  try {\n    // 处理iOS兼容性问题：将\"YYYY-MM-DD HH:mm:ss\"格式转换为\"YYYY-MM-DDTHH:mm:ss\"\n    let dateString = date\n    if (typeof date === 'string') {\n      // 如果是\"2025-06-28 14:54:59\"格式，转换为\"2025-06-28T14:54:59\"\n      dateString = date.replace(/^(\\d{4}-\\d{2}-\\d{2})\\s(\\d{2}:\\d{2}:\\d{2})$/, '$1T$2')\n      // 如果是\"2025-06-28 14:54\"格式，转换为\"2025-06-28T14:54\"\n      dateString = dateString.replace(/^(\\d{4}-\\d{2}-\\d{2})\\s(\\d{2}:\\d{2})$/, '$1T$2')\n    }\n\n    const d = new Date(dateString)\n    if (isNaN(d.getTime())) return '--'\n\n    const year = d.getFullYear()\n    const month = String(d.getMonth() + 1).padStart(2, '0')\n    const day = String(d.getDate()).padStart(2, '0')\n    const hour = String(d.getHours()).padStart(2, '0')\n    const minute = String(d.getMinutes()).padStart(2, '0')\n    const second = String(d.getSeconds()).padStart(2, '0')\n    const dayOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][d.getDay()]\n\n    return format\n      .replace('YYYY', year)\n      .replace('MM', month)\n      .replace('DD', day)\n      .replace('HH', hour)\n      .replace('mm', minute)\n      .replace('ss', second)\n      .replace('dddd', dayOfWeek)\n  } catch (error) {\n    console.error('日期格式化错误:', error)\n    return '--'\n  }\n}\n\n// 格式化日期时间\nexport function formatDateTime(datetime, format = 'YYYY-MM-DD HH:mm') {\n  if (!datetime) return '--'\n  \n  try {\n    // 处理iOS兼容性问题：将\"YYYY-MM-DD HH:mm:ss\"格式转换为\"YYYY-MM-DDTHH:mm:ss\"\n    let dateString = datetime\n    if (typeof datetime === 'string') {\n      // 如果是\"2025-06-28 14:54:59\"格式，转换为\"2025-06-28T14:54:59\"\n      dateString = datetime.replace(/^(\\d{4}-\\d{2}-\\d{2})\\s(\\d{2}:\\d{2}:\\d{2})$/, '$1T$2')\n      // 如果是\"2025-06-28 14:54\"格式，转换为\"2025-06-28T14:54\"\n      dateString = dateString.replace(/^(\\d{4}-\\d{2}-\\d{2})\\s(\\d{2}:\\d{2})$/, '$1T$2')\n    }\n    \n    const date = new Date(dateString)\n    if (isNaN(date.getTime())) return '--'\n    \n    const year = date.getFullYear()\n    const month = String(date.getMonth() + 1).padStart(2, '0')\n    const day = String(date.getDate()).padStart(2, '0')\n    const hour = String(date.getHours()).padStart(2, '0')\n    const minute = String(date.getMinutes()).padStart(2, '0')\n    const second = String(date.getSeconds()).padStart(2, '0')\n    \n    // 如果没有指定格式，默认返回 YYYY-MM-DD HH:mm 格式\n    if (format === 'YYYY-MM-DD HH:mm') {\n      return `${year}-${month}-${day} ${hour}:${minute}`\n    }\n    \n    return format\n      .replace('YYYY', year)\n      .replace('MM', month)\n      .replace('DD', day)\n      .replace('HH', hour)\n      .replace('mm', minute)\n      .replace('ss', second)\n  } catch (error) {\n    console.error('时间格式化错误:', error)\n    return '--'\n  }\n}\n\n// 价格格式化\nexport function formatPrice(price) {\n  return `¥${Number(price).toFixed(2)}`\n}\n\n// 手机号脱敏\nexport function maskPhone(phone) {\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2')\n}\n\n// 防抖函数\nexport function debounce(func, wait) {\n  let timeout\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout)\n      func(...args)\n    }\n    clearTimeout(timeout)\n    timeout = setTimeout(later, wait)\n  }\n}\n\n// 节流函数\nexport function throttle(func, limit) {\n  let inThrottle\n  return function() {\n    const args = arguments\n    const context = this\n    if (!inThrottle) {\n      func.apply(context, args)\n      inThrottle = true\n      setTimeout(() => inThrottle = false, limit)\n    }\n  }\n}\n\n/**\n * 安全的日期解析函数，处理iOS兼容性问题\n * @param {string|Date} dateInput - 日期输入\n * @returns {Date|null} - 解析后的Date对象，失败返回null\n */\nexport function safeDateParse(dateInput) {\n  if (!dateInput) return null\n\n  if (dateInput instanceof Date) {\n    return isNaN(dateInput.getTime()) ? null : dateInput\n  }\n\n  try {\n    // 处理iOS兼容性问题：将\"YYYY-MM-DD HH:mm:ss\"格式转换为\"YYYY-MM-DDTHH:mm:ss\"\n    let dateString = dateInput\n    if (typeof dateInput === 'string') {\n      // 如果是\"2025-06-28 14:54:59\"格式，转换为\"2025-06-28T14:54:59\"\n      dateString = dateInput.replace(/^(\\d{4}-\\d{2}-\\d{2})\\s(\\d{2}:\\d{2}:\\d{2})$/, '$1T$2')\n      // 如果是\"2025-06-28 14:54\"格式，转换为\"2025-06-28T14:54\"\n      dateString = dateString.replace(/^(\\d{4}-\\d{2}-\\d{2})\\s(\\d{2}:\\d{2})$/, '$1T$2')\n    }\n\n    const date = new Date(dateString)\n    return isNaN(date.getTime()) ? null : date\n  } catch (error) {\n    console.error('日期解析错误:', error, '输入:', dateInput)\n    return null\n  }\n}\n\n/**\n * 通用的时间格式化函数\n * @param {string|Date} dateInput - 日期输入\n * @param {string} format - 格式字符串，默认'YYYY-MM-DD HH:mm'\n * @returns {string} - 格式化后的时间字符串\n */\nexport function formatTime(dateInput, format = 'YYYY-MM-DD HH:mm') {\n  const date = safeDateParse(dateInput)\n  if (!date) return '--'\n\n  const year = date.getFullYear()\n  const month = String(date.getMonth() + 1).padStart(2, '0')\n  const day = String(date.getDate()).padStart(2, '0')\n  const hour = String(date.getHours()).padStart(2, '0')\n  const minute = String(date.getMinutes()).padStart(2, '0')\n  const second = String(date.getSeconds()).padStart(2, '0')\n  const dayOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][date.getDay()]\n\n  return format\n    .replace('YYYY', year)\n    .replace('MM', month)\n    .replace('DD', day)\n    .replace('HH', hour)\n    .replace('mm', minute)\n    .replace('ss', second)\n    .replace('dddd', dayOfWeek)\n}\n\n// 生成唯一ID\nexport function generateId() {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n\n// 深拷贝\nexport function deepClone(obj) {\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n  \n  if (obj instanceof Date) {\n    return new Date(obj.getTime())\n  }\n  \n  if (obj instanceof Array) {\n    return obj.map(item => deepClone(item))\n  }\n  \n  if (typeof obj === 'object') {\n    const clonedObj = {}\n    for (let key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n}\n\n// 验证手机号\nexport function validatePhone(phone) {\n  const phoneRegex = /^1[3-9]\\d{9}$/\n  return phoneRegex.test(phone)\n}\n\n// 验证邮箱\nexport function validateEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// 获取文件扩展名\nexport function getFileExtension(filename) {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// 格式化文件大小\nexport function formatFileSize(bytes) {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}"], "names": ["uni"], "mappings": ";;AACO,SAAS,WAAW,MAAM,SAAS,cAAc;AACtD,MAAI,CAAC;AAAM,WAAO;AAElB,MAAI;AAEF,QAAI,aAAa;AACjB,QAAI,OAAO,SAAS,UAAU;AAE5B,mBAAa,KAAK,QAAQ,8CAA8C,OAAO;AAE/E,mBAAa,WAAW,QAAQ,wCAAwC,OAAO;AAAA,IAChF;AAED,UAAM,IAAI,IAAI,KAAK,UAAU;AAC7B,QAAI,MAAM,EAAE,QAAS,CAAA;AAAG,aAAO;AAE/B,UAAM,OAAO,EAAE,YAAa;AAC5B,UAAM,QAAQ,OAAO,EAAE,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACtD,UAAM,MAAM,OAAO,EAAE,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAC/C,UAAM,OAAO,OAAO,EAAE,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACjD,UAAM,SAAS,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,UAAM,SAAS,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,UAAM,YAAY,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,EAAE,EAAE,OAAM,CAAE;AAE9E,WAAO,OACJ,QAAQ,QAAQ,IAAI,EACpB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,GAAG,EACjB,QAAQ,MAAM,IAAI,EAClB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,QAAQ,SAAS;AAAA,EAC7B,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,0BAAc,YAAY,KAAK;AAC/B,WAAO;AAAA,EACR;AACH;AAGO,SAAS,eAAe,UAAU,SAAS,oBAAoB;AACpE,MAAI,CAAC;AAAU,WAAO;AAEtB,MAAI;AAEF,QAAI,aAAa;AACjB,QAAI,OAAO,aAAa,UAAU;AAEhC,mBAAa,SAAS,QAAQ,8CAA8C,OAAO;AAEnF,mBAAa,WAAW,QAAQ,wCAAwC,OAAO;AAAA,IAChF;AAED,UAAM,OAAO,IAAI,KAAK,UAAU;AAChC,QAAI,MAAM,KAAK,QAAS,CAAA;AAAG,aAAO;AAElC,UAAM,OAAO,KAAK,YAAa;AAC/B,UAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,UAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,UAAM,OAAO,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACpD,UAAM,SAAS,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACxD,UAAM,SAAS,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAGxD,QAAI,WAAW,oBAAoB;AACjC,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAAA,IACjD;AAED,WAAO,OACJ,QAAQ,QAAQ,IAAI,EACpB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,GAAG,EACjB,QAAQ,MAAM,IAAI,EAClB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM;AAAA,EACxB,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,0BAAc,YAAY,KAAK;AAC/B,WAAO;AAAA,EACR;AACH;AAaO,SAAS,SAAS,MAAM,MAAM;AACnC,MAAI;AACJ,SAAO,SAAS,oBAAoB,MAAM;AACxC,UAAM,QAAQ,MAAM;AAClB,mBAAa,OAAO;AACpB,WAAK,GAAG,IAAI;AAAA,IACb;AACD,iBAAa,OAAO;AACpB,cAAU,WAAW,OAAO,IAAI;AAAA,EACjC;AACH;AAqBO,SAAS,cAAc,WAAW;AACvC,MAAI,CAAC;AAAW,WAAO;AAEvB,MAAI,qBAAqB,MAAM;AAC7B,WAAO,MAAM,UAAU,QAAO,CAAE,IAAI,OAAO;AAAA,EAC5C;AAED,MAAI;AAEF,QAAI,aAAa;AACjB,QAAI,OAAO,cAAc,UAAU;AAEjC,mBAAa,UAAU,QAAQ,8CAA8C,OAAO;AAEpF,mBAAa,WAAW,QAAQ,wCAAwC,OAAO;AAAA,IAChF;AAED,UAAM,OAAO,IAAI,KAAK,UAAU;AAChC,WAAO,MAAM,KAAK,QAAO,CAAE,IAAI,OAAO;AAAA,EACvC,SAAQ,OAAO;AACdA,kBAAc,MAAA,MAAA,SAAA,2BAAA,WAAW,OAAO,OAAO,SAAS;AAChD,WAAO;AAAA,EACR;AACH;AAQO,SAAS,WAAW,WAAW,SAAS,oBAAoB;AACjE,QAAM,OAAO,cAAc,SAAS;AACpC,MAAI,CAAC;AAAM,WAAO;AAElB,QAAM,OAAO,KAAK,YAAa;AAC/B,QAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,QAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,QAAM,OAAO,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACpD,QAAM,SAAS,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACxD,QAAM,SAAS,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACxD,QAAM,YAAY,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,EAAE,KAAK,OAAM,CAAE;AAEjF,SAAO,OACJ,QAAQ,QAAQ,IAAI,EACpB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,GAAG,EACjB,QAAQ,MAAM,IAAI,EAClB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,QAAQ,SAAS;AAC9B;;;;;"}