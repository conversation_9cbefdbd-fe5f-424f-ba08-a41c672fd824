{"version": 3, "file": "simple-test.js", "sources": ["pages/test/simple-test.vue", "pages/test/simple-test.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">用户预约测试页面</text>\n    </view>\n\n    <view class=\"content\">\n      <view class=\"auth-section\">\n        <text class=\"section-title\">认证状态</text>\n        <text class=\"status\">Token: {{ token ? '已设置' : '未设置' }}</text>\n        <text class=\"status\">用户: {{ userInfo ? userInfo.username : '未登录' }}</text>\n        <button class=\"test-btn\" @click=\"setAuth\">设置测试认证</button>\n      </view>\n\n      <view class=\"api-section\">\n        <text class=\"section-title\">API测试</text>\n        <button class=\"test-btn\" @click=\"testGetBookings\">获取用户预约列表</button>\n        <text class=\"status\">状态: {{ apiStatus }}</text>\n      </view>\n\n      <view class=\"result-section\">\n        <text class=\"section-title\">结果</text>\n        <text class=\"result\">{{ result }}</text>\n      </view>\n\n      <button class=\"test-btn\" @click=\"goBack\">\n        返回首页\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getToken, getUserInfo, setToken, setUserInfo } from '@/utils/auth.js'\nimport * as userApi from '@/api/user.js'\n\nexport default {\n  name: 'SimpleTest',\n\n  data() {\n    return {\n      token: '',\n      userInfo: null,\n      apiStatus: '待测试',\n      result: '暂无结果'\n    }\n  },\n\n  onLoad() {\n    this.refreshAuth()\n  },\n\n  methods: {\n    refreshAuth() {\n      this.token = getToken()\n      this.userInfo = getUserInfo()\n    },\n\n    setAuth() {\n      const realToken = 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMzgwMDAwMDAwMSIsImlhdCI6MTc1MjQwMTgyMSwiZXhwIjoxNzUyNDg4MjIxfQ.vFAd19NFzyYyxS2cRXy3dCq_Va_dguz01QSX2lwN_c0'\n      const realUserInfo = {\n        id: 33,\n        username: '13800000001',\n        phone: '13800000001',\n        nickname: '测试用户2'\n      }\n\n      setToken(realToken)\n      setUserInfo(realUserInfo)\n      this.refreshAuth()\n\n      uni.showToast({\n        title: '认证信息已设置',\n        icon: 'success'\n      })\n    },\n\n    async testGetBookings() {\n      try {\n        this.apiStatus = '请求中...'\n        const response = await userApi.getUserBookings({ page: 1, pageSize: 10 })\n        this.apiStatus = '请求成功'\n\n        // 详细分析返回的数据\n        let analysis = `总记录数: ${response.total}\\n`\n        analysis += `数据数组长度: ${response.data ? response.data.length : 0}\\n\\n`\n\n        if (response.data && response.data.length > 0) {\n          response.data.forEach((item, index) => {\n            analysis += `=== 订单 ${index + 1} ===\\n`\n            analysis += `ID: ${item.id}\\n`\n            analysis += `订单号: ${item.orderNo}\\n`\n            analysis += `状态: ${item.status}\\n`\n            analysis += `类型: ${item.bookingType}\\n`\n            analysis += `场馆: ${item.venueName}\\n`\n            analysis += `价格: ${item.totalPrice}\\n`\n            analysis += `预约日期: ${item.bookingDate}\\n`\n            analysis += `开始时间: ${item.startTime}\\n`\n            analysis += `结束时间: ${item.endTime}\\n`\n            analysis += `创建时间: ${item.createdAt}\\n\\n`\n          })\n        } else {\n          analysis += '没有找到任何订单数据\\n'\n        }\n\n        this.result = analysis\n\n        uni.showToast({\n          title: `获取到${response.total}条记录`,\n          icon: 'success'\n        })\n      } catch (error) {\n        this.apiStatus = '请求失败'\n        this.result = `错误: ${error.message}\\n堆栈: ${error.stack || '无'}`\n\n        uni.showToast({\n          title: '请求失败',\n          icon: 'error'\n        })\n      }\n    },\n\n    goBack() {\n      uni.navigateBack()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  padding: 40rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n  \n  .title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n  }\n}\n\n.content {\n  .auth-section, .api-section, .result-section {\n    background: white;\n    padding: 30rpx;\n    margin-bottom: 20rpx;\n    border-radius: 12rpx;\n\n    .section-title {\n      display: block;\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 20rpx;\n    }\n\n    .status {\n      display: block;\n      font-size: 28rpx;\n      color: #666;\n      margin-bottom: 10rpx;\n    }\n\n    .result {\n      display: block;\n      font-size: 24rpx;\n      color: #333;\n      background: #f8f8f8;\n      padding: 20rpx;\n      border-radius: 8rpx;\n      white-space: pre-wrap;\n      word-break: break-all;\n    }\n  }\n\n  .test-btn {\n    width: 100%;\n    height: 88rpx;\n    background-color: #007aff;\n    color: white;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 32rpx;\n    margin-bottom: 20rpx;\n\n    &:active {\n      background-color: #0056cc;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/simple-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getToken", "getUserInfo", "setToken", "setUserInfo", "uni", "userApi.getUserBookings"], "mappings": ";;;;AAoCA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA,IACP,cAAc;AACZ,WAAK,QAAQA,oBAAS;AACtB,WAAK,WAAWC,uBAAY;AAAA,IAC7B;AAAA,IAED,UAAU;AACR,YAAM,YAAY;AAClB,YAAM,eAAe;AAAA,QACnB,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAEAC,iBAAAA,SAAS,SAAS;AAClBC,iBAAAA,YAAY,YAAY;AACxB,WAAK,YAAY;AAEjBC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACF;AAAA,IAED,MAAM,kBAAkB;AACtB,UAAI;AACF,aAAK,YAAY;AACjB,cAAM,WAAW,MAAMC,yBAAwB,EAAE,MAAM,GAAG,UAAU,IAAI;AACxE,aAAK,YAAY;AAGjB,YAAI,WAAW,SAAS,SAAS,KAAK;AAAA;AACtC,oBAAY,WAAW,SAAS,OAAO,SAAS,KAAK,SAAS,CAAC;AAAA;AAAA;AAE/D,YAAI,SAAS,QAAQ,SAAS,KAAK,SAAS,GAAG;AAC7C,mBAAS,KAAK,QAAQ,CAAC,MAAM,UAAU;AACrC,wBAAY,UAAU,QAAQ,CAAC;AAAA;AAC/B,wBAAY,OAAO,KAAK,EAAE;AAAA;AAC1B,wBAAY,QAAQ,KAAK,OAAO;AAAA;AAChC,wBAAY,OAAO,KAAK,MAAM;AAAA;AAC9B,wBAAY,OAAO,KAAK,WAAW;AAAA;AACnC,wBAAY,OAAO,KAAK,SAAS;AAAA;AACjC,wBAAY,OAAO,KAAK,UAAU;AAAA;AAClC,wBAAY,SAAS,KAAK,WAAW;AAAA;AACrC,wBAAY,SAAS,KAAK,SAAS;AAAA;AACnC,wBAAY,SAAS,KAAK,OAAO;AAAA;AACjC,wBAAY,SAAS,KAAK,SAAS;AAAA;AAAA;AAAA,WACpC;AAAA,eACI;AACL,sBAAY;AAAA,QACd;AAEA,aAAK,SAAS;AAEdD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,SAAS,KAAK;AAAA,UAC3B,MAAM;AAAA,SACP;AAAA,MACD,SAAO,OAAO;AACd,aAAK,YAAY;AACjB,aAAK,SAAS,OAAO,MAAM,OAAO;AAAA,MAAS,MAAM,SAAS,GAAG;AAE7DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA,IAED,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IACnB;AAAA,EACF;AACF;;;;;;;;;;;;;AC7HA,GAAG,WAAW,eAAe;"}