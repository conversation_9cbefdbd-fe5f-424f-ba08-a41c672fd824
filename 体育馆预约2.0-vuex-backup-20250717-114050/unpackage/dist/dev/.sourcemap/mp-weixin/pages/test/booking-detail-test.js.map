{"version": 3, "file": "booking-detail-test.js", "sources": ["pages/test/booking-detail-test.vue", "pages/test/booking-detail-test.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"test-container\">\n    <view class=\"test-header\">\n      <text class=\"test-title\">订单详情API测试</text>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">测试订单ID: 238</text>\n      <button @click=\"testBookingDetail\" class=\"test-btn\">测试获取订单详情</button>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">清除缓存</text>\n      <button @click=\"clearBookingCache\" class=\"test-btn\">清除订单缓存</button>\n    </view>\n    \n    <view class=\"test-section\">\n      <text class=\"section-title\">测试结果</text>\n      <view class=\"result-container\">\n        <text class=\"result-text\">{{ testResult }}</text>\n      </view>\n    </view>\n    \n    <view class=\"test-section\" v-if=\"bookingData\">\n      <text class=\"section-title\">订单数据</text>\n      <view class=\"data-container\">\n        <text class=\"data-text\">{{ JSON.stringify(bookingData, null, 2) }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapActions, mapState } from 'vuex'\nimport { clearCache } from '@/utils/request.js'\n\nexport default {\n  name: 'BookingDetailTest',\n  data() {\n    return {\n      testResult: '等待测试...',\n      bookingData: null\n    }\n  },\n  computed: {\n    ...mapState('booking', ['bookingDetail', 'loading'])\n  },\n  methods: {\n    ...mapActions('booking', ['getBookingDetail']),\n    \n    async testBookingDetail() {\n      try {\n        this.testResult = '开始测试...'\n        this.bookingData = null\n        \n        console.log('🧪 开始测试订单详情API')\n        \n        // 清除缓存\n        clearCache('/bookings/238')\n        console.log('🗑️ 已清除缓存')\n        \n        // 调用API\n        const response = await this.getBookingDetail(238)\n        console.log('🧪 测试响应:', response)\n        \n        // 检查store中的数据\n        console.log('🧪 Store中的数据:', this.bookingDetail)\n        \n        this.bookingData = this.bookingDetail\n        \n        if (this.bookingDetail && this.bookingDetail.orderNo) {\n          this.testResult = '✅ 测试成功！获取到有效的订单数据'\n        } else {\n          this.testResult = '❌ 测试失败：获取到的数据无效'\n        }\n        \n      } catch (error) {\n        console.error('🧪 测试失败:', error)\n        this.testResult = `❌ 测试失败: ${error.message}`\n      }\n    },\n    \n    clearBookingCache() {\n      clearCache('/bookings/238')\n      this.testResult = '🗑️ 缓存已清除'\n      console.log('🗑️ 订单缓存已清除')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.test-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.test-title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n\n.test-section {\n  background-color: white;\n  margin-bottom: 20px;\n  padding: 15px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.test-btn {\n  background-color: #007aff;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  font-size: 14px;\n}\n\n.result-container,\n.data-container {\n  background-color: #f8f8f8;\n  padding: 10px;\n  border-radius: 5px;\n  margin-top: 10px;\n}\n\n.result-text,\n.data-text {\n  font-size: 12px;\n  color: #666;\n  word-break: break-all;\n  white-space: pre-wrap;\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/booking-detail-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapState", "mapActions", "uni", "clearCache"], "mappings": ";;;AAoCA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,IACf;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,GAAGA,cAAQ,SAAC,WAAW,CAAC,iBAAiB,SAAS,CAAC;AAAA,EACpD;AAAA,EACD,SAAS;AAAA,IACP,GAAGC,yBAAW,WAAW,CAAC,kBAAkB,CAAC;AAAA,IAE7C,MAAM,oBAAoB;AACxB,UAAI;AACF,aAAK,aAAa;AAClB,aAAK,cAAc;AAEnBC,sBAAAA,MAAA,MAAA,OAAA,4CAAY,gBAAgB;AAG5BC,sBAAAA,WAAW,eAAe;AAC1BD,sBAAAA,MAAY,MAAA,OAAA,4CAAA,WAAW;AAGvB,cAAM,WAAW,MAAM,KAAK,iBAAiB,GAAG;AAChDA,sBAAAA,MAAA,MAAA,OAAA,4CAAY,YAAY,QAAQ;AAGhCA,sBAAY,MAAA,MAAA,OAAA,4CAAA,iBAAiB,KAAK,aAAa;AAE/C,aAAK,cAAc,KAAK;AAExB,YAAI,KAAK,iBAAiB,KAAK,cAAc,SAAS;AACpD,eAAK,aAAa;AAAA,eACb;AACL,eAAK,aAAa;AAAA,QACpB;AAAA,MAEA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,4CAAc,YAAY,KAAK;AAC/B,aAAK,aAAa,WAAW,MAAM,OAAO;AAAA,MAC5C;AAAA,IACD;AAAA,IAED,oBAAoB;AAClBC,oBAAAA,WAAW,eAAe;AAC1B,WAAK,aAAa;AAClBD,oBAAAA,MAAA,MAAA,OAAA,4CAAY,aAAa;AAAA,IAC3B;AAAA,EACF;AACF;;;;;;;;;;;;ACvFA,GAAG,WAAW,eAAe;"}