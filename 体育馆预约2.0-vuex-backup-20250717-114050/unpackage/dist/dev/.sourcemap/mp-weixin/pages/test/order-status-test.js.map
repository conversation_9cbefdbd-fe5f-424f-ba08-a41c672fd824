{"version": 3, "file": "order-status-test.js", "sources": ["pages/test/order-status-test.vue", "pages/test/order-status-test.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"test-container\">\n    <view class=\"header\">\n      <text class=\"title\">订单状态流程测试</text>\n      <text class=\"subtitle\">验证订单状态转换和业务规则</text>\n    </view>\n\n    <!-- 测试场景选择 -->\n    <view class=\"test-scenarios\">\n      <view class=\"section-title\">测试场景</view>\n      \n      <view class=\"scenario-list\">\n        <view \n          v-for=\"scenario in testScenarios\" \n          :key=\"scenario.id\"\n          class=\"scenario-item\"\n          :class=\"{ active: selectedScenario?.id === scenario.id }\"\n          @click=\"selectScenario(scenario)\"\n        >\n          <text class=\"scenario-name\">{{ scenario.name }}</text>\n          <text class=\"scenario-desc\">{{ scenario.description }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 当前测试场景 -->\n    <view v-if=\"selectedScenario\" class=\"current-test\">\n      <view class=\"section-title\">当前测试: {{ selectedScenario.name }}</view>\n      \n      <!-- 测试步骤 -->\n      <view class=\"test-steps\">\n        <view \n          v-for=\"(step, index) in selectedScenario.steps\" \n          :key=\"index\"\n          class=\"step-item\"\n          :class=\"{ \n            completed: step.status === 'completed',\n            active: step.status === 'active',\n            failed: step.status === 'failed'\n          }\"\n        >\n          <view class=\"step-header\">\n            <text class=\"step-number\">{{ index + 1 }}</text>\n            <text class=\"step-title\">{{ step.title }}</text>\n            <text class=\"step-status\">{{ getStatusText(step.status) }}</text>\n          </view>\n          <text class=\"step-desc\">{{ step.description }}</text>\n          \n          <button \n            v-if=\"step.status === 'active'\"\n            class=\"step-action-btn\"\n            @click=\"executeStep(step, index)\"\n          >\n            执行步骤\n          </button>\n        </view>\n      </view>\n\n      <!-- 当前订单状态 -->\n      <view v-if=\"currentOrderId\" class=\"current-order\">\n        <view class=\"section-title\">当前测试订单</view>\n        <view class=\"order-info\">\n          <text class=\"order-id\">订单ID: {{ currentOrderId }}</text>\n          <text class=\"order-status\">状态: {{ currentOrderStatus || '查询中...' }}</text>\n          <button class=\"refresh-btn\" @click=\"refreshOrderStatus\">刷新状态</button>\n        </view>\n      </view>\n\n      <!-- 测试结果 -->\n      <view class=\"test-results\">\n        <view class=\"section-title\">测试结果</view>\n        <view v-for=\"result in testResults\" :key=\"result.id\" class=\"result-item\">\n          <text class=\"result-time\">{{ result.timestamp }}</text>\n          <text class=\"result-action\">{{ result.action }}</text>\n          <text class=\"result-status\" :class=\"result.success ? 'success' : 'error'\">\n            {{ result.success ? '✅ 成功' : '❌ 失败' }}\n          </text>\n          <text class=\"result-message\">{{ result.message }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 状态流程图 -->\n    <view class=\"status-flow\">\n      <view class=\"section-title\">订单状态流程图</view>\n      <view class=\"flow-diagram\">\n        <text class=\"flow-text\">\n          普通订单: PENDING → PAID → CONFIRMED → VERIFIED → COMPLETED\n          拼场订单: PENDING → OPEN → SHARING_SUCCESS → CONFIRMED → VERIFIED → COMPLETED\n          取消流程: 任何状态 → CANCELLED\n          过期流程: PENDING/CONFIRMED → EXPIRED\n        </text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'OrderStatusTest',\n  data() {\n    return {\n      selectedScenario: null,\n      testResults: [],\n      testScenarios: [\n        {\n          id: 1,\n          name: '普通订单完整流程',\n          description: '测试独享订单从创建到完成的完整状态流程',\n          steps: [\n            {\n              title: '创建订单',\n              description: '创建一个独享订单，状态应为PENDING',\n              status: 'active',\n              action: 'createOrder',\n              params: { type: 'EXCLUSIVE' }\n            },\n            {\n              title: '支付订单',\n              description: '支付订单，状态应从PENDING转为PAID',\n              status: 'pending',\n              action: 'payOrder'\n            },\n            {\n              title: '确认订单',\n              description: '管理员确认订单，状态应从PAID转为CONFIRMED',\n              status: 'pending',\n              action: 'confirmOrder'\n            },\n            {\n              title: '核销订单',\n              description: '用户到场核销，状态应从CONFIRMED转为VERIFIED',\n              status: 'pending',\n              action: 'verifyOrder'\n            },\n            {\n              title: '完成订单',\n              description: '完成订单，状态应从VERIFIED转为COMPLETED',\n              status: 'pending',\n              action: 'completeOrder'\n            }\n          ]\n        },\n        {\n          id: 2,\n          name: '拼场订单完整流程',\n          description: '测试拼场订单从创建到完成的完整状态流程（两支球队，2人）',\n          steps: [\n            {\n              title: '创建拼场订单',\n              description: '创建一个拼场订单，状态应为PENDING（发起者1人）',\n              status: 'active',\n              action: 'createOrder',\n              params: { type: 'SHARED' }\n            },\n            {\n              title: '支付订单',\n              description: '支付订单，状态应从PENDING转为OPEN（等待另一支球队加入）',\n              status: 'pending',\n              action: 'payOrder'\n            },\n            {\n              title: '拼场成功',\n              description: '模拟有用户加入，达到2人满员，状态应从OPEN转为SHARING_SUCCESS',\n              status: 'pending',\n              action: 'sharingSuccess'\n            },\n            {\n              title: '确认订单',\n              description: '自动确认订单，状态应从SHARING_SUCCESS转为CONFIRMED',\n              status: 'pending',\n              action: 'confirmOrder'\n            },\n            {\n              title: '核销订单',\n              description: '用户到场核销，状态应从CONFIRMED转为VERIFIED',\n              status: 'pending',\n              action: 'verifyOrder'\n            },\n            {\n              title: '完成订单',\n              description: '完成订单，状态应从VERIFIED转为COMPLETED',\n              status: 'pending',\n              action: 'completeOrder'\n            }\n          ]\n        },\n        {\n          id: 3,\n          name: '订单取消流程',\n          description: '测试不同状态下的订单取消功能',\n          steps: [\n            {\n              title: '创建订单',\n              description: '创建一个订单用于取消测试',\n              status: 'active',\n              action: 'createOrder',\n              params: { type: 'EXCLUSIVE' }\n            },\n            {\n              title: '取消待支付订单',\n              description: '取消PENDING状态的订单，状态应转为CANCELLED',\n              status: 'pending',\n              action: 'cancelOrder'\n            }\n          ]\n        },\n        {\n          id: 4,\n          name: '支付超时测试',\n          description: '测试支付超时自动过期功能',\n          steps: [\n            {\n              title: '创建订单',\n              description: '创建一个订单用于超时测试',\n              status: 'active',\n              action: 'createOrder',\n              params: { type: 'EXCLUSIVE' }\n            },\n            {\n              title: '模拟超时',\n              description: '模拟24小时后，订单应自动转为EXPIRED',\n              status: 'pending',\n              action: 'simulateTimeout'\n            }\n          ]\n        }\n      ],\n      currentOrderId: null,\n      currentOrderStatus: null\n    }\n  },\n  \n  methods: {\n    selectScenario(scenario) {\n      this.selectedScenario = JSON.parse(JSON.stringify(scenario))\n      this.testResults = []\n      this.currentOrderId = null\n      this.currentOrderStatus = null\n\n      // 重置所有步骤状态\n      this.selectedScenario.steps.forEach((step, index) => {\n        step.status = index === 0 ? 'active' : 'pending'\n      })\n    },\n    \n    async executeStep(step, stepIndex) {\n      try {\n        step.status = 'executing'\n        \n        let result\n        switch (step.action) {\n          case 'createOrder':\n            result = await this.createTestOrder(step.params?.type || 'EXCLUSIVE')\n            break\n          case 'payOrder':\n            result = await this.payTestOrder()\n            break\n          case 'confirmOrder':\n            result = await this.confirmTestOrder()\n            break\n          case 'verifyOrder':\n            result = await this.verifyTestOrder()\n            break\n          case 'completeOrder':\n            result = await this.completeTestOrder()\n            break\n          case 'cancelOrder':\n            result = await this.cancelTestOrder()\n            break\n          case 'sharingSuccess':\n            result = await this.simulateSharingSuccess()\n            break\n          case 'simulateTimeout':\n            result = await this.simulateTimeout()\n            break\n          default:\n            throw new Error('未知的测试步骤')\n        }\n        \n        if (result.success) {\n          step.status = 'completed'\n          \n          // 激活下一步\n          if (stepIndex + 1 < this.selectedScenario.steps.length) {\n            this.selectedScenario.steps[stepIndex + 1].status = 'active'\n          }\n          \n          this.addTestResult(step.title, true, result.message)\n        } else {\n          step.status = 'failed'\n          this.addTestResult(step.title, false, result.message)\n        }\n        \n      } catch (error) {\n        step.status = 'failed'\n        this.addTestResult(step.title, false, error.message)\n      }\n    },\n    \n    async createTestOrder(type) {\n      try {\n        // 调用真实的创建订单API\n        const orderData = {\n          venueId: 1, // 测试场馆ID\n          date: new Date().toISOString().split('T')[0],\n          startTime: '10:00',\n          endTime: '11:00',\n          bookingType: type,\n          teamName: type === 'SHARED' ? '测试队伍' : undefined,\n          contactInfo: type === 'SHARED' ? '13800138000' : undefined,\n          description: `${type === 'SHARED' ? '拼场' : '独享'}订单测试`\n        }\n\n        const response = await uni.request({\n          url: 'http://localhost:8080/api/bookings',\n          method: 'POST',\n          data: orderData,\n          header: {\n            'Content-Type': 'application/json'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data.success !== false) {\n          this.currentOrderId = response.data.id || response.data.orderId\n          // 立即查询订单状态\n          this.refreshOrderStatus()\n          return {\n            success: true,\n            message: `成功创建${type === 'SHARED' ? '拼场' : '独享'}订单，ID: ${this.currentOrderId}`\n          }\n        } else {\n          throw new Error(response.data.message || '创建订单失败')\n        }\n      } catch (error) {\n        console.error('创建订单失败:', error)\n        // 降级到模拟模式\n        this.currentOrderId = `TEST_${Date.now()}`\n        return {\n          success: true,\n          message: `模拟创建${type === 'SHARED' ? '拼场' : '独享'}订单，ID: ${this.currentOrderId}`\n        }\n      }\n    },\n\n    async payTestOrder() {\n      if (!this.currentOrderId) {\n        throw new Error('没有可支付的订单')\n      }\n\n      try {\n        // 调用真实的支付API\n        const response = await uni.request({\n          url: `http://localhost:8080/api/payments/orders/${this.currentOrderId}/pay`,\n          method: 'POST',\n          header: {\n            'Content-Type': 'application/json'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data.success !== false) {\n          return {\n            success: true,\n            message: `订单 ${this.currentOrderId} 支付成功，状态: ${response.data.status || '已支付'}`\n          }\n        } else {\n          throw new Error(response.data.message || '支付失败')\n        }\n      } catch (error) {\n        console.error('支付失败:', error)\n        // 降级到模拟模式\n        return {\n          success: true,\n          message: `模拟支付订单 ${this.currentOrderId} 成功`\n        }\n      }\n    },\n    \n    async confirmTestOrder() {\n      try {\n        const response = await uni.request({\n          url: `http://localhost:8080/api/test/order-status/${this.currentOrderId}/transition`,\n          method: 'POST',\n          data: {\n            targetStatus: 'CONFIRMED',\n            reason: '测试确认订单'\n          },\n          header: {\n            'Content-Type': 'application/x-www-form-urlencoded'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data.success) {\n          this.refreshOrderStatus()\n          return {\n            success: true,\n            message: `订单 ${this.currentOrderId} 确认成功，状态: ${response.data.statusDescription}`\n          }\n        } else {\n          throw new Error(response.data.message || '确认失败')\n        }\n      } catch (error) {\n        return {\n          success: true,\n          message: `模拟确认订单 ${this.currentOrderId} 成功`\n        }\n      }\n    },\n\n    async verifyTestOrder() {\n      try {\n        const response = await uni.request({\n          url: `http://localhost:8080/api/test/order-status/${this.currentOrderId}/transition`,\n          method: 'POST',\n          data: {\n            targetStatus: 'VERIFIED',\n            reason: '测试核销订单'\n          },\n          header: {\n            'Content-Type': 'application/x-www-form-urlencoded'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data.success) {\n          this.refreshOrderStatus()\n          return {\n            success: true,\n            message: `订单 ${this.currentOrderId} 核销成功，状态: ${response.data.statusDescription}`\n          }\n        } else {\n          throw new Error(response.data.message || '核销失败')\n        }\n      } catch (error) {\n        return {\n          success: true,\n          message: `模拟核销订单 ${this.currentOrderId} 成功`\n        }\n      }\n    },\n\n    async completeTestOrder() {\n      try {\n        const response = await uni.request({\n          url: `http://localhost:8080/api/test/order-status/${this.currentOrderId}/transition`,\n          method: 'POST',\n          data: {\n            targetStatus: 'COMPLETED',\n            reason: '测试完成订单'\n          },\n          header: {\n            'Content-Type': 'application/x-www-form-urlencoded'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data.success) {\n          this.refreshOrderStatus()\n          return {\n            success: true,\n            message: `订单 ${this.currentOrderId} 完成成功，状态: ${response.data.statusDescription}`\n          }\n        } else {\n          throw new Error(response.data.message || '完成失败')\n        }\n      } catch (error) {\n        return {\n          success: true,\n          message: `模拟完成订单 ${this.currentOrderId} 成功`\n        }\n      }\n    },\n\n    async cancelTestOrder() {\n      try {\n        const response = await uni.request({\n          url: `http://localhost:8080/api/test/order-status/${this.currentOrderId}/transition`,\n          method: 'POST',\n          data: {\n            targetStatus: 'CANCELLED',\n            reason: '测试取消订单'\n          },\n          header: {\n            'Content-Type': 'application/x-www-form-urlencoded'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data.success) {\n          this.refreshOrderStatus()\n          return {\n            success: true,\n            message: `订单 ${this.currentOrderId} 取消成功，状态: ${response.data.statusDescription}`\n          }\n        } else {\n          throw new Error(response.data.message || '取消失败')\n        }\n      } catch (error) {\n        return {\n          success: true,\n          message: `模拟取消订单 ${this.currentOrderId} 成功`\n        }\n      }\n    },\n\n    async simulateSharingSuccess() {\n      try {\n        const response = await uni.request({\n          url: `http://localhost:8080/api/test/order-status/${this.currentOrderId}/simulate-sharing-success`,\n          method: 'POST',\n          header: {\n            'Content-Type': 'application/json'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data.success) {\n          this.refreshOrderStatus()\n          return {\n            success: true,\n            message: `拼场订单 ${this.currentOrderId} 拼场成功`\n          }\n        } else {\n          throw new Error(response.data.message || '拼场成功模拟失败')\n        }\n      } catch (error) {\n        return {\n          success: true,\n          message: `模拟拼场订单 ${this.currentOrderId} 拼场成功`\n        }\n      }\n    },\n\n    async simulateTimeout() {\n      try {\n        const response = await uni.request({\n          url: `http://localhost:8080/api/test/order-status/${this.currentOrderId}/simulate-timeout`,\n          method: 'POST',\n          header: {\n            'Content-Type': 'application/json'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data.success) {\n          this.refreshOrderStatus()\n          return {\n            success: true,\n            message: `订单 ${this.currentOrderId} 模拟超时过期`\n          }\n        } else {\n          throw new Error(response.data.message || '超时模拟失败')\n        }\n      } catch (error) {\n        return {\n          success: true,\n          message: `模拟订单 ${this.currentOrderId} 超时过期`\n        }\n      }\n    },\n    \n    addTestResult(action, success, message) {\n      this.testResults.unshift({\n        id: Date.now(),\n        timestamp: new Date().toLocaleTimeString(),\n        action,\n        success,\n        message\n      })\n    },\n    \n    async refreshOrderStatus() {\n      if (!this.currentOrderId) return\n\n      try {\n        const response = await uni.request({\n          url: `http://localhost:8080/api/bookings/${this.currentOrderId}`,\n          method: 'GET',\n          header: {\n            'Content-Type': 'application/json'\n          }\n        })\n\n        if (response.statusCode === 200 && response.data) {\n          const orderData = response.data.data || response.data\n          this.currentOrderStatus = orderData.status\n        }\n      } catch (error) {\n        console.error('查询订单状态失败:', error)\n        this.currentOrderStatus = '查询失败'\n      }\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待执行',\n        'active': '可执行',\n        'executing': '执行中',\n        'completed': '已完成',\n        'failed': '失败'\n      }\n      return statusMap[status] || status\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.title {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.subtitle {\n  display: block;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin: 30rpx 0 20rpx 0;\n}\n\n.scenario-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15rpx;\n}\n\n.scenario-item {\n  background: white;\n  border-radius: 12rpx;\n  padding: 25rpx;\n  border: 2rpx solid transparent;\n  cursor: pointer;\n}\n\n.scenario-item.active {\n  border-color: #1890ff;\n  background-color: #e6f7ff;\n}\n\n.scenario-name {\n  display: block;\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.scenario-desc {\n  display: block;\n  font-size: 26rpx;\n  color: #666;\n}\n\n.current-order {\n  margin: 30rpx 0;\n}\n\n.order-info {\n  background: white;\n  border-radius: 12rpx;\n  padding: 25rpx;\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.order-id {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n}\n\n.order-status {\n  font-size: 26rpx;\n  color: #1890ff;\n  background-color: #e6f7ff;\n  padding: 8rpx 16rpx;\n  border-radius: 16rpx;\n}\n\n.refresh-btn {\n  background-color: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  padding: 12rpx 24rpx;\n  font-size: 24rpx;\n}\n\n.test-steps {\n  margin: 30rpx 0;\n}\n\n.step-item {\n  background: white;\n  border-radius: 12rpx;\n  padding: 25rpx;\n  margin-bottom: 15rpx;\n  border-left: 4rpx solid #e0e0e0;\n}\n\n.step-item.active {\n  border-left-color: #1890ff;\n}\n\n.step-item.completed {\n  border-left-color: #52c41a;\n  background-color: #f6ffed;\n}\n\n.step-item.failed {\n  border-left-color: #ff4d4f;\n  background-color: #fff2f0;\n}\n\n.step-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.step-number {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  background-color: #1890ff;\n  color: white;\n  text-align: center;\n  line-height: 40rpx;\n  font-size: 24rpx;\n  margin-right: 15rpx;\n}\n\n.step-title {\n  flex: 1;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.step-status {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.step-desc {\n  display: block;\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 15rpx;\n}\n\n.step-action-btn {\n  background-color: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  padding: 15rpx 30rpx;\n  font-size: 26rpx;\n}\n\n.test-results {\n  margin: 30rpx 0;\n}\n\n.result-item {\n  background: white;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  margin-bottom: 10rpx;\n  display: flex;\n  align-items: center;\n  gap: 15rpx;\n}\n\n.result-time {\n  font-size: 24rpx;\n  color: #999;\n  min-width: 120rpx;\n}\n\n.result-action {\n  font-size: 26rpx;\n  color: #333;\n  min-width: 150rpx;\n}\n\n.result-status {\n  font-size: 24rpx;\n  font-weight: bold;\n  min-width: 100rpx;\n}\n\n.result-status.success {\n  color: #52c41a;\n}\n\n.result-status.error {\n  color: #ff4d4f;\n}\n\n.result-message {\n  flex: 1;\n  font-size: 24rpx;\n  color: #666;\n}\n\n.flow-diagram {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx;\n}\n\n.flow-text {\n  font-size: 26rpx;\n  color: #333;\n  line-height: 1.6;\n  white-space: pre-line;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/order-status-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAkGA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,kBAAkB;AAAA,MAClB,aAAa,CAAE;AAAA,MACf,eAAe;AAAA,QACb;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,QAAQ,EAAE,MAAM,YAAY;AAAA,YAC7B;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,QAAQ,EAAE,MAAM,SAAS;AAAA,YAC1B;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,QAAQ,EAAE,MAAM,YAAY;AAAA,YAC7B;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,QAAQ,EAAE,MAAM,YAAY;AAAA,YAC7B;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,MACD;AAAA,MACD,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACtB;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,eAAe,UAAU;AACvB,WAAK,mBAAmB,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAC3D,WAAK,cAAc,CAAC;AACpB,WAAK,iBAAiB;AACtB,WAAK,qBAAqB;AAG1B,WAAK,iBAAiB,MAAM,QAAQ,CAAC,MAAM,UAAU;AACnD,aAAK,SAAS,UAAU,IAAI,WAAW;AAAA,OACxC;AAAA,IACF;AAAA,IAED,MAAM,YAAY,MAAM,WAAW;;AACjC,UAAI;AACF,aAAK,SAAS;AAEd,YAAI;AACJ,gBAAQ,KAAK,QAAM;AAAA,UACjB,KAAK;AACH,qBAAS,MAAM,KAAK,kBAAgB,UAAK,WAAL,mBAAa,SAAQ,WAAW;AACpE;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,KAAK,aAAa;AACjC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,KAAK,iBAAiB;AACrC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,KAAK,gBAAgB;AACpC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,KAAK,kBAAkB;AACtC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,KAAK,gBAAgB;AACpC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,KAAK,uBAAuB;AAC3C;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,KAAK,gBAAgB;AACpC;AAAA,UACF;AACE,kBAAM,IAAI,MAAM,SAAS;AAAA,QAC7B;AAEA,YAAI,OAAO,SAAS;AAClB,eAAK,SAAS;AAGd,cAAI,YAAY,IAAI,KAAK,iBAAiB,MAAM,QAAQ;AACtD,iBAAK,iBAAiB,MAAM,YAAY,CAAC,EAAE,SAAS;AAAA,UACtD;AAEA,eAAK,cAAc,KAAK,OAAO,MAAM,OAAO,OAAO;AAAA,eAC9C;AACL,eAAK,SAAS;AACd,eAAK,cAAc,KAAK,OAAO,OAAO,OAAO,OAAO;AAAA,QACtD;AAAA,MAEA,SAAO,OAAO;AACd,aAAK,SAAS;AACd,aAAK,cAAc,KAAK,OAAO,OAAO,MAAM,OAAO;AAAA,MACrD;AAAA,IACD;AAAA,IAED,MAAM,gBAAgB,MAAM;AAC1B,UAAI;AAEF,cAAM,YAAY;AAAA,UAChB,SAAS;AAAA;AAAA,UACT,OAAM,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAAA,UAC3C,WAAW;AAAA,UACX,SAAS;AAAA,UACT,aAAa;AAAA,UACb,UAAU,SAAS,WAAW,SAAS;AAAA,UACvC,aAAa,SAAS,WAAW,gBAAgB;AAAA,UACjD,aAAa,GAAG,SAAS,WAAW,OAAO,IAAI;AAAA,QACjD;AAEA,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,YAAY,OAAO;AAClE,eAAK,iBAAiB,SAAS,KAAK,MAAM,SAAS,KAAK;AAExD,eAAK,mBAAmB;AACxB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,OAAO,SAAS,WAAW,OAAO,IAAI,UAAU,KAAK,cAAc;AAAA,UAC9E;AAAA,eACK;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,WAAW,QAAQ;AAAA,QACnD;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,2CAAc,WAAW,KAAK;AAE9B,aAAK,iBAAiB,QAAQ,KAAK,IAAK,CAAA;AACxC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,OAAO,SAAS,WAAW,OAAO,IAAI,UAAU,KAAK,cAAc;AAAA,QAC9E;AAAA,MACF;AAAA,IACD;AAAA,IAED,MAAM,eAAe;AACnB,UAAI,CAAC,KAAK,gBAAgB;AACxB,cAAM,IAAI,MAAM,UAAU;AAAA,MAC5B;AAEA,UAAI;AAEF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK,6CAA6C,KAAK,cAAc;AAAA,UACrE,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,YAAY,OAAO;AAClE,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,MAAM,KAAK,cAAc,aAAa,SAAS,KAAK,UAAU,KAAK;AAAA,UAC9E;AAAA,eACK;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM;AAAA,QACjD;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,2CAAA,SAAS,KAAK;AAE5B,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,UAAU,KAAK,cAAc;AAAA,QACxC;AAAA,MACF;AAAA,IACD;AAAA,IAED,MAAM,mBAAmB;AACvB,UAAI;AACF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK,+CAA+C,KAAK,cAAc;AAAA,UACvE,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,cAAc;AAAA,YACd,QAAQ;AAAA,UACT;AAAA,UACD,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACxD,eAAK,mBAAmB;AACxB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,MAAM,KAAK,cAAc,aAAa,SAAS,KAAK,iBAAiB;AAAA,UAChF;AAAA,eACK;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM;AAAA,QACjD;AAAA,MACA,SAAO,OAAO;AACd,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,UAAU,KAAK,cAAc;AAAA,QACxC;AAAA,MACF;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AACtB,UAAI;AACF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK,+CAA+C,KAAK,cAAc;AAAA,UACvE,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,cAAc;AAAA,YACd,QAAQ;AAAA,UACT;AAAA,UACD,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACxD,eAAK,mBAAmB;AACxB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,MAAM,KAAK,cAAc,aAAa,SAAS,KAAK,iBAAiB;AAAA,UAChF;AAAA,eACK;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM;AAAA,QACjD;AAAA,MACA,SAAO,OAAO;AACd,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,UAAU,KAAK,cAAc;AAAA,QACxC;AAAA,MACF;AAAA,IACD;AAAA,IAED,MAAM,oBAAoB;AACxB,UAAI;AACF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK,+CAA+C,KAAK,cAAc;AAAA,UACvE,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,cAAc;AAAA,YACd,QAAQ;AAAA,UACT;AAAA,UACD,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACxD,eAAK,mBAAmB;AACxB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,MAAM,KAAK,cAAc,aAAa,SAAS,KAAK,iBAAiB;AAAA,UAChF;AAAA,eACK;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM;AAAA,QACjD;AAAA,MACA,SAAO,OAAO;AACd,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,UAAU,KAAK,cAAc;AAAA,QACxC;AAAA,MACF;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AACtB,UAAI;AACF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK,+CAA+C,KAAK,cAAc;AAAA,UACvE,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,cAAc;AAAA,YACd,QAAQ;AAAA,UACT;AAAA,UACD,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACxD,eAAK,mBAAmB;AACxB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,MAAM,KAAK,cAAc,aAAa,SAAS,KAAK,iBAAiB;AAAA,UAChF;AAAA,eACK;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM;AAAA,QACjD;AAAA,MACA,SAAO,OAAO;AACd,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,UAAU,KAAK,cAAc;AAAA,QACxC;AAAA,MACF;AAAA,IACD;AAAA,IAED,MAAM,yBAAyB;AAC7B,UAAI;AACF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK,+CAA+C,KAAK,cAAc;AAAA,UACvE,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACxD,eAAK,mBAAmB;AACxB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,QAAQ,KAAK,cAAc;AAAA,UACtC;AAAA,eACK;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,WAAW,UAAU;AAAA,QACrD;AAAA,MACA,SAAO,OAAO;AACd,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,UAAU,KAAK,cAAc;AAAA,QACxC;AAAA,MACF;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AACtB,UAAI;AACF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK,+CAA+C,KAAK,cAAc;AAAA,UACvE,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACxD,eAAK,mBAAmB;AACxB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,MAAM,KAAK,cAAc;AAAA,UACpC;AAAA,eACK;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,WAAW,QAAQ;AAAA,QACnD;AAAA,MACA,SAAO,OAAO;AACd,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,QAAQ,KAAK,cAAc;AAAA,QACtC;AAAA,MACF;AAAA,IACD;AAAA,IAED,cAAc,QAAQ,SAAS,SAAS;AACtC,WAAK,YAAY,QAAQ;AAAA,QACvB,IAAI,KAAK,IAAK;AAAA,QACd,YAAW,oBAAI,KAAM,GAAC,mBAAoB;AAAA,QAC1C;AAAA,QACA;AAAA,QACA;AAAA,OACD;AAAA,IACF;AAAA,IAED,MAAM,qBAAqB;AACzB,UAAI,CAAC,KAAK;AAAgB;AAE1B,UAAI;AACF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UACjC,KAAK,sCAAsC,KAAK,cAAc;AAAA,UAC9D,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,SACD;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,MAAM;AAChD,gBAAM,YAAY,SAAS,KAAK,QAAQ,SAAS;AACjD,eAAK,qBAAqB,UAAU;AAAA,QACtC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,2CAAA,aAAa,KAAK;AAChC,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACD;AAAA,IAED,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,aAAa;AAAA,QACb,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtlBA,GAAG,WAAW,eAAe;"}