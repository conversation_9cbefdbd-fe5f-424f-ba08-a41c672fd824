{"version": 3, "file": "auth-test.js", "sources": ["pages/test/auth-test.vue", "pages/test/auth-test.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">认证测试页面</text>\n    </view>\n    \n    <view class=\"status-section\">\n      <text class=\"section-title\">当前状态</text>\n      <view class=\"status-item\">\n        <text>Token: {{ currentToken || '未设置' }}</text>\n      </view>\n      <view class=\"status-item\">\n        <text>用户信息: {{ currentUserInfo ? JSON.stringify(currentUserInfo) : '未设置' }}</text>\n      </view>\n      <view class=\"status-item\">\n        <text>登录状态: {{ isLoggedIn ? '已登录' : '未登录' }}</text>\n      </view>\n    </view>\n    \n    <view class=\"action-section\">\n      <text class=\"section-title\">测试操作</text>\n      \n      <button class=\"action-btn\" @click=\"setTestAuth\">设置测试认证信息</button>\n      <button class=\"action-btn\" @click=\"clearAuth\">清除认证信息</button>\n      <button class=\"action-btn\" @click=\"refreshStatus\">刷新状态</button>\n      \n      <view class=\"divider\"></view>\n      \n      <button class=\"action-btn\" @click=\"testNavigateToProfile\">测试跳转到个人中心</button>\n      <button class=\"action-btn\" @click=\"testNavigateToBooking\">测试跳转到预约页面</button>\n      <button class=\"action-btn\" @click=\"testNavigateToVenueList\">测试跳转到场馆列表</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getToken, getUserInfo, setToken, setUserInfo, clearAuth, isLoggedIn } from '@/utils/auth.js'\n\nexport default {\n  data() {\n    return {\n      currentToken: '',\n      currentUserInfo: null,\n      isLoggedIn: false\n    }\n  },\n  \n  onLoad() {\n    this.refreshStatus()\n  },\n  \n  onShow() {\n    this.refreshStatus()\n  },\n  \n  methods: {\n    // 刷新状态\n    refreshStatus() {\n      this.currentToken = getToken()\n      this.currentUserInfo = getUserInfo()\n      this.isLoggedIn = isLoggedIn()\n    },\n    \n    // 设置测试认证信息\n    setTestAuth() {\n      // 使用真实的token和用户信息\n      const realToken = 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMzgwMDAwMDAwMSIsImlhdCI6MTc1MjQwMTgyMSwiZXhwIjoxNzUyNDg4MjIxfQ.vFAd19NFzyYyxS2cRXy3dCq_Va_dguz01QSX2lwN_c0'\n      const realUserInfo = {\n        id: 33,\n        username: '13800000001',\n        phone: '13800000001',\n        nickname: '测试用户2',\n        avatar: '/static/images/default-avatar.svg'\n      }\n\n      setToken(realToken)\n      setUserInfo(realUserInfo)\n\n      uni.showToast({\n        title: '真实认证信息已设置',\n        icon: 'success'\n      })\n\n      this.refreshStatus()\n    },\n    \n    // 清除认证信息\n    clearAuth() {\n      clearAuth()\n      \n      uni.showToast({\n        title: '认证信息已清除',\n        icon: 'success'\n      })\n      \n      this.refreshStatus()\n    },\n    \n    // 测试跳转到个人中心（需要登录）\n    testNavigateToProfile() {\n      uni.navigateTo({\n        url: '/pages/user/profile'\n      })\n    },\n    \n    // 测试跳转到预约页面（需要登录）\n    testNavigateToBooking() {\n      uni.navigateTo({\n        url: '/pages/booking/create'\n      })\n    },\n    \n    // 测试跳转到场馆列表（不需要登录）\n    testNavigateToVenueList() {\n      uni.navigateTo({\n        url: '/pages/venue/list'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.status-section, .action-section {\n  background-color: white;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n.status-item {\n  padding: 15rpx 0;\n  border-bottom: 1rpx solid #eee;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  text {\n    font-size: 28rpx;\n    color: #666;\n    word-break: break-all;\n  }\n}\n\n.action-btn {\n  width: 100%;\n  height: 80rpx;\n  background-color: #007aff;\n  color: white;\n  border: none;\n  border-radius: 12rpx;\n  font-size: 30rpx;\n  margin-bottom: 20rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n  \n  &:active {\n    background-color: #0056cc;\n  }\n}\n\n.divider {\n  height: 1rpx;\n  background-color: #eee;\n  margin: 30rpx 0;\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/auth-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getToken", "getUserInfo", "isLoggedIn", "setToken", "setUserInfo", "uni", "clearAuth"], "mappings": ";;;AAsCA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,YAAY;AAAA,IACd;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,SAAS;AACP,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,gBAAgB;AACd,WAAK,eAAeA,oBAAS;AAC7B,WAAK,kBAAkBC,uBAAY;AACnC,WAAK,aAAaC,sBAAW;AAAA,IAC9B;AAAA;AAAA,IAGD,cAAc;AAEZ,YAAM,YAAY;AAClB,YAAM,eAAe;AAAA,QACnB,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,OAAO;AAAA,QACP,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAEAC,iBAAAA,SAAS,SAAS;AAClBC,iBAAAA,YAAY,YAAY;AAExBC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAED,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,YAAY;AACVC,2BAAU;AAEVD,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAED,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,wBAAwB;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,wBAAwB;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,0BAA0B;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;ACtHA,GAAG,WAAW,eAAe;"}