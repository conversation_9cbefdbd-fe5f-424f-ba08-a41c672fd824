{"version": 3, "file": "payment-test.js", "sources": ["pages/test/payment-test.vue", "pages/test/payment-test.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"payment-test-container\">\n    <view class=\"header\">\n      <text class=\"title\">支付功能测试</text>\n      <text class=\"subtitle\">验证所有订单类型都能正常支付</text>\n    </view>\n\n    <!-- 测试订单列表 -->\n    <view class=\"test-orders\">\n      <view class=\"section-title\">测试订单</view>\n      \n      <view v-for=\"order in testOrders\" :key=\"order.id\" class=\"order-card\">\n        <view class=\"order-header\">\n          <text class=\"order-no\">{{ order.orderNo }}</text>\n          <view class=\"order-type\" :class=\"getTypeClass(order.bookingType)\">\n            {{ getTypeText(order.bookingType) }}\n          </view>\n        </view>\n        \n        <view class=\"order-info\">\n          <text class=\"venue-name\">{{ order.venueName }}</text>\n          <text class=\"booking-time\">{{ order.bookingDate }} {{ order.startTime }}-{{ order.endTime }}</text>\n          <text class=\"price\">¥{{ order.totalPrice }}</text>\n        </view>\n        \n        <view class=\"order-status\">\n          <text class=\"status-text\" :class=\"getStatusClass(order.status)\">\n            {{ getStatusText(order.status) }}\n          </text>\n        </view>\n        \n        <view class=\"order-actions\">\n          <!-- 支付按钮 - 所有PENDING状态的订单都应该显示 -->\n          <button \n            v-if=\"order.status === 'PENDING'\" \n            class=\"action-btn pay-btn\" \n            @click=\"testPayment(order)\"\n          >\n            立即支付\n          </button>\n          \n          <button \n            v-else \n            class=\"action-btn disabled-btn\" \n            disabled\n          >\n            {{ order.status === 'PAID' ? '已支付' : '不可支付' }}\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 测试结果 -->\n    <view class=\"test-results\">\n      <view class=\"section-title\">测试结果</view>\n      <view v-for=\"result in testResults\" :key=\"result.id\" class=\"result-item\">\n        <text class=\"result-order\">{{ result.orderNo }}</text>\n        <text class=\"result-status\" :class=\"result.success ? 'success' : 'error'\">\n          {{ result.success ? '✅ 支付成功' : '❌ 支付失败' }}\n        </text>\n        <text class=\"result-message\">{{ result.message }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { payOrder } from '@/api/payment.js'\n\nexport default {\n  name: 'PaymentTest',\n  data() {\n    return {\n      testOrders: [\n        {\n          id: 1,\n          orderNo: 'TEST001',\n          bookingType: 'EXCLUSIVE',\n          venueName: '篮球场A',\n          bookingDate: '2025-01-15',\n          startTime: '09:00',\n          endTime: '10:00',\n          totalPrice: 100,\n          status: 'PENDING'\n        },\n        {\n          id: 2,\n          orderNo: 'TEST002',\n          bookingType: 'SHARED',\n          venueName: '羽毛球场B',\n          bookingDate: '2025-01-15',\n          startTime: '14:00',\n          endTime: '15:00',\n          totalPrice: 50,\n          status: 'PENDING'\n        },\n        {\n          id: 3,\n          orderNo: 'TEST003',\n          bookingType: 'EXCLUSIVE',\n          venueName: '网球场C',\n          bookingDate: '2025-01-15',\n          startTime: '16:00',\n          endTime: '17:00',\n          totalPrice: 150,\n          status: 'PAID'\n        },\n        {\n          id: 4,\n          orderNo: 'TEST004',\n          bookingType: 'SHARED',\n          venueName: '乒乓球场D',\n          bookingDate: '2025-01-15',\n          startTime: '19:00',\n          endTime: '20:00',\n          totalPrice: 30,\n          status: 'PENDING'\n        }\n      ],\n      testResults: []\n    }\n  },\n  \n  methods: {\n    // 测试支付功能\n    async testPayment(order) {\n      try {\n        uni.showLoading({ title: '测试支付中...' })\n        \n        // 模拟支付API调用\n        const response = await payOrder(order.id)\n        \n        // 更新订单状态\n        order.status = 'PAID'\n        \n        // 记录测试结果\n        this.testResults.push({\n          id: Date.now(),\n          orderNo: order.orderNo,\n          success: true,\n          message: `${this.getTypeText(order.bookingType)}订单支付成功`\n        })\n        \n        uni.hideLoading()\n        uni.showToast({\n          title: '支付测试成功',\n          icon: 'success'\n        })\n        \n      } catch (error) {\n        console.error('支付测试失败:', error)\n        \n        // 记录测试结果\n        this.testResults.push({\n          id: Date.now(),\n          orderNo: order.orderNo,\n          success: false,\n          message: `支付失败: ${error.message || '未知错误'}`\n        })\n        \n        uni.hideLoading()\n        uni.showToast({\n          title: '支付测试失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 获取订单类型文本\n    getTypeText(bookingType) {\n      const typeMap = {\n        'EXCLUSIVE': '独享',\n        'SHARED': '拼场'\n      }\n      return typeMap[bookingType] || '普通'\n    },\n    \n    // 获取订单类型样式\n    getTypeClass(bookingType) {\n      const classMap = {\n        'EXCLUSIVE': 'type-exclusive',\n        'SHARED': 'type-shared'\n      }\n      return classMap[bookingType] || 'type-normal'\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'PENDING': '待支付',\n        'PAID': '已支付',\n        'CONFIRMED': '已确认',\n        'SHARING': '拼场中',\n        'SHARING_SUCCESS': '拼场成功',\n        'COMPLETED': '已完成',\n        'CANCELLED': '已取消',\n        'EXPIRED': '已过期'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    \n    // 获取状态样式\n    getStatusClass(status) {\n      const classMap = {\n        'PENDING': 'status-pending',\n        'PAID': 'status-paid',\n        'CONFIRMED': 'status-confirmed',\n        'SHARING': 'status-sharing',\n        'COMPLETED': 'status-completed',\n        'CANCELLED': 'status-cancelled'\n      }\n      return classMap[status] || 'status-default'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.payment-test-container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.title {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.subtitle {\n  display: block;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin: 30rpx 0 20rpx 0;\n}\n\n.order-card {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.order-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.order-no {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.order-type {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  color: white;\n}\n\n.type-exclusive {\n  background-color: #007aff;\n}\n\n.type-shared {\n  background-color: #ff9500;\n}\n\n.type-normal {\n  background-color: #8e8e93;\n}\n\n.order-info {\n  margin-bottom: 20rpx;\n}\n\n.venue-name {\n  display: block;\n  font-size: 30rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.booking-time {\n  display: block;\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n}\n\n.price {\n  display: block;\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #ff6b35;\n}\n\n.order-status {\n  margin-bottom: 20rpx;\n}\n\n.status-text {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  color: white;\n}\n\n.status-pending {\n  background-color: #ff9500;\n}\n\n.status-paid {\n  background-color: #34c759;\n}\n\n.status-confirmed {\n  background-color: #007aff;\n}\n\n.status-sharing {\n  background-color: #ff9500;\n}\n\n.order-actions {\n  display: flex;\n  gap: 20rpx;\n}\n\n.action-btn {\n  flex: 1;\n  height: 80rpx;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.pay-btn {\n  background-color: #ff6b35;\n  color: white;\n}\n\n.disabled-btn {\n  background-color: #e0e0e0;\n  color: #999;\n}\n\n.test-results {\n  margin-top: 40rpx;\n}\n\n.result-item {\n  background: white;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-bottom: 15rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.result-order {\n  font-size: 26rpx;\n  color: #333;\n}\n\n.result-status {\n  font-size: 26rpx;\n  font-weight: bold;\n}\n\n.result-status.success {\n  color: #34c759;\n}\n\n.result-status.error {\n  color: #ff3b30;\n}\n\n.result-message {\n  font-size: 24rpx;\n  color: #666;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/payment-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "payOrder"], "mappings": ";;;AAqEA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,QACV;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,aAAa;AAAA,UACb,WAAW;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,aAAa;AAAA,UACb,WAAW;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,aAAa;AAAA,UACb,WAAW;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,aAAa;AAAA,UACb,WAAW;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,MACD;AAAA,MACD,aAAa,CAAC;AAAA,IAChB;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,YAAY,OAAO;AACvB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,YAAY;AAGrC,cAAM,WAAW,MAAMC,qBAAS,MAAM,EAAE;AAGxC,cAAM,SAAS;AAGf,aAAK,YAAY,KAAK;AAAA,UACpB,IAAI,KAAK,IAAK;AAAA,UACd,SAAS,MAAM;AAAA,UACf,SAAS;AAAA,UACT,SAAS,GAAG,KAAK,YAAY,MAAM,WAAW,CAAC;AAAA,SAChD;AAEDD,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,sCAAc,WAAW,KAAK;AAG9B,aAAK,YAAY,KAAK;AAAA,UACpB,IAAI,KAAK,IAAK;AAAA,UACd,SAAS,MAAM;AAAA,UACf,SAAS;AAAA,UACT,SAAS,SAAS,MAAM,WAAW,MAAM;AAAA,SAC1C;AAEDA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,aAAa;AACvB,YAAM,UAAU;AAAA,QACd,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,aAAO,QAAQ,WAAW,KAAK;AAAA,IAChC;AAAA;AAAA,IAGD,aAAa,aAAa;AACxB,YAAM,WAAW;AAAA,QACf,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,aAAO,SAAS,WAAW,KAAK;AAAA,IACjC;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,WAAW;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AACA,aAAO,SAAS,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrNA,GAAG,WAAW,eAAe;"}