{"version": 3, "file": "received.js", "sources": ["pages/sharing/received.vue", "pages/sharing/received.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <text class=\"nav-icon\">‹</text>\n      </view>\n      <text class=\"nav-title\">收到的申请</text>\n      <view class=\"nav-right\"></view>\n    </view>\n    \n    <!-- 筛选标签 -->\n    <view class=\"filter-tabs\">\n      <view \n        v-for=\"tab in filterTabs\" \n        :key=\"tab.value\"\n        class=\"filter-tab\"\n        :class=\"{ active: currentFilter === tab.value }\"\n        @click=\"switchFilter(tab.value)\"\n      >\n        <text class=\"tab-text\">{{ tab.label }}</text>\n        <text v-if=\"tab.count > 0\" class=\"tab-count\">{{ tab.count }}</text>\n      </view>\n    </view>\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-state\">\n      <text>加载中...</text>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else-if=\"error\" class=\"error-state\">\n      <text class=\"error-icon\">⚠️</text>\n      <text class=\"error-text\">{{ error }}</text>\n      <button class=\"retry-btn\" @click=\"loadRequests\">\n        重新加载\n      </button>\n    </view>\n    \n    <!-- 申请列表 -->\n    <view v-else class=\"content\">\n      <view v-if=\"filteredRequests.length > 0\" class=\"requests-list\">\n        <view \n          v-for=\"request in filteredRequests\" \n          :key=\"request.id\"\n          class=\"request-item\"\n        >\n          <!-- 拼场信息 -->\n          <view class=\"sharing-info\">\n            <view class=\"sharing-header\">\n              <text class=\"venue-name\">{{ request.venueName }}</text>\n              <view class=\"status-badge\" :class=\"getStatusClass(request.status)\">\n                <text class=\"status-text\">{{ getStatusText(request.status) }}</text>\n              </view>\n            </view>\n            \n            <view class=\"sharing-details\">\n              <text class=\"team-name\">{{ request.teamName }}</text>\n              <text class=\"activity-time\">{{ formatActivityTime(request) }}</text>\n              <text class=\"price\">人均 ¥{{ request.pricePerPerson }}</text>\n            </view>\n            \n            <!-- 参与人数进度 -->\n            <view class=\"participants-progress\">\n              <view class=\"progress-info\">\n                <text class=\"progress-text\">\n                  {{ request.currentParticipants }}/{{ request.maxParticipants }}人\n                </text>\n                <text class=\"progress-percent\">\n                  {{ getProgressPercent(request.currentParticipants, request.maxParticipants) }}%\n                </text>\n              </view>\n              <view class=\"progress-bar\">\n                <view \n                  class=\"progress-fill\"\n                  :style=\"{ width: getProgressPercent(request.currentParticipants, request.maxParticipants) + '%' }\"\n                ></view>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 申请人信息 -->\n          <view class=\"applicant-info\">\n            <view class=\"applicant-header\">\n              <view class=\"applicant-avatar\">\n                <image \n                  v-if=\"request.applicantAvatar\" \n                  :src=\"request.applicantAvatar\" \n                  class=\"avatar-img\"\n                />\n                <text v-else class=\"avatar-placeholder\">{{ getAvatarText(request.applicantName) }}</text>\n              </view>\n              <view class=\"applicant-details\">\n                <text class=\"applicant-name\">{{ request.applicantName }}</text>\n                <text class=\"apply-time\">申请时间：{{ formatDateTime(request.createdAt) }}</text>\n              </view>\n            </view>\n            \n            <!-- 申请留言 -->\n            <view v-if=\"request.message\" class=\"apply-message\">\n              <text class=\"message-label\">申请留言：</text>\n              <text class=\"message-content\">{{ request.message }}</text>\n            </view>\n          </view>\n          \n          <!-- 申请操作 -->\n          <view class=\"request-actions\">\n            <view v-if=\"request.status === 'PENDING'\" class=\"pending-actions\">\n              <button \n                class=\"action-btn reject-btn\"\n                @click=\"showRejectDialog(request)\"\n              >\n                拒绝\n              </button>\n              <button \n                class=\"action-btn approve-btn\"\n                @click=\"showApproveConfirm(request)\"\n              >\n                同意\n              </button>\n            </view>\n            \n            <view v-else-if=\"request.status === 'APPROVED'\" class=\"processed-actions\">\n              <text class=\"processed-text\">已同意</text>\n              <text class=\"process-time\">{{ formatDateTime(request.processedAt) }}</text>\n            </view>\n            \n            <view v-else-if=\"request.status === 'REJECTED'\" class=\"processed-actions\">\n              <text class=\"processed-text rejected\">已拒绝</text>\n              <text class=\"process-time\">{{ formatDateTime(request.processedAt) }}</text>\n              <text v-if=\"request.rejectReason\" class=\"reject-reason\">\n                拒绝原因：{{ request.rejectReason }}\n              </text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view v-else class=\"empty-state\">\n        <text class=\"empty-icon\">📬</text>\n        <text class=\"empty-title\">{{ getEmptyTitle() }}</text>\n        <text class=\"empty-desc\">{{ getEmptyDesc() }}</text>\n        <button class=\"create-btn\" @click=\"goToCreateSharing\">\n          创建拼场\n        </button>\n      </view>\n    </view>\n    \n    <!-- 同意申请确认弹窗 -->\n    <uni-popup ref=\"approvePopup\" type=\"dialog\">\n      <uni-popup-dialog \n        type=\"info\"\n        title=\"同意申请\"\n        :content=\"`确定同意 ${approveTarget?.applicantName} 的申请吗？`\"\n        @confirm=\"confirmApprove\"\n        @close=\"() => { approveTarget = null }\"\n      ></uni-popup-dialog>\n    </uni-popup>\n    \n    <!-- 拒绝申请弹窗 -->\n    <uni-popup ref=\"rejectPopup\" type=\"bottom\">\n      <view class=\"reject-dialog\">\n        <view class=\"dialog-header\">\n          <text class=\"dialog-title\">拒绝申请</text>\n          <text class=\"close-btn\" @click=\"closeRejectDialog\">✕</text>\n        </view>\n        \n        <view class=\"dialog-content\">\n          <view class=\"applicant-info\">\n            <text class=\"applicant-name\">申请人：{{ rejectTarget?.applicantName }}</text>\n          </view>\n          \n          <view class=\"reason-section\">\n            <text class=\"reason-label\">拒绝原因（可选）</text>\n            <textarea \n              v-model=\"rejectReason\"\n              class=\"reason-input\"\n              placeholder=\"请输入拒绝原因...\"\n              maxlength=\"100\"\n            ></textarea>\n            <text class=\"char-count\">{{ rejectReason.length }}/100</text>\n          </view>\n        </view>\n        \n        <view class=\"dialog-actions\">\n          <button class=\"cancel-btn\" @click=\"closeRejectDialog\">\n            取消\n          </button>\n          <button class=\"confirm-btn\" @click=\"confirmReject\">\n            确定拒绝\n          </button>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\nimport { formatDate, formatDateTime } from '@/utils/helpers.js'\n\nexport default {\n  name: 'ReceivedRequests',\n  \n  data() {\n    return {\n      currentFilter: 'all',\n      error: '',\n      approveTarget: null,\n      rejectTarget: null,\n      rejectReason: '',\n      requests: [],\n      filterTabs: [\n        { label: '全部', value: 'all', count: 0 },\n        { label: '待处理', value: 'pending', count: 0 },\n        { label: '已同意', value: 'approved', count: 0 },\n        { label: '已拒绝', value: 'rejected', count: 0 }\n      ]\n    }\n  },\n  \n  computed: {\n    ...mapState('sharing', ['loading']),\n    ...mapState('user', ['userInfo']),\n    \n    // 过滤后的申请列表\n    filteredRequests() {\n      if (this.currentFilter === 'all') {\n        return this.requests\n      }\n      \n      const statusMap = {\n        'pending': 'PENDING',\n        'approved': 'APPROVED',\n        'rejected': 'REJECTED'\n      }\n      \n      return this.requests.filter(request => \n        request.status === statusMap[this.currentFilter]\n      )\n    }\n  },\n  \n  onLoad() {\n    this.loadRequests()\n  },\n  \n  onShow() {\n    // 页面显示时刷新数据\n    this.loadRequests()\n  },\n  \n  onPullDownRefresh() {\n    this.loadRequests().finally(() => {\n      uni.stopPullDownRefresh()\n    })\n  },\n  \n  methods: {\n    ...mapActions('sharing', [\n      'getReceivedSharingRequests',\n      'processSharingRequest'\n    ]),\n    \n    // 返回上一页\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    // 加载申请列表\n    async loadRequests() {\n      try {\n        this.error = ''\n        console.log('收到申请页面：开始加载申请列表')\n        \n        // 调用真实API\n        const requests = await this.getReceivedSharingRequests()\n        this.requests = requests || []\n        \n        // 更新筛选标签计数\n        this.updateFilterCounts()\n        \n        console.log('收到申请页面：加载申请列表成功:', this.requests)\n        \n      } catch (error) {\n        console.error('收到申请页面：加载申请列表失败:', error)\n        this.error = error.message || '加载失败，请重试'\n        this.requests = []\n      }\n    },\n    \n\n    \n    // 更新筛选标签计数\n    updateFilterCounts() {\n      const counts = {\n        all: this.requests.length,\n        pending: this.requests.filter(r => r.status === 'PENDING').length,\n        approved: this.requests.filter(r => r.status === 'APPROVED').length,\n        rejected: this.requests.filter(r => r.status === 'REJECTED').length\n      }\n      \n      this.filterTabs.forEach(tab => {\n        tab.count = counts[tab.value] || 0\n      })\n    },\n    \n    // 切换筛选\n    switchFilter(filter) {\n      this.currentFilter = filter\n    },\n    \n    // 显示同意确认弹窗\n    showApproveConfirm(request) {\n      this.approveTarget = request\n      this.$refs.approvePopup.open()\n    },\n    \n    // 显示拒绝对话框\n    showRejectDialog(request) {\n      this.rejectTarget = request\n      this.rejectReason = ''\n      this.$refs.rejectPopup.open()\n    },\n    \n    // 关闭拒绝对话框\n    closeRejectDialog() {\n      this.rejectTarget = null\n      this.rejectReason = ''\n      this.$refs.rejectPopup.close()\n    },\n    \n    // 确认同意申请\n    async confirmApprove() {\n      if (!this.approveTarget) return\n      \n      try {\n        uni.showLoading({ title: '处理中...' })\n        \n        await this.processSharingRequest({\n          requestId: this.approveTarget.id,\n          action: 'approve'\n        })\n        \n        // 更新本地状态\n        const request = this.requests.find(r => r.id === this.approveTarget.id)\n        if (request) {\n          request.status = 'APPROVED'\n          request.processedAt = new Date().toISOString()\n        }\n        \n        // 更新计数\n        this.updateFilterCounts()\n        \n        uni.hideLoading()\n        \n        uni.showToast({\n          title: '已同意申请',\n          icon: 'success'\n        })\n        \n        this.approveTarget = null\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('收到申请页面：同意申请失败:', error)\n        uni.showToast({\n          title: error.message || '操作失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 确认拒绝申请\n    async confirmReject() {\n      if (!this.rejectTarget) return\n      \n      try {\n        uni.showLoading({ title: '处理中...' })\n        \n        await this.processSharingRequest({\n          requestId: this.rejectTarget.id,\n          action: 'reject',\n          reason: this.rejectReason\n        })\n        \n        // 更新本地状态\n        const request = this.requests.find(r => r.id === this.rejectTarget.id)\n        if (request) {\n          request.status = 'REJECTED'\n          request.processedAt = new Date().toISOString()\n          request.rejectReason = this.rejectReason\n        }\n        \n        // 更新计数\n        this.updateFilterCounts()\n        \n        uni.hideLoading()\n        \n        uni.showToast({\n          title: '已拒绝申请',\n          icon: 'success'\n        })\n        \n        this.closeRejectDialog()\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('收到申请页面：拒绝申请失败:', error)\n        uni.showToast({\n          title: error.message || '操作失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 跳转到创建拼场\n    goToCreateSharing() {\n      uni.navigateTo({\n        url: '/pages/sharing/create'\n      })\n    },\n    \n    // 获取头像文字\n    getAvatarText(name) {\n      if (!name) return '?'\n      return name.charAt(name.length - 1)\n    },\n    \n    // 获取进度百分比\n    getProgressPercent(current, max) {\n      if (!max || max === 0) return 0\n      return Math.round((current / max) * 100)\n    },\n    \n    // 格式化活动时间\n    formatActivityTime(request) {\n      if (!request) return '--'\n      \n      const date = this.formatDate(request.bookingDate)\n      const timeSlot = this.formatTimeSlot(request.startTime, request.endTime)\n      \n      return `${date} ${timeSlot}`\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      if (!date) return '--'\n      return formatDate(date, 'MM-DD')\n    },\n    \n    // 格式化日期时间\n    formatDateTime(datetime) {\n      if (!datetime) return '--'\n      return formatDateTime(datetime, 'MM-DD HH:mm')\n    },\n    \n    // 格式化时间段\n    formatTimeSlot(startTime, endTime) {\n      if (!startTime && !endTime) {\n        return '时间未指定'\n      }\n      if (startTime && !endTime) {\n        return startTime\n      }\n      if (!startTime && endTime) {\n        return endTime\n      }\n      return `${startTime}-${endTime}`\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'PENDING': '待处理',\n        'APPROVED': '已同意',\n        'APPROVED_PENDING_PAYMENT': '已批准待支付',\n        'PAID': '拼场成功',\n        'REJECTED': '已拒绝',\n        'TIMEOUT_CANCELLED': '超时取消'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    \n    // 获取状态样式类\n    getStatusClass(status) {\n      const classMap = {\n        'PENDING': 'status-pending',\n        'APPROVED': 'status-approved',\n        'REJECTED': 'status-rejected'\n      }\n      return classMap[status] || 'status-unknown'\n    },\n    \n    // 获取空状态标题\n    getEmptyTitle() {\n      const titleMap = {\n        'all': '暂无申请记录',\n        'pending': '暂无待处理申请',\n        'approved': '暂无已同意申请',\n        'rejected': '暂无已拒绝申请'\n      }\n      return titleMap[this.currentFilter] || '暂无申请记录'\n    },\n    \n    // 获取空状态描述\n    getEmptyDesc() {\n      const descMap = {\n        'all': '还没有人申请您的拼场',\n        'pending': '暂时没有需要处理的申请',\n        'approved': '暂时没有同意的申请',\n        'rejected': '暂时没有拒绝的申请'\n      }\n      return descMap[this.currentFilter] || '还没有人申请您的拼场'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n// 导航栏\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 30rpx;\n  background-color: #ffffff;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  .nav-left {\n    width: 60rpx;\n    \n    .nav-icon {\n      font-size: 40rpx;\n      color: #333333;\n    }\n  }\n  \n  .nav-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333333;\n  }\n  \n  .nav-right {\n    width: 60rpx;\n  }\n}\n\n// 筛选标签\n.filter-tabs {\n  display: flex;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  .filter-tab {\n    display: flex;\n    align-items: center;\n    padding: 12rpx 20rpx;\n    margin-right: 20rpx;\n    border-radius: 20rpx;\n    background-color: #f5f5f5;\n    transition: all 0.3s ease;\n    \n    &:last-child {\n      margin-right: 0;\n    }\n    \n    &.active {\n      background-color: #ff6b35;\n      \n      .tab-text {\n        color: #ffffff;\n      }\n      \n      .tab-count {\n        background-color: rgba(255, 255, 255, 0.3);\n        color: #ffffff;\n      }\n    }\n    \n    .tab-text {\n      font-size: 26rpx;\n      color: #666666;\n      transition: color 0.3s ease;\n    }\n    \n    .tab-count {\n      font-size: 20rpx;\n      color: #ff6b35;\n      background-color: #fff7f0;\n      padding: 2rpx 8rpx;\n      border-radius: 10rpx;\n      margin-left: 8rpx;\n      min-width: 32rpx;\n      text-align: center;\n      transition: all 0.3s ease;\n    }\n  }\n}\n\n// 加载状态\n.loading-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 200rpx 0;\n  \n  text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n// 错误状态\n.error-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 200rpx 60rpx;\n  \n  .error-icon {\n    font-size: 120rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .error-text {\n    font-size: 28rpx;\n    color: #333333;\n    text-align: center;\n    margin-bottom: 40rpx;\n    line-height: 1.4;\n  }\n  \n  .retry-btn {\n    width: 200rpx;\n    height: 70rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 12rpx;\n    font-size: 26rpx;\n  }\n}\n\n// 内容区域\n.content {\n  padding: 20rpx;\n}\n\n// 申请列表\n.requests-list {\n  .request-item {\n    background-color: #ffffff;\n    border-radius: 16rpx;\n    padding: 30rpx;\n    margin-bottom: 20rpx;\n    \n    .sharing-info {\n      margin-bottom: 24rpx;\n      \n      .sharing-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-bottom: 16rpx;\n        \n        .venue-name {\n          font-size: 32rpx;\n          font-weight: bold;\n          color: #333333;\n        }\n        \n        .status-badge {\n          padding: 6rpx 12rpx;\n          border-radius: 12rpx;\n          \n          .status-text {\n            font-size: 22rpx;\n            font-weight: bold;\n          }\n          \n          &.status-pending {\n            background-color: #fff7e6;\n            .status-text { color: #fa8c16; }\n          }\n          \n          &.status-approved {\n            background-color: #f6ffed;\n            .status-text { color: #52c41a; }\n          }\n          \n          &.status-rejected {\n            background-color: #fff2f0;\n            .status-text { color: #ff4d4f; }\n          }\n        }\n      }\n      \n      .sharing-details {\n        margin-bottom: 16rpx;\n        \n        .team-name {\n          font-size: 28rpx;\n          color: #333333;\n          font-weight: bold;\n          display: block;\n          margin-bottom: 8rpx;\n        }\n        \n        .activity-time {\n          font-size: 24rpx;\n          color: #666666;\n          display: block;\n          margin-bottom: 6rpx;\n        }\n        \n        .price {\n          font-size: 24rpx;\n          color: #ff6b35;\n          font-weight: bold;\n        }\n      }\n      \n      .participants-progress {\n        .progress-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 8rpx;\n          \n          .progress-text {\n            font-size: 22rpx;\n            color: #666666;\n          }\n          \n          .progress-percent {\n            font-size: 20rpx;\n            color: #ff6b35;\n            font-weight: bold;\n          }\n        }\n        \n        .progress-bar {\n          height: 6rpx;\n          background-color: #f0f0f0;\n          border-radius: 3rpx;\n          overflow: hidden;\n          \n          .progress-fill {\n            height: 100%;\n            background-color: #ff6b35;\n            transition: width 0.3s ease;\n          }\n        }\n      }\n    }\n    \n    .applicant-info {\n      margin-bottom: 24rpx;\n      \n      .applicant-header {\n        display: flex;\n        align-items: center;\n        margin-bottom: 12rpx;\n        \n        .applicant-avatar {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 40rpx;\n          margin-right: 20rpx;\n          overflow: hidden;\n          background-color: #f0f0f0;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          \n          .avatar-img {\n            width: 100%;\n            height: 100%;\n          }\n          \n          .avatar-placeholder {\n            font-size: 28rpx;\n            color: #999999;\n            font-weight: bold;\n          }\n        }\n        \n        .applicant-details {\n          flex: 1;\n          \n          .applicant-name {\n            font-size: 28rpx;\n            color: #333333;\n            font-weight: bold;\n            display: block;\n            margin-bottom: 6rpx;\n          }\n          \n          .apply-time {\n            font-size: 22rpx;\n            color: #999999;\n          }\n        }\n      }\n      \n      .apply-message {\n        background-color: #f8f9fa;\n        padding: 16rpx;\n        border-radius: 12rpx;\n        \n        .message-label {\n          font-size: 22rpx;\n          color: #666666;\n          display: block;\n          margin-bottom: 8rpx;\n        }\n        \n        .message-content {\n          font-size: 26rpx;\n          color: #333333;\n          line-height: 1.4;\n        }\n      }\n    }\n    \n    .request-actions {\n      .pending-actions {\n        display: flex;\n        justify-content: flex-end;\n        gap: 20rpx;\n        \n        .action-btn {\n          padding: 12rpx 24rpx;\n          border: none;\n          border-radius: 20rpx;\n          font-size: 24rpx;\n          \n          &.reject-btn {\n            background-color: #fff2f0;\n            color: #ff4d4f;\n          }\n          \n          &.approve-btn {\n            background-color: #ff6b35;\n            color: #ffffff;\n          }\n        }\n      }\n      \n      .processed-actions {\n        .processed-text {\n          font-size: 24rpx;\n          color: #52c41a;\n          font-weight: bold;\n          display: block;\n          margin-bottom: 6rpx;\n          \n          &.rejected {\n            color: #ff4d4f;\n          }\n        }\n        \n        .process-time {\n          font-size: 22rpx;\n          color: #999999;\n          display: block;\n          margin-bottom: 6rpx;\n        }\n        \n        .reject-reason {\n          font-size: 22rpx;\n          color: #666666;\n        }\n      }\n    }\n  }\n}\n\n// 空状态\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 200rpx 60rpx;\n  \n  .empty-icon {\n    font-size: 120rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .empty-title {\n    font-size: 32rpx;\n    color: #333333;\n    font-weight: bold;\n    margin-bottom: 16rpx;\n  }\n  \n  .empty-desc {\n    font-size: 26rpx;\n    color: #999999;\n    text-align: center;\n    line-height: 1.4;\n    margin-bottom: 40rpx;\n  }\n  \n  .create-btn {\n    width: 200rpx;\n    height: 70rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 12rpx;\n    font-size: 26rpx;\n  }\n}\n\n// 拒绝对话框\n.reject-dialog {\n  background-color: #ffffff;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 40rpx 30rpx;\n  \n  .dialog-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 30rpx;\n    \n    .dialog-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333333;\n    }\n    \n    .close-btn {\n      font-size: 32rpx;\n      color: #999999;\n      padding: 10rpx;\n    }\n  }\n  \n  .dialog-content {\n    margin-bottom: 40rpx;\n    \n    .applicant-info {\n      margin-bottom: 30rpx;\n      \n      .applicant-name {\n        font-size: 28rpx;\n        color: #333333;\n      }\n    }\n    \n    .reason-section {\n      .reason-label {\n        font-size: 26rpx;\n        color: #333333;\n        display: block;\n        margin-bottom: 16rpx;\n      }\n      \n      .reason-input {\n        width: 100%;\n        min-height: 200rpx;\n        padding: 20rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 12rpx;\n        font-size: 26rpx;\n        color: #333333;\n        background-color: #ffffff;\n        box-sizing: border-box;\n        resize: none;\n      }\n      \n      .char-count {\n        font-size: 22rpx;\n        color: #999999;\n        text-align: right;\n        display: block;\n        margin-top: 8rpx;\n      }\n    }\n  }\n  \n  .dialog-actions {\n    display: flex;\n    gap: 20rpx;\n    \n    .cancel-btn {\n      flex: 1;\n      height: 80rpx;\n      background-color: #f5f5f5;\n      color: #333333;\n      border: none;\n      border-radius: 12rpx;\n      font-size: 28rpx;\n    }\n    \n    .confirm-btn {\n      flex: 1;\n      height: 80rpx;\n      background-color: #ff4d4f;\n      color: #ffffff;\n      border: none;\n      border-radius: 12rpx;\n      font-size: 28rpx;\n    }\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/sharing/received.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapState", "uni", "mapActions", "formatDate", "formatDateTime"], "mappings": ";;;AA0MA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA,MACf,OAAO;AAAA,MACP,eAAe;AAAA,MACf,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,CAAE;AAAA,MACZ,YAAY;AAAA,QACV,EAAE,OAAO,MAAM,OAAO,OAAO,OAAO,EAAG;AAAA,QACvC,EAAE,OAAO,OAAO,OAAO,WAAW,OAAO,EAAG;AAAA,QAC5C,EAAE,OAAO,OAAO,OAAO,YAAY,OAAO,EAAG;AAAA,QAC7C,EAAE,OAAO,OAAO,OAAO,YAAY,OAAO,EAAE;AAAA,MAC9C;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,GAAGA,uBAAS,WAAW,CAAC,SAAS,CAAC;AAAA,IAClC,GAAGA,uBAAS,QAAQ,CAAC,UAAU,CAAC;AAAA;AAAA,IAGhC,mBAAmB;AACjB,UAAI,KAAK,kBAAkB,OAAO;AAChC,eAAO,KAAK;AAAA,MACd;AAEA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAEA,aAAO,KAAK,SAAS;AAAA,QAAO,aAC1B,QAAQ,WAAW,UAAU,KAAK,aAAa;AAAA,MACjD;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,SAAS;AAEP,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,oBAAoB;AAClB,SAAK,eAAe,QAAQ,MAAM;AAChCC,oBAAAA,MAAI,oBAAoB;AAAA,KACzB;AAAA,EACF;AAAA,EAED,SAAS;AAAA,IACP,GAAGC,cAAAA,WAAW,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,IACF,CAAC;AAAA;AAAA,IAGD,SAAS;AACPD,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,MAAM,eAAe;AACnB,UAAI;AACF,aAAK,QAAQ;AACbA,sBAAAA,MAAA,MAAA,OAAA,qCAAY,iBAAiB;AAG7B,cAAM,WAAW,MAAM,KAAK,2BAA2B;AACvD,aAAK,WAAW,YAAY,CAAC;AAG7B,aAAK,mBAAmB;AAExBA,sBAAY,MAAA,MAAA,OAAA,qCAAA,oBAAoB,KAAK,QAAQ;AAAA,MAE7C,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,qCAAA,oBAAoB,KAAK;AACvC,aAAK,QAAQ,MAAM,WAAW;AAC9B,aAAK,WAAW,CAAC;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAKD,qBAAqB;AACnB,YAAM,SAAS;AAAA,QACb,KAAK,KAAK,SAAS;AAAA,QACnB,SAAS,KAAK,SAAS,OAAO,OAAK,EAAE,WAAW,SAAS,EAAE;AAAA,QAC3D,UAAU,KAAK,SAAS,OAAO,OAAK,EAAE,WAAW,UAAU,EAAE;AAAA,QAC7D,UAAU,KAAK,SAAS,OAAO,OAAK,EAAE,WAAW,UAAU,EAAE;AAAA,MAC/D;AAEA,WAAK,WAAW,QAAQ,SAAO;AAC7B,YAAI,QAAQ,OAAO,IAAI,KAAK,KAAK;AAAA,OAClC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,QAAQ;AACnB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,mBAAmB,SAAS;AAC1B,WAAK,gBAAgB;AACrB,WAAK,MAAM,aAAa,KAAK;AAAA,IAC9B;AAAA;AAAA,IAGD,iBAAiB,SAAS;AACxB,WAAK,eAAe;AACpB,WAAK,eAAe;AACpB,WAAK,MAAM,YAAY,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,eAAe;AACpB,WAAK,eAAe;AACpB,WAAK,MAAM,YAAY,MAAM;AAAA,IAC9B;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrB,UAAI,CAAC,KAAK;AAAe;AAEzB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,KAAK,sBAAsB;AAAA,UAC/B,WAAW,KAAK,cAAc;AAAA,UAC9B,QAAQ;AAAA,SACT;AAGD,cAAM,UAAU,KAAK,SAAS,KAAK,OAAK,EAAE,OAAO,KAAK,cAAc,EAAE;AACtE,YAAI,SAAS;AACX,kBAAQ,SAAS;AACjB,kBAAQ,eAAc,oBAAI,KAAI,GAAG,YAAY;AAAA,QAC/C;AAGA,aAAK,mBAAmB;AAExBA,sBAAAA,MAAI,YAAY;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAED,aAAK,gBAAgB;AAAA,MAErB,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,qCAAc,kBAAkB,KAAK;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI,CAAC,KAAK;AAAc;AAExB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,KAAK,sBAAsB;AAAA,UAC/B,WAAW,KAAK,aAAa;AAAA,UAC7B,QAAQ;AAAA,UACR,QAAQ,KAAK;AAAA,SACd;AAGD,cAAM,UAAU,KAAK,SAAS,KAAK,OAAK,EAAE,OAAO,KAAK,aAAa,EAAE;AACrE,YAAI,SAAS;AACX,kBAAQ,SAAS;AACjB,kBAAQ,eAAc,oBAAI,KAAI,GAAG,YAAY;AAC7C,kBAAQ,eAAe,KAAK;AAAA,QAC9B;AAGA,aAAK,mBAAmB;AAExBA,sBAAAA,MAAI,YAAY;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAED,aAAK,kBAAkB;AAAA,MAEvB,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,qCAAc,kBAAkB,KAAK;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,MAAM;AAClB,UAAI,CAAC;AAAM,eAAO;AAClB,aAAO,KAAK,OAAO,KAAK,SAAS,CAAC;AAAA,IACnC;AAAA;AAAA,IAGD,mBAAmB,SAAS,KAAK;AAC/B,UAAI,CAAC,OAAO,QAAQ;AAAG,eAAO;AAC9B,aAAO,KAAK,MAAO,UAAU,MAAO,GAAG;AAAA,IACxC;AAAA;AAAA,IAGD,mBAAmB,SAAS;AAC1B,UAAI,CAAC;AAAS,eAAO;AAErB,YAAM,OAAO,KAAK,WAAW,QAAQ,WAAW;AAChD,YAAM,WAAW,KAAK,eAAe,QAAQ,WAAW,QAAQ,OAAO;AAEvE,aAAO,GAAG,IAAI,IAAI,QAAQ;AAAA,IAC3B;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,UAAI,CAAC;AAAM,eAAO;AAClB,aAAOE,cAAU,WAAC,MAAM,OAAO;AAAA,IAChC;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AACtB,aAAOC,cAAc,eAAC,UAAU,aAAa;AAAA,IAC9C;AAAA;AAAA,IAGD,eAAe,WAAW,SAAS;AACjC,UAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,eAAO;AAAA,MACT;AACA,UAAI,aAAa,CAAC,SAAS;AACzB,eAAO;AAAA,MACT;AACA,UAAI,CAAC,aAAa,SAAS;AACzB,eAAO;AAAA,MACT;AACA,aAAO,GAAG,SAAS,IAAI,OAAO;AAAA,IAC/B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,4BAA4B;AAAA,QAC5B,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,qBAAqB;AAAA,MACvB;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AACA,aAAO,SAAS,MAAM,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,gBAAgB;AACd,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AACA,aAAO,SAAS,KAAK,aAAa,KAAK;AAAA,IACxC;AAAA;AAAA,IAGD,eAAe;AACb,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AACA,aAAO,QAAQ,KAAK,aAAa,KAAK;AAAA,IACxC;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpgBA,GAAG,WAAW,eAAe;"}