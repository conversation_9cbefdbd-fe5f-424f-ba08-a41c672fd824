{"version": 3, "file": "detail.js", "sources": ["pages/sharing/detail.vue", "pages/sharing/detail.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-state\">\n      <text>加载中...</text>\n    </view>\n    \n    <!-- 拼场详情 -->\n    <view v-else-if=\"sharingOrderDetail\" class=\"sharing-detail\">\n      <!-- 拼场状态 -->\n      <view class=\"status-section\">\n        <view class=\"status-badge\" :class=\"getStatusClass(sharingOrderDetail.status)\">\n          {{ getStatusText(sharingOrderDetail.status) }}\n        </view>\n\n        <!-- 倒计时显示 -->\n        <CountdownTimer\n          v-if=\"shouldShowCountdown(sharingOrderDetail)\"\n          :order=\"sharingOrderDetail\"\n          label=\"距离自动取消\"\n          @expired=\"onCountdownExpired\"\n        />\n      </view>\n      \n      <!-- 队伍信息 -->\n      <view class=\"team-section\">\n        <view class=\"section-title\">队伍信息</view>\n        <view class=\"team-card\">\n          <view class=\"team-header\">\n            <text class=\"team-name\">{{ sharingOrderDetail.teamName || '未命名队伍' }}</text>\n            <text class=\"team-creator\">队长：{{ sharingOrderDetail.creatorUsername || '未知' }}</text>\n          </view>\n          \n          <view class=\"participants-info\">\n            <view class=\"participants-count\">\n              <text class=\"count-text\">参与球队：{{ sharingOrderDetail.currentParticipants || 0 }}/{{ sharingOrderDetail.maxParticipants || 2 }}支</text>\n            </view>\n            <view class=\"progress-bar\">\n              <view \n                class=\"progress-fill\" \n                :style=\"{ width: getProgressWidth() + '%' }\"\n              ></view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 场馆信息 -->\n      <view class=\"venue-section\">\n        <view class=\"section-title\">场馆信息</view>\n        <view class=\"venue-card\" @click=\"navigateToVenue\">\n          <view class=\"venue-header\">\n            <text class=\"venue-name\">{{ sharingOrderDetail.venueName || '未知场馆' }}</text>\n            <text class=\"venue-arrow\">></text>\n          </view>\n          <view class=\"venue-info\">\n            <text class=\"venue-location\">📍 {{ sharingOrderDetail.venueLocation || '位置未知' }}</text>\n            <view class=\"venue-rating\">\n              <text class=\"rating-text\">⭐ {{ sharingOrderDetail.venueRating || '暂无评分' }}</text>\n              <text class=\"review-count\">({{ sharingOrderDetail.venueReviewCount || 0 }}条评价)</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 活动信息 -->\n      <view class=\"activity-section\">\n        <view class=\"section-title\">活动信息</view>\n        <view class=\"activity-card\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">活动时间</text>\n            <text class=\"info-value\">{{ formatActivityTime() }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">每队费用</text>\n            <text class=\"info-value price\">¥{{ getPerTeamPrice() }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">总费用</text>\n            <text class=\"info-value\">¥{{ getTotalPrice() }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">订单号</text>\n            <text class=\"info-value\">{{ sharingOrderDetail.orderNo || '无' }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">创建时间</text>\n            <text class=\"info-value\">{{ formatDateTime(sharingOrderDetail.createdAt) }}</text>\n          </view>\n          <view v-if=\"sharingOrderDetail.description\" class=\"description-item\">\n            <text class=\"info-label\">活动描述</text>\n            <text class=\"description-text\">{{ sharingOrderDetail.description }}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 参与者列表 -->\n      <view class=\"participants-section\">\n        <view class=\"section-title\">参与队伍 ({{ participants.length }}支)</view>\n        <view class=\"participants-list\">\n          <view \n            v-for=\"participant in participants\" \n            :key=\"participant.id || participant.username\" \n            class=\"participant-item\"\n          >\n            <view class=\"participant-info\">\n              <image \n                :src=\"participant.avatar || '/static/images/default-avatar.svg'\" \n                class=\"participant-avatar\"\n              />\n              <view class=\"participant-details\">\n                <text class=\"participant-name\">{{ participant.nickname || participant.username || '未知用户' }}</text>\n                <text class=\"participant-role\">{{ getParticipantRole(participant) }}</text>\n              </view>\n            </view>\n            \n            <!-- 队长可以移除队员 -->\n            <view \n              v-if=\"isOrganizer && !isParticipantOrganizer(participant)\" \n              class=\"remove-btn\"\n              @click=\"removeParticipant(participant)\"\n            >\n              移除\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系信息（仅参与者可见） -->\n      <view v-if=\"showContactInfo\" class=\"contact-section\">\n        <view class=\"section-title\">联系信息</view>\n        <view class=\"contact-card\">\n          <view v-if=\"sharingOrderDetail.contactInfo?.phone\" class=\"contact-item\">\n            <text class=\"contact-label\">联系电话</text>\n            <text class=\"contact-value\" @click=\"makePhoneCall\">{{ sharingOrderDetail.contactInfo.phone }}</text>\n          </view>\n          <view v-if=\"sharingOrderDetail.contactInfo?.wechat\" class=\"contact-item\">\n            <text class=\"contact-label\">微信号</text>\n            <text class=\"contact-value\">{{ sharingOrderDetail.contactInfo.wechat }}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 活动规则 -->\n      <view class=\"rules-section\">\n        <view class=\"section-title\">活动规则</view>\n        <view class=\"rules-card\">\n          <view class=\"rule-item\">\n            <text class=\"rule-label\">自动通过申请</text>\n            <text class=\"rule-value\">{{ sharingOrderDetail.autoApprove ? '是' : '否' }}</text>\n          </view>\n          <view class=\"rule-item\">\n            <text class=\"rule-label\">允许中途退出</text>\n            <text class=\"rule-value\">{{ sharingOrderDetail.allowExit ? '是' : '否' }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else class=\"error-state\">\n      <text class=\"error-icon\">❌</text>\n      <text class=\"error-text\">拼场信息加载失败</text>\n      <button class=\"retry-btn\" @click=\"loadSharingDetail\">重试</button>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view v-if=\"sharingOrderDetail\" class=\"bottom-actions\">\n      <!-- 非参与者 -->\n      <view v-if=\"!isParticipant\" class=\"action-group\">\n        <button \n          v-if=\"canJoin\" \n          class=\"action-btn primary\"\n          @click=\"joinSharing\"\n        >\n          立即加入\n        </button>\n        <button v-else class=\"action-btn disabled\">\n          {{ getJoinButtonText() }}\n        </button>\n      </view>\n      \n      <!-- 参与者（非队长） -->\n      <view v-else-if=\"!isOrganizer\" class=\"action-group\">\n        <button class=\"action-btn secondary\" @click=\"contactOrganizer\">\n          联系队长\n        </button>\n        <button \n          v-if=\"canExit\" \n          class=\"action-btn danger\"\n          @click=\"exitSharing\"\n        >\n          退出拼场\n        </button>\n      </view>\n      \n      <!-- 队长 -->\n      <view v-else class=\"action-group\">\n        <button class=\"action-btn secondary\" @click=\"manageSharing\">\n          管理拼场\n        </button>\n        <button class=\"action-btn danger\" @click=\"cancelSharing\">\n          取消拼场\n        </button>\n      </view>\n    </view>\n    \n    <!-- 申请拼场弹窗 -->\n    <uni-popup ref=\"applyPopup\" type=\"bottom\">\n      <view class=\"apply-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">申请加入拼场</text>\n          <text class=\"close-btn\" @click=\"closeApplyModal\">✕</text>\n        </view>\n        \n        <view class=\"modal-content\">\n          <view class=\"form-item\">\n            <text class=\"form-label\">队伍名称</text>\n            <input \n              v-model=\"applyForm.teamName\"\n              class=\"form-input\"\n              placeholder=\"请输入队伍名称（可选）\"\n              maxlength=\"20\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">联系方式 <text class=\"required\">*</text></text>\n            <input \n              v-model=\"applyForm.contactInfo\"\n              class=\"form-input\"\n              placeholder=\"请输入手机号或微信号\"\n              maxlength=\"50\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">申请说明</text>\n            <text class=\"form-hint\">您将代表一支球队申请加入此拼场</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">申请留言</text>\n            <textarea \n              v-model=\"applyForm.message\"\n              class=\"form-textarea\"\n              placeholder=\"请输入申请留言（可选）\"\n              maxlength=\"200\"\n            ></textarea>\n            <text class=\"char-count\">{{ applyForm.message.length }}/200</text>\n          </view>\n        </view>\n        \n        <view class=\"modal-actions\">\n          <button class=\"modal-btn cancel-btn\" @click=\"closeApplyModal\">\n            取消\n          </button>\n          <button \n            class=\"modal-btn confirm-btn\" \n            :disabled=\"!canSubmitApplication\"\n            @click=\"submitApplication\"\n          >\n            提交申请\n          </button>\n        </view>\n      </view>\n    </uni-popup>\n    \n    <!-- 确认弹窗 -->\n    <uni-popup ref=\"confirmPopup\" type=\"center\">\n      <view class=\"confirm-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">{{ confirmData.title }}</text>\n        </view>\n        <view class=\"modal-content\">\n          <text class=\"modal-text\">{{ confirmData.content }}</text>\n        </view>\n        <view class=\"modal-actions\">\n          <button class=\"modal-btn cancel-btn\" @click=\"closeConfirmModal\">\n            取消\n          </button>\n          <button class=\"modal-btn confirm-btn\" @click=\"confirmAction\">\n            确认\n          </button>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { mapState, mapActions, mapGetters } from 'vuex'\nimport { formatDate, formatDateTime } from '@/utils/helpers.js'\nimport CountdownTimer from '@/components/CountdownTimer.vue'\nimport { shouldShowCountdown } from '@/utils/countdown.js'\n\nexport default {\n  name: 'SharingDetail',\n\n  components: {\n    CountdownTimer\n  },\n\n  data() {\n    return {\n      sharingId: null,\n      confirmData: {\n        title: '',\n        content: '',\n        action: null\n      },\n      applyForm: {\n        teamName: '',\n        contactInfo: '',\n        message: ''\n      }\n    }\n  },\n  \n  computed: {\n    ...mapState('sharing', ['sharingOrderDetail', 'loading']),\n    ...mapState('user', ['userInfo']),\n    \n    // 参与者列表（包含队长）\n    participants() {\n      if (!this.sharingOrderDetail) return []\n      \n      const participants = []\n      \n      // 添加队长\n      participants.push({\n        username: this.sharingOrderDetail.creatorUsername,\n        nickname: this.sharingOrderDetail.creatorUsername,\n        role: 'organizer',\n        isOrganizer: true\n      })\n      \n      // 添加其他参与者（如果有的话）\n      if (this.sharingOrderDetail.participants && Array.isArray(this.sharingOrderDetail.participants)) {\n        this.sharingOrderDetail.participants.forEach(participant => {\n          if (participant.username !== this.sharingOrderDetail.creatorUsername) {\n            participants.push({\n              ...participant,\n              role: 'member',\n              isOrganizer: false\n            })\n          }\n        })\n      }\n      \n      return participants\n    },\n    \n    // 是否为参与者\n    isParticipant() {\n      if (!this.userInfo || !this.sharingOrderDetail) return false\n      return this.participants.some(p => p.username === this.userInfo.username)\n    },\n    \n    // 是否为队长\n    isOrganizer() {\n      if (!this.userInfo || !this.sharingOrderDetail) return false\n      return this.sharingOrderDetail.creatorUsername === this.userInfo.username\n    },\n    \n    // 是否可以加入\n    canJoin() {\n      if (!this.sharingOrderDetail) return false\n      return this.sharingOrderDetail.status === 'OPEN' && \n             (this.sharingOrderDetail.currentParticipants || 0) < (this.sharingOrderDetail.maxParticipants || 0) &&\n             !this.isParticipant\n    },\n    \n    // 是否可以退出\n    canExit() {\n      if (!this.sharingOrderDetail) return false\n      return this.sharingOrderDetail.allowExit && \n             this.sharingOrderDetail.status === 'OPEN' &&\n             this.isParticipant && \n             !this.isOrganizer\n    },\n    \n    // 是否显示联系信息\n    showContactInfo() {\n      return this.isParticipant && this.sharingOrderDetail?.contactInfo\n    },\n    \n    // 剩余名额（两支球队模式）\n    remainingSlots() {\n      if (!this.sharingOrderDetail) return 0\n      return 2 - (this.sharingOrderDetail.currentParticipants || 0)\n    },\n    \n    // 是否可以提交申请\n    canSubmitApplication() {\n      return this.applyForm.contactInfo.trim().length > 0\n    }\n  },\n  \n  onLoad(options) {\n    console.log('拼场详情页面：接收到的参数:', options)\n    console.log('拼场详情页面：options.id:', options.id)\n\n    if (options.id) {\n      this.sharingId = options.id\n      console.log('拼场详情页面：设置sharingId为:', this.sharingId)\n      this.loadSharingDetail()\n    } else {\n      console.error('拼场详情页面：未接收到id参数')\n      uni.showToast({\n        title: '订单ID缺失',\n        icon: 'error'\n      })\n    }\n  },\n  \n  onShow() {\n    if (this.sharingId) {\n      // 每次显示页面时强制刷新数据，确保显示最新状态\n      this.loadSharingDetail()\n    }\n  },\n  \n  onPullDownRefresh() {\n    this.loadSharingDetail()\n  },\n  \n  methods: {\n    ...mapActions('sharing', [\n      'getSharingOrderDetail',\n      'applySharingOrder',\n      'exitSharingOrder',\n      'cancelSharingOrder',\n      'removeParticipantFromSharing'\n    ]),\n    \n    // 加载拼场详情\n    async loadSharingDetail() {\n      try {\n        console.log('拼场详情页面：开始加载详情，ID:', this.sharingId)\n        await this.getSharingOrderDetail(this.sharingId)\n        console.log('拼场详情页面：加载详情完成:', this.sharingOrderDetail)\n        // 强制更新视图，确保显示最新状态\n        this.$forceUpdate()\n        uni.stopPullDownRefresh()\n      } catch (error) {\n        uni.stopPullDownRefresh()\n        console.error('拼场详情页面：加载详情失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 跳转到场馆详情\n    navigateToVenue() {\n      if (this.sharingOrderDetail?.venueId) {\n        uni.navigateTo({\n          url: `/pages/venue/detail?id=${this.sharingOrderDetail.venueId}`\n        })\n      }\n    },\n    \n    // 加入拼场\n    joinSharing() {\n      // 初始化申请表单\n      this.initApplyForm()\n      // 打开申请弹窗\n      this.$refs.applyPopup.open()\n    },\n    \n    // 退出拼场\n    async exitSharing() {\n      this.confirmData = {\n        title: '退出拼场',\n        content: '确认退出此拼场活动吗？退出后需要重新申请才能加入。',\n        action: 'exit'\n      }\n      this.$refs.confirmPopup.open()\n    },\n    \n    // 取消拼场\n    async cancelSharing() {\n      this.confirmData = {\n        title: '取消拼场',\n        content: '确认取消此拼场活动吗？取消后所有参与者将收到通知。',\n        action: 'cancel'\n      }\n      this.$refs.confirmPopup.open()\n    },\n    \n    // 移除参与者\n    async removeParticipant(participant) {\n      this.confirmData = {\n        title: '移除参与者',\n        content: `确认移除「${participant.nickname || participant.username}」吗？`,\n        action: 'remove',\n        data: participant\n      }\n      this.$refs.confirmPopup.open()\n    },\n    \n    // 联系队长\n    contactOrganizer() {\n      if (this.sharingOrderDetail?.contactInfo?.phone) {\n        this.makePhoneCall()\n      } else {\n        uni.showToast({\n          title: '暂无联系方式',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 管理拼场\n    manageSharing() {\n      uni.navigateTo({\n        url: `/pages/sharing/manage?id=${this.sharingId}`\n      })\n    },\n    \n    // 拨打电话\n    makePhoneCall() {\n      const phone = this.sharingOrderDetail?.contactInfo?.phone\n      if (phone) {\n        uni.makePhoneCall({\n          phoneNumber: phone\n        })\n      }\n    },\n    \n    // 关闭确认弹窗\n    closeConfirmModal() {\n      this.$refs.confirmPopup.close()\n    },\n    \n    // 初始化申请表单\n    initApplyForm() {\n      this.applyForm = {\n        teamName: '', // 队名默认为空，让用户自己填写\n        contactInfo: this.userInfo?.phone || this.userInfo?.mobile || '', // 联系方式默认为手机号\n        message: ''\n      }\n    },\n    \n    // 关闭申请弹窗\n    closeApplyModal() {\n      this.$refs.applyPopup.close()\n    },\n    \n    // 注释：移除了增减参与人数的方法，因为现在是固定的两支球队模式\n    \n    // 提交申请\n    async submitApplication() {\n      if (!this.canSubmitApplication) {\n        uni.showToast({\n          title: '请填写完整信息',\n          icon: 'none'\n        })\n        return\n      }\n      \n      try {\n        uni.showLoading({ title: '提交中...' })\n        \n        const applicationData = {\n          teamName: this.applyForm.teamName.trim(),\n          contactInfo: this.applyForm.contactInfo.trim(),\n          message: this.applyForm.message.trim()\n        }\n        \n        const response = await this.applySharingOrder({ orderId: this.sharingId, data: applicationData })\n\n        uni.hideLoading()\n\n        this.closeApplyModal()\n\n        // 检查申请是否被自动通过\n        if (response && response.data &&\n            (response.data.status === 'APPROVED_PENDING_PAYMENT' || response.data.status === 'APPROVED')) {\n          // 自动通过，提示用户并引导支付\n          uni.showModal({\n            title: '申请已通过',\n            content: '您的拼场申请已自动通过！请在30分钟内完成支付以确认参与。',\n            showCancel: false,\n            confirmText: '去支付',\n            success: () => {\n              // 跳转到支付页面，使用虚拟订单ID\n              uni.navigateTo({\n                url: `/pages/payment/index?orderId=${-response.data.id}&type=sharing&from=sharing-detail`\n              })\n            }\n          })\n        } else {\n          // 普通提交，显示等待审核提示\n          uni.showToast({\n            title: response?.message || '申请提交成功，等待审核',\n            icon: 'success',\n            duration: 2000\n          })\n        }\n\n        // 刷新详情\n        await this.loadSharingDetail()\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('提交申请失败:', error)\n        uni.showToast({\n          title: error.message || '提交失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 确认操作\n    async confirmAction() {\n      try {\n        uni.showLoading({ title: '处理中...' })\n        \n        switch (this.confirmData.action) {\n          case 'exit':\n            await this.exitSharingOrder(this.sharingId)\n            uni.showToast({ title: '退出成功', icon: 'success' })\n            break\n            \n          case 'cancel':\n            await this.cancelSharingOrder(this.sharingId)\n            uni.showToast({ title: '取消成功', icon: 'success' })\n\n            // 取消拼场后跳转回上一页，因为当前页面已无效\n            setTimeout(() => {\n              uni.navigateBack({\n                delta: 1\n              })\n            }, 1500)\n            return // 不需要刷新详情，直接返回\n            \n          case 'remove':\n            await this.removeParticipantFromSharing({\n              sharingId: this.sharingId,\n              participantId: this.confirmData.data.id || this.confirmData.data.username\n            })\n            uni.showToast({ title: '移除成功', icon: 'success' })\n            break\n        }\n        \n        uni.hideLoading()\n        this.closeConfirmModal()\n        \n        // 刷新详情\n        await this.loadSharingDetail()\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('操作失败:', error)\n        uni.showToast({\n          title: error.message || '操作失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 格式化活动时间（参考预约订单的实现）\n    formatActivityTime() {\n      if (!this.sharingOrderDetail) return '--'\n      \n      const bookingDate = this.sharingOrderDetail.bookingDate || this.sharingOrderDetail.date\n      const startTime = this.sharingOrderDetail.startTime || this.sharingOrderDetail.bookingStartTime\n      const endTime = this.sharingOrderDetail.endTime || this.sharingOrderDetail.bookingEndTime\n      \n      if (!bookingDate) {\n        return '时间未知'\n      }\n      \n      const date = this.formatDate(bookingDate)\n      \n      if (!startTime || !endTime) {\n        return `${date} 时间待定`\n      }\n      \n      // 格式化时间显示（去掉秒数）\n      const formatTime = (timeStr) => {\n        if (!timeStr) return ''\n        // 确保时间格式正确，后端返回的格式可能是HH:mm\n        if (typeof timeStr === 'string') {\n          // 如果是HH:mm格式，直接返回\n          if (timeStr.length === 5 && timeStr.includes(':')) {\n            return timeStr\n          }\n          // 如果是更长的格式，截取前5个字符（HH:mm）\n          if (timeStr.length > 5 && timeStr.includes(':')) {\n            return timeStr.substring(0, 5)\n          }\n          return timeStr\n        }\n        return ''\n      }\n      \n      const formattedStart = formatTime(startTime)\n      const formattedEnd = formatTime(endTime)\n      \n      console.log('⏰ 开始时间:', formattedStart)\n      console.log('⏰ 结束时间:', formattedEnd)\n      \n      // 直接显示完整时间段\n      return `${date} ${formattedStart}-${formattedEnd}`\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      if (!date) return '--'\n      try {\n        const dateObj = new Date(date)\n        if (isNaN(dateObj.getTime())) return '--'\n        const year = dateObj.getFullYear()\n        const month = String(dateObj.getMonth() + 1).padStart(2, '0')\n        const day = String(dateObj.getDate()).padStart(2, '0')\n        return `${year}-${month}-${day}`\n      } catch (error) {\n        console.error('日期格式化错误:', error)\n        return '--'\n      }\n    },\n    \n    // 格式化时间\n    formatDateTime(datetime) {\n      if (!datetime) return '--'\n      try {\n        // 处理iOS兼容性问题：将空格分隔的日期时间格式转换为T分隔的ISO格式\n        let dateStr = datetime\n        if (typeof dateStr === 'string' && dateStr.includes(' ') && !dateStr.includes('T')) {\n          dateStr = dateStr.replace(' ', 'T')\n        }\n        \n        const date = new Date(dateStr)\n        if (isNaN(date.getTime())) return '--'\n        const year = date.getFullYear()\n        const month = String(date.getMonth() + 1).padStart(2, '0')\n        const day = String(date.getDate()).padStart(2, '0')\n        const hour = String(date.getHours()).padStart(2, '0')\n        const minute = String(date.getMinutes()).padStart(2, '0')\n        return `${year}-${month}-${day} ${hour}:${minute}`\n      } catch (error) {\n        console.error('时间格式化错误:', error)\n        return '--'\n      }\n    },\n    \n    // 格式化时间段\n    formatTimeSlot(startTime, endTime) {\n      if (!startTime && !endTime) {\n        return '时间未指定'\n      }\n      if (startTime && !endTime) {\n        return startTime\n      }\n      if (!startTime && endTime) {\n        return endTime\n      }\n      \n      // 格式化时间显示（去掉秒数）\n      const formatTime = (timeStr) => {\n        if (!timeStr) return ''\n        // 确保时间格式正确，后端返回的格式可能是HH:mm\n        if (typeof timeStr === 'string') {\n          // 如果是HH:mm格式，直接返回\n          if (timeStr.length === 5 && timeStr.includes(':')) {\n            return timeStr\n          }\n          // 如果是更长的格式，截取前5个字符（HH:mm）\n          if (timeStr.length > 5 && timeStr.includes(':')) {\n            return timeStr.substring(0, 5)\n          }\n          return timeStr\n        }\n        return ''\n      }\n      \n      const formattedStart = formatTime(startTime)\n      const formattedEnd = formatTime(endTime)\n      \n      console.log('格式化时间段:', formattedStart, '-', formattedEnd)\n      \n      return `${formattedStart}-${formattedEnd}`\n    },\n    \n    // 获取总费用\n    getTotalPrice() {\n      if (!this.sharingOrderDetail) return '0.00'\n      \n      // 优先使用后端返回的totalPrice\n      if (this.sharingOrderDetail.totalPrice) {\n        return this.formatPrice(this.sharingOrderDetail.totalPrice)\n      }\n      \n      // 如果没有totalPrice，则根据每队价格计算（每队价格 * 2）\n      if (this.sharingOrderDetail.pricePerPerson) {\n        // pricePerPerson已经是每队的价格，总价 = 每队价格 * 2\n        const perTeamPrice = Number(this.sharingOrderDetail.pricePerPerson)\n        return this.formatPrice(perTeamPrice * 2)\n      }\n      \n      // 兜底使用price字段\n      const price = this.sharingOrderDetail.price || 0\n      return this.formatPrice(price)\n    },\n    \n    // 格式化价格显示\n    formatPrice(price) {\n      if (!price && price !== 0) return '0.00'\n      const numPrice = Number(price)\n      if (isNaN(numPrice)) return '0.00'\n      return numPrice.toFixed(2)\n    },\n    \n    // 获取进度条宽度\n    getProgressWidth() {\n      if (!this.sharingOrderDetail) return 0\n      const current = this.sharingOrderDetail.currentParticipants || 0\n      const max = this.sharingOrderDetail.maxParticipants || 2\n      return Math.min((current / max) * 100, 100)\n    },\n\n    // 获取每队费用\n    getPerTeamPrice() {\n      if (!this.sharingOrderDetail) return '0.00'\n      \n      // 优先使用后端返回的perTeamPrice字段\n      if (this.sharingOrderDetail.perTeamPrice) {\n        return this.formatPrice(this.sharingOrderDetail.perTeamPrice)\n      }\n      \n      // 其次使用后端返回的totalPrice除以2（因为拼场只有两队）\n      if (this.sharingOrderDetail.totalPrice) {\n        return this.formatPrice(this.sharingOrderDetail.totalPrice / 2)\n      }\n      \n      // 如果有pricePerPerson，直接使用（pricePerPerson已经是每队费用）\n      if (this.sharingOrderDetail.pricePerPerson) {\n        return this.formatPrice(this.sharingOrderDetail.pricePerPerson)\n      }\n      \n      // 如果没有必要的数据，则使用price字段除以2\n      const price = this.sharingOrderDetail.price || 0\n      return this.formatPrice(price / 2)\n    },\n    \n    // 获取参与者角色\n    getParticipantRole(participant) {\n      if (participant.isOrganizer || participant.role === 'organizer') {\n        return '队长'\n      }\n      return '队员'\n    },\n    \n    // 判断参与者是否为队长\n    isParticipantOrganizer(participant) {\n      return participant.isOrganizer || participant.role === 'organizer' ||\n             participant.username === this.sharingOrderDetail?.creatorUsername\n    },\n    \n    // 获取状态样式类\n    getStatusClass(status) {\n      const statusMap = {\n        'OPEN': 'status-open',\n        'FULL': 'status-full',\n        'CONFIRMED': 'status-confirmed',\n        'CANCELLED': 'status-cancelled',\n        'EXPIRED': 'status-expired'\n      }\n      return statusMap[status] || 'status-open'\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'OPEN': '开放中',\n        'FULL': '已满员',\n        'CONFIRMED': '已确认',\n        'CANCELLED': '已取消',\n        'EXPIRED': '已过期'\n      }\n      return statusMap[status] || '开放中'\n    },\n    \n    // 获取加入按钮文本\n    getJoinButtonText() {\n      if (!this.sharingOrderDetail) return '不可加入'\n      \n      if (this.sharingOrderDetail.status === 'FULL') {\n        return '已满员'\n      }\n      if (this.sharingOrderDetail.status === 'CONFIRMED') {\n        return '已确认'\n      }\n      if (this.sharingOrderDetail.status === 'CANCELLED') {\n        return '已取消'\n      }\n      if (this.sharingOrderDetail.status === 'EXPIRED') {\n        return '已过期'\n      }\n      if (this.isParticipant) {\n        return '已加入'\n      }\n      return '不可加入'\n    },\n\n    // 判断是否显示倒计时\n    shouldShowCountdown(order) {\n      return shouldShowCountdown(order)\n    },\n\n    // 倒计时过期处理\n    onCountdownExpired(order) {\n      console.log('拼场订单倒计时过期:', order.orderNo)\n      // 刷新详情数据，更新订单状态\n      this.loadSharingDetail()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n// 加载状态\n.loading-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 200rpx 60rpx;\n  \n  text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n// 错误状态\n.error-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 200rpx 60rpx;\n  \n  .error-icon {\n    font-size: 120rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .error-text {\n    font-size: 28rpx;\n    color: #999999;\n    margin-bottom: 40rpx;\n  }\n  \n  .retry-btn {\n    padding: 16rpx 40rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 26rpx;\n  }\n}\n\n// 拼场详情\n.sharing-detail {\n  padding: 20rpx;\n}\n\n// 状态区域\n.status-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 20rpx;\n  gap: 16rpx;\n\n  .status-badge {\n    padding: 12rpx 32rpx;\n    border-radius: 30rpx;\n    font-size: 26rpx;\n    font-weight: bold;\n    \n    &.status-open {\n      background-color: #e8f5e8;\n      color: #52c41a;\n    }\n    \n    &.status-full {\n      background-color: #fff2e8;\n      color: #fa8c16;\n    }\n    \n    &.status-confirmed {\n      background-color: #e6f7ff;\n      color: #1890ff;\n    }\n    \n    &.status-cancelled {\n      background-color: #fff1f0;\n      color: #ff4d4f;\n    }\n    \n    &.status-expired {\n      background-color: #f6f6f6;\n      color: #999999;\n    }\n  }\n}\n\n// 通用区域样式\n.team-section,\n.venue-section,\n.activity-section,\n.participants-section,\n.contact-section,\n.rules-section {\n  margin-bottom: 20rpx;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333333;\n    margin-bottom: 16rpx;\n    padding: 0 10rpx;\n  }\n}\n\n// 队伍信息\n.team-card {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  \n  .team-header {\n    margin-bottom: 20rpx;\n    \n    .team-name {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #333333;\n      display: block;\n      margin-bottom: 8rpx;\n    }\n    \n    .team-creator {\n      font-size: 26rpx;\n      color: #666666;\n    }\n  }\n  \n  .participants-info {\n    .participants-count {\n      margin-bottom: 12rpx;\n      \n      .count-text {\n        font-size: 28rpx;\n        color: #333333;\n      }\n    }\n    \n    .progress-bar {\n      height: 12rpx;\n      background-color: #f0f0f0;\n      border-radius: 6rpx;\n      overflow: hidden;\n      \n      .progress-fill {\n        height: 100%;\n        background-color: #ff6b35;\n        transition: width 0.3s ease;\n      }\n    }\n  }\n}\n\n// 场馆信息\n.venue-card {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  \n  .venue-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16rpx;\n    \n    .venue-name {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333333;\n    }\n    \n    .venue-arrow {\n      font-size: 28rpx;\n      color: #999999;\n    }\n  }\n  \n  .venue-info {\n    .venue-location {\n      font-size: 26rpx;\n      color: #666666;\n      display: block;\n      margin-bottom: 8rpx;\n    }\n    \n    .venue-rating {\n      display: flex;\n      align-items: center;\n      \n      .rating-text {\n        font-size: 24rpx;\n        color: #fa8c16;\n        margin-right: 8rpx;\n      }\n      \n      .review-count {\n        font-size: 24rpx;\n        color: #999999;\n      }\n    }\n  }\n}\n\n// 活动信息\n.activity-card {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  \n  .info-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 0;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      font-size: 28rpx;\n      color: #666666;\n    }\n    \n    .info-value {\n      font-size: 28rpx;\n      color: #333333;\n      \n      &.price {\n        color: #ff6b35;\n        font-weight: bold;\n      }\n    }\n  }\n  \n  .description-item {\n    padding: 16rpx 0;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      font-size: 28rpx;\n      color: #666666;\n      margin-bottom: 12rpx;\n      display: block;\n    }\n    \n    .description-text {\n      font-size: 26rpx;\n      color: #333333;\n      line-height: 1.6;\n    }\n  }\n}\n\n// 参与者列表\n.participants-list {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  \n  .participant-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 20rpx 10rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .participant-info {\n      display: flex;\n      align-items: center;\n      flex: 1;\n      \n      .participant-avatar {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: 50%;\n        margin-right: 20rpx;\n      }\n      \n      .participant-details {\n        .participant-name {\n          font-size: 28rpx;\n          color: #333333;\n          display: block;\n          margin-bottom: 4rpx;\n        }\n        \n        .participant-role {\n          font-size: 24rpx;\n          color: #999999;\n        }\n      }\n    }\n    \n    .remove-btn {\n      padding: 8rpx 16rpx;\n      background-color: #ff4d4f;\n      color: #ffffff;\n      border-radius: 16rpx;\n      font-size: 24rpx;\n    }\n  }\n}\n\n// 联系信息\n.contact-card {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  \n  .contact-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 0;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .contact-label {\n      font-size: 28rpx;\n      color: #666666;\n    }\n    \n    .contact-value {\n      font-size: 28rpx;\n      color: #1890ff;\n    }\n  }\n}\n\n// 活动规则\n.rules-card {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  \n  .rule-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 0;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .rule-label {\n      font-size: 28rpx;\n      color: #666666;\n    }\n    \n    .rule-value {\n      font-size: 28rpx;\n      color: #333333;\n    }\n  }\n}\n\n// 底部操作栏\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n  \n  .action-group {\n    display: flex;\n    gap: 20rpx;\n    \n    .action-btn {\n      flex: 1;\n      height: 80rpx;\n      border-radius: 12rpx;\n      font-size: 28rpx;\n      border: none;\n      \n      &.primary {\n        background-color: #ff6b35;\n        color: #ffffff;\n      }\n      \n      &.secondary {\n        background-color: #f5f5f5;\n        color: #333333;\n      }\n      \n      &.danger {\n        background-color: #ff4d4f;\n        color: #ffffff;\n      }\n      \n      &.disabled {\n        background-color: #f0f0f0;\n        color: #999999;\n      }\n    }\n  }\n}\n\n// 申请拼场弹窗\n.apply-modal {\n  background-color: #ffffff;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 0;\n  width: 100%;\n  max-height: 80vh;\n  \n  .modal-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 40rpx 40rpx 20rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333333;\n    }\n    \n    .close-btn {\n      font-size: 36rpx;\n      color: #999999;\n      padding: 10rpx;\n    }\n  }\n  \n  .modal-content {\n    padding: 40rpx;\n    max-height: 60vh;\n    overflow-y: auto;\n    \n    .form-item {\n      margin-bottom: 40rpx;\n      \n      .form-label {\n        display: block;\n        font-size: 28rpx;\n        color: #333333;\n        margin-bottom: 16rpx;\n        \n        .required {\n          color: #ff4d4f;\n        }\n      }\n      \n      .form-input {\n        width: 100%;\n        padding: 24rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 12rpx;\n        font-size: 28rpx;\n        background-color: #ffffff;\n        \n        &:focus {\n          border-color: #ff6b35;\n        }\n      }\n      \n      .form-textarea {\n        width: 100%;\n        padding: 24rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 12rpx;\n        font-size: 28rpx;\n        background-color: #ffffff;\n        min-height: 120rpx;\n        resize: none;\n        \n        &:focus {\n          border-color: #ff6b35;\n        }\n      }\n      \n      .number-selector {\n        display: flex;\n        align-items: center;\n        gap: 20rpx;\n        margin-bottom: 16rpx;\n        \n        .number-btn {\n          width: 60rpx;\n          height: 60rpx;\n          border: 1rpx solid #e0e0e0;\n          border-radius: 8rpx;\n          background-color: #ffffff;\n          font-size: 32rpx;\n          color: #333333;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          \n          &:disabled {\n            background-color: #f5f5f5;\n            color: #cccccc;\n            border-color: #f0f0f0;\n          }\n        }\n        \n        .number-text {\n          font-size: 28rpx;\n          color: #333333;\n          min-width: 40rpx;\n          text-align: center;\n        }\n      }\n      \n      .form-hint {\n        font-size: 24rpx;\n        color: #999999;\n      }\n      \n      .char-count {\n        display: block;\n        text-align: right;\n        font-size: 24rpx;\n        color: #999999;\n        margin-top: 8rpx;\n      }\n    }\n  }\n  \n  .modal-actions {\n    display: flex;\n    gap: 20rpx;\n    padding: 20rpx 40rpx 40rpx;\n    \n    .modal-btn {\n      flex: 1;\n      padding: 28rpx;\n      border-radius: 12rpx;\n      font-size: 28rpx;\n      border: none;\n    }\n    \n    .cancel-btn {\n      background-color: #f5f5f5;\n      color: #666666;\n    }\n    \n    .confirm-btn {\n      background-color: #ff6b35;\n      color: #ffffff;\n      \n      &:disabled {\n        background-color: #ffcab3;\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n// 确认弹窗\n.confirm-modal {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 40rpx;\n  width: 600rpx;\n  \n  .modal-header {\n    text-align: center;\n    margin-bottom: 30rpx;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333333;\n    }\n  }\n  \n  .modal-content {\n    margin-bottom: 40rpx;\n    \n    .modal-text {\n      font-size: 28rpx;\n      color: #666666;\n      line-height: 1.5;\n      text-align: center;\n    }\n  }\n  \n  .modal-actions {\n    display: flex;\n    gap: 20rpx;\n    \n    .modal-btn {\n      flex: 1;\n      padding: 24rpx;\n      border-radius: 12rpx;\n      font-size: 28rpx;\n      border: none;\n    }\n    \n    .cancel-btn {\n      background-color: #f5f5f5;\n      color: #666666;\n    }\n    \n    .confirm-btn {\n      background-color: #ff6b35;\n      color: #ffffff;\n    }\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/sharing/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapState", "uni", "mapActions", "shouldShowCountdown"], "mappings": ";;;AAqSA,MAAK,iBAAkB,MAAW;AAGlC,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EAED,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,aAAa;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,MACT;AAAA,MACD,WAAW;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,GAAGA,cAAQ,SAAC,WAAW,CAAC,sBAAsB,SAAS,CAAC;AAAA,IACxD,GAAGA,uBAAS,QAAQ,CAAC,UAAU,CAAC;AAAA;AAAA,IAGhC,eAAe;AACb,UAAI,CAAC,KAAK;AAAoB,eAAO,CAAC;AAEtC,YAAM,eAAe,CAAC;AAGtB,mBAAa,KAAK;AAAA,QAChB,UAAU,KAAK,mBAAmB;AAAA,QAClC,UAAU,KAAK,mBAAmB;AAAA,QAClC,MAAM;AAAA,QACN,aAAa;AAAA,OACd;AAGD,UAAI,KAAK,mBAAmB,gBAAgB,MAAM,QAAQ,KAAK,mBAAmB,YAAY,GAAG;AAC/F,aAAK,mBAAmB,aAAa,QAAQ,iBAAe;AAC1D,cAAI,YAAY,aAAa,KAAK,mBAAmB,iBAAiB;AACpE,yBAAa,KAAK;AAAA,cAChB,GAAG;AAAA,cACH,MAAM;AAAA,cACN,aAAa;AAAA,aACd;AAAA,UACH;AAAA,SACD;AAAA,MACH;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK;AAAoB,eAAO;AACvD,aAAO,KAAK,aAAa,KAAK,OAAK,EAAE,aAAa,KAAK,SAAS,QAAQ;AAAA,IACzE;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK;AAAoB,eAAO;AACvD,aAAO,KAAK,mBAAmB,oBAAoB,KAAK,SAAS;AAAA,IAClE;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,CAAC,KAAK;AAAoB,eAAO;AACrC,aAAO,KAAK,mBAAmB,WAAW,WAClC,KAAK,mBAAmB,uBAAuB,MAAM,KAAK,mBAAmB,mBAAmB,MACjG,CAAC,KAAK;AAAA,IACd;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,CAAC,KAAK;AAAoB,eAAO;AACrC,aAAO,KAAK,mBAAmB,aACxB,KAAK,mBAAmB,WAAW,UACnC,KAAK,iBACL,CAAC,KAAK;AAAA,IACd;AAAA;AAAA,IAGD,kBAAkB;;AAChB,aAAO,KAAK,mBAAiB,UAAK,uBAAL,mBAAyB;AAAA,IACvD;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,CAAC,KAAK;AAAoB,eAAO;AACrC,aAAO,KAAK,KAAK,mBAAmB,uBAAuB;AAAA,IAC5D;AAAA;AAAA,IAGD,uBAAuB;AACrB,aAAO,KAAK,UAAU,YAAY,KAAM,EAAC,SAAS;AAAA,IACpD;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACdC,kBAAAA,MAAA,MAAA,OAAA,mCAAY,kBAAkB,OAAO;AACrCA,kBAAY,MAAA,MAAA,OAAA,mCAAA,sBAAsB,QAAQ,EAAE;AAE5C,QAAI,QAAQ,IAAI;AACd,WAAK,YAAY,QAAQ;AACzBA,oBAAY,MAAA,MAAA,OAAA,mCAAA,wBAAwB,KAAK,SAAS;AAClD,WAAK,kBAAkB;AAAA,WAClB;AACLA,oBAAAA,MAAc,MAAA,SAAA,mCAAA,iBAAiB;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACH;AAAA,EACD;AAAA,EAED,SAAS;AACP,QAAI,KAAK,WAAW;AAElB,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACD;AAAA,EAED,oBAAoB;AAClB,SAAK,kBAAkB;AAAA,EACxB;AAAA,EAED,SAAS;AAAA,IACP,GAAGC,cAAAA,WAAW,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI;AACFD,sBAAY,MAAA,MAAA,OAAA,mCAAA,qBAAqB,KAAK,SAAS;AAC/C,cAAM,KAAK,sBAAsB,KAAK,SAAS;AAC/CA,sBAAY,MAAA,MAAA,OAAA,mCAAA,kBAAkB,KAAK,kBAAkB;AAErD,aAAK,aAAa;AAClBA,sBAAAA,MAAI,oBAAoB;AAAA,MACxB,SAAO,OAAO;AACdA,sBAAAA,MAAI,oBAAoB;AACxBA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,kBAAkB,KAAK;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;;AAChB,WAAI,UAAK,uBAAL,mBAAyB,SAAS;AACpCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,0BAA0B,KAAK,mBAAmB,OAAO;AAAA,SAC/D;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEZ,WAAK,cAAc;AAEnB,WAAK,MAAM,WAAW,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,WAAK,cAAc;AAAA,QACjB,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AACA,WAAK,MAAM,aAAa,KAAK;AAAA,IAC9B;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,WAAK,cAAc;AAAA,QACjB,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AACA,WAAK,MAAM,aAAa,KAAK;AAAA,IAC9B;AAAA;AAAA,IAGD,MAAM,kBAAkB,aAAa;AACnC,WAAK,cAAc;AAAA,QACjB,OAAO;AAAA,QACP,SAAS,QAAQ,YAAY,YAAY,YAAY,QAAQ;AAAA,QAC7D,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AACA,WAAK,MAAM,aAAa,KAAK;AAAA,IAC9B;AAAA;AAAA,IAGD,mBAAmB;;AACjB,WAAI,gBAAK,uBAAL,mBAAyB,gBAAzB,mBAAsC,OAAO;AAC/C,aAAK,cAAc;AAAA,aACd;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,KAAK,SAAS;AAAA,OAChD;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;;AACd,YAAM,SAAQ,gBAAK,uBAAL,mBAAyB,gBAAzB,mBAAsC;AACpD,UAAI,OAAO;AACTA,sBAAAA,MAAI,cAAc;AAAA,UAChB,aAAa;AAAA,SACd;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,MAAM,aAAa,MAAM;AAAA,IAC/B;AAAA;AAAA,IAGD,gBAAgB;;AACd,WAAK,YAAY;AAAA,QACf,UAAU;AAAA;AAAA,QACV,eAAa,UAAK,aAAL,mBAAe,YAAS,UAAK,aAAL,mBAAe,WAAU;AAAA;AAAA,QAC9D,SAAS;AAAA,MACX;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,MAAM,WAAW,MAAM;AAAA,IAC7B;AAAA;AAAA;AAAA,IAKD,MAAM,oBAAoB;AACxB,UAAI,CAAC,KAAK,sBAAsB;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,kBAAkB;AAAA,UACtB,UAAU,KAAK,UAAU,SAAS,KAAM;AAAA,UACxC,aAAa,KAAK,UAAU,YAAY,KAAM;AAAA,UAC9C,SAAS,KAAK,UAAU,QAAQ,KAAK;AAAA,QACvC;AAEA,cAAM,WAAW,MAAM,KAAK,kBAAkB,EAAE,SAAS,KAAK,WAAW,MAAM,iBAAiB;AAEhGA,sBAAAA,MAAI,YAAY;AAEhB,aAAK,gBAAgB;AAGrB,YAAI,YAAY,SAAS,SACpB,SAAS,KAAK,WAAW,8BAA8B,SAAS,KAAK,WAAW,aAAa;AAEhGA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,MAAM;AAEbA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK,gCAAgC,CAAC,SAAS,KAAK,EAAE;AAAA,eACvD;AAAA,YACH;AAAA,WACD;AAAA,eACI;AAELA,wBAAAA,MAAI,UAAU;AAAA,YACZ,QAAO,qCAAU,YAAW;AAAA,YAC5B,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAAA,QACH;AAGA,cAAM,KAAK,kBAAkB;AAAA,MAE7B,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,gBAAQ,KAAK,YAAY,QAAM;AAAA,UAC7B,KAAK;AACH,kBAAM,KAAK,iBAAiB,KAAK,SAAS;AAC1CA,0BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AAChD;AAAA,UAEF,KAAK;AACH,kBAAM,KAAK,mBAAmB,KAAK,SAAS;AAC5CA,0BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AAGhD,uBAAW,MAAM;AACfA,4BAAAA,MAAI,aAAa;AAAA,gBACf,OAAO;AAAA,eACR;AAAA,YACF,GAAE,IAAI;AACP;AAAA,UAEF,KAAK;AACH,kBAAM,KAAK,6BAA6B;AAAA,cACtC,WAAW,KAAK;AAAA,cAChB,eAAe,KAAK,YAAY,KAAK,MAAM,KAAK,YAAY,KAAK;AAAA,aAClE;AACDA,0BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AAChD;AAAA,QACJ;AAEAA,sBAAAA,MAAI,YAAY;AAChB,aAAK,kBAAkB;AAGvB,cAAM,KAAK,kBAAkB;AAAA,MAE7B,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnB,UAAI,CAAC,KAAK;AAAoB,eAAO;AAErC,YAAM,cAAc,KAAK,mBAAmB,eAAe,KAAK,mBAAmB;AACnF,YAAM,YAAY,KAAK,mBAAmB,aAAa,KAAK,mBAAmB;AAC/E,YAAM,UAAU,KAAK,mBAAmB,WAAW,KAAK,mBAAmB;AAE3E,UAAI,CAAC,aAAa;AAChB,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,KAAK,WAAW,WAAW;AAExC,UAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,eAAO,GAAG,IAAI;AAAA,MAChB;AAGA,YAAM,aAAa,CAAC,YAAY;AAC9B,YAAI,CAAC;AAAS,iBAAO;AAErB,YAAI,OAAO,YAAY,UAAU;AAE/B,cAAI,QAAQ,WAAW,KAAK,QAAQ,SAAS,GAAG,GAAG;AACjD,mBAAO;AAAA,UACT;AAEA,cAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AAC/C,mBAAO,QAAQ,UAAU,GAAG,CAAC;AAAA,UAC/B;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,WAAW,SAAS;AAC3C,YAAM,eAAe,WAAW,OAAO;AAEvCA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,cAAc;AACrCA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,YAAY;AAGnC,aAAO,GAAG,IAAI,IAAI,cAAc,IAAI,YAAY;AAAA,IACjD;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,UAAI,CAAC;AAAM,eAAO;AAClB,UAAI;AACF,cAAM,UAAU,IAAI,KAAK,IAAI;AAC7B,YAAI,MAAM,QAAQ,QAAS,CAAA;AAAG,iBAAO;AACrC,cAAM,OAAO,QAAQ,YAAY;AACjC,cAAM,QAAQ,OAAO,QAAQ,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AAC5D,cAAM,MAAM,OAAO,QAAQ,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,MAC9B,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,YAAY,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AACtB,UAAI;AAEF,YAAI,UAAU;AACd,YAAI,OAAO,YAAY,YAAY,QAAQ,SAAS,GAAG,KAAK,CAAC,QAAQ,SAAS,GAAG,GAAG;AAClF,oBAAU,QAAQ,QAAQ,KAAK,GAAG;AAAA,QACpC;AAEA,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAI,MAAM,KAAK,QAAS,CAAA;AAAG,iBAAO;AAClC,cAAM,OAAO,KAAK,YAAY;AAC9B,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,OAAO,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACpD,cAAM,SAAS,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACxD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAAA,MAChD,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,YAAY,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,WAAW,SAAS;AACjC,UAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,eAAO;AAAA,MACT;AACA,UAAI,aAAa,CAAC,SAAS;AACzB,eAAO;AAAA,MACT;AACA,UAAI,CAAC,aAAa,SAAS;AACzB,eAAO;AAAA,MACT;AAGA,YAAM,aAAa,CAAC,YAAY;AAC9B,YAAI,CAAC;AAAS,iBAAO;AAErB,YAAI,OAAO,YAAY,UAAU;AAE/B,cAAI,QAAQ,WAAW,KAAK,QAAQ,SAAS,GAAG,GAAG;AACjD,mBAAO;AAAA,UACT;AAEA,cAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AAC/C,mBAAO,QAAQ,UAAU,GAAG,CAAC;AAAA,UAC/B;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,WAAW,SAAS;AAC3C,YAAM,eAAe,WAAW,OAAO;AAEvCA,oBAAY,MAAA,MAAA,OAAA,mCAAA,WAAW,gBAAgB,KAAK,YAAY;AAExD,aAAO,GAAG,cAAc,IAAI,YAAY;AAAA,IACzC;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,CAAC,KAAK;AAAoB,eAAO;AAGrC,UAAI,KAAK,mBAAmB,YAAY;AACtC,eAAO,KAAK,YAAY,KAAK,mBAAmB,UAAU;AAAA,MAC5D;AAGA,UAAI,KAAK,mBAAmB,gBAAgB;AAE1C,cAAM,eAAe,OAAO,KAAK,mBAAmB,cAAc;AAClE,eAAO,KAAK,YAAY,eAAe,CAAC;AAAA,MAC1C;AAGA,YAAM,QAAQ,KAAK,mBAAmB,SAAS;AAC/C,aAAO,KAAK,YAAY,KAAK;AAAA,IAC9B;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,CAAC,SAAS,UAAU;AAAG,eAAO;AAClC,YAAM,WAAW,OAAO,KAAK;AAC7B,UAAI,MAAM,QAAQ;AAAG,eAAO;AAC5B,aAAO,SAAS,QAAQ,CAAC;AAAA,IAC1B;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,CAAC,KAAK;AAAoB,eAAO;AACrC,YAAM,UAAU,KAAK,mBAAmB,uBAAuB;AAC/D,YAAM,MAAM,KAAK,mBAAmB,mBAAmB;AACvD,aAAO,KAAK,IAAK,UAAU,MAAO,KAAK,GAAG;AAAA,IAC3C;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,CAAC,KAAK;AAAoB,eAAO;AAGrC,UAAI,KAAK,mBAAmB,cAAc;AACxC,eAAO,KAAK,YAAY,KAAK,mBAAmB,YAAY;AAAA,MAC9D;AAGA,UAAI,KAAK,mBAAmB,YAAY;AACtC,eAAO,KAAK,YAAY,KAAK,mBAAmB,aAAa,CAAC;AAAA,MAChE;AAGA,UAAI,KAAK,mBAAmB,gBAAgB;AAC1C,eAAO,KAAK,YAAY,KAAK,mBAAmB,cAAc;AAAA,MAChE;AAGA,YAAM,QAAQ,KAAK,mBAAmB,SAAS;AAC/C,aAAO,KAAK,YAAY,QAAQ,CAAC;AAAA,IAClC;AAAA;AAAA,IAGD,mBAAmB,aAAa;AAC9B,UAAI,YAAY,eAAe,YAAY,SAAS,aAAa;AAC/D,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,uBAAuB,aAAa;;AAClC,aAAO,YAAY,eAAe,YAAY,SAAS,eAChD,YAAY,eAAa,UAAK,uBAAL,mBAAyB;AAAA,IAC1D;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,CAAC,KAAK;AAAoB,eAAO;AAErC,UAAI,KAAK,mBAAmB,WAAW,QAAQ;AAC7C,eAAO;AAAA,MACT;AACA,UAAI,KAAK,mBAAmB,WAAW,aAAa;AAClD,eAAO;AAAA,MACT;AACA,UAAI,KAAK,mBAAmB,WAAW,aAAa;AAClD,eAAO;AAAA,MACT;AACA,UAAI,KAAK,mBAAmB,WAAW,WAAW;AAChD,eAAO;AAAA,MACT;AACA,UAAI,KAAK,eAAe;AACtB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,oBAAoB,OAAO;AACzB,aAAOE,gBAAAA,oBAAoB,KAAK;AAAA,IACjC;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACxBF,oBAAY,MAAA,MAAA,OAAA,mCAAA,cAAc,MAAM,OAAO;AAEvC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACx5BA,GAAG,WAAW,eAAe;"}