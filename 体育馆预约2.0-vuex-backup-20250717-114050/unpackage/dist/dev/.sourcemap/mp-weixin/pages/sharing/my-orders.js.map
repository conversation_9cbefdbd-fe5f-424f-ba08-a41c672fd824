{"version": 3, "file": "my-orders.js", "sources": ["pages/sharing/my-orders.vue", "pages/sharing/my-orders.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 通知提示区域 -->\n    <view v-if=\"notifications.length > 0\" class=\"notification-area\">\n      <view \n        v-for=\"(notification, index) in notifications\" \n        :key=\"index\"\n        class=\"notification-item\"\n        :class=\"notification.type\"\n        @click=\"dismissNotification(index)\"\n      >\n        <text class=\"notification-icon\">{{ getNotificationIcon(notification.type) }}</text>\n        <text class=\"notification-text\">{{ notification.message }}</text>\n        <text class=\"notification-close\">×</text>\n      </view>\n    </view>\n    \n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <text class=\"nav-icon\">‹</text>\n      </view>\n      <text class=\"nav-title\">我的拼场</text>\n      <view class=\"nav-right\"></view>\n    </view>\n    \n    <!-- 标签切换 -->\n    <view class=\"tabs-container\">\n      <view \n        v-for=\"tab in tabs\" \n        :key=\"tab.value\"\n        class=\"tab-item\"\n        :class=\"{ active: currentTab === tab.value }\"\n        @click=\"switchTab(tab.value)\"\n      >\n        <text class=\"tab-text\">{{ tab.label }}</text>\n        <text v-if=\"tab.count > 0\" class=\"tab-count\">{{ tab.count }}</text>\n      </view>\n    </view>\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-state\">\n      <text>加载中...</text>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else-if=\"error\" class=\"error-state\">\n      <text class=\"error-icon\">⚠️</text>\n      <text class=\"error-text\">{{ error }}</text>\n      <button class=\"retry-btn\" @click=\"loadData\">\n        重新加载\n      </button>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view v-else class=\"content\">\n      <!-- 我创建的拼场 -->\n      <view v-if=\"currentTab === 'created'\" class=\"orders-list\">\n        <view v-if=\"sortedCreatedOrders.length > 0\">\n          <view\n            v-for=\"order in sortedCreatedOrders\"\n            :key=\"order.id\"\n            class=\"order-item\"\n            @click=\"goToDetail(order.id)\"\n          >\n            <view class=\"order-header\">\n              <text class=\"venue-name\">{{ order.venueName }}</text>\n              <view class=\"status-badge\" :class=\"getStatusClass(order.status)\">\n                <text class=\"status-text\">{{ getStatusText(order.status) }}</text>\n              </view>\n            </view>\n            \n            <view class=\"order-info\">\n              <view class=\"info-row\">\n                <text class=\"time-icon\">🕐</text>\n                <text class=\"info-value\">{{ formatTimeRange(order) }}</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"location-icon\">📍</text>\n                <text class=\"info-value\">{{ order.venueLocation || '位置未知' }}</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"team-icon\">👥</text>\n                <text class=\"info-value\">{{ order.teamName }}</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"participants-icon\">👥</text>\n                <text class=\"info-value\">参与球队：{{ order.currentParticipants || 0 }}/{{ order.maxParticipants || 2 }}支</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"price-icon\">💰</text>\n                <text class=\"info-value\">费用：¥{{ formatPrice(getPerTeamPrice(order)) }}（每队平分）</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"create-icon\">📅</text>\n                <text class=\"info-value\">创建时间：{{ formatCreateTime(order.createdAt) }}</text>\n              </view>\n            </view>\n            \n            <view class=\"order-actions\">\n              <button \n                v-if=\"order.status === 'OPEN'\"\n                class=\"action-btn manage-btn\"\n                @click.stop=\"goToManage(order.id)\"\n              >\n                管理\n              </button>\n              <button \n                v-if=\"order.status === 'OPEN'\"\n                class=\"action-btn cancel-btn\"\n                @click.stop=\"cancelOrder(order.id)\"\n              >\n                取消\n              </button>\n            </view>\n          </view>\n        </view>\n        <view v-else class=\"empty-state\">\n          <text class=\"empty-icon\">📝</text>\n          <text class=\"empty-text\">您还没有创建过拼场订单</text>\n          <button class=\"browse-btn\" @click=\"goToVenueList\">\n            去场馆预约\n          </button>\n        </view>\n      </view>\n      \n      <!-- 我申请的拼场 -->\n      <view v-if=\"currentTab === 'applied'\" class=\"orders-list\">\n        <view v-if=\"sortedAppliedOrders.length > 0\">\n          <view\n            v-for=\"request in sortedAppliedOrders\"\n            :key=\"request.id\"\n            class=\"order-item\"\n            @click=\"goToRequestDetail(request)\"\n          >\n            <view class=\"order-header\">\n              <text class=\"venue-name\">{{ request.venueName }}</text>\n              <view class=\"status-badge\" :class=\"getRequestStatusClass(request.status)\">\n                <text class=\"status-text\">{{ getRequestStatusText(request.status) }}</text>\n              </view>\n            </view>\n            \n            <view class=\"order-info\">\n              <view class=\"info-row\">\n                <text class=\"time-icon\">🕐</text>\n                <text class=\"info-value\">{{ formatTimeRange(request.sharingOrder || request) }}</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"location-icon\">📍</text>\n                <text class=\"info-value\">{{ request.venueLocation || request.sharingOrder?.venueLocation || request.venueName || request.sharingOrder?.venueName || '位置未知' }}</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"team-icon\">👥</text>\n                <text class=\"info-value\">对方队伍：{{ request.teamName }}</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"price-icon\">💰</text>\n                <text class=\"info-value\">费用：¥{{ formatPrice(getPerTeamPrice(request.sharingOrder || request)) }}（每队平分）</text>\n              </view>\n              \n              <view class=\"info-row\">\n                <text class=\"create-icon\">📅</text>\n                <text class=\"info-value\">申请时间：{{ formatCreateTime(request.createdAt) }}</text>\n              </view>\n              \n              <view v-if=\"request.responseMessage\" class=\"info-row\">\n                <text class=\"message-icon\">💬</text>\n                <text class=\"info-value\">回复：{{ request.responseMessage }}</text>\n              </view>\n            </view>\n            \n            <view class=\"order-actions\">\n              <button \n                v-if=\"request.status === 'PENDING'\"\n                class=\"action-btn cancel-btn\"\n                @click.stop=\"cancelRequest(request.id)\"\n              >\n                取消申请\n              </button>\n            </view>\n          </view>\n        </view>\n        <view v-else class=\"empty-state\">\n          <text class=\"empty-icon\">📋</text>\n          <text class=\"empty-text\">您还没有申请过拼场</text>\n          <button class=\"browse-btn\" @click=\"goToBrowse\">\n            浏览拼场\n          </button>\n        </view>\n      </view>\n      \n      <!-- 拼场成功的订单 -->\n      <view v-if=\"currentTab === 'success'\" class=\"orders-list\">\n        <view v-if=\"sortedSuccessOrders.length > 0\">\n          <view\n            v-for=\"order in sortedSuccessOrders\"\n            :key=\"order.id\"\n            class=\"order-item success-item\"\n            @click=\"goToDetail(order.id)\"\n          >\n            <view class=\"order-header\">\n              <text class=\"venue-name\">{{ order.venueName }}</text>\n              <view class=\"status-badge success\">\n                <text class=\"status-text\">拼场成功</text>\n              </view>\n            </view>\n            \n            <view class=\"order-info\">\n              <view class=\"info-row\">\n                <text class=\"info-label\">时间：</text>\n                <text class=\"info-value\">{{ formatTimeRange(order) }}</text>\n              </view>\n              <view class=\"info-row\">\n                <text class=\"info-label\">位置：</text>\n                <text class=\"info-value\">{{ order.venueLocation || order.venueName || '位置未知' }}</text>\n              </view>\n              <view class=\"info-row\">\n                <text class=\"info-label\">队伍：</text>\n                <text class=\"info-value\">{{ order.teamName }}</text>\n              </view>\n              <view class=\"info-row\">\n                <text class=\"info-label\">人数：</text>\n                <text class=\"info-value\">{{ order.currentParticipants }}/{{ order.maxParticipants }}人</text>\n              </view>\n              <view class=\"info-row\">\n                <text class=\"info-label\">费用：</text>\n                <text class=\"info-value\">¥{{ formatPrice(getPerTeamPrice(order)) }}（每队平分）</text>\n              </view>\n              <view class=\"info-row\">\n                <text class=\"info-label\">联系方式：</text>\n                <text class=\"info-value\">{{ order.contactInfo }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view v-else class=\"empty-state\">\n          <text class=\"empty-icon\">🎉</text>\n          <text class=\"empty-text\">还没有拼场成功的订单</text>\n          <button class=\"browse-btn\" @click=\"goToBrowse\">\n            去拼场\n          </button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapActions, mapGetters } from 'vuex'\n\nexport default {\n  name: 'MyOrders',\n  data() {\n    return {\n      currentTab: 'created',\n      loading: false,\n      error: null,\n      createdOrders: [],\n      appliedOrders: [],\n      successOrders: [],\n      notifications: [] // 通知数组\n    }\n  },\n  \n  computed: {\n    ...mapGetters('user', ['userInfo']),\n\n    // 按时间倒序排列的订单列表\n    sortedCreatedOrders() {\n      return [...this.createdOrders].sort((a, b) => {\n        const dateA = new Date(a.createdAt?.replace(/-/g, '/') || 0);\n        const dateB = new Date(b.createdAt?.replace(/-/g, '/') || 0);\n        return dateB.getTime() - dateA.getTime(); // 倒序：最新的在前\n      });\n    },\n\n    sortedAppliedOrders() {\n      return [...this.appliedOrders].sort((a, b) => {\n        const dateA = new Date(a.createdAt?.replace(/-/g, '/') || 0);\n        const dateB = new Date(b.createdAt?.replace(/-/g, '/') || 0);\n        return dateB.getTime() - dateA.getTime(); // 倒序：最新的在前\n      });\n    },\n\n    sortedSuccessOrders() {\n      return [...this.successOrders].sort((a, b) => {\n        const dateA = new Date(a.createdAt?.replace(/-/g, '/') || 0);\n        const dateB = new Date(b.createdAt?.replace(/-/g, '/') || 0);\n        return dateB.getTime() - dateA.getTime(); // 倒序：最新的在前\n      });\n    },\n\n    tabs() {\n      return [\n        {\n          value: 'created',\n          label: '我创建的',\n          count: this.sortedCreatedOrders.length\n        },\n        {\n          value: 'applied',\n          label: '我申请的',\n          count: this.sortedAppliedOrders.length\n        },\n        {\n          value: 'success',\n          label: '拼场成功',\n          count: this.sortedSuccessOrders.length\n        }\n      ]\n    }\n  },\n  \n  onLoad() {\n    this.loadData();\n    this.checkForNewNotifications();\n  },\n  \n  onShow() {\n    // 页面显示时检查新通知\n    this.checkForNewNotifications();\n  },\n  \n  onShow() {\n    // 每次显示页面时强制刷新数据，确保显示最新状态\n    this.loadData()\n  },\n  \n  onPullDownRefresh() {\n    this.loadData().finally(() => {\n      uni.stopPullDownRefresh()\n    })\n  },\n  \n  methods: {\n    ...mapActions('sharing', [\n      'getMyCreatedSharingOrders',\n      'getMySharingRequests',\n      'cancelSharingOrder',\n      'cancelSharingRequest'\n    ]),\n    \n    async loadData() {\n      this.loading = true\n      this.error = null\n      \n      // 清除相关缓存\n      if (typeof window !== 'undefined' && window.cacheManager) {\n        window.cacheManager.clearUrl('/sharing-orders/my-created')\n        window.cacheManager.clearUrl('/shared/my-requests')\n      }\n      \n      try {\n        await Promise.all([\n          this.loadCreatedOrders(),\n          this.loadAppliedOrders(),\n          this.loadSuccessOrders()\n        ])\n        // 强制更新视图，确保显示最新数据\n        this.$forceUpdate()\n      } catch (error) {\n        console.error('加载数据失败:', error)\n        this.error = error.message || '加载失败'\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async loadCreatedOrders() {\n      try {\n        console.log('🔍 开始加载创建的订单...')\n        const response = await this.getMyCreatedSharingOrders()\n        console.log('📦 API响应:', response)\n        console.log('📊 响应数据类型:', typeof response)\n        console.log('📋 响应数据内容:', JSON.stringify(response, null, 2))\n        \n        // 检查不同的响应格式\n        let orders = []\n        if (Array.isArray(response)) {\n          orders = response\n        } else if (response && Array.isArray(response.data)) {\n          orders = response.data\n        } else if (response && Array.isArray(response.list)) {\n          orders = response.list\n        }\n        \n        console.log('✅ 最终订单数据:', orders)\n        console.log('📈 订单数量:', orders.length)\n        \n        // 显示所有创建的拼场订单，不管状态如何（OPEN、FULL、CONFIRMED、CANCELLED等）\n        this.createdOrders = orders\n      } catch (error) {\n        console.error('❌ 加载创建的订单失败:', error)\n        this.createdOrders = []\n      }\n    },\n    \n    async loadAppliedOrders() {\n      try {\n        console.log('🔍 开始加载申请的订单...')\n        const response = await this.getMySharingRequests()\n        console.log('📦 申请订单API响应:', response)\n        console.log('📊 申请订单响应数据类型:', typeof response)\n        console.log('📋 申请订单响应数据内容:', JSON.stringify(response, null, 2))\n        \n        // 检查不同的响应格式\n        let requests = []\n        if (Array.isArray(response)) {\n          requests = response\n        } else if (response && Array.isArray(response.data)) {\n          requests = response.data\n        } else if (response && Array.isArray(response.list)) {\n          requests = response.list\n        }\n        \n        console.log('✅ 最终申请数据:', requests)\n        console.log('📈 申请数量:', requests.length)\n        \n        this.appliedOrders = requests\n      } catch (error) {\n        console.error('加载申请的订单失败:', error)\n        this.appliedOrders = []\n      }\n    },\n    \n    async loadSuccessOrders() {\n      try {\n        console.log('🎯 开始加载成功订单...')\n        \n        // 获取我创建的成功拼场订单（状态为FULL或CONFIRMED）\n        const createdResponse = await this.getMyCreatedSharingOrders()\n        console.log('📋 我创建的订单原始响应:', createdResponse)\n        \n        const createdOrders = createdResponse.data || createdResponse.list || createdResponse || []\n        console.log('📋 我创建的订单数据:', createdOrders)\n        console.log('📋 我创建的订单状态分布:', createdOrders.map(o => ({ id: o.id, status: o.status })))\n        \n        const myCreatedSuccessOrders = createdOrders.filter(order =>\n          order.status === 'FULL' || order.status === 'CONFIRMED' || order.status === 'SHARING_SUCCESS'\n        )\n        console.log('✅ 我创建的成功订单:', myCreatedSuccessOrders)\n        \n        // 获取我申请成功的拼场订单（申请状态为APPROVED且拼场状态为FULL或CONFIRMED）\n        const appliedResponse = await this.getMySharingRequests()\n        console.log('📝 我申请的订单原始响应:', appliedResponse)\n        \n        const appliedRequests = appliedResponse.data || appliedResponse.list || appliedResponse || []\n        console.log('📝 我申请的订单数据:', appliedRequests)\n        console.log('📝 我申请的订单状态分布:', appliedRequests.map(r => ({ \n          id: r.id, \n          status: r.status, \n          sharingOrderStatus: r.sharingOrder?.status \n        })))\n        \n        const myAppliedSuccessOrders = appliedRequests.filter(request => {\n          const isPaid = request.status === 'PAID'\n          console.log(`📝 申请${request.id}: status=${request.status}, isPaid=${isPaid}`)\n          return isPaid\n        }).map(request => {\n          console.log('📝 申请成功订单原始数据:', request)\n          return {\n            // 使用申请记录中的订单信息，而不是sharingOrder\n            id: request.orderId || request.id,\n            orderNo: request.orderNo || `REQ_${request.id}`,\n            venueName: request.venueName || '未知场馆',\n            venueId: request.venueId,\n            venueLocation: request.venueLocation || '位置未知',\n            status: 'SHARING_SUCCESS', // 申请成功的订单状态\n            createdAt: request.createdAt,\n            updatedAt: request.updatedAt,\n\n            // 拼场相关信息\n            teamName: request.applicantTeamName || request.teamName || '未知队伍',\n            contactInfo: request.applicantContact || request.contactInfo || '未知联系方式',\n            currentParticipants: request.currentParticipants || 2,\n            maxParticipants: request.maxParticipants || 2,\n            description: request.description || '拼场申请',\n\n            // 价格信息\n            totalPrice: request.totalPrice || 0,\n            pricePerTeam: request.paymentAmount || request.totalPrice || 0,\n            paymentAmount: request.paymentAmount || 0,\n\n            // 时间信息\n            bookingDate: request.bookingDate,\n            startTime: request.startTime,\n            endTime: request.endTime,\n            bookingTime: request.bookingTime,\n\n            // 标记这是申请成功的订单\n            isAppliedOrder: true,\n            applicationId: request.id\n          }\n        })\n        console.log('✅ 我申请的成功订单:', myAppliedSuccessOrders)\n        \n        // 合并两种类型的成功订单\n        this.successOrders = [...myCreatedSuccessOrders, ...myAppliedSuccessOrders]\n        console.log('🎉 最终成功订单数据:', this.successOrders)\n        console.log('📈 成功订单数量:', this.successOrders.length)\n      } catch (error) {\n        console.error('加载成功订单失败:', error)\n        this.successOrders = []\n      }\n    },\n    \n    switchTab(tab) {\n      this.currentTab = tab\n    },\n    \n    goBack() {\n      uni.navigateBack()\n    },\n    \n    goToDetail(sharingId) {\n      if (!sharingId) {\n        console.error('拼场订单ID为空，无法跳转')\n        uni.showToast({\n          title: '订单信息错误',\n          icon: 'error'\n        })\n        return\n      }\n      uni.navigateTo({\n        url: `/pages/sharing/detail?id=${sharingId}`\n      })\n    },\n\n    goToRequestDetail(request) {\n      console.log('点击申请记录:', request)\n      // 对于申请记录，优先使用sharingOrderId，如果没有则使用orderId\n      const targetId = request.sharingOrderId || request.orderId\n      if (targetId) {\n        uni.navigateTo({\n          url: `/pages/sharing/detail?id=${targetId}`\n        })\n      } else {\n        console.error('申请记录缺少sharingOrderId和orderId字段')\n        uni.showToast({\n          title: '申请信息错误',\n          icon: 'error'\n        })\n      }\n    },\n    \n    goToManage(sharingId) {\n      console.log('=== 跳转到拼场管理页面 ===')\n      console.log('传递的拼场ID:', sharingId)\n      console.log('跳转URL:', `/pages/sharing/manage?id=${sharingId}`)\n\n      uni.navigateTo({\n        url: `/pages/sharing/manage?id=${sharingId}`\n      })\n    },\n    \n    goToVenueList() {\n      uni.switchTab({\n        url: '/pages/venue/list'\n      })\n    },\n    \n    goToBrowse() {\n      uni.switchTab({\n        url: '/pages/sharing/list'\n      })\n    },\n    \n    async cancelOrder(orderId) {\n      try {\n        await uni.showModal({\n          title: '确认取消',\n          content: '确定要取消这个拼场订单吗？'\n        })\n        \n        uni.showLoading({ title: '取消中...' })\n        await this.cancelSharingOrder(orderId)\n        uni.hideLoading()\n        \n        this.addNotification('info', '已取消拼场订单');\n        \n        this.addNotification('info', '已取消拼场申请');\n        \n        uni.showToast({\n          title: '取消成功',\n          icon: 'success'\n        })\n        \n        this.loadData()\n      } catch (error) {\n        uni.hideLoading()\n        if (error.errMsg !== 'showModal:fail cancel') {\n          this.addNotification('error', '取消申请失败');\n          uni.showToast({\n            title: error.message || '取消失败',\n            icon: 'error'\n          })\n        }\n      }\n    },\n    \n    async cancelRequest(requestId) {\n      try {\n        await uni.showModal({\n          title: '确认取消',\n          content: '确定要取消这个申请吗？'\n        })\n        \n        uni.showLoading({ title: '取消中...' })\n        await this.cancelSharingRequest(requestId)\n        uni.hideLoading()\n        \n        uni.showToast({\n          title: '取消成功',\n          icon: 'success'\n        })\n        \n        this.loadData()\n      } catch (error) {\n        uni.hideLoading()\n        if (error.errMsg !== 'showModal:fail cancel') {\n          uni.showToast({\n            title: error.message || '取消失败',\n            icon: 'error'\n          })\n        }\n      }\n    },\n    \n\n    \n    getStatusClass(status) {\n      const statusMap = {\n        'OPEN': 'open',\n        'FULL': 'full',\n        'CONFIRMED': 'confirmed',\n        'CANCELLED': 'cancelled',\n        'EXPIRED': 'expired'\n      }\n      return statusMap[status] || 'unknown'\n    },\n    \n    getStatusText(status) {\n      const statusMap = {\n        'OPEN': '开放中',\n        'FULL': '已满员',\n        'CONFIRMED': '已确认',\n        'CANCELLED': '已取消',\n        'EXPIRED': '已过期',\n        'SHARING_SUCCESS': '拼场成功'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    \n    getRequestStatusClass(status) {\n      const statusMap = {\n        'PENDING': 'pending',\n        'APPROVED': 'approved',\n        'REJECTED': 'rejected',\n        'CANCELLED': 'cancelled'\n      }\n      return statusMap[status] || 'unknown'\n    },\n    \n    getRequestStatusText(status) {\n      const statusMap = {\n        'PENDING': '待审核',\n        'APPROVED': '已通过',\n        'APPROVED_PENDING_PAYMENT': '已批准待支付',\n        'PAID': '拼场成功',\n        'REJECTED': '已拒绝',\n        'CANCELLED': '已取消',\n        'TIMEOUT_CANCELLED': '超时取消'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    \n    // 格式化日期显示\n    formatDate(dateStr) {\n      if (!dateStr) return '日期未知';\n      try {\n        const date = new Date(dateStr);\n        if (isNaN(date.getTime())) return '日期未知';\n        return `${date.getMonth() + 1}月${date.getDate()}日`;\n      } catch (error) {\n        console.error('日期格式化错误:', error);\n        return '日期未知';\n      }\n    },\n    \n    // 格式化时间段显示\n    formatTimeSlot(startTime, endTime) {\n      if (!startTime || !endTime) return '时间未知';\n      \n      // 格式化时间显示（去掉秒数）\n      const formatTime = (timeStr) => {\n        if (!timeStr) return '';\n        // 如果是完整的时间格式（HH:mm:ss），只取前5位\n        if (timeStr.length > 5 && timeStr.includes(':')) {\n          return timeStr.substring(0, 5);\n        }\n        return timeStr;\n      };\n      \n      const formattedStart = formatTime(startTime);\n      const formattedEnd = formatTime(endTime);\n      \n      return `${formattedStart}-${formattedEnd}`;\n    },\n    \n    // 格式化时间范围显示（与预约订单保持一致）\n    formatTimeRange(sharing) {\n      if (!sharing) return '时间未知';\n\n      console.log('格式化时间范围 - 输入数据:', sharing);\n\n      // 优先使用 startTime 和 endTime 字段（这些是纯时间字符串）\n      if (sharing.startTime && sharing.endTime) {\n        console.log('使用 startTime 和 endTime:', sharing.startTime, sharing.endTime);\n        return `${sharing.startTime} - ${sharing.endTime}`;\n      }\n\n      // 检查是否是虚拟订单（有bookingTime字段）\n      if (sharing.bookingTime) {\n        try {\n          // 处理iOS兼容性：确保时间格式正确\n          let bookingTimeStr = sharing.bookingTime\n          if (typeof bookingTimeStr === 'string' && bookingTimeStr.includes(' ') && !bookingTimeStr.includes('T')) {\n            bookingTimeStr = bookingTimeStr.replace(' ', 'T')\n          }\n          const bookingTime = new Date(bookingTimeStr);\n\n          console.log('虚拟订单时间转换 - bookingTime:', sharing.bookingTime, '→', bookingTimeStr, '→', bookingTime)\n\n          const startTimeStr = bookingTime.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n          });\n\n          // 如果有 endTime 字段且是时间字符串格式，直接使用\n          if (sharing.endTime && typeof sharing.endTime === 'string' && sharing.endTime.match(/^\\d{2}:\\d{2}$/)) {\n            console.log('使用字符串格式的 endTime:', sharing.endTime);\n            return `${startTimeStr} - ${sharing.endTime}`;\n          }\n\n          return startTimeStr;\n        } catch (error) {\n          console.error('虚拟订单时间格式化错误:', error);\n          return '时间格式错误';\n        }\n      }\n\n      // 与预约订单保持一致的字段获取逻辑\n      const startTime = sharing.startTime || sharing.bookingStartTime || sharing.start_time;\n      const endTime = sharing.endTime || sharing.bookingEndTime || sharing.end_time;\n\n      if (!startTime || !endTime) {\n        console.log('时间字段缺失 - startTime:', startTime, 'endTime:', endTime);\n        return '时间待定';\n      }\n\n      // 格式化时间显示（去掉秒数）- 与预约订单保持一致\n      const formatTime = (timeStr) => {\n        if (!timeStr) return '';\n        // 如果是完整的时间格式（HH:mm:ss），只取前5位\n        if (timeStr.length > 5 && timeStr.includes(':')) {\n          return timeStr.substring(0, 5);\n        }\n        return timeStr;\n      };\n\n      const formattedStart = formatTime(startTime);\n      const formattedEnd = formatTime(endTime);\n\n      // 如果有多个时间段，显示时间段数量\n      if (timeSlotCount > 1) {\n        return `${formattedStart} - ${formattedEnd} (${timeSlotCount}个时段)`;\n      }\n\n      return `${formattedStart} - ${formattedEnd}`;\n    },\n      \n      formatCreateTime(dateTime) {\n        if (!dateTime) return '';\n\n        // 处理iOS兼容性：将 \"2025-07-12 19:14:58\" 转换为 \"2025/07/12 19:14:58\"\n        let formattedDateTime = dateTime;\n        if (typeof dateTime === 'string' && dateTime.includes(' ') && dateTime.includes('-')) {\n          // 将 \"YYYY-MM-DD HH:mm:ss\" 转换为 \"YYYY/MM/DD HH:mm:ss\"\n          formattedDateTime = dateTime.replace(/-/g, '/');\n        }\n\n        const date = new Date(formattedDateTime);\n\n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          console.warn('Invalid date format:', dateTime);\n          return dateTime; // 返回原始字符串\n        }\n\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hours = String(date.getHours()).padStart(2, '0');\n        const minutes = String(date.getMinutes()).padStart(2, '0');\n        return `${month}-${day} ${hours}:${minutes}`;\n      },\n      \n      // 格式化价格显示\n      formatPrice(price) {\n        if (!price && price !== 0) return '0';\n        const numPrice = Number(price);\n        if (isNaN(numPrice)) return '0';\n        return numPrice.toFixed(2);\n      },\n      \n      // 计算每队费用\n      getPerTeamPrice(order) {\n        if (!order) return '0';\n\n        // 检查是否是虚拟订单（拼场申请）\n        const isVirtualOrder = order.id && order.id < 0;\n\n        if (isVirtualOrder) {\n          // 虚拟订单使用 paymentAmount\n          const amount = order.paymentAmount || 0;\n          return this.formatPrice(amount);\n        }\n\n        // 如果有pricePerPerson字段，直接使用（现在表示每队费用）\n        if (order.pricePerPerson) {\n          return this.formatPrice(order.pricePerPerson);\n        }\n\n        // 如果有paymentAmount字段（拼场申请），直接使用\n        if (order.paymentAmount) {\n          return this.formatPrice(order.paymentAmount);\n        }\n\n        // 否则计算总价的一半（每队费用）\n        const totalPrice = order.totalPrice || order.price || 0;\n        const perTeamPrice = totalPrice / 2; // 费用是总金额的一半\n\n        return this.formatPrice(perTeamPrice);\n      },\n      \n      // 添加通知\n      addNotification(type, message) {\n        const notification = {\n          type: type, // 'success', 'info', 'warning', 'error'\n          message: message,\n          timestamp: Date.now()\n        };\n        this.notifications.unshift(notification);\n        \n        // 自动移除通知（5秒后）\n        setTimeout(() => {\n          this.dismissNotification(0);\n        }, 5000);\n      },\n      \n      // 移除通知\n      dismissNotification(index) {\n        this.notifications.splice(index, 1);\n      },\n      \n      // 获取通知图标\n       getNotificationIcon(type) {\n         const icons = {\n           success: '✅',\n           info: 'ℹ️',\n           warning: '⚠️',\n           error: '❌'\n         };\n         return icons[type] || 'ℹ️';\n       },\n       \n       // 检查新通知\n       async checkForNewNotifications() {\n         try {\n           // 获取最新的申请状态\n           const appliedRequests = await this.getMySharingRequests();\n           const requests = appliedRequests.data || appliedRequests.list || appliedRequests || [];\n           \n           // 检查本地存储的上次状态\n           const lastStatusKey = 'sharing_request_status';\n           const lastStatus = uni.getStorageSync(lastStatusKey) || {};\n           \n           requests.forEach(request => {\n             const requestId = request.id;\n             const currentStatus = request.status;\n             const lastRequestStatus = lastStatus[requestId];\n             \n             // 如果状态发生变化，显示通知\n             if (lastRequestStatus && lastRequestStatus !== currentStatus) {\n               if (currentStatus === 'APPROVED') {\n                 this.addNotification('success', `您的拼场申请已被批准！队伍：${request.teamName}`);\n               } else if (currentStatus === 'REJECTED') {\n                 this.addNotification('warning', `您的拼场申请已被拒绝。队伍：${request.teamName}`);\n               }\n             }\n             \n             // 更新状态记录\n             lastStatus[requestId] = currentStatus;\n           });\n           \n           // 保存最新状态到本地存储\n           uni.setStorageSync(lastStatusKey, lastStatus);\n           \n           // 检查是否有新的申请（针对我创建的拼场）\n           const createdOrders = await this.getMySharingOrders();\n           const orders = createdOrders.data || createdOrders.list || createdOrders || [];\n           \n           const lastApplicationsKey = 'sharing_applications';\n           const lastApplications = uni.getStorageSync(lastApplicationsKey) || {};\n           \n           for (const order of orders) {\n             if (order.status === 'OPEN') {\n               // 获取该拼场的申请列表\n               try {\n                 const applicationsResponse = await this.getSharingRequests(order.id);\n                 const applications = applicationsResponse.data || applicationsResponse.list || applicationsResponse || [];\n                 \n                 const currentApplicationCount = applications.filter(app => app.status === 'PENDING').length;\n                 const lastApplicationCount = lastApplications[order.id] || 0;\n                 \n                 if (currentApplicationCount > lastApplicationCount) {\n                   const newApplications = currentApplicationCount - lastApplicationCount;\n                   this.addNotification('info', `您的拼场「${order.teamName}」收到了 ${newApplications} 个新申请`);\n                 }\n                 \n                 lastApplications[order.id] = currentApplicationCount;\n               } catch (error) {\n                 console.log('获取申请列表失败:', error);\n               }\n             }\n           }\n           \n           // 保存申请数量记录\n           uni.setStorageSync(lastApplicationsKey, lastApplications);\n           \n         } catch (error) {\n           console.log('检查通知失败:', error);\n         }\n       },\n    \n    formatDateTime(dateTimeStr) {\n      if (!dateTimeStr) return ''\n      \n      try {\n        // 处理iOS兼容性问题：将\"YYYY-MM-DD HH:mm:ss\"格式转换为\"YYYY-MM-DDTHH:mm:ss\"\n        let dateString = dateTimeStr\n        if (typeof dateTimeStr === 'string') {\n          // 如果是\"2025-06-28 14:54:59\"格式，转换为\"2025-06-28T14:54:59\"\n          dateString = dateTimeStr.replace(/^(\\d{4}-\\d{2}-\\d{2})\\s(\\d{2}:\\d{2}:\\d{2})$/, '$1T$2')\n          // 如果是\"2025-06-28 14:54\"格式，转换为\"2025-06-28T14:54\"\n          dateString = dateString.replace(/^(\\d{4}-\\d{2}-\\d{2})\\s(\\d{2}:\\d{2})$/, '$1T$2')\n        }\n        \n        const date = new Date(dateString)\n        if (isNaN(date.getTime())) return '--'\n        \n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`\n      } catch (error) {\n        console.error('日期格式化错误:', error)\n        return '--'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 通知区域样式 */\n.notification-area {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 9999;\n  padding: 20rpx;\n}\n\n.notification-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  margin-bottom: 16rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  animation: slideDown 0.3s ease-out;\n  \n  &.success {\n    background-color: #f0f9ff;\n    border-left: 8rpx solid #10b981;\n  }\n  \n  &.info {\n    background-color: #eff6ff;\n    border-left: 8rpx solid #3b82f6;\n  }\n  \n  &.warning {\n    background-color: #fffbeb;\n    border-left: 8rpx solid #f59e0b;\n  }\n  \n  &.error {\n    background-color: #fef2f2;\n    border-left: 8rpx solid #ef4444;\n  }\n}\n\n.notification-icon {\n  font-size: 32rpx;\n  margin-right: 16rpx;\n}\n\n.notification-text {\n  flex: 1;\n  font-size: 28rpx;\n  color: #374151;\n  line-height: 1.4;\n}\n\n.notification-close {\n  font-size: 36rpx;\n  color: #9ca3af;\n  margin-left: 16rpx;\n  cursor: pointer;\n}\n\n@keyframes slideDown {\n  from {\n    transform: translateY(-100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 32rpx;\n  background-color: #ffffff;\n  border-bottom: 1rpx solid #eee;\n  \n  .nav-left {\n    width: 80rpx;\n    \n    .nav-icon {\n      font-size: 48rpx;\n      color: #333;\n    }\n  }\n  \n  .nav-title {\n    font-size: 36rpx;\n    font-weight: 600;\n    color: #333;\n  }\n  \n  .nav-right {\n    width: 80rpx;\n  }\n}\n\n.tabs-container {\n  display: flex;\n  background-color: #ffffff;\n  border-bottom: 1rpx solid #eee;\n  \n  .tab-item {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 32rpx 16rpx;\n    position: relative;\n    \n    .tab-text {\n      font-size: 28rpx;\n      color: #666;\n      margin-right: 8rpx;\n    }\n    \n    .tab-count {\n      background-color: #ff6b35;\n      color: #ffffff;\n      font-size: 20rpx;\n      padding: 4rpx 8rpx;\n      border-radius: 10rpx;\n      min-width: 32rpx;\n      text-align: center;\n    }\n    \n    &.active {\n      .tab-text {\n        color: #ff6b35;\n        font-weight: 600;\n      }\n      \n      &::after {\n        content: '';\n        position: absolute;\n        bottom: 0;\n        left: 50%;\n        transform: translateX(-50%);\n        width: 60rpx;\n        height: 4rpx;\n        background-color: #ff6b35;\n        border-radius: 2rpx;\n      }\n    }\n  }\n}\n\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 32rpx;\n  \n  text {\n    font-size: 28rpx;\n    color: #999;\n  }\n}\n\n.error-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 32rpx;\n  \n  .error-icon {\n    font-size: 80rpx;\n    margin-bottom: 24rpx;\n  }\n  \n  .error-text {\n    font-size: 28rpx;\n    color: #999;\n    margin-bottom: 32rpx;\n  }\n  \n  .retry-btn {\n    padding: 16rpx 32rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n  }\n}\n\n.content {\n  padding: 24rpx;\n}\n\n.orders-list {\n  .order-item {\n    background-color: #ffffff;\n    border-radius: 16rpx;\n    padding: 32rpx;\n    margin-bottom: 24rpx;\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);\n    \n    &.success-item {\n      border: 2rpx solid #4caf50;\n    }\n    \n    .order-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: 24rpx;\n      \n      .venue-name {\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #333;\n      }\n      \n      .status-badge {\n        padding: 8rpx 16rpx;\n        border-radius: 20rpx;\n        \n        .status-text {\n          font-size: 24rpx;\n          font-weight: 500;\n        }\n        \n        &.open {\n          background-color: #e3f2fd;\n          color: #1976d2;\n        }\n        \n        &.full {\n          background-color: #fff3e0;\n          color: #f57c00;\n        }\n        \n        &.confirmed {\n          background-color: #e8f5e8;\n          color: #4caf50;\n        }\n        \n        &.success {\n          background-color: #e8f5e8;\n          color: #4caf50;\n        }\n        \n        &.cancelled {\n          background-color: #ffebee;\n          color: #f44336;\n        }\n        \n        &.pending {\n          background-color: #fff3e0;\n          color: #f57c00;\n        }\n        \n        &.approved {\n          background-color: #e8f5e8;\n          color: #4caf50;\n        }\n        \n        &.rejected {\n          background-color: #ffebee;\n          color: #f44336;\n        }\n      }\n    }\n    \n    .order-info {\n      margin-bottom: 24rpx;\n      \n      .info-row {\n        display: flex;\n        margin-bottom: 8rpx;\n        align-items: center;\n      }\n      \n      .time-icon,\n      .location-icon,\n      .team-icon,\n      .participants-icon,\n      .price-icon,\n      .create-icon,\n      .message-icon {\n        font-size: 28rpx;\n        margin-right: 12rpx;\n        width: 32rpx;\n        text-align: center;\n        flex-shrink: 0;\n      }\n      \n      .info-value {\n        font-size: 28rpx;\n        color: #333;\n        flex: 1;\n        line-height: 1.4;\n      }\n    }\n    \n    .order-actions {\n      display: flex;\n      gap: 16rpx;\n      \n      .action-btn {\n        padding: 16rpx 24rpx;\n        border-radius: 8rpx;\n        font-size: 26rpx;\n        border: none;\n        \n        &.manage-btn {\n          background-color: #ff6b35;\n          color: #ffffff;\n        }\n        \n        &.cancel-btn {\n          background-color: #f5f5f5;\n          color: #666;\n        }\n        \n        &.contact-btn {\n          background-color: #4caf50;\n          color: #ffffff;\n        }\n      }\n    }\n  }\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 32rpx;\n  \n  .empty-icon {\n    font-size: 80rpx;\n    margin-bottom: 24rpx;\n  }\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: #999;\n    margin-bottom: 32rpx;\n  }\n  \n  .create-btn,\n  .browse-btn {\n    padding: 20rpx 40rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/sharing/my-orders.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapGetters", "uni", "mapActions"], "mappings": ";;AAkQA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,OAAO;AAAA,MACP,eAAe,CAAE;AAAA,MACjB,eAAe,CAAE;AAAA,MACjB,eAAe,CAAE;AAAA,MACjB,eAAe,CAAC;AAAA;AAAA,IAClB;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,GAAGA,yBAAW,QAAQ,CAAC,UAAU,CAAC;AAAA;AAAA,IAGlC,sBAAsB;AACpB,aAAO,CAAC,GAAG,KAAK,aAAa,EAAE,KAAK,CAAC,GAAG,MAAM;;AAC5C,cAAM,QAAQ,IAAI,OAAK,OAAE,cAAF,mBAAa,QAAQ,MAAM,SAAQ,CAAC;AAC3D,cAAM,QAAQ,IAAI,OAAK,OAAE,cAAF,mBAAa,QAAQ,MAAM,SAAQ,CAAC;AAC3D,eAAO,MAAM,QAAO,IAAK,MAAM,QAAO;AAAA,MACxC,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB;AACpB,aAAO,CAAC,GAAG,KAAK,aAAa,EAAE,KAAK,CAAC,GAAG,MAAM;;AAC5C,cAAM,QAAQ,IAAI,OAAK,OAAE,cAAF,mBAAa,QAAQ,MAAM,SAAQ,CAAC;AAC3D,cAAM,QAAQ,IAAI,OAAK,OAAE,cAAF,mBAAa,QAAQ,MAAM,SAAQ,CAAC;AAC3D,eAAO,MAAM,QAAO,IAAK,MAAM,QAAO;AAAA,MACxC,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB;AACpB,aAAO,CAAC,GAAG,KAAK,aAAa,EAAE,KAAK,CAAC,GAAG,MAAM;;AAC5C,cAAM,QAAQ,IAAI,OAAK,OAAE,cAAF,mBAAa,QAAQ,MAAM,SAAQ,CAAC;AAC3D,cAAM,QAAQ,IAAI,OAAK,OAAE,cAAF,mBAAa,QAAQ,MAAM,SAAQ,CAAC;AAC3D,eAAO,MAAM,QAAO,IAAK,MAAM,QAAO;AAAA,MACxC,CAAC;AAAA,IACF;AAAA,IAED,OAAO;AACL,aAAO;AAAA,QACL;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO,KAAK,oBAAoB;AAAA,QACjC;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO,KAAK,oBAAoB;AAAA,QACjC;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO,KAAK,oBAAoB;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,SAAQ;AACb,SAAK,yBAAwB;AAAA,EAC9B;AAAA,EAED,SAAS;AAEP,SAAK,yBAAwB;AAAA,EAC9B;AAAA,EAED,SAAS;AAEP,SAAK,SAAS;AAAA,EACf;AAAA,EAED,oBAAoB;AAClB,SAAK,WAAW,QAAQ,MAAM;AAC5BC,oBAAAA,MAAI,oBAAoB;AAAA,KACzB;AAAA,EACF;AAAA,EAED,SAAS;AAAA,IACP,GAAGC,cAAAA,WAAW,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IAED,MAAM,WAAW;AACf,WAAK,UAAU;AACf,WAAK,QAAQ;AAGb,UAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,eAAO,aAAa,SAAS,4BAA4B;AACzD,eAAO,aAAa,SAAS,qBAAqB;AAAA,MACpD;AAEA,UAAI;AACF,cAAM,QAAQ,IAAI;AAAA,UAChB,KAAK,kBAAmB;AAAA,UACxB,KAAK,kBAAmB;AAAA,UACxB,KAAK,kBAAkB;AAAA,SACxB;AAED,aAAK,aAAa;AAAA,MAClB,SAAO,OAAO;AACdD,sBAAAA,MAAA,MAAA,SAAA,sCAAc,WAAW,KAAK;AAC9B,aAAK,QAAQ,MAAM,WAAW;AAAA,MAChC,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,MAAM,oBAAoB;AACxB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,iBAAiB;AAC7B,cAAM,WAAW,MAAM,KAAK,0BAA0B;AACtDA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,aAAa,QAAQ;AACjCA,sBAAA,MAAA,MAAA,OAAA,sCAAY,cAAc,OAAO,QAAQ;AACzCA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,cAAc,KAAK,UAAU,UAAU,MAAM,CAAC,CAAC;AAG3D,YAAI,SAAS,CAAC;AACd,YAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,mBAAS;AAAA,mBACA,YAAY,MAAM,QAAQ,SAAS,IAAI,GAAG;AACnD,mBAAS,SAAS;AAAA,mBACT,YAAY,MAAM,QAAQ,SAAS,IAAI,GAAG;AACnD,mBAAS,SAAS;AAAA,QACpB;AAEAA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,aAAa,MAAM;AAC/BA,sBAAY,MAAA,MAAA,OAAA,sCAAA,YAAY,OAAO,MAAM;AAGrC,aAAK,gBAAgB;AAAA,MACrB,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,sCAAc,gBAAgB,KAAK;AACnC,aAAK,gBAAgB,CAAC;AAAA,MACxB;AAAA,IACD;AAAA,IAED,MAAM,oBAAoB;AACxB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,iBAAiB;AAC7B,cAAM,WAAW,MAAM,KAAK,qBAAqB;AACjDA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,iBAAiB,QAAQ;AACrCA,sBAAY,MAAA,MAAA,OAAA,sCAAA,kBAAkB,OAAO,QAAQ;AAC7CA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,kBAAkB,KAAK,UAAU,UAAU,MAAM,CAAC,CAAC;AAG/D,YAAI,WAAW,CAAC;AAChB,YAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,qBAAW;AAAA,mBACF,YAAY,MAAM,QAAQ,SAAS,IAAI,GAAG;AACnD,qBAAW,SAAS;AAAA,mBACX,YAAY,MAAM,QAAQ,SAAS,IAAI,GAAG;AACnD,qBAAW,SAAS;AAAA,QACtB;AAEAA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,aAAa,QAAQ;AACjCA,sBAAY,MAAA,MAAA,OAAA,sCAAA,YAAY,SAAS,MAAM;AAEvC,aAAK,gBAAgB;AAAA,MACrB,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,cAAc,KAAK;AACjC,aAAK,gBAAgB,CAAC;AAAA,MACxB;AAAA,IACD;AAAA,IAED,MAAM,oBAAoB;AACxB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,gBAAgB;AAG5B,cAAM,kBAAkB,MAAM,KAAK,0BAA0B;AAC7DA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,kBAAkB,eAAe;AAE7C,cAAM,gBAAgB,gBAAgB,QAAQ,gBAAgB,QAAQ,mBAAmB,CAAC;AAC1FA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,gBAAgB,aAAa;AACzCA,sBAAA,MAAA,MAAA,OAAA,sCAAY,kBAAkB,cAAc,IAAI,QAAM,EAAE,IAAI,EAAE,IAAI,QAAQ,EAAE,OAAQ,EAAC,CAAC;AAEtF,cAAM,yBAAyB,cAAc;AAAA,UAAO,WAClD,MAAM,WAAW,UAAU,MAAM,WAAW,eAAe,MAAM,WAAW;AAAA,QAC9E;AACAA,sBAAAA,yDAAY,eAAe,sBAAsB;AAGjD,cAAM,kBAAkB,MAAM,KAAK,qBAAqB;AACxDA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,kBAAkB,eAAe;AAE7C,cAAM,kBAAkB,gBAAgB,QAAQ,gBAAgB,QAAQ,mBAAmB,CAAC;AAC5FA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,gBAAgB,eAAe;AAC3CA,sBAAA,MAAA,MAAA,OAAA,sCAAY,kBAAkB,gBAAgB,IAAI,OAAM;;AAAA;AAAA,YACtD,IAAI,EAAE;AAAA,YACN,QAAQ,EAAE;AAAA,YACV,qBAAoB,OAAE,iBAAF,mBAAgB;AAAA,UACrC;AAAA,SAAC,CAAC;AAEH,cAAM,yBAAyB,gBAAgB,OAAO,aAAW;AAC/D,gBAAM,SAAS,QAAQ,WAAW;AAClCA,wBAAAA,yDAAY,QAAQ,QAAQ,EAAE,YAAY,QAAQ,MAAM,YAAY,MAAM,EAAE;AAC5E,iBAAO;AAAA,QACT,CAAC,EAAE,IAAI,aAAW;AAChBA,wBAAAA,yDAAY,kBAAkB,OAAO;AACrC,iBAAO;AAAA;AAAA,YAEL,IAAI,QAAQ,WAAW,QAAQ;AAAA,YAC/B,SAAS,QAAQ,WAAW,OAAO,QAAQ,EAAE;AAAA,YAC7C,WAAW,QAAQ,aAAa;AAAA,YAChC,SAAS,QAAQ;AAAA,YACjB,eAAe,QAAQ,iBAAiB;AAAA,YACxC,QAAQ;AAAA;AAAA,YACR,WAAW,QAAQ;AAAA,YACnB,WAAW,QAAQ;AAAA;AAAA,YAGnB,UAAU,QAAQ,qBAAqB,QAAQ,YAAY;AAAA,YAC3D,aAAa,QAAQ,oBAAoB,QAAQ,eAAe;AAAA,YAChE,qBAAqB,QAAQ,uBAAuB;AAAA,YACpD,iBAAiB,QAAQ,mBAAmB;AAAA,YAC5C,aAAa,QAAQ,eAAe;AAAA;AAAA,YAGpC,YAAY,QAAQ,cAAc;AAAA,YAClC,cAAc,QAAQ,iBAAiB,QAAQ,cAAc;AAAA,YAC7D,eAAe,QAAQ,iBAAiB;AAAA;AAAA,YAGxC,aAAa,QAAQ;AAAA,YACrB,WAAW,QAAQ;AAAA,YACnB,SAAS,QAAQ;AAAA,YACjB,aAAa,QAAQ;AAAA;AAAA,YAGrB,gBAAgB;AAAA,YAChB,eAAe,QAAQ;AAAA,UACzB;AAAA,SACD;AACDA,sBAAAA,yDAAY,eAAe,sBAAsB;AAGjD,aAAK,gBAAgB,CAAC,GAAG,wBAAwB,GAAG,sBAAsB;AAC1EA,sBAAY,MAAA,MAAA,OAAA,sCAAA,gBAAgB,KAAK,aAAa;AAC9CA,+EAAY,cAAc,KAAK,cAAc,MAAM;AAAA,MACnD,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,aAAa,KAAK;AAChC,aAAK,gBAAgB,CAAC;AAAA,MACxB;AAAA,IACD;AAAA,IAED,UAAU,KAAK;AACb,WAAK,aAAa;AAAA,IACnB;AAAA,IAED,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA,IAED,WAAW,WAAW;AACpB,UAAI,CAAC,WAAW;AACdA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,eAAe;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AACAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,SAAS;AAAA,OAC3C;AAAA,IACF;AAAA,IAED,kBAAkB,SAAS;AACzBA,oBAAAA,yDAAY,WAAW,OAAO;AAE9B,YAAM,WAAW,QAAQ,kBAAkB,QAAQ;AACnD,UAAI,UAAU;AACZA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,4BAA4B,QAAQ;AAAA,SAC1C;AAAA,aACI;AACLA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,gCAAgC;AAC9CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA,IAED,WAAW,WAAW;AACpBA,oBAAAA,yDAAY,mBAAmB;AAC/BA,oBAAAA,MAAY,MAAA,OAAA,sCAAA,YAAY,SAAS;AACjCA,6EAAY,UAAU,4BAA4B,SAAS,EAAE;AAE7DA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,SAAS;AAAA,OAC3C;AAAA,IACF;AAAA,IAED,gBAAgB;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACF;AAAA,IAED,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACF;AAAA,IAED,MAAM,YAAY,SAAS;AACzB,UAAI;AACF,cAAMA,cAAAA,MAAI,UAAU;AAAA,UAClB,OAAO;AAAA,UACP,SAAS;AAAA,SACV;AAEDA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AACnC,cAAM,KAAK,mBAAmB,OAAO;AACrCA,sBAAAA,MAAI,YAAY;AAEhB,aAAK,gBAAgB,QAAQ,SAAS;AAEtC,aAAK,gBAAgB,QAAQ,SAAS;AAEtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAED,aAAK,SAAS;AAAA,MACd,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChB,YAAI,MAAM,WAAW,yBAAyB;AAC5C,eAAK,gBAAgB,SAAS,QAAQ;AACtCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,WAAW;AAAA,YACxB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACF;AAAA,IACD;AAAA,IAED,MAAM,cAAc,WAAW;AAC7B,UAAI;AACF,cAAMA,cAAAA,MAAI,UAAU;AAAA,UAClB,OAAO;AAAA,UACP,SAAS;AAAA,SACV;AAEDA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AACnC,cAAM,KAAK,qBAAqB,SAAS;AACzCA,sBAAAA,MAAI,YAAY;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAED,aAAK,SAAS;AAAA,MACd,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChB,YAAI,MAAM,WAAW,yBAAyB;AAC5CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,WAAW;AAAA,YACxB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACF;AAAA,IACD;AAAA,IAID,eAAe,QAAQ;AACrB,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA,IAED,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,QACX,mBAAmB;AAAA,MACrB;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA,IAED,sBAAsB,QAAQ;AAC5B,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,MACf;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA,IAED,qBAAqB,QAAQ;AAC3B,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,4BAA4B;AAAA,QAC5B,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,qBAAqB;AAAA,MACvB;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AACrB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAI,MAAM,KAAK,QAAO,CAAE;AAAG,iBAAO;AAClC,eAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,MAC/C,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,sCAAA,YAAY,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,WAAW,SAAS;AACjC,UAAI,CAAC,aAAa,CAAC;AAAS,eAAO;AAGnC,YAAM,aAAa,CAAC,YAAY;AAC9B,YAAI,CAAC;AAAS,iBAAO;AAErB,YAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AAC/C,iBAAO,QAAQ,UAAU,GAAG,CAAC;AAAA,QAC/B;AACA,eAAO;AAAA;AAGT,YAAM,iBAAiB,WAAW,SAAS;AAC3C,YAAM,eAAe,WAAW,OAAO;AAEvC,aAAO,GAAG,cAAc,IAAI,YAAY;AAAA,IACzC;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACvB,UAAI,CAAC;AAAS,eAAO;AAErBA,oBAAY,MAAA,MAAA,OAAA,sCAAA,mBAAmB,OAAO;AAGtC,UAAI,QAAQ,aAAa,QAAQ,SAAS;AACxCA,4BAAY,MAAA,OAAA,sCAAA,2BAA2B,QAAQ,WAAW,QAAQ,OAAO;AACzE,eAAO,GAAG,QAAQ,SAAS,MAAM,QAAQ,OAAO;AAAA,MAClD;AAGA,UAAI,QAAQ,aAAa;AACvB,YAAI;AAEF,cAAI,iBAAiB,QAAQ;AAC7B,cAAI,OAAO,mBAAmB,YAAY,eAAe,SAAS,GAAG,KAAK,CAAC,eAAe,SAAS,GAAG,GAAG;AACvG,6BAAiB,eAAe,QAAQ,KAAK,GAAG;AAAA,UAClD;AACA,gBAAM,cAAc,IAAI,KAAK,cAAc;AAE3CA,wBAAAA,MAAA,MAAA,OAAA,sCAAY,2BAA2B,QAAQ,aAAa,KAAK,gBAAgB,KAAK,WAAW;AAEjG,gBAAM,eAAe,YAAY,mBAAmB,SAAS;AAAA,YAC3D,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV,CAAC;AAGD,cAAI,QAAQ,WAAW,OAAO,QAAQ,YAAY,YAAY,QAAQ,QAAQ,MAAM,eAAe,GAAG;AACpGA,mFAAY,qBAAqB,QAAQ,OAAO;AAChD,mBAAO,GAAG,YAAY,MAAM,QAAQ,OAAO;AAAA,UAC7C;AAEA,iBAAO;AAAA,QACP,SAAO,OAAO;AACdA,wBAAc,MAAA,MAAA,SAAA,sCAAA,gBAAgB,KAAK;AACnC,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,YAAM,YAAY,QAAQ,aAAa,QAAQ,oBAAoB,QAAQ;AAC3E,YAAM,UAAU,QAAQ,WAAW,QAAQ,kBAAkB,QAAQ;AAErE,UAAI,CAAC,aAAa,CAAC,SAAS;AAC1BA,4BAAA,MAAA,OAAA,sCAAY,uBAAuB,WAAW,YAAY,OAAO;AACjE,eAAO;AAAA,MACT;AAGA,YAAM,aAAa,CAAC,YAAY;AAC9B,YAAI,CAAC;AAAS,iBAAO;AAErB,YAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AAC/C,iBAAO,QAAQ,UAAU,GAAG,CAAC;AAAA,QAC/B;AACA,eAAO;AAAA;AAGT,YAAM,iBAAiB,WAAW,SAAS;AAC3C,YAAM,eAAe,WAAW,OAAO;AAGvC,UAAI,gBAAgB,GAAG;AACrB,eAAO,GAAG,cAAc,MAAM,YAAY,KAAK,aAAa;AAAA,MAC9D;AAEA,aAAO,GAAG,cAAc,MAAM,YAAY;AAAA,IAC3C;AAAA,IAEC,iBAAiB,UAAU;AACzB,UAAI,CAAC;AAAU,eAAO;AAGtB,UAAI,oBAAoB;AACxB,UAAI,OAAO,aAAa,YAAY,SAAS,SAAS,GAAG,KAAK,SAAS,SAAS,GAAG,GAAG;AAEpF,4BAAoB,SAAS,QAAQ,MAAM,GAAG;AAAA,MAChD;AAEA,YAAM,OAAO,IAAI,KAAK,iBAAiB;AAGvC,UAAI,MAAM,KAAK,QAAO,CAAE,GAAG;AACzBA,gFAAa,wBAAwB,QAAQ;AAC7C,eAAO;AAAA,MACT;AAEA,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,YAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,YAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,aAAO,GAAG,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO;AAAA,IAC3C;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,CAAC,SAAS,UAAU;AAAG,eAAO;AAClC,YAAM,WAAW,OAAO,KAAK;AAC7B,UAAI,MAAM,QAAQ;AAAG,eAAO;AAC5B,aAAO,SAAS,QAAQ,CAAC;AAAA,IAC1B;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACrB,UAAI,CAAC;AAAO,eAAO;AAGnB,YAAM,iBAAiB,MAAM,MAAM,MAAM,KAAK;AAE9C,UAAI,gBAAgB;AAElB,cAAM,SAAS,MAAM,iBAAiB;AACtC,eAAO,KAAK,YAAY,MAAM;AAAA,MAChC;AAGA,UAAI,MAAM,gBAAgB;AACxB,eAAO,KAAK,YAAY,MAAM,cAAc;AAAA,MAC9C;AAGA,UAAI,MAAM,eAAe;AACvB,eAAO,KAAK,YAAY,MAAM,aAAa;AAAA,MAC7C;AAGA,YAAM,aAAa,MAAM,cAAc,MAAM,SAAS;AACtD,YAAM,eAAe,aAAa;AAElC,aAAO,KAAK,YAAY,YAAY;AAAA,IACrC;AAAA;AAAA,IAGD,gBAAgB,MAAM,SAAS;AAC7B,YAAM,eAAe;AAAA,QACnB;AAAA;AAAA,QACA;AAAA,QACA,WAAW,KAAK,IAAI;AAAA;AAEtB,WAAK,cAAc,QAAQ,YAAY;AAGvC,iBAAW,MAAM;AACf,aAAK,oBAAoB,CAAC;AAAA,MAC3B,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,oBAAoB,OAAO;AACzB,WAAK,cAAc,OAAO,OAAO,CAAC;AAAA,IACnC;AAAA;AAAA,IAGA,oBAAoB,MAAM;AACxB,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,QACT,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO;AAAA;AAET,aAAO,MAAM,IAAI,KAAK;AAAA,IACvB;AAAA;AAAA,IAGD,MAAM,2BAA2B;AAC/B,UAAI;AAEF,cAAM,kBAAkB,MAAM,KAAK;AACnC,cAAM,WAAW,gBAAgB,QAAQ,gBAAgB,QAAQ,mBAAmB;AAGpF,cAAM,gBAAgB;AACtB,cAAM,aAAaA,cAAG,MAAC,eAAe,aAAa,KAAK,CAAA;AAExD,iBAAS,QAAQ,aAAW;AAC1B,gBAAM,YAAY,QAAQ;AAC1B,gBAAM,gBAAgB,QAAQ;AAC9B,gBAAM,oBAAoB,WAAW,SAAS;AAG9C,cAAI,qBAAqB,sBAAsB,eAAe;AAC5D,gBAAI,kBAAkB,YAAY;AAChC,mBAAK,gBAAgB,WAAW,iBAAiB,QAAQ,QAAQ,EAAE;AAAA,YACrE,WAAW,kBAAkB,YAAY;AACvC,mBAAK,gBAAgB,WAAW,iBAAiB,QAAQ,QAAQ,EAAE;AAAA,YACrE;AAAA,UACF;AAGA,qBAAW,SAAS,IAAI;AAAA,QAC1B,CAAC;AAGDA,sBAAAA,MAAI,eAAe,eAAe,UAAU;AAG5C,cAAM,gBAAgB,MAAM,KAAK;AACjC,cAAM,SAAS,cAAc,QAAQ,cAAc,QAAQ,iBAAiB;AAE5E,cAAM,sBAAsB;AAC5B,cAAM,mBAAmBA,cAAG,MAAC,eAAe,mBAAmB,KAAK,CAAA;AAEpE,mBAAW,SAAS,QAAQ;AAC1B,cAAI,MAAM,WAAW,QAAQ;AAE3B,gBAAI;AACF,oBAAM,uBAAuB,MAAM,KAAK,mBAAmB,MAAM,EAAE;AACnE,oBAAM,eAAe,qBAAqB,QAAQ,qBAAqB,QAAQ,wBAAwB;AAEvG,oBAAM,0BAA0B,aAAa,OAAO,SAAO,IAAI,WAAW,SAAS,EAAE;AACrF,oBAAM,uBAAuB,iBAAiB,MAAM,EAAE,KAAK;AAE3D,kBAAI,0BAA0B,sBAAsB;AAClD,sBAAM,kBAAkB,0BAA0B;AAClD,qBAAK,gBAAgB,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,eAAe,OAAO;AAAA,cACnF;AAEA,+BAAiB,MAAM,EAAE,IAAI;AAAA,YAC7B,SAAO,OAAO;AACdA,4BAAY,MAAA,MAAA,OAAA,sCAAA,aAAa,KAAK;AAAA,YAChC;AAAA,UACF;AAAA,QACF;AAGAA,sBAAAA,MAAI,eAAe,qBAAqB,gBAAgB;AAAA,MAExD,SAAO,OAAO;AACdA,sBAAY,MAAA,MAAA,OAAA,sCAAA,WAAW,KAAK;AAAA,MAC9B;AAAA,IACD;AAAA,IAEJ,eAAe,aAAa;AAC1B,UAAI,CAAC;AAAa,eAAO;AAEzB,UAAI;AAEF,YAAI,aAAa;AACjB,YAAI,OAAO,gBAAgB,UAAU;AAEnC,uBAAa,YAAY,QAAQ,8CAA8C,OAAO;AAEtF,uBAAa,WAAW,QAAQ,wCAAwC,OAAO;AAAA,QACjF;AAEA,cAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAI,MAAM,KAAK,QAAS,CAAA;AAAG,iBAAO;AAElC,eAAO,GAAG,KAAK,SAAQ,IAAK,CAAC,IAAI,KAAK,QAAO,CAAE,KAAK,KAAK,SAAQ,EAAG,WAAW,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,aAAa,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAAA,MAChJ,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,sCAAc,YAAY,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/8BA,GAAG,WAAW,eAAe;"}