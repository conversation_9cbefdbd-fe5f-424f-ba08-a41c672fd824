{"version": 3, "file": "login.js", "sources": ["pages/user/login.vue", "pages/user/login.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 头部logo -->\n    <view class=\"header\">\n      <image src=\"/static/logo.png\" class=\"logo\" mode=\"aspectFit\" />\n      <text class=\"app-name\">体育馆预约</text>\n      <text class=\"app-slogan\">让运动更简单</text>\n    </view>\n    \n    <!-- 登录表单 -->\n    <view class=\"login-form\">\n      <!-- 标签切换 -->\n      <view class=\"tab-bar\">\n        <view \n          class=\"tab-item\" \n          :class=\"{ active: loginType === 'password' }\"\n          @click=\"switchLoginType('password')\"\n        >\n          密码登录\n        </view>\n        <view \n          class=\"tab-item\" \n          :class=\"{ active: loginType === 'sms' }\"\n          @click=\"switchLoginType('sms')\"\n        >\n          验证码登录\n        </view>\n      </view>\n      \n      <!-- 手机号输入 -->\n      <view class=\"form-item\">\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">📱</text>\n          <input \n            v-model=\"formData.phone\" \n            class=\"input-field\" \n            type=\"number\"\n            placeholder=\"请输入手机号\"\n            maxlength=\"11\"\n          />\n        </view>\n      </view>\n      \n      <!-- 密码登录 -->\n      <view v-if=\"loginType === 'password'\" class=\"form-item\">\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">🔒</text>\n          <input \n            v-model=\"formData.password\" \n            class=\"input-field\" \n            :password=\"!showPassword\"\n            placeholder=\"请输入密码\"\n          />\n          <text \n            class=\"password-toggle\" \n            @click=\"togglePassword\"\n          >\n            {{ showPassword ? '🙈' : '👁️' }}\n          </text>\n        </view>\n      </view>\n      \n      <!-- 验证码登录 -->\n      <view v-if=\"loginType === 'sms'\" class=\"form-item\">\n        <view class=\"input-wrapper\">\n          <text class=\"input-icon\">💬</text>\n          <input \n            v-model=\"formData.smsCode\" \n            class=\"input-field\" \n            type=\"number\"\n            placeholder=\"请输入验证码\"\n            maxlength=\"6\"\n          />\n          <button \n            class=\"sms-btn\" \n            :disabled=\"!canSendSms || smsCountdown > 0\"\n            @click=\"sendSmsCode\"\n          >\n            {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}\n          </button>\n        </view>\n      </view>\n      \n      <!-- 登录按钮 -->\n      <button \n        class=\"login-btn\" \n        :disabled=\"!canLogin\"\n        @click=\"handleLogin\"\n      >\n        登录\n      </button>\n      \n      <!-- 忘记密码 -->\n      <view v-if=\"loginType === 'password'\" class=\"forgot-password\">\n        <text @click=\"navigateToReset\">忘记密码？</text>\n      </view>\n    </view>\n    \n    <!-- 其他登录方式 -->\n    <view class=\"other-login\">\n      <view class=\"divider\">\n        <text class=\"divider-text\">其他登录方式</text>\n      </view>\n      \n      <view class=\"social-login\">\n        <view class=\"social-item\" @click=\"wechatLogin\">\n          <text class=\"social-icon\">💬</text>\n          <text class=\"social-text\">微信登录</text>\n        </view>\n        \n        <view class=\"social-item\" @click=\"appleLogin\">\n          <text class=\"social-icon\">🍎</text>\n          <text class=\"social-text\">Apple登录</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部链接 -->\n    <view class=\"footer\">\n      <text class=\"footer-text\">还没有账号？</text>\n      <text class=\"footer-link\" @click=\"navigateToRegister\">立即注册</text>\n    </view>\n    \n    <!-- 协议提示 -->\n    <view class=\"agreement\">\n      <text class=\"agreement-text\">登录即表示同意</text>\n      <text class=\"agreement-link\" @click=\"showUserAgreement\">《用户协议》</text>\n      <text class=\"agreement-text\">和</text>\n      <text class=\"agreement-link\" @click=\"showPrivacyPolicy\">《隐私政策》</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useUserStore } from '@/stores/user.js'\n\nexport default {\n  name: 'UserLogin',\n  \n  data() {\n    return {\n      loginType: 'password', // 'password' | 'sms'\n      showPassword: false,\n      smsCountdown: 0,\n      smsTimer: null,\n      userStore: null,\n\n      formData: {\n        phone: '',\n        password: '',\n        smsCode: ''\n      }\n    }\n  },\n  \n  computed: {\n    loading() {\n      return this.userStore?.loading || false\n    },\n\n    // 是否可以发送短信\n    canSendSms() {\n      return this.formData.phone.length === 11 && /^1[3-9]\\d{9}$/.test(this.formData.phone)\n    },\n    \n    // 是否可以登录\n    canLogin() {\n      if (this.loginType === 'password') {\n        return this.canSendSms && this.formData.password.length >= 6\n      } else {\n        return this.canSendSms && this.formData.smsCode.length === 6\n      }\n    }\n  },\n  \n  onLoad(options) {\n    // 初始化Pinia store\n    try {\n      this.userStore = useUserStore()\n      console.log('[Login] userStore初始化成功:', !!this.userStore)\n      console.log('[Login] userStore所有方法:', Object.getOwnPropertyNames(this.userStore))\n      console.log('[Login] userStore所有可枚举属性:', Object.keys(this.userStore))\n      console.log('[Login] getUserInfo方法存在:', typeof this.userStore.getUserInfo === 'function')\n      console.log('[Login] getUserInfo在原型上:', typeof this.userStore.__proto__.getUserInfo === 'function')\n    } catch (error) {\n      console.error('[Login] userStore初始化失败:', error)\n    }\n\n    // 如果有重定向页面，保存起来\n    if (options.redirect) {\n      this.redirectUrl = decodeURIComponent(options.redirect)\n    }\n  },\n  \n  onUnload() {\n    // 清除定时器\n    if (this.smsTimer) {\n      clearInterval(this.smsTimer)\n    }\n  },\n  \n  methods: {\n    \n    // 切换登录方式\n    switchLoginType(type) {\n      this.loginType = type\n      // 清空表单\n      this.formData.password = ''\n      this.formData.smsCode = ''\n    },\n    \n    // 切换密码显示\n    togglePassword() {\n      this.showPassword = !this.showPassword\n    },\n    \n    // 发送短信验证码\n    async sendSmsCode() {\n      if (!this.canSendSms || this.smsCountdown > 0) return\n\n      try {\n        await this.userStore.getSmsCode({\n          phone: this.formData.phone,\n          type: 'login'\n        })\n        uni.showToast({\n          title: '验证码已发送',\n          icon: 'success'\n        })\n\n        // 开始倒计时\n        this.startCountdown()\n\n      } catch (error) {\n        console.error('发送验证码失败:', error)\n        uni.showToast({\n          title: error.message || '发送失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 开始倒计时\n    startCountdown() {\n      this.smsCountdown = 60\n      this.smsTimer = setInterval(() => {\n        this.smsCountdown--\n        if (this.smsCountdown <= 0) {\n          clearInterval(this.smsTimer)\n          this.smsTimer = null\n        }\n      }, 1000)\n    },\n    \n    // 处理登录\n    async handleLogin() {\n      if (!this.canLogin) return\n      \n      // 验证手机号\n      if (!this.validatePhone()) return\n      \n      try {\n        let result\n        if (this.loginType === 'password') {\n          // 密码登录\n          result = await this.userStore.login({\n            username: this.formData.phone,\n            password: this.formData.password\n          })\n        } else {\n          // 验证码登录\n          result = await this.userStore.smsLogin({\n            phone: this.formData.phone,\n            smsCode: this.formData.smsCode\n          })\n        }\n\n        // 登录成功后立即获取用户信息\n        try {\n          if (this.userStore && typeof this.userStore.getUserInfo === 'function') {\n            await this.userStore.getUserInfo()\n          } else {\n            console.error('userStore或getUserInfo方法不存在:', this.userStore)\n          }\n        } catch (error) {\n          console.error('获取用户信息失败:', error)\n        }\n        \n        uni.showToast({\n          title: '登录成功',\n          icon: 'success'\n        })\n        \n        // 登录成功后跳转\n        setTimeout(() => {\n          this.handleLoginSuccess()\n        }, 1500)\n        \n      } catch (error) {\n        console.error('登录失败:', error)\n        // 根据错误类型显示不同的提示信息\n        let errorMessage = '登录失败'\n        if (error.message) {\n          if (error.message.includes('用户名或密码错误') || error.message.includes('账号或密码错误')) {\n            errorMessage = '账号或密码错误，请重新输入'\n          } else if (error.message.includes('用户不存在')) {\n            errorMessage = '账号不存在，请先注册'\n          } else if (error.message.includes('密码错误')) {\n            errorMessage = '密码错误，请重新输入'\n          } else if (error.message.includes('验证码错误') || error.message.includes('验证码不正确')) {\n            errorMessage = '验证码错误，请重新输入'\n          } else if (error.message.includes('验证码已过期')) {\n            errorMessage = '验证码已过期，请重新获取'\n          } else if (error.message.includes('网络')) {\n            errorMessage = '网络连接失败，请检查网络'\n          } else {\n            errorMessage = error.message\n          }\n        }\n        uni.showToast({\n          title: errorMessage,\n          icon: 'error',\n          duration: 3000\n        })\n      }\n    },\n    \n    // 处理登录成功\n    handleLoginSuccess() {\n      console.log('[Login] 处理登录成功，重定向URL:', this.redirectUrl)\n\n      // 清除路由守卫缓存，确保登录状态更新\n      // 注意：Pinia中登录状态已经在login方法中自动更新，无需手动dispatch\n      \n      if (this.redirectUrl) {\n        try {\n          // 解码重定向URL\n          const decodedUrl = decodeURIComponent(this.redirectUrl)\n          console.log('[Login] 跳转到重定向页面:', decodedUrl)\n          \n          // 检查是否是tabBar页面\n          const tabBarPages = [\n            '/pages/index/index',\n            '/pages/venue/list', \n            '/pages/sharing/list',\n            '/pages/booking/list',\n            '/pages/user/profile'\n          ]\n          \n          const pagePath = decodedUrl.split('?')[0]\n          const isTabBarPage = tabBarPages.includes(pagePath)\n          \n          if (isTabBarPage) {\n            uni.switchTab({\n              url: pagePath,\n              fail: (err) => {\n                console.error('[Login] switchTab失败:', err)\n                // 失败时跳转到首页\n                uni.switchTab({ url: '/pages/index/index' })\n              }\n            })\n          } else {\n            uni.redirectTo({\n              url: decodedUrl,\n              fail: (err) => {\n                console.error('[Login] redirectTo失败:', err)\n                // 失败时跳转到首页\n                uni.switchTab({ url: '/pages/index/index' })\n              }\n            })\n          }\n        } catch (error) {\n          console.error('[Login] 处理重定向URL失败:', error)\n          // 出错时跳转到首页\n          uni.switchTab({ url: '/pages/index/index' })\n        }\n      } else {\n        // 没有重定向页面，跳转到首页\n        console.log('[Login] 跳转到首页')\n        uni.switchTab({\n          url: '/pages/index/index',\n          fail: (err) => {\n            console.error('[Login] 跳转首页失败:', err)\n          }\n        })\n      }\n    },\n    \n    // 验证手机号\n    validatePhone() {\n      if (!this.formData.phone) {\n        uni.showToast({\n          title: '请输入手机号',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      if (!/^1[3-9]\\d{9}$/.test(this.formData.phone)) {\n        uni.showToast({\n          title: '手机号格式不正确',\n          icon: 'error'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 微信登录\n    wechatLogin() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // Apple登录\n    appleLogin() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    },\n    \n    // 跳转到注册页\n    navigateToRegister() {\n      uni.navigateTo({\n        url: '/pages/user/register'\n      })\n    },\n    \n    // 跳转到重置密码页\n    navigateToReset() {\n      uni.navigateTo({\n        url: '/pages/user/reset-password'\n      })\n    },\n    \n    // 显示用户协议\n    showUserAgreement() {\n      uni.navigateTo({\n        url: '/pages/user/agreement?type=user'\n      })\n    },\n    \n    // 显示隐私政策\n    showPrivacyPolicy() {\n      uni.navigateTo({\n        url: '/pages/user/agreement?type=privacy'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8f65 100%);\n  padding: 60rpx 60rpx 40rpx;\n  display: flex;\n  flex-direction: column;\n}\n\n// 头部\n.header {\n  text-align: center;\n  margin-bottom: 80rpx;\n  \n  .logo {\n    width: 120rpx;\n    height: 120rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .app-name {\n    display: block;\n    font-size: 48rpx;\n    font-weight: 600;\n    color: #ffffff;\n    margin-bottom: 16rpx;\n  }\n  \n  .app-slogan {\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.8);\n  }\n}\n\n// 登录表单\n.login-form {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n  \n  // 标签栏\n  .tab-bar {\n    display: flex;\n    background-color: #f5f5f5;\n    border-radius: 12rpx;\n    padding: 8rpx;\n    margin-bottom: 40rpx;\n    \n    .tab-item {\n      flex: 1;\n      text-align: center;\n      padding: 16rpx;\n      font-size: 28rpx;\n      color: #666666;\n      border-radius: 8rpx;\n      transition: all 0.3s ease;\n      \n      &.active {\n        background-color: #ffffff;\n        color: #ff6b35;\n        font-weight: 600;\n        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n      }\n    }\n  }\n  \n  // 表单项\n  .form-item {\n    margin-bottom: 30rpx;\n    \n    .input-wrapper {\n      display: flex;\n      align-items: center;\n      background-color: #f8f8f8;\n      border-radius: 12rpx;\n      padding: 0 20rpx;\n      height: 88rpx;\n      \n      .input-icon {\n        font-size: 32rpx;\n        margin-right: 16rpx;\n        opacity: 0.6;\n      }\n      \n      .input-field {\n        flex: 1;\n        font-size: 28rpx;\n        color: #333333;\n        background: transparent;\n        border: none;\n      }\n      \n      .password-toggle {\n        font-size: 32rpx;\n        opacity: 0.6;\n        padding: 8rpx;\n      }\n      \n      .sms-btn {\n        padding: 12rpx 24rpx;\n        background-color: #ff6b35;\n        color: #ffffff;\n        border: none;\n        border-radius: 8rpx;\n        font-size: 24rpx;\n        \n        &[disabled] {\n          background-color: #cccccc;\n          color: #ffffff;\n        }\n      }\n    }\n  }\n  \n  // 登录按钮\n  .login-btn {\n    width: 100%;\n    height: 88rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 12rpx;\n    font-size: 32rpx;\n    font-weight: 600;\n    margin-bottom: 30rpx;\n    \n    &[disabled] {\n      background-color: #cccccc;\n      color: #ffffff;\n    }\n  }\n  \n  // 忘记密码\n  .forgot-password {\n    text-align: right;\n    \n    text {\n      font-size: 26rpx;\n      color: #ff6b35;\n    }\n  }\n}\n\n// 其他登录方式\n.other-login {\n  margin-bottom: 40rpx;\n  \n  .divider {\n    position: relative;\n    text-align: center;\n    margin-bottom: 30rpx;\n    \n    &::before {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 0;\n      right: 0;\n      height: 1rpx;\n      background-color: rgba(255, 255, 255, 0.3);\n    }\n    \n    .divider-text {\n      background: linear-gradient(135deg, #ff6b35 0%, #ff8f65 100%);\n      padding: 0 20rpx;\n      font-size: 24rpx;\n      color: rgba(255, 255, 255, 0.8);\n      position: relative;\n      z-index: 1;\n    }\n  }\n  \n  .social-login {\n    display: flex;\n    justify-content: center;\n    gap: 60rpx;\n    \n    .social-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      \n      .social-icon {\n        width: 80rpx;\n        height: 80rpx;\n        background-color: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 36rpx;\n        margin-bottom: 12rpx;\n      }\n      \n      .social-text {\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.8);\n      }\n    }\n  }\n}\n\n// 底部链接\n.footer {\n  text-align: center;\n  margin-bottom: 30rpx;\n  \n  .footer-text {\n    font-size: 26rpx;\n    color: rgba(255, 255, 255, 0.8);\n  }\n  \n  .footer-link {\n    font-size: 26rpx;\n    color: #ffffff;\n    font-weight: 600;\n    margin-left: 8rpx;\n  }\n}\n\n// 协议提示\n.agreement {\n  text-align: center;\n  \n  .agreement-text {\n    font-size: 22rpx;\n    color: rgba(255, 255, 255, 0.6);\n  }\n  \n  .agreement-link {\n    font-size: 22rpx;\n    color: rgba(255, 255, 255, 0.8);\n    text-decoration: underline;\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/user/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "uni"], "mappings": ";;;;AAwIA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA;AAAA,MACX,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MAEX,UAAU;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,UAAU;;AACR,eAAO,UAAK,cAAL,mBAAgB,YAAW;AAAA,IACnC;AAAA;AAAA,IAGD,aAAa;AACX,aAAO,KAAK,SAAS,MAAM,WAAW,MAAM,gBAAgB,KAAK,KAAK,SAAS,KAAK;AAAA,IACrF;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,KAAK,cAAc,YAAY;AACjC,eAAO,KAAK,cAAc,KAAK,SAAS,SAAS,UAAU;AAAA,aACtD;AACL,eAAO,KAAK,cAAc,KAAK,SAAS,QAAQ,WAAW;AAAA,MAC7D;AAAA,IACF;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AAEd,QAAI;AACF,WAAK,YAAYA,yBAAa;AAC9BC,oBAAY,MAAA,MAAA,OAAA,+BAAA,2BAA2B,CAAC,CAAC,KAAK,SAAS;AACvDA,0BAAY,MAAA,OAAA,+BAAA,0BAA0B,OAAO,oBAAoB,KAAK,SAAS,CAAC;AAChFA,0BAAA,MAAA,OAAA,+BAAY,6BAA6B,OAAO,KAAK,KAAK,SAAS,CAAC;AACpEA,sEAAY,4BAA4B,OAAO,KAAK,UAAU,gBAAgB,UAAU;AACxFA,oBAAAA,MAAY,MAAA,OAAA,+BAAA,4BAA4B,OAAO,KAAK,UAAU,UAAU,gBAAgB,UAAU;AAAA,IAClG,SAAO,OAAO;AACdA,oBAAAA,oDAAc,2BAA2B,KAAK;AAAA,IAChD;AAGA,QAAI,QAAQ,UAAU;AACpB,WAAK,cAAc,mBAAmB,QAAQ,QAAQ;AAAA,IACxD;AAAA,EACD;AAAA,EAED,WAAW;AAET,QAAI,KAAK,UAAU;AACjB,oBAAc,KAAK,QAAQ;AAAA,IAC7B;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAGP,gBAAgB,MAAM;AACpB,WAAK,YAAY;AAEjB,WAAK,SAAS,WAAW;AACzB,WAAK,SAAS,UAAU;AAAA,IACzB;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,eAAe,CAAC,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI,CAAC,KAAK,cAAc,KAAK,eAAe;AAAG;AAE/C,UAAI;AACF,cAAM,KAAK,UAAU,WAAW;AAAA,UAC9B,OAAO,KAAK,SAAS;AAAA,UACrB,MAAM;AAAA,SACP;AACDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,aAAK,eAAe;AAAA,MAEpB,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,+BAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,eAAe;AACpB,WAAK,WAAW,YAAY,MAAM;AAChC,aAAK;AACL,YAAI,KAAK,gBAAgB,GAAG;AAC1B,wBAAc,KAAK,QAAQ;AAC3B,eAAK,WAAW;AAAA,QAClB;AAAA,MACD,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI,CAAC,KAAK;AAAU;AAGpB,UAAI,CAAC,KAAK,cAAa;AAAI;AAE3B,UAAI;AACF,YAAI;AACJ,YAAI,KAAK,cAAc,YAAY;AAEjC,mBAAS,MAAM,KAAK,UAAU,MAAM;AAAA,YAClC,UAAU,KAAK,SAAS;AAAA,YACxB,UAAU,KAAK,SAAS;AAAA,WACzB;AAAA,eACI;AAEL,mBAAS,MAAM,KAAK,UAAU,SAAS;AAAA,YACrC,OAAO,KAAK,SAAS;AAAA,YACrB,SAAS,KAAK,SAAS;AAAA,WACxB;AAAA,QACH;AAGA,YAAI;AACF,cAAI,KAAK,aAAa,OAAO,KAAK,UAAU,gBAAgB,YAAY;AACtE,kBAAM,KAAK,UAAU,YAAY;AAAA,iBAC5B;AACLA,0BAAc,MAAA,MAAA,SAAA,+BAAA,+BAA+B,KAAK,SAAS;AAAA,UAC7D;AAAA,QACA,SAAO,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,KAAK;AAAA,QAClC;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,mBAAW,MAAM;AACf,eAAK,mBAAmB;AAAA,QACzB,GAAE,IAAI;AAAA,MAEP,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,+BAAA,SAAS,KAAK;AAE5B,YAAI,eAAe;AACnB,YAAI,MAAM,SAAS;AACjB,cAAI,MAAM,QAAQ,SAAS,UAAU,KAAK,MAAM,QAAQ,SAAS,SAAS,GAAG;AAC3E,2BAAe;AAAA,UACjB,WAAW,MAAM,QAAQ,SAAS,OAAO,GAAG;AAC1C,2BAAe;AAAA,UACjB,WAAW,MAAM,QAAQ,SAAS,MAAM,GAAG;AACzC,2BAAe;AAAA,UACjB,WAAW,MAAM,QAAQ,SAAS,OAAO,KAAK,MAAM,QAAQ,SAAS,QAAQ,GAAG;AAC9E,2BAAe;AAAA,UACjB,WAAW,MAAM,QAAQ,SAAS,QAAQ,GAAG;AAC3C,2BAAe;AAAA,UACjB,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACvC,2BAAe;AAAA,iBACV;AACL,2BAAe,MAAM;AAAA,UACvB;AAAA,QACF;AACAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnBA,oBAAY,MAAA,MAAA,OAAA,+BAAA,0BAA0B,KAAK,WAAW;AAKtD,UAAI,KAAK,aAAa;AACpB,YAAI;AAEF,gBAAM,aAAa,mBAAmB,KAAK,WAAW;AACtDA,wBAAAA,MAAY,MAAA,OAAA,+BAAA,qBAAqB,UAAU;AAG3C,gBAAM,cAAc;AAAA,YAClB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAEA,gBAAM,WAAW,WAAW,MAAM,GAAG,EAAE,CAAC;AACxC,gBAAM,eAAe,YAAY,SAAS,QAAQ;AAElD,cAAI,cAAc;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,KAAK;AAAA,cACL,MAAM,CAAC,QAAQ;AACbA,8BAAAA,oDAAc,wBAAwB,GAAG;AAEzCA,8BAAAA,MAAI,UAAU,EAAE,KAAK,sBAAsB;AAAA,cAC7C;AAAA,aACD;AAAA,iBACI;AACLA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,cACL,MAAM,CAAC,QAAQ;AACbA,8BAAAA,MAAA,MAAA,SAAA,+BAAc,yBAAyB,GAAG;AAE1CA,8BAAAA,MAAI,UAAU,EAAE,KAAK,sBAAsB;AAAA,cAC7C;AAAA,aACD;AAAA,UACH;AAAA,QACA,SAAO,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,+BAAc,uBAAuB,KAAK;AAE1CA,wBAAAA,MAAI,UAAU,EAAE,KAAK,sBAAsB;AAAA,QAC7C;AAAA,aACK;AAELA,sBAAAA,MAAY,MAAA,OAAA,+BAAA,eAAe;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,UACL,MAAM,CAAC,QAAQ;AACbA,0BAAAA,MAAA,MAAA,SAAA,+BAAc,mBAAmB,GAAG;AAAA,UACtC;AAAA,SACD;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,CAAC,KAAK,SAAS,OAAO;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,gBAAgB,KAAK,KAAK,SAAS,KAAK,GAAG;AAC9CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACF;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACF;AAAA;AAAA,IAGD,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpcA,GAAG,WAAW,eAAe;"}