{"version": 3, "file": "list.js", "sources": ["pages/booking/list.vue", "pages/booking/list.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 状态筛选 -->\n    <view class=\"status-filter\">\n      <view \n        v-for=\"status in statusOptions\" \n        :key=\"status.value\" \n        class=\"filter-item\"\n        :class=\"{ active: selectedStatus === status.value }\"\n        @click=\"selectStatus(status.value)\"\n      >\n        {{ status.label }}\n      </view>\n    </view>\n    \n\n    \n    <!-- 预约列表 -->\n    <view class=\"booking-list\">\n      <view \n        v-for=\"booking in filteredBookings\" \n        :key=\"booking.id\" \n        class=\"booking-card\"\n        @click=\"navigateToDetail(booking.id)\"\n      >\n        <view class=\"card-header\">\n          <view class=\"venue-info\">\n            <!-- 场馆名和类型标识在同一行 -->\n            <view class=\"venue-name-row\">\n              <text class=\"venue-name\">{{ booking.venueName || '未知场馆' }}</text>\n              <!-- 订单类型标识显示在场馆名右边 -->\n              <view class=\"booking-type-tag\" v-if=\"(booking as any).bookingType || (booking as any).type || (booking as any).orderType\">\n                <text class=\"tag-text\" :class=\"getBookingTypeClass((booking as any).bookingType || (booking as any).type || (booking as any).orderType)\">\n                  {{ getBookingTypeText((booking as any).bookingType || (booking as any).type || (booking as any).orderType) }}\n                </text>\n              </view>\n              <!-- 虚拟订单标识 -->\n              <view class=\"virtual-order-tag\" v-if=\"isVirtualOrder(booking)\">\n                <text class=\"virtual-tag-text\">拼场申请</text>\n              </view>\n            </view>\n            <text class=\"booking-date\">{{ formatBookingDate(booking) }}</text>\n          </view>\n          <view class=\"booking-status\" :class=\"getStatusClass(booking.status)\">\n            {{ getStatusText(booking.status) }}\n          </view>\n        </view>\n        \n        <view class=\"card-content\">\n          <view class=\"time-info\">\n            <text class=\"time-icon\">🕐</text>\n            <text class=\"time-text\">{{ formatTimeRange(booking) }}</text>\n          </view>\n          \n          <view class=\"location-info\">\n            <text class=\"location-icon\">📍</text>\n            <text class=\"location-text\">{{ booking.venueLocation || '未知地点' }}</text>\n          </view>\n          \n\n          \n          <view class=\"order-info\">\n            <text class=\"order-icon\">📋</text>\n            <text class=\"order-text\">订单号：{{ booking.orderNo || (booking.id ? booking.id : '') }}</text>\n          </view>\n          \n          <view class=\"create-time-info\">\n            <text class=\"time-icon\">📅</text>\n            <text class=\"create-time-text\">创建时间：{{ formatCreateTime(booking && (booking.createdAt || booking.createTime)) }}</text>\n          </view>\n          \n          <view class=\"price-info\">\n            <text class=\"price-label\">费用：</text>\n            <text class=\"price-value\">¥{{ getBookingPrice(booking) }}</text>\n          </view>\n\n          <!-- 倒计时显示（仅拼场订单） -->\n          <CountdownTimer\n            v-if=\"shouldShowCountdown(booking)\"\n            :order=\"booking\"\n            label=\"自动取消\"\n            :short=\"true\"\n            class=\"simple\"\n            @expired=\"onCountdownExpired\"\n          />\n        </view>\n        \n        <view class=\"card-actions\">\n          <!-- 待支付状态 -->\n          <template v-if=\"booking.status === 'PENDING'\">\n            <button class=\"action-btn pay-btn\" @click.stop=\"payOrder(booking)\">立即支付</button>\n            <button class=\"action-btn cancel-btn\" @click.stop=\"showCancelModal(booking.id)\">取消预约</button>\n          </template>\n\n          <!-- 已支付状态 -->\n          <template v-else-if=\"booking.status === 'PAID'\">\n            <button class=\"action-btn info-btn\" @click.stop=\"viewOrderDetail(booking)\">查看详情</button>\n            <button class=\"action-btn cancel-btn\" @click.stop=\"showCancelModal(booking.id)\">取消预约</button>\n          </template>\n\n          <!-- 拼场相关状态 -->\n          <template v-else-if=\"booking.status === 'OPEN' || booking.status === 'SHARING' || booking.status === 'PENDING_FULL'\">\n            <button class=\"action-btn info-btn\" @click.stop=\"viewOrderDetail(booking)\">查看详情</button>\n            <button class=\"action-btn participants-btn\" @click.stop=\"viewParticipants(booking)\">查看参与者</button>\n            <button class=\"action-btn cancel-btn\" @click.stop=\"showCancelModal(booking.id)\">取消预约</button>\n          </template>\n\n          <!-- 拼场成功/已满员状态 -->\n          <template v-else-if=\"booking.status === 'SHARING_SUCCESS' || booking.status === 'FULL'\">\n            <button class=\"action-btn info-btn\" @click.stop=\"viewOrderDetail(booking)\">查看详情</button>\n            <button class=\"action-btn participants-btn\" @click.stop=\"viewParticipants(booking)\">查看参与者</button>\n          </template>\n\n          <!-- 已确认状态 -->\n          <template v-else-if=\"booking.status === 'CONFIRMED'\">\n            <button class=\"action-btn checkin-btn\" @click.stop=\"checkinOrder(booking)\">签到</button>\n            <button class=\"action-btn cancel-btn\" @click.stop=\"showCancelModal(booking.id)\">取消预约</button>\n          </template>\n\n          <!-- 已核销状态 -->\n          <template v-else-if=\"booking.status === 'VERIFIED'\">\n            <button class=\"action-btn complete-btn\" @click.stop=\"completeOrder(booking)\">完成订单</button>\n          </template>\n\n          <!-- 已完成状态 -->\n          <template v-else-if=\"booking.status === 'COMPLETED'\">\n            <button class=\"action-btn review-btn\" @click.stop=\"reviewVenue(booking)\">评价场馆</button>\n            <button class=\"action-btn rebook-btn\" @click.stop=\"rebookVenue(booking)\">再次预约</button>\n          </template>\n\n          <!-- 已取消/已过期状态 -->\n          <template v-else-if=\"booking.status === 'CANCELLED' || booking.status === 'EXPIRED'\">\n            <button class=\"action-btn rebook-btn\" @click.stop=\"rebookVenue(booking)\">再次预约</button>\n          </template>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 空状态 -->\n    <view v-if=\"filteredBookings.length === 0\" class=\"empty-state\">\n      <text class=\"empty-icon\">📅</text>\n      <text class=\"empty-text\">暂无预约记录</text>\n      <button class=\"empty-btn\" @click=\"navigateToVenueList\">去预约场馆</button>\n    </view>\n    \n    <!-- 加载更多 -->\n    <view v-if=\"hasMore && filteredBookings.length > 0\" class=\"load-more\" @click=\"loadMore\">\n      <text>{{ loading ? '加载中...' : '加载更多' }}</text>\n    </view>\n    \n    <!-- 取消预约确认弹窗 -->\n    <uni-popup ref=\"cancelPopup\" type=\"center\">\n      <view class=\"cancel-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">取消预约</text>\n        </view>\n        \n        <view class=\"modal-content\">\n          <text class=\"modal-text\">确定要取消这个预约吗？</text>\n          <text class=\"modal-note\">取消后可能产生手续费，具体以场馆规定为准</text>\n        </view>\n        \n        <view class=\"modal-actions\">\n          <button class=\"modal-btn cancel-btn\" @click=\"closeCancelModal\">暂不取消</button>\n          <button class=\"modal-btn confirm-btn\" @click=\"confirmCancel\">确认取消</button>\n        </view>\n      </view>\n    </uni-popup>\n    \n\n  </view>\n</template>\n\n/// <reference types=\"@dcloudio/uni-app\" />\n<script lang=\"ts\">\nimport { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue';\nimport { useBookingStore } from '@/stores/booking.js';\nimport { useUserStore } from '@/stores/user.js';\nimport { formatDate, formatTime } from '@/utils/helpers';\nimport { onShow, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';\nimport CountdownTimer from '@/components/CountdownTimer.vue';\nimport { shouldShowCountdown } from '@/utils/countdown.js';\n\ndeclare const uni: UniNamespace.Uni;\n\n// Type definitions\ntype TimeSlot = {\n  startTime: string\n  endTime: string\n}\n\ntype BookingStatus = 'PENDING' | 'PAID' | 'CONFIRMED' | 'VERIFIED' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED' |\n                    'SHARING' | 'SHARING_SUCCESS' | 'OPEN' | 'PENDING_FULL' | 'FULL'\ntype BookingType = 'EXCLUSIVE' | 'SHARED'\n\ntype Booking = {\n  id: string\n  venueName?: string\n  venueLocation?: string\n  venueId?: string\n  bookingDate?: string | Date\n  bookingType?: BookingType\n  status: BookingStatus\n  orderNo?: string\n  createdAt?: string | Date\n  createTime?: string | Date\n  totalPrice?: number\n  startTime?: string\n  endTime?: string\n  timeSlots?: TimeSlot[]\n  timeSlotCount?: number\n  bookingStartTime?: string\n  bookingEndTime?: string\n}\n\ntype StatusOption = {\n  label: string\n  value: string | BookingStatus\n}\n\ntype Pagination = {\n  current: number\n  pageSize: number\n  total: number\n  totalPages: number\n}\n\n// type UserInfo = {\n//   username: string\n//   [key: string]: any\n// }\n\nexport default defineComponent({\n  name: 'BookingList',\n\n  components: {\n    CountdownTimer\n  },\n\n  setup() {\n    // 使用Pinia stores\n    const bookingStore = useBookingStore()\n    const userStore = useUserStore()\n\n\n    const selectedStatus = ref<string | BookingStatus>('all');\n    const statusOptions = ref<StatusOption[]>([\n      { label: '全部', value: 'all' },\n      { label: '待支付', value: 'PENDING' },\n      { label: '已支付', value: 'PAID' },\n      { label: '已确认', value: 'CONFIRMED' },\n      { label: '已核销', value: 'VERIFIED' },\n      { label: '已完成', value: 'COMPLETED' },\n      { label: '已取消', value: 'CANCELLED' },\n      { label: '已过期', value: 'EXPIRED' },\n      // 拼场相关状态\n      { label: '开放中', value: 'OPEN' },\n      { label: '等待对方支付', value: 'APPROVED_PENDING_PAYMENT' },\n      { label: '拼场成功', value: 'SHARING_SUCCESS' }\n    ]);\n    const currentBookingId = ref<string | null>(null);\n    // const showCancelModal = ref(false);\n    const lastRequestStatus = ref('');\n    const lastError = ref('');\n    const cancelPopup = ref<any>(null);\n\n    const token = computed(() => userStore.getToken);\n    const userInfo = computed(() => userStore.getUserInfo);\n    const isLoggedIn = computed(() => userStore.isLoggedIn);\n    const bookingList = computed(() => bookingStore.getBookingList);\n    const loading = computed(() => bookingStore.isLoading);\n\n    const pagination = computed<Pagination>(() => {\n      return bookingStore.getPagination;\n    });\n\n    const hasMore = computed<boolean>(() => {\n      return pagination.value.current < pagination.value.totalPages;\n    });\n\n    const filteredBookings = computed<Booking[]>(() => {\n      const bookings = bookingList.value || [];\n\n      uni.__f__('log','at pages/booking/list.vue:284','[BookingList] filteredBookings计算中...')\n      uni.__f__('log','at pages/booking/list.vue:285','[BookingList] 原始bookings数量:', bookings.length)\n      uni.__f__('log','at pages/booking/list.vue:286','[BookingList] 原始bookings数据:', bookings)\n      uni.__f__('log','at pages/booking/list.vue:287','[BookingList] 当前选中状态:', selectedStatus.value)\n\n      if (selectedStatus.value === 'all') {\n        uni.__f__('log','at pages/booking/list.vue:290','[BookingList] 返回所有订单，数量:', bookings.length)\n        return bookings;\n      }\n\n      const filtered = bookings.filter(\n        (booking: Booking) => {\n          uni.__f__('log','at pages/booking/list.vue:296','[BookingList] 筛选订单:', booking.id, '状态:', booking.status, '匹配:', booking.status === selectedStatus.value)\n          return booking.status === selectedStatus.value\n        }\n      );\n\n      uni.__f__('log','at pages/booking/list.vue:301','[BookingList] 筛选后订单数量:', filtered.length)\n      return filtered;\n    });\n\n    // 初始化数据\n    const initData = async () => {\n      try {\n        lastRequestStatus.value = '请求中...';\n        lastError.value = '无';\n\n        await bookingStore.getUserBookings({ page: 1, pageSize: 10 });\n        lastRequestStatus.value = '请求成功';\n      } catch (error: any) {\n        lastRequestStatus.value = '请求失败';\n        lastError.value = error.message || '获取预约列表失败';\n        uni.__f__('error','at pages/booking/list.vue:316','初始化数据失败:', error);\n        uni.showToast({\n          title: error.message || '获取预约列表失败',\n          icon: 'none',\n          duration: 2000\n        });\n      }\n    };\n\n    // 刷新数据\n    const refreshData = async () => {\n      try {\n        lastRequestStatus.value = '刷新中...';\n        lastError.value = '无';\n\n        await bookingStore.getUserBookings({\n          page: 1,\n          pageSize: 10,\n          refresh: true,\n          timestamp: Date.now()\n        });\n        lastRequestStatus.value = '刷新成功';\n        uni.stopPullDownRefresh();\n      } catch (error: any) {\n        lastRequestStatus.value = '刷新失败';\n        lastError.value = error.message || '刷新数据失败';\n        uni.stopPullDownRefresh();\n        uni.__f__('error','at pages/booking/list.vue:343','刷新数据失败:', error);\n        uni.showToast({\n          title: error.message || '刷新数据失败',\n          icon: 'none',\n          duration: 2000\n        });\n      }\n    };\n\n    // 加载更多\n    const loadMore = async () => {\n      if (loading.value || !hasMore.value) return;\n\n      try {\n        const nextPage = pagination.value.current + 1;\n        await bookingStore.getUserBookings({ page: nextPage, pageSize: 10 });\n      } catch (error) {\n        uni.__f__('error','at pages/booking/list.vue:360','加载更多失败:', error);\n      }\n    };\n\n    // 选择状态\n    const selectStatus = (status: string) => {\n      selectedStatus.value = status;\n    };\n\n    // 跳转到详情页\n    const navigateToDetail = (bookingId: string) => {\n      uni.navigateTo({\n        url: `/pages/booking/detail?id=${bookingId}`\n      });\n    };\n\n    // 显示取消弹窗\n    const showCancelModalFunc = (bookingId: string) => {\n      currentBookingId.value = bookingId;\n      if (cancelPopup.value) {\n        cancelPopup.value.open();\n      }\n    };\n\n    // 关闭取消弹窗\n    const closeCancelModal = () => {\n      if (cancelPopup.value) {\n        cancelPopup.value.close();\n      }\n      currentBookingId.value = null;\n    };\n\n    // 确认取消\n    const confirmCancel = async () => {\n      try {\n        uni.showLoading({ title: '取消中...' });\n\n        await bookingStore.cancelBooking(currentBookingId.value);\n\n        uni.hideLoading();\n        closeCancelModal();\n\n        uni.showToast({\n          title: '取消成功',\n          icon: 'success'\n        });\n\n        await refreshData();\n      } catch (error: any) {\n        uni.hideLoading();\n        uni.__f__('error','at pages/booking/list.vue:410','取消预约失败:', error);\n        uni.showToast({\n          title: error.message || '取消失败',\n          icon: 'error'\n        });\n      }\n    };\n\n    // 评价场馆\n    const reviewVenue = (booking: Booking) => {\n      uni.navigateTo({\n        url: `/pages/venue/review?venueId=${booking.venueId}&bookingId=${booking.id}`\n      });\n    };\n\n    // 再次预约\n    const rebookVenue = (booking: Booking) => {\n      uni.navigateTo({\n        url: `/pages/venue/detail?id=${booking.venueId}`\n      });\n    };\n\n    // 跳转到场馆列表\n    const navigateToVenueList = () => {\n      uni.switchTab({\n        url: '/pages/venue/list'\n      });\n    };\n\n    // 格式化日期\n    const formatDateFunc = (date?: string | Date): string => {\n      if (!date) return '';\n      return formatDate(date, 'MM-DD dddd');\n    };\n\n    // 格式化创建时间\n    const formatCreateTime = (datetime?: string | Date): string => {\n      return formatTime(datetime, 'YYYY-MM-DD HH:mm');\n    };\n\n    // 获取状态样式类\n    const getStatusClass = (status: string): string => {\n      const statusMap: Record<string, string> = {\n        // 基础状态样式\n        'PENDING': 'status-pending',\n        'PAID': 'status-paid',\n        'CONFIRMED': 'status-confirmed',\n        'VERIFIED': 'status-verified',\n        'COMPLETED': 'status-completed',\n        'CANCELLED': 'status-cancelled',\n        'EXPIRED': 'status-expired',\n\n        // 拼场状态样式\n        'OPEN': 'status-open',\n        'APPROVED_PENDING_PAYMENT': 'status-approved-pending-payment',\n        'SHARING_SUCCESS': 'status-sharing-success',\n        'PENDING_FULL': 'status-pending-full',\n        'FULL': 'status-full'\n      };\n      return statusMap[status] || 'status-pending';\n    };\n\n    // 获取状态文本\n    const getStatusText = (status: string): string => {\n      const statusMap: Record<string, string> = {\n        // 基础状态（所有订单通用）\n        'PENDING': '待支付',\n        'PAID': '已支付',\n        'CONFIRMED': '已确认',\n        'VERIFIED': '已核销',\n        'COMPLETED': '已完成',\n        'CANCELLED': '已取消',\n        'EXPIRED': '已过期',\n\n        // 拼场订单特有状态\n        'OPEN': '开放中(1/2)',\n        'APPROVED_PENDING_PAYMENT': '等待对方支付',\n        'SHARING_SUCCESS': '拼场成功(2人)',\n        'PENDING_FULL': '待满员',\n        'FULL': '已满员(2/2)'\n      };\n      return statusMap[status] || '待支付';\n    };\n\n    // 格式化时间范围显示\n    const formatTimeRange = (booking: Booking): string => {\n      // 检查是否是虚拟订单\n      const bookingId = typeof booking.id === 'string' ? parseInt(booking.id) : booking.id;\n      const isVirtual = bookingId < 0;\n\n      if (isVirtual) {\n        // 虚拟订单使用预约列表API返回的 startTime 和 endTime 字段 (格式: \"HH:mm\")\n        const startTime = booking.startTime;\n        const endTime = booking.endTime;\n\n        uni.__f__('log','at pages/booking/list.vue:505','虚拟订单时间显示 - startTime:', startTime, 'endTime:', endTime);\n\n        if (!startTime) return '时间待定';\n\n        try {\n          // 预约列表API返回的时间格式是 \"HH:mm\"，可以直接使用\n          const startTimeStr = startTime;\n          const endTimeStr = endTime;\n\n          if (endTimeStr) {\n            return `${startTimeStr} - ${endTimeStr}`;\n          } else {\n            return startTimeStr;\n          }\n        } catch (error) {\n          uni.__f__('error','at pages/booking/list.vue:520','虚拟订单时间格式化错误:', error);\n          return '时间待定';\n        }\n      } else {\n        // 普通订单使用原有逻辑\n        const startTime = booking.startTime || booking.bookingStartTime;\n        const endTime = booking.endTime || booking.bookingEndTime;\n        const timeSlotCount = booking.timeSlotCount || 1;\n\n        if (!startTime || !endTime) {\n          return '时间待定';\n        }\n\n        const formatTime = (timeStr?: string): string => {\n          if (!timeStr) return '';\n          if (timeStr.length > 5 && timeStr.includes(':')) {\n            return timeStr.substring(0, 5);\n          }\n          return timeStr;\n        };\n\n        const formattedStart = formatTime(startTime);\n        const formattedEnd = formatTime(endTime);\n\n        if (timeSlotCount > 1) {\n          return `${formattedStart} - ${formattedEnd} (${timeSlotCount}个时段)`;\n        }\n\n        return `${formattedStart} - ${formattedEnd}`;\n      }\n    };\n\n    // 获取预约类型文本\n    const getBookingTypeText = (bookingType?: string): string => {\n      const typeMap: Record<string, string> = {\n        'EXCLUSIVE': '包场',\n        'SHARED': '拼场'\n      };\n      return bookingType ? (typeMap[bookingType] || '普通') : '普通';\n    };\n\n    // 获取预约类型样式类\n    const getBookingTypeClass = (bookingType?: string): string => {\n      const classMap: Record<string, string> = {\n        'EXCLUSIVE': 'tag-exclusive',\n        'SHARED': 'tag-shared'\n      };\n      return bookingType ? (classMap[bookingType] || 'tag-default') : 'tag-default';\n    };\n\n    // 检查是否是虚拟订单\n    const isVirtualOrder = (booking: Booking): boolean => {\n      if (!booking) return false;\n      const bookingId = typeof booking.id === 'string' ? parseInt(booking.id) : booking.id;\n      return bookingId < 0;\n    };\n\n    // 获取订单价格（兼容虚拟订单和普通订单）\n    const getBookingPrice = (booking: Booking): string => {\n      if (!booking) return '0.00';\n\n      // 检查是否是虚拟订单（负数ID）\n      const virtualOrder = isVirtualOrder(booking);\n\n      let price: number;\n      if (virtualOrder) {\n        // 虚拟订单使用 paymentAmount\n        price = (booking as any).paymentAmount || 0;\n      } else {\n        // 普通订单使用 totalPrice\n        price = booking.totalPrice || 0;\n      }\n\n      return price.toFixed(2);\n    };\n\n    // 格式化预约日期（兼容虚拟订单和普通订单）\n    const formatBookingDate = (booking: Booking): string => {\n      if (!booking) return '';\n\n      // 检查是否是虚拟订单\n      const virtualOrder = isVirtualOrder(booking);\n\n      if (virtualOrder) {\n        // 虚拟订单从 bookingTime 中提取日期\n        const bookingTime = (booking as any).bookingTime;\n        if (!bookingTime) return '';\n\n        try {\n          let dateTime: Date;\n          if (typeof bookingTime === 'string') {\n            let isoTime = bookingTime;\n            if (bookingTime.includes(' ') && !bookingTime.includes('T')) {\n              isoTime = bookingTime.replace(' ', 'T');\n            }\n            dateTime = new Date(isoTime);\n          } else {\n            dateTime = new Date(bookingTime);\n          }\n\n          if (isNaN(dateTime.getTime())) {\n            uni.__f__('error','at pages/booking/list.vue:621','虚拟订单日期格式化错误 - 无效的时间:', bookingTime);\n            return '';\n          }\n\n          return dateTime.toLocaleDateString('zh-CN', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit'\n          }).replace(/\\//g, '-');\n        } catch (error) {\n          uni.__f__('error','at pages/booking/list.vue:631','虚拟订单日期格式化错误:', error);\n          return '';\n        }\n      } else {\n        // 普通订单使用 bookingDate 字段\n        if (booking.bookingDate) {\n          return formatDate(booking.bookingDate);\n        }\n        return '';\n      }\n    };\n\n    onMounted(() => {\n      // 监听拼场数据变化\n      uni.$on('sharingDataChanged', onSharingDataChanged)\n      initData();\n    });\n\n    onUnmounted(() => {\n      // 移除监听器\n      uni.$off('sharingDataChanged', onSharingDataChanged)\n    });\n\n    // 处理拼场数据变化\n    const onSharingDataChanged = (data: any) => {\n      uni.__f__('log','at pages/booking/list.vue:656','预约列表页面：收到拼场数据变化通知:', data)\n      // 刷新数据以确保状态一致\n      initData()\n    };\n\n    onShow(async () => {\n      await refreshData();\n    });\n\n    onPullDownRefresh(async () => {\n      await refreshData();\n    });\n\n    onReachBottom(() => {\n      loadMore();\n    });\n\n    // 倒计时相关方法\n    const shouldShowCountdownFunc = (order: Booking): boolean => {\n      return shouldShowCountdown(order);\n    };\n\n    const onCountdownExpired = (_order: Booking) => {\n      // 刷新数据，更新订单状态\n      initData();\n    };\n\n    // 支付订单\n    const payOrder = (booking: Booking) => {\n      uni.navigateTo({\n        url: `/pages/payment/index?orderId=${booking.id}&type=booking`\n      });\n    };\n\n    // 查看订单详情\n    const viewOrderDetail = (booking: Booking) => {\n      uni.navigateTo({\n        url: `/pages/booking/detail?id=${booking.id}`\n      });\n    };\n\n    // 查看参与者\n    const viewParticipants = (booking: Booking) => {\n      uni.navigateTo({\n        url: `/pages/sharing/participants?orderId=${booking.id}`\n      });\n    };\n\n    // 签到\n    const checkinOrder = (_booking: Booking) => {\n      uni.showModal({\n        title: '确认签到',\n        content: '确认已到达场馆并开始使用？',\n        success: (res) => {\n          if (res.confirm) {\n            // TODO: 调用签到API\n            uni.showToast({\n              title: '签到成功',\n              icon: 'success'\n            });\n            initData(); // 刷新数据\n          }\n        }\n      });\n    };\n\n    // 完成订单\n    const completeOrder = (_booking: Booking) => {\n      uni.showModal({\n        title: '完成订单',\n        content: '确认完成此次预约？',\n        success: (res) => {\n          if (res.confirm) {\n            // TODO: 调用完成订单API\n            uni.showToast({\n              title: '订单已完成',\n              icon: 'success'\n            });\n            initData(); // 刷新数据\n          }\n        }\n      });\n    };\n\n    return {\n      // Pinia stores\n      bookingStore,\n      userStore,\n      // reactive data\n      selectedStatus,\n      statusOptions,\n      filteredBookings,\n      hasMore,\n      loading,\n      lastRequestStatus,\n      lastError,\n      cancelPopup,\n      // methods\n      selectStatus,\n      navigateToDetail,\n      showCancelModal: showCancelModalFunc,\n      closeCancelModal,\n      confirmCancel,\n      reviewVenue,\n      rebookVenue,\n      navigateToVenueList,\n      formatDate: formatDateFunc,\n      formatCreateTime,\n      getStatusClass,\n      getStatusText,\n      formatTimeRange,\n      getBookingTypeText,\n      getBookingTypeClass,\n      getBookingPrice,\n      formatBookingDate,\n      isVirtualOrder,\n      // computed\n      bookingList,\n      isLoggedIn,\n      token,\n      userInfo,\n      loadMore,\n      shouldShowCountdown: shouldShowCountdownFunc,\n      onCountdownExpired,\n      payOrder,\n      viewOrderDetail,\n      viewParticipants,\n      checkinOrder,\n      completeOrder\n    };\n  }\n\n\n\n})\n</script>\n\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n// 状态筛选\n.status-filter {\n  display: flex;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  .filter-item {\n    flex: 1;\n    text-align: center;\n    padding: 16rpx 0;\n    font-size: 28rpx;\n    color: #666666;\n    position: relative;\n    \n    &.active {\n      color: #ff6b35;\n      font-weight: 600;\n      \n      &::after {\n        content: '';\n        position: absolute;\n        bottom: 0;\n        left: 50%;\n        transform: translateX(-50%);\n        width: 60rpx;\n        height: 4rpx;\n        background-color: #ff6b35;\n        border-radius: 2rpx;\n      }\n    }\n  }\n}\n\n// 预约列表\n.booking-list {\n  padding: 20rpx 30rpx;\n  \n  .booking-card {\n    background-color: #ffffff;\n    border-radius: 16rpx;\n    padding: 30rpx;\n    margin-bottom: 20rpx;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n    \n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: 24rpx;\n      \n      .venue-info {\n        flex: 1;\n\n        .venue-name-row {\n          display: flex;\n          align-items: center;\n          margin-bottom: 8rpx;\n\n          .venue-name {\n            font-size: 32rpx;\n            font-weight: 600;\n            color: #333333;\n            margin-right: 12rpx;\n          }\n\n          .booking-type-tag {\n            display: inline-block;\n\n            .tag-text {\n              font-size: 20rpx;\n              padding: 4rpx 12rpx;\n              border-radius: 12rpx;\n              border: 1rpx solid;\n\n              // 拼场样式\n              &.tag-shared {\n                color: #ff6b35;\n                background-color: #fff7e6;\n                border-color: #ff6b35;\n              }\n\n              // 包场样式\n              &.tag-exclusive {\n                color: #1890ff;\n                background-color: #e6f7ff;\n                border-color: #1890ff;\n              }\n\n              // 默认样式\n              &.tag-default {\n                color: #666666;\n                background-color: #f5f5f5;\n                border-color: #d9d9d9;\n              }\n            }\n          }\n\n          // 虚拟订单标识\n          .virtual-order-tag {\n            display: inline-block;\n            margin-left: 8rpx;\n\n            .virtual-tag-text {\n              font-size: 20rpx;\n              padding: 4rpx 12rpx;\n              border-radius: 12rpx;\n              color: #722ed1;\n              background-color: #f9f0ff;\n              border: 1rpx solid #722ed1;\n            }\n          }\n        }\n\n        .booking-date {\n          font-size: 24rpx;\n          color: #666666;\n          margin-bottom: 8rpx;\n        }\n      }\n      \n      .booking-status {\n        font-size: 22rpx;\n        padding: 8rpx 16rpx;\n        border-radius: 16rpx;\n        \n        // 基础状态样式\n        &.status-pending {\n          background-color: #fff7e6;\n          color: #fa8c16;\n        }\n\n        &.status-paid {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &.status-confirmed {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &.status-verified {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n\n        &.status-completed {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n\n        &.status-cancelled {\n          background-color: #fff2f0;\n          color: #ff4d4f;\n        }\n\n        &.status-expired {\n          background-color: #f5f5f5;\n          color: #8c8c8c;\n        }\n\n        // 拼场状态样式\n        &.status-sharing {\n          background-color: #fff0f6;\n          color: #eb2f96;\n        }\n\n        &.status-sharing-success {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n\n        &.status-open {\n          background-color: #fff0f6;\n          color: #eb2f96;\n        }\n\n        &.status-pending-full {\n          background-color: #fff7e6;\n          color: #fa8c16;\n        }\n\n        &.status-full {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n      }\n    }\n    \n    .card-content {\n      margin-bottom: 24rpx;\n      \n      .time-info,\n      .location-info,\n      .order-info,\n      .create-time-info,\n      .price-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 16rpx;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n      }\n      \n      .time-icon,\n      .location-icon,\n      .order-icon {\n        font-size: 24rpx;\n        margin-right: 12rpx;\n      }\n      \n      .time-text,\n      .location-text,\n      .order-text,\n      .create-time-text {\n        font-size: 26rpx;\n        color: #666666;\n      }\n      \n      .price-info {\n        .price-label {\n          font-size: 26rpx;\n          color: #666666;\n        }\n\n        .price-value {\n          font-size: 28rpx;\n          color: #ff6b35;\n          font-weight: 600;\n          margin-left: 8rpx;\n        }\n      }\n\n      // 倒计时样式\n      .countdown-container {\n        margin-top: 12rpx;\n        display: flex;\n        align-items: center;\n\n        &.simple {\n          padding: 8rpx 12rpx;\n          font-size: 22rpx;\n          border-radius: 8rpx;\n\n          .countdown-icon {\n            font-size: 24rpx;\n            margin-right: 6rpx;\n          }\n\n          .countdown-content {\n            display: flex;\n            align-items: center;\n\n            .countdown-label {\n              font-size: 20rpx;\n              margin-right: 6rpx;\n            }\n\n            .countdown-time {\n              font-size: 22rpx;\n              font-weight: bold;\n            }\n          }\n        }\n      }\n    }\n    \n    .card-actions {\n      display: flex;\n      justify-content: flex-end;\n      \n      .action-btn {\n        padding: 12rpx 24rpx;\n        border-radius: 8rpx;\n        font-size: 24rpx;\n        margin-left: 16rpx;\n        border: 1rpx solid;\n        \n        &.cancel-btn {\n          background-color: transparent;\n          color: #ff4d4f;\n          border-color: #ff4d4f;\n        }\n\n        &.pay-btn {\n          background-color: #ff6b35;\n          color: #ffffff;\n          border-color: #ff6b35;\n        }\n\n        &.info-btn {\n          background-color: transparent;\n          color: #1890ff;\n          border-color: #1890ff;\n        }\n        \n        &.share-btn {\n          background-color: #ff6b35;\n          color: #ffffff;\n          border-color: #ff6b35;\n        }\n        \n        &.review-btn {\n          background-color: transparent;\n          color: #1890ff;\n          border-color: #1890ff;\n        }\n        \n        &.rebook-btn {\n          background-color: #ff6b35;\n          color: #ffffff;\n          border-color: #ff6b35;\n        }\n\n        &.participants-btn {\n          background-color: transparent;\n          color: #722ed1;\n          border-color: #722ed1;\n        }\n\n        &.checkin-btn {\n          background-color: #52c41a;\n          color: #ffffff;\n          border-color: #52c41a;\n        }\n\n        &.complete-btn {\n          background-color: #1890ff;\n          color: #ffffff;\n          border-color: #1890ff;\n        }\n      }\n    }\n  }\n}\n\n// 空状态\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 120rpx 60rpx;\n  \n  .empty-icon {\n    font-size: 120rpx;\n    margin-bottom: 30rpx;\n    opacity: 0.3;\n  }\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: #999999;\n    margin-bottom: 40rpx;\n  }\n  \n  .empty-btn {\n    padding: 16rpx 40rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 26rpx;\n  }\n}\n\n// 加载更多\n.load-more {\n  text-align: center;\n  padding: 40rpx;\n  font-size: 28rpx;\n  color: #666666;\n}\n\n// 弹窗通用样式\n.cancel-modal,\n.share-modal {\n  width: 600rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  \n  .modal-header {\n    padding: 30rpx;\n    text-align: center;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n  }\n  \n  .modal-actions {\n    display: flex;\n    border-top: 1rpx solid #f0f0f0;\n    \n    .modal-btn {\n      flex: 1;\n      height: 100rpx;\n      border: none;\n      font-size: 28rpx;\n      \n      &.cancel-btn {\n        background-color: #f5f5f5;\n        color: #666666;\n      }\n      \n      &.confirm-btn {\n        background-color: #ff6b35;\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n// 取消弹窗\n.cancel-modal {\n  .modal-content {\n    padding: 40rpx 30rpx;\n    text-align: center;\n    \n    .modal-text {\n      display: block;\n      font-size: 28rpx;\n      color: #333333;\n      margin-bottom: 16rpx;\n    }\n    \n    .modal-note {\n      font-size: 24rpx;\n      color: #999999;\n    }\n  }\n}\n\n// 拼场弹窗\n.share-modal {\n  .share-form {\n    padding: 30rpx;\n    \n    .form-item {\n      margin-bottom: 30rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .form-label {\n        display: block;\n        font-size: 28rpx;\n        color: #333333;\n        margin-bottom: 16rpx;\n      }\n      \n      .form-input,\n      .picker-text {\n        width: 100%;\n        height: 80rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 8rpx;\n        padding: 0 20rpx;\n        font-size: 28rpx;\n        background-color: #ffffff;\n      }\n      \n      .picker-text {\n        display: flex;\n        align-items: center;\n        color: #333333;\n      }\n      \n      .form-textarea {\n        width: 100%;\n        min-height: 120rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 8rpx;\n        padding: 20rpx;\n        font-size: 28rpx;\n        background-color: #ffffff;\n      }\n    }\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/booking/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["defineComponent", "useBookingStore", "useUserStore", "ref", "computed", "uni", "formatDate", "formatTime", "onMounted", "onUnmounted", "onShow", "onPullDownRefresh", "onReachBottom", "shouldShowCountdown"], "mappings": ";;;;;;AAoLA,MAAA,iBAA2B,MAAA;AAoD3B,MAAA,YAAeA,8BAAgB;AAAA,EAC7B,MAAM;AAAA,EAEN,YAAY;AAAA,IACV;AAAA,EACF;AAAA,EAEA,QAAQ;AAEN,UAAM,eAAeC,eAAAA;AACrB,UAAM,YAAYC,YAAAA;AAGZ,UAAA,iBAAiBC,kBAA4B,KAAK;AACxD,UAAM,gBAAgBA,cAAAA,IAAoB;AAAA,MACxC,EAAE,OAAO,MAAM,OAAO,MAAM;AAAA,MAC5B,EAAE,OAAO,OAAO,OAAO,UAAU;AAAA,MACjC,EAAE,OAAO,OAAO,OAAO,OAAO;AAAA,MAC9B,EAAE,OAAO,OAAO,OAAO,YAAY;AAAA,MACnC,EAAE,OAAO,OAAO,OAAO,WAAW;AAAA,MAClC,EAAE,OAAO,OAAO,OAAO,YAAY;AAAA,MACnC,EAAE,OAAO,OAAO,OAAO,YAAY;AAAA,MACnC,EAAE,OAAO,OAAO,OAAO,UAAU;AAAA;AAAA,MAEjC,EAAE,OAAO,OAAO,OAAO,OAAO;AAAA,MAC9B,EAAE,OAAO,UAAU,OAAO,2BAA2B;AAAA,MACrD,EAAE,OAAO,QAAQ,OAAO,kBAAkB;AAAA,IAAA,CAC3C;AACK,UAAA,mBAAmBA,kBAAmB,IAAI;AAE1C,UAAA,oBAAoBA,kBAAI,EAAE;AAC1B,UAAA,YAAYA,kBAAI,EAAE;AAClB,UAAA,cAAcA,kBAAS,IAAI;AAEjC,UAAM,QAAQC,cAAA,SAAS,MAAM,UAAU,QAAQ;AAC/C,UAAM,WAAWA,cAAA,SAAS,MAAM,UAAU,WAAW;AACrD,UAAM,aAAaA,cAAA,SAAS,MAAM,UAAU,UAAU;AACtD,UAAM,cAAcA,cAAA,SAAS,MAAM,aAAa,cAAc;AAC9D,UAAM,UAAUA,cAAA,SAAS,MAAM,aAAa,SAAS;AAE/C,UAAA,aAAaA,cAAAA,SAAqB,MAAM;AAC5C,aAAO,aAAa;AAAA,IAAA,CACrB;AAEK,UAAA,UAAUA,cAAAA,SAAkB,MAAM;AACtC,aAAO,WAAW,MAAM,UAAU,WAAW,MAAM;AAAA,IAAA,CACpD;AAEK,UAAA,mBAAmBA,cAAAA,SAAoB,MAAM;AAC3C,YAAA,WAAW,YAAY,SAAS;AAElCC,oBAAAA,MAAA,MAAM,OAAM,iCAAgC,sCAAsC;AACtFA,oBAAA,MAAI,MAAM,OAAM,iCAAgC,+BAA+B,SAAS,MAAM;AAC9FA,oBAAA,MAAI,MAAM,OAAM,iCAAgC,+BAA+B,QAAQ;AACvFA,oBAAA,MAAI,MAAM,OAAM,iCAAgC,yBAAyB,eAAe,KAAK;AAEzF,UAAA,eAAe,UAAU,OAAO;AAClCA,sBAAA,MAAI,MAAM,OAAM,iCAAgC,4BAA4B,SAAS,MAAM;AACpF,eAAA;AAAA,MACT;AAEA,YAAM,WAAW,SAAS;AAAA,QACxB,CAAC,YAAqB;AACpBA,wBAAA,MAAI,MAAM,OAAM,iCAAgC,uBAAuB,QAAQ,IAAI,OAAO,QAAQ,QAAQ,OAAO,QAAQ,WAAW,eAAe,KAAK;AACjJ,iBAAA,QAAQ,WAAW,eAAe;AAAA,QAC3C;AAAA,MAAA;AAGFA,oBAAA,MAAI,MAAM,OAAM,iCAAgC,0BAA0B,SAAS,MAAM;AAClF,aAAA;AAAA,IAAA,CACR;AAGD,UAAM,WAAW,YAAY;AACvB,UAAA;AACF,0BAAkB,QAAQ;AAC1B,kBAAU,QAAQ;AAElB,cAAM,aAAa,gBAAgB,EAAE,MAAM,GAAG,UAAU,IAAI;AAC5D,0BAAkB,QAAQ;AAAA,eACnB,OAAY;AACnB,0BAAkB,QAAQ;AAChB,kBAAA,QAAQ,MAAM,WAAW;AACnCA,sBAAA,MAAI,MAAM,SAAQ,iCAAgC,YAAY,KAAK;AACnEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,cAAc,YAAY;AAC1B,UAAA;AACF,0BAAkB,QAAQ;AAC1B,kBAAU,QAAQ;AAElB,cAAM,aAAa,gBAAgB;AAAA,UACjC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,WAAW,KAAK,IAAI;AAAA,QAAA,CACrB;AACD,0BAAkB,QAAQ;AAC1BA,sBAAA,MAAI,oBAAoB;AAAA,eACjB,OAAY;AACnB,0BAAkB,QAAQ;AAChB,kBAAA,QAAQ,MAAM,WAAW;AACnCA,sBAAA,MAAI,oBAAoB;AACxBA,sBAAA,MAAI,MAAM,SAAQ,iCAAgC,WAAW,KAAK;AAClEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,WAAW,YAAY;AACvB,UAAA,QAAQ,SAAS,CAAC,QAAQ;AAAO;AAEjC,UAAA;AACI,cAAA,WAAW,WAAW,MAAM,UAAU;AAC5C,cAAM,aAAa,gBAAgB,EAAE,MAAM,UAAU,UAAU,IAAI;AAAA,eAC5D,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,iCAAgC,WAAW,KAAK;AAAA,MACpE;AAAA,IAAA;AAII,UAAA,eAAe,CAAC,WAAmB;AACvC,qBAAe,QAAQ;AAAA,IAAA;AAInB,UAAA,mBAAmB,CAAC,cAAsB;AAC9CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,SAAS;AAAA,MAAA,CAC3C;AAAA,IAAA;AAIG,UAAA,sBAAsB,CAAC,cAAsB;AACjD,uBAAiB,QAAQ;AACzB,UAAI,YAAY,OAAO;AACrB,oBAAY,MAAM;MACpB;AAAA,IAAA;AAIF,UAAM,mBAAmB,MAAM;AAC7B,UAAI,YAAY,OAAO;AACrB,oBAAY,MAAM;MACpB;AACA,uBAAiB,QAAQ;AAAA,IAAA;AAI3B,UAAM,gBAAgB,YAAY;AAC5B,UAAA;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAE7B,cAAA,aAAa,cAAc,iBAAiB,KAAK;AAEvDA,sBAAA,MAAI,YAAY;AACC;AAEjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAED,cAAM,YAAY;AAAA,eACX,OAAY;AACnBA,sBAAA,MAAI,YAAY;AAChBA,sBAAA,MAAI,MAAM,SAAQ,iCAAgC,WAAW,KAAK;AAClEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA;AAII,UAAA,cAAc,CAAC,YAAqB;AACxCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,QAAQ,OAAO,cAAc,QAAQ,EAAE;AAAA,MAAA,CAC5E;AAAA,IAAA;AAIG,UAAA,cAAc,CAAC,YAAqB;AACxCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0BAA0B,QAAQ,OAAO;AAAA,MAAA,CAC/C;AAAA,IAAA;AAIH,UAAM,sBAAsB,MAAM;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAIG,UAAA,iBAAiB,CAAC,SAAiC;AACvD,UAAI,CAAC;AAAa,eAAA;AACX,aAAAC,cAAA,WAAW,MAAM,YAAY;AAAA,IAAA;AAIhC,UAAA,mBAAmB,CAAC,aAAqC;AACtD,aAAAC,cAAA,WAAW,UAAU,kBAAkB;AAAA,IAAA;AAI1C,UAAA,iBAAiB,CAAC,WAA2B;AACjD,YAAM,YAAoC;AAAA;AAAA,QAExC,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA;AAAA,QAGX,QAAQ;AAAA,QACR,4BAA4B;AAAA,QAC5B,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,QAAQ;AAAA,MAAA;AAEH,aAAA,UAAU,MAAM,KAAK;AAAA,IAAA;AAIxB,UAAA,gBAAgB,CAAC,WAA2B;AAChD,YAAM,YAAoC;AAAA;AAAA,QAExC,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA;AAAA,QAGX,QAAQ;AAAA,QACR,4BAA4B;AAAA,QAC5B,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,QAAQ;AAAA,MAAA;AAEH,aAAA,UAAU,MAAM,KAAK;AAAA,IAAA;AAIxB,UAAA,kBAAkB,CAAC,YAA6B;AAE9C,YAAA,YAAY,OAAO,QAAQ,OAAO,WAAW,SAAS,QAAQ,EAAE,IAAI,QAAQ;AAClF,YAAM,YAAY,YAAY;AAE9B,UAAI,WAAW;AAEb,cAAM,YAAY,QAAQ;AAC1B,cAAM,UAAU,QAAQ;AAExBF,4BAAI,MAAM,OAAM,iCAAgC,yBAAyB,WAAW,YAAY,OAAO;AAEvG,YAAI,CAAC;AAAkB,iBAAA;AAEnB,YAAA;AAEF,gBAAM,eAAe;AACrB,gBAAM,aAAa;AAEnB,cAAI,YAAY;AACP,mBAAA,GAAG,YAAY,MAAM,UAAU;AAAA,UAAA,OACjC;AACE,mBAAA;AAAA,UACT;AAAA,iBACO,OAAO;AACdA,wBAAA,MAAI,MAAM,SAAQ,iCAAgC,gBAAgB,KAAK;AAChE,iBAAA;AAAA,QACT;AAAA,MAAA,OACK;AAEC,cAAA,YAAY,QAAQ,aAAa,QAAQ;AACzC,cAAA,UAAU,QAAQ,WAAW,QAAQ;AACrC,cAAA,gBAAgB,QAAQ,iBAAiB;AAE3C,YAAA,CAAC,aAAa,CAAC,SAAS;AACnB,iBAAA;AAAA,QACT;AAEME,cAAAA,cAAa,CAAC,YAA6B;AAC/C,cAAI,CAAC;AAAgB,mBAAA;AACrB,cAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AACxC,mBAAA,QAAQ,UAAU,GAAG,CAAC;AAAA,UAC/B;AACO,iBAAA;AAAA,QAAA;AAGH,cAAA,iBAAiBA,YAAW,SAAS;AACrC,cAAA,eAAeA,YAAW,OAAO;AAEvC,YAAI,gBAAgB,GAAG;AACrB,iBAAO,GAAG,cAAc,MAAM,YAAY,KAAK,aAAa;AAAA,QAC9D;AAEO,eAAA,GAAG,cAAc,MAAM,YAAY;AAAA,MAC5C;AAAA,IAAA;AAII,UAAA,qBAAqB,CAAC,gBAAiC;AAC3D,YAAM,UAAkC;AAAA,QACtC,aAAa;AAAA,QACb,UAAU;AAAA,MAAA;AAEZ,aAAO,cAAe,QAAQ,WAAW,KAAK,OAAQ;AAAA,IAAA;AAIlD,UAAA,sBAAsB,CAAC,gBAAiC;AAC5D,YAAM,WAAmC;AAAA,QACvC,aAAa;AAAA,QACb,UAAU;AAAA,MAAA;AAEZ,aAAO,cAAe,SAAS,WAAW,KAAK,gBAAiB;AAAA,IAAA;AAI5D,UAAA,iBAAiB,CAAC,YAA8B;AACpD,UAAI,CAAC;AAAgB,eAAA;AACf,YAAA,YAAY,OAAO,QAAQ,OAAO,WAAW,SAAS,QAAQ,EAAE,IAAI,QAAQ;AAClF,aAAO,YAAY;AAAA,IAAA;AAIf,UAAA,kBAAkB,CAAC,YAA6B;AACpD,UAAI,CAAC;AAAgB,eAAA;AAGf,YAAA,eAAe,eAAe,OAAO;AAEvC,UAAA;AACJ,UAAI,cAAc;AAEhB,gBAAS,QAAgB,iBAAiB;AAAA,MAAA,OACrC;AAEL,gBAAQ,QAAQ,cAAc;AAAA,MAChC;AAEO,aAAA,MAAM,QAAQ,CAAC;AAAA,IAAA;AAIlB,UAAA,oBAAoB,CAAC,YAA6B;AACtD,UAAI,CAAC;AAAgB,eAAA;AAGf,YAAA,eAAe,eAAe,OAAO;AAE3C,UAAI,cAAc;AAEhB,cAAM,cAAe,QAAgB;AACrC,YAAI,CAAC;AAAoB,iBAAA;AAErB,YAAA;AACE,cAAA;AACA,cAAA,OAAO,gBAAgB,UAAU;AACnC,gBAAI,UAAU;AACV,gBAAA,YAAY,SAAS,GAAG,KAAK,CAAC,YAAY,SAAS,GAAG,GAAG;AACjD,wBAAA,YAAY,QAAQ,KAAK,GAAG;AAAA,YACxC;AACW,uBAAA,IAAI,KAAK,OAAO;AAAA,UAAA,OACtB;AACM,uBAAA,IAAI,KAAK,WAAW;AAAA,UACjC;AAEA,cAAI,MAAM,SAAS,QAAQ,CAAC,GAAG;AAC7BF,0BAAA,MAAI,MAAM,SAAQ,iCAAgC,wBAAwB,WAAW;AAC9E,mBAAA;AAAA,UACT;AAEO,iBAAA,SAAS,mBAAmB,SAAS;AAAA,YAC1C,MAAM;AAAA,YACN,OAAO;AAAA,YACP,KAAK;AAAA,UACN,CAAA,EAAE,QAAQ,OAAO,GAAG;AAAA,iBACd,OAAO;AACdA,wBAAA,MAAI,MAAM,SAAQ,iCAAgC,gBAAgB,KAAK;AAChE,iBAAA;AAAA,QACT;AAAA,MAAA,OACK;AAEL,YAAI,QAAQ,aAAa;AAChB,iBAAAC,cAAA,WAAW,QAAQ,WAAW;AAAA,QACvC;AACO,eAAA;AAAA,MACT;AAAA,IAAA;AAGFE,kBAAAA,UAAU,MAAM;AAEVH,oBAAAA,MAAA,IAAI,sBAAsB,oBAAoB;AACzC;IAAA,CACV;AAEDI,kBAAAA,YAAY,MAAM;AAEZJ,oBAAAA,MAAA,KAAK,sBAAsB,oBAAoB;AAAA,IAAA,CACpD;AAGK,UAAA,uBAAuB,CAAC,SAAc;AAC1CA,oBAAA,MAAI,MAAM,OAAM,iCAAgC,sBAAsB,IAAI;AAEjE;IAAA;AAGXK,kBAAAA,OAAO,YAAY;AACjB,YAAM,YAAY;AAAA,IAAA,CACnB;AAEDC,kBAAAA,kBAAkB,YAAY;AAC5B,YAAM,YAAY;AAAA,IAAA,CACnB;AAEDC,kBAAAA,cAAc,MAAM;AACT;IAAA,CACV;AAGK,UAAA,0BAA0B,CAAC,UAA4B;AAC3D,aAAOC,gBAAAA,oBAAoB,KAAK;AAAA,IAAA;AAG5B,UAAA,qBAAqB,CAAC,WAAoB;AAErC;IAAA;AAIL,UAAA,WAAW,CAAC,YAAqB;AACrCR,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gCAAgC,QAAQ,EAAE;AAAA,MAAA,CAChD;AAAA,IAAA;AAIG,UAAA,kBAAkB,CAAC,YAAqB;AAC5CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,QAAQ,EAAE;AAAA,MAAA,CAC5C;AAAA,IAAA;AAIG,UAAA,mBAAmB,CAAC,YAAqB;AAC7CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uCAAuC,QAAQ,EAAE;AAAA,MAAA,CACvD;AAAA,IAAA;AAIG,UAAA,eAAe,CAAC,aAAsB;AAC1CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AACQ;UACX;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,gBAAgB,CAAC,aAAsB;AAC3CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AACQ;UACX;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAGI,WAAA;AAAA;AAAA,MAEL;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,qBAAqB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAAA,EAEJ;AAIF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpxBD,GAAG,WAAW,eAAe;"}