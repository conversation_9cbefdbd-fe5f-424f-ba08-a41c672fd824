{"version": 3, "file": "detail.js", "sources": ["pages/booking/detail.vue", "pages/booking/detail.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-state\">\n      <text class=\"loading-text\">正在加载订单详情...</text>\n    </view>\n    \n    <!-- 数据为空状态 -->\n    <view v-else-if=\"!bookingDetail || !bookingDetail.orderNo\" class=\"empty-state\">\n      <text class=\"empty-text\">订单信息不存在</text>\n      <button class=\"retry-btn\" @click=\"initData\">重新加载</button>\n    </view>\n    \n    <!-- 正常内容 -->\n    <view v-else>\n    <!-- 预约状态 -->\n    <view class=\"status-section\">\n      <view class=\"status-icon\" :class=\"getStatusClass(bookingDetail?.status)\">\n        <text>{{ getStatusIcon(bookingDetail?.status) }}</text>\n      </view>\n      <view class=\"status-info\">\n        <text class=\"status-text\">{{ getStatusText(bookingDetail?.status) }}</text>\n        <text class=\"status-desc\">{{ getStatusDesc(bookingDetail?.status) }}</text>\n      </view>\n    </view>\n    \n    <!-- 预约信息 -->\n    <view class=\"info-section\">\n      <view class=\"section-title\">预约信息</view>\n      \n      <view class=\"info-item\">\n        <text class=\"info-label\">预约编号</text>\n        <text class=\"info-value\">{{ bookingDetail?.orderNo || '--' }}</text>\n      </view>\n      \n      <view class=\"info-item\">\n        <text class=\"info-label\">场馆名称</text>\n        <text class=\"info-value\">{{ bookingDetail?.venueName || '--' }}</text>\n      </view>\n      \n      <!-- 预约类型标注 -->\n      <view class=\"info-item\">\n        <text class=\"info-label\">预约类型</text>\n        <view class=\"booking-type-container\">\n          <text class=\"info-value booking-type\" :class=\"getBookingTypeClass(bookingDetail?.bookingType)\">\n            {{ getBookingTypeText(bookingDetail?.bookingType) }}\n          </text>\n          <!-- 虚拟订单标识 -->\n          <text v-if=\"isVirtualOrder()\" class=\"virtual-order-badge\">拼场申请</text>\n        </view>\n      </view>\n      \n      <view class=\"info-item\">\n        <text class=\"info-label\">场馆地址</text>\n        <text class=\"info-value\">{{ bookingDetail?.venueLocation || '--' }}</text>\n      </view>\n      \n      <view class=\"info-item\">\n        <text class=\"info-label\">预约日期</text>\n        <text class=\"info-value\">{{ formatBookingDate() }}</text>\n      </view>\n      \n      <view class=\"info-item\">\n        <text class=\"info-label\">预约时间</text>\n        <text class=\"info-value\">{{ formatBookingTime() }}</text>\n      </view>\n      \n      <!-- 预约费用 - 根据订单类型显示不同内容 -->\n      <template v-if=\"isVirtualOrder()\">\n        <!-- 虚拟订单（拼场申请）的价格显示 -->\n        <view class=\"info-item\">\n          <text class=\"info-label\">支付金额</text>\n          <text class=\"info-value price\" style=\"color: #722ed1; font-size: 36rpx;\">\n            ¥{{ getPaymentAmount() }}\n          </text>\n        </view>\n        <view class=\"info-tip\" style=\"font-size: 24rpx; color: #999; margin-top: 10rpx;\">\n          <text>拼场申请订单，支付成功后即可参与拼场</text>\n        </view>\n      </template>\n      <template v-else-if=\"bookingDetail && bookingDetail.isSharedBooking\">\n        <!-- 普通拼场订单的价格显示 -->\n        <view class=\"price-section\" style=\"margin: 20rpx 0;\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">总费用</text>\n            <text class=\"info-value price\">¥{{ (bookingDetail && bookingDetail.totalOriginalPrice) || 0 }}</text>\n          </view>\n          <view class=\"info-item\" style=\"color: #ff6b00; background-color: #fff8f0; padding: 10rpx; border-radius: 8rpx;\">\n            <text class=\"info-label\" style=\"color: #ff6b00;\">拼场优惠</text>\n            <text class=\"info-value\" style=\"color: #ff6b00;\">\n              ¥{{ ((bookingDetail && bookingDetail.totalOriginalPrice) - (bookingDetail && bookingDetail.totalPrice)) || 0 }}（5折）\n            </text>\n          </view>\n          <view class=\"info-item\" style=\"font-weight: bold; margin-top: 10rpx;\">\n            <text class=\"info-label\">实付金额</text>\n            <text class=\"info-value price\" style=\"color: #ff6b00; font-size: 36rpx;\">\n              ¥{{ (bookingDetail && bookingDetail.totalPrice) || 0 }}\n            </text>\n          </view>\n          <view class=\"info-tip\" style=\"font-size: 24rpx; color: #999; margin-top: 10rpx;\">\n            <text>拼场订单，费用由两队均摊，每队支付总费用的50%</text>\n          </view>\n        </view>\n      </template>\n      <template v-else>\n        <!-- 普通订单的价格显示 -->\n        <view class=\"info-item\">\n          <text class=\"info-label\">预约费用</text>\n          <text class=\"info-value price\">¥{{ (bookingDetail && bookingDetail.totalPrice) || 0 }}</text>\n        </view>\n      </template>\n  \n      <view class=\"info-item\">\n        <text class=\"info-label\">创建时间</text>\n        <text class=\"info-value\">{{ formatCreateTime((bookingDetail && bookingDetail.createdAt) || (bookingDetail && bookingDetail.createTime)) }}</text>\n      </view>\n    </view>\n    \n    <!-- 联系信息 -->\n    <view class=\"contact-section\">\n      <view class=\"section-title\">联系信息</view>\n      \n      <view class=\"contact-item\" @click=\"callVenue\">\n        <view class=\"contact-icon\">\n          <text>📞</text>\n        </view>\n        <view class=\"contact-info\">\n          <text class=\"contact-label\">场馆电话</text>\n          <text class=\"contact-value\">{{ (bookingDetail && bookingDetail.venuePhone) || '暂无' }}</text>\n        </view>\n        <view class=\"contact-arrow\">\n          <text>></text>\n        </view>\n      </view>\n      \n      <view class=\"contact-item\" @click=\"openMap\">\n        <view class=\"contact-icon\">\n          <text>📍</text>\n        </view>\n        <view class=\"contact-info\">\n          <text class=\"contact-label\">导航到场馆</text>\n          <text class=\"contact-value\">{{ (bookingDetail && bookingDetail.venueLocation) || '暂无' }}</text>\n        </view>\n        <view class=\"contact-arrow\">\n          <text>></text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 拼场信息 -->\n    <view v-if=\"bookingDetail && bookingDetail.sharingOrder\" class=\"sharing-section\">\n      <view class=\"section-title\">拼场信息</view>\n      \n      <view class=\"sharing-card\" @click=\"navigateToSharingDetail\">\n        <view class=\"sharing-header\">\n          <text class=\"sharing-team\">{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.teamName) || '' }}</text>\n          <text class=\"sharing-status\">{{ getSharingStatusText(bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.status) }}</text>\n        </view>\n        \n        <view class=\"sharing-info\">\n          <text class=\"sharing-participants\">\n            当前人数：{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.currentParticipants) || 0 }}/{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.maxParticipants) || 0 }}人\n          </text>\n          <text class=\"sharing-price\">人均：¥{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.pricePerPerson) || 0 }}</text>\n        </view>\n        \n        <view class=\"sharing-desc\">\n          <text>{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.description) || '暂无说明' }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 操作按钮 -->\n    <view class=\"action-section\">\n      <button v-if=\"bookingDetail && bookingDetail.status === 'PENDING'\" class=\"action-btn cancel-btn\" @click=\"cancelBooking\">\n        取消预约\n      </button>\n\n      <button v-if=\"bookingDetail && bookingDetail.status === 'PENDING'\" class=\"action-btn pay-btn\" @click=\"payBooking\">\n        立即支付\n      </button>\n      \n\n      \n      <button v-if=\"bookingDetail && bookingDetail.status === 'COMPLETED'\" class=\"action-btn review-btn\" @click=\"reviewVenue\">\n        评价场馆\n      </button>\n      \n      <button class=\"action-btn rebook-btn\" @click=\"rebookVenue\">\n        再次预约\n      </button>\n    </view>\n    \n    <!-- 取消预约确认弹窗 -->\n    <uni-popup ref=\"cancelPopup\" type=\"center\">\n      <view class=\"cancel-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">取消预约</text>\n        </view>\n        \n        <view class=\"modal-content\">\n          <text class=\"modal-text\">确定要取消这个预约吗？</text>\n          <text class=\"modal-note\">取消后可能产生手续费，具体以场馆规定为准</text>\n        </view>\n        \n        <view class=\"modal-actions\">\n          <button class=\"modal-btn cancel-btn\" @click=\"closeCancelModal\">暂不取消</button>\n          <button class=\"modal-btn confirm-btn\" @click=\"confirmCancel\">确认取消</button>\n        </view>\n      </view>\n    </uni-popup>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useBookingStore } from '@/stores/booking.js'\nimport { formatDate, formatDateTime, formatTime } from '@/utils/helpers.js'\nimport { clearCache } from '@/utils/request.js'\n\nexport default {\n  name: 'BookingDetail',\n\n  data() {\n    return {\n      bookingId: '',\n      bookingStore: null\n    }\n  },\n\n  computed: {\n    bookingDetail() {\n      return this.bookingStore?.bookingDetail || {}\n    },\n\n    loading() {\n      return this.bookingStore?.loading || false\n    }\n  },\n  \n  onLoad(options) {\n    // 初始化Pinia store\n    this.bookingStore = useBookingStore()\n    console.log('[BookingDetail] bookingStore初始化:', !!this.bookingStore)\n    console.log('[BookingDetail] bookingStore类型:', typeof this.bookingStore)\n    console.log('[BookingDetail] bookingStore所有方法:', Object.getOwnPropertyNames(this.bookingStore))\n    console.log('[BookingDetail] getBookingDetail方法存在:', typeof this.bookingStore.getBookingDetail === 'function')\n\n    // 尝试直接访问方法\n    if (this.bookingStore.getBookingDetail) {\n      console.log('[BookingDetail] getBookingDetail方法可访问')\n    } else {\n      console.log('[BookingDetail] getBookingDetail方法不可访问')\n    }\n\n    this.bookingId = options.id\n    this.initData()\n  },\n  \n  onPullDownRefresh() {\n    this.refreshData()\n  },\n  \n  methods: {\n    \n    // 初始化数据\n    async initData() {\n      try {\n        // 验证bookingId是否有效\n        if (!this.bookingId) {\n          throw new Error('订单ID无效，请重新进入页面')\n        }\n\n        // 清除可能存在的无效缓存\n        clearCache(`/bookings/${this.bookingId}`)\n\n        // 临时解决方案：直接调用API\n        if (typeof this.bookingStore.getBookingDetail === 'function') {\n          await this.bookingStore.getBookingDetail(this.bookingId)\n        } else {\n          console.log('[BookingDetail] 使用临时解决方案直接调用API')\n          // 直接导入API并调用\n          const { getBookingDetail } = await import('@/api/booking.js')\n          const response = await getBookingDetail(this.bookingId)\n\n          // 手动设置到store\n          if (response && response.data) {\n            this.bookingStore.setBookingDetail(response.data)\n          } else if (response) {\n            this.bookingStore.setBookingDetail(response)\n          }\n        }\n\n        // 等待一下确保数据已经更新到store\n        await this.$nextTick()\n\n        // 检查数据是否有效\n        if (!this.bookingDetail) {\n          throw new Error('未能获取到订单数据，请检查网络连接')\n        }\n\n        if (!this.bookingDetail.orderNo && !this.bookingDetail.id) {\n          throw new Error('订单数据不完整，订单可能不存在或已被删除')\n        }\n\n      } catch (error) {\n        console.error('初始化数据失败:', error)\n        \n        uni.showModal({\n          title: '加载失败',\n          content: error.message || '无法获取订单详情，请检查订单号是否正确',\n          showCancel: true,\n          cancelText: '返回',\n          confirmText: '重试',\n          success: (res) => {\n            if (res.confirm) {\n              // 重试\n              this.initData()\n            } else {\n              // 返回上一页\n              uni.navigateBack()\n            }\n          }\n        })\n      }\n    },\n    \n    // 刷新数据\n    async refreshData() {\n      try {\n        await this.initData()\n        uni.stopPullDownRefresh()\n      } catch (error) {\n        uni.stopPullDownRefresh()\n        console.error('刷新数据失败:', error)\n      }\n    },\n    \n    // 取消预约\n    cancelBooking() {\n      this.$refs.cancelPopup.open()\n    },\n    \n    // 关闭取消弹窗\n    closeCancelModal() {\n      this.$refs.cancelPopup.close()\n    },\n    \n    // 确认取消\n    async confirmCancel() {\n      try {\n        uni.showLoading({ title: '取消中...' })\n        \n        await this.bookingStore.cancelBooking(this.bookingId)\n        \n        uni.hideLoading()\n        this.closeCancelModal()\n        \n        uni.showToast({\n          title: '取消成功',\n          icon: 'success'\n        })\n        \n        // 刷新数据\n        await this.refreshData()\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('取消预约失败:', error)\n        uni.showToast({\n          title: error.message || '取消失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n\n    \n    // 评价场馆\n    reviewVenue() {\n      uni.navigateTo({\n        url: `/pages/venue/review?venueId=${this.bookingDetail.venueId}&bookingId=${this.bookingId}`\n      })\n    },\n    \n    // 支付订单\n    payBooking() {\n      if (!this.bookingDetail || !this.bookingDetail.id) {\n        uni.showToast({\n          title: '订单信息不完整',\n          icon: 'none'\n        })\n        return\n      }\n\n      console.log('跳转到支付页面，订单ID:', this.bookingDetail.id)\n      uni.navigateTo({\n        url: `/pages/payment/index?orderId=${this.bookingDetail.id}&type=booking`\n      })\n    },\n\n    // 再次预约\n    rebookVenue() {\n      uni.navigateTo({\n        url: `/pages/venue/detail?id=${this.bookingDetail.venueId}`\n      })\n    },\n    \n    // 跳转到拼场详情\n    navigateToSharingDetail() {\n      if (this.bookingDetail.sharingOrder) {\n        uni.navigateTo({\n          url: `/pages/sharing/detail?id=${this.bookingDetail.sharingOrder.id}`\n        })\n      }\n    },\n    \n    // 拨打电话\n    callVenue() {\n      if (this.bookingDetail.venuePhone) {\n        uni.makePhoneCall({\n          phoneNumber: this.bookingDetail.venuePhone\n        })\n      } else {\n        uni.showToast({\n          title: '暂无联系方式',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 打开地图\n    openMap() {\n      if (this.bookingDetail.venueLatitude && this.bookingDetail.venueLongitude) {\n        uni.openLocation({\n          latitude: this.bookingDetail.venueLatitude,\n          longitude: this.bookingDetail.venueLongitude,\n          name: this.bookingDetail.venueName,\n          address: this.bookingDetail.venueLocation\n        })\n      } else {\n        uni.showToast({\n          title: '暂无位置信息',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      return formatDate(date, 'YYYY年MM月DD日 dddd')\n    },\n    \n    // 格式化日期时间\n    formatDateTime(datetime) {\n      return formatDateTime(datetime, 'YYYY-MM-DD HH:mm')\n    },\n    \n    // 格式化创建时间\n    formatCreateTime(datetime) {\n      return formatTime(datetime, 'YYYY-MM-DD HH:mm')\n    },\n    \n    // 获取状态样式类\n    getStatusClass(status) {\n      const statusMap = {\n        'PENDING': 'status-pending',\n        'CONFIRMED': 'status-confirmed',\n        'COMPLETED': 'status-completed',\n        'CANCELLED': 'status-cancelled'\n      }\n      return statusMap[status] || 'status-pending'\n    },\n    \n    // 获取状态图标\n    getStatusIcon(status) {\n      const iconMap = {\n        'PENDING': '⏳',\n        'CONFIRMED': '✅',\n        'COMPLETED': '🎉',\n        'CANCELLED': '❌'\n      }\n      return iconMap[status] || '⏳'\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'PENDING': '待确认',\n        'CONFIRMED': '已确认',\n        'COMPLETED': '已完成',\n        'CANCELLED': '已取消'\n      }\n      return statusMap[status] || '待确认'\n    },\n    \n    // 获取状态描述\n    getStatusDesc(status) {\n      const descMap = {\n        'PENDING': '场馆正在确认您的预约',\n        'CONFIRMED': '预约已确认，请按时到场',\n        'COMPLETED': '预约已完成，感谢您的使用',\n        'CANCELLED': '预约已取消'\n      }\n      return descMap[status] || ''\n    },\n    \n    // 获取拼场状态文本\n    getSharingStatusText(status) {\n      const statusMap = {\n        'RECRUITING': '招募中',\n        'FULL': '已满员',\n        'COMPLETED': '已完成',\n        'CANCELLED': '已取消'\n      }\n      return statusMap[status] || '招募中'\n    },\n    \n    // 获取预约类型文本\n    getBookingTypeText(bookingType) {\n      const typeMap = {\n        'EXCLUSIVE': '包场',\n        'SHARED': '拼场'\n      }\n      return typeMap[bookingType] || '--'\n    },\n    \n    // 获取预约类型样式类\n    getBookingTypeClass(bookingType) {\n      const classMap = {\n        'EXCLUSIVE': 'booking-type-exclusive',\n        'SHARED': 'booking-type-shared'\n      }\n      return classMap[bookingType] || ''\n    },\n\n    // 检查是否是虚拟订单\n    isVirtualOrder() {\n      if (!this.bookingDetail) return false\n      const bookingId = typeof this.bookingDetail.id === 'string' ? parseInt(this.bookingDetail.id) : this.bookingDetail.id\n      return bookingId < 0\n    },\n\n    // 获取支付金额（兼容虚拟订单和普通订单）\n    getPaymentAmount() {\n      if (!this.bookingDetail) return '0.00'\n\n      if (this.isVirtualOrder()) {\n        // 虚拟订单使用 paymentAmount\n        const amount = this.bookingDetail.paymentAmount || 0\n        return amount.toFixed(2)\n      } else {\n        // 普通订单使用 totalPrice\n        const amount = this.bookingDetail.totalPrice || 0\n        return amount.toFixed(2)\n      }\n    },\n\n    // 格式化预约日期（兼容虚拟订单和普通订单）\n    formatBookingDate() {\n      if (!this.bookingDetail) return '--'\n\n      if (this.isVirtualOrder()) {\n        // 虚拟订单从 bookingTime 中提取日期\n        const bookingTime = this.bookingDetail.bookingTime\n        if (!bookingTime) return '--'\n\n        try {\n          let dateTime\n          if (typeof bookingTime === 'string') {\n            let isoTime = bookingTime\n            if (bookingTime.includes(' ') && !bookingTime.includes('T')) {\n              isoTime = bookingTime.replace(' ', 'T')\n            }\n            dateTime = new Date(isoTime)\n          } else {\n            dateTime = new Date(bookingTime)\n          }\n\n          if (isNaN(dateTime.getTime())) {\n            console.error('虚拟订单日期格式化错误 - 无效的时间:', bookingTime)\n            return '--'\n          }\n\n          return dateTime.toLocaleDateString('zh-CN', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit'\n          }).replace(/\\//g, '-')\n        } catch (error) {\n          console.error('虚拟订单日期格式化错误:', error)\n          return '--'\n        }\n      } else {\n        // 普通订单使用 bookingDate 字段\n        if (this.bookingDetail.bookingDate) {\n          return this.formatDate(this.bookingDetail.bookingDate)\n        }\n        return '--'\n      }\n    },\n\n    // 格式化预约时间（兼容虚拟订单和普通订单）\n    formatBookingTime() {\n      if (!this.bookingDetail) return '--'\n\n      if (this.isVirtualOrder()) {\n        // 虚拟订单使用 bookingTime 和 endTime (LocalDateTime格式: \"yyyy-MM-dd HH:mm:ss\")\n        const startTime = this.bookingDetail.bookingTime\n        const endTime = this.bookingDetail.endTime\n\n        if (!startTime) return '--'\n\n        try {\n          // 处理后端返回的时间格式 \"yyyy-MM-dd HH:mm:ss\"，转换为iOS兼容格式\n          let startDateTime, endDateTime\n\n          if (typeof startTime === 'string') {\n            // 确保iOS兼容性：将空格替换为T\n            let isoTime = startTime\n            if (startTime.includes(' ') && !startTime.includes('T')) {\n              isoTime = startTime.replace(' ', 'T')\n            }\n            startDateTime = new Date(isoTime)\n            console.log('预约详情时间转换 - 原始:', startTime, '转换后:', isoTime, '解析结果:', startDateTime)\n          } else {\n            startDateTime = new Date(startTime)\n          }\n\n          if (endTime) {\n            if (typeof endTime === 'string') {\n              let isoEndTime = endTime\n              if (endTime.includes(' ') && !endTime.includes('T')) {\n                isoEndTime = endTime.replace(' ', 'T')\n              }\n              endDateTime = new Date(isoEndTime)\n              console.log('预约详情结束时间转换 - 原始:', endTime, '转换后:', isoEndTime, '解析结果:', endDateTime)\n            } else {\n              endDateTime = new Date(endTime)\n            }\n          }\n\n          // 检查日期是否有效\n          if (isNaN(startDateTime.getTime())) {\n            console.error('虚拟订单时间格式化错误 - 无效的开始时间:', startTime)\n            return '--'\n          }\n\n          // 格式化开始时间 (HH:mm)\n          const startTimeStr = startDateTime.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n          })\n\n          // 格式化结束时间 (HH:mm)\n          let endTimeStr = ''\n          if (endDateTime && !isNaN(endDateTime.getTime())) {\n            endTimeStr = endDateTime.toLocaleTimeString('zh-CN', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false\n            })\n          }\n\n          return endTimeStr ? `${startTimeStr} - ${endTimeStr}` : startTimeStr\n        } catch (error) {\n          console.error('虚拟订单时间格式化错误:', error)\n          return '--'\n        }\n      } else {\n        // 普通订单使用 startTime 和 endTime\n        if (this.bookingDetail.startTime && this.bookingDetail.endTime) {\n          return `${this.bookingDetail.startTime} - ${this.bookingDetail.endTime}`\n        }\n        return '--'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n// 状态区域\n.status-section {\n  display: flex;\n  align-items: center;\n  background-color: #ffffff;\n  padding: 40rpx 30rpx;\n  margin-bottom: 20rpx;\n  \n  .status-icon {\n    width: 100rpx;\n    height: 100rpx;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 48rpx;\n    margin-right: 30rpx;\n    \n    &.status-pending {\n      background-color: #fff7e6;\n    }\n    \n    &.status-confirmed {\n      background-color: #e6f7ff;\n    }\n    \n    &.status-completed {\n      background-color: #f6ffed;\n    }\n    \n    &.status-cancelled {\n      background-color: #fff2f0;\n    }\n  }\n  \n  .status-info {\n    flex: 1;\n    \n    .status-text {\n      display: block;\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n      margin-bottom: 8rpx;\n    }\n    \n    .status-desc {\n      font-size: 24rpx;\n      color: #666666;\n    }\n  }\n}\n\n// 信息区域\n.info-section,\n.contact-section,\n.sharing-section {\n  background-color: #ffffff;\n  margin-bottom: 20rpx;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333333;\n    padding: 30rpx 30rpx 20rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n  }\n}\n\n// 预约信息\n.info-section {\n  .info-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 24rpx 30rpx;\n    border-bottom: 1rpx solid #f8f8f8;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      font-size: 28rpx;\n      color: #666666;\n    }\n    \n    .info-value {\n      font-size: 28rpx;\n      color: #333333;\n      text-align: right;\n      max-width: 60%;\n      \n      &.price {\n        color: #ff6b35;\n        font-weight: 600;\n      }\n      \n      &.booking-type {\n        padding: 8rpx 16rpx;\n        border-radius: 20rpx;\n        font-size: 24rpx;\n        font-weight: 500;\n\n        &.booking-type-exclusive {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &.booking-type-shared {\n          background-color: #fff7e6;\n          color: #fa8c16;\n        }\n      }\n    }\n\n    // 预约类型容器\n    .booking-type-container {\n      display: flex;\n      align-items: center;\n      gap: 12rpx;\n    }\n\n    // 虚拟订单标识\n    .virtual-order-badge {\n      font-size: 20rpx;\n      padding: 4rpx 12rpx;\n      border-radius: 12rpx;\n      background-color: #f9f0ff;\n      color: #722ed1;\n      border: 1rpx solid #722ed1;\n    }\n  }\n}\n\n// 联系信息\n.contact-section {\n  .contact-item {\n    display: flex;\n    align-items: center;\n    padding: 24rpx 30rpx;\n    border-bottom: 1rpx solid #f8f8f8;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .contact-icon {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32rpx;\n      margin-right: 20rpx;\n    }\n    \n    .contact-info {\n      flex: 1;\n      \n      .contact-label {\n        display: block;\n        font-size: 28rpx;\n        color: #333333;\n        margin-bottom: 4rpx;\n      }\n      \n      .contact-value {\n        font-size: 24rpx;\n        color: #666666;\n      }\n    }\n    \n    .contact-arrow {\n      font-size: 24rpx;\n      color: #cccccc;\n    }\n  }\n}\n\n// 拼场信息\n.sharing-section {\n  .sharing-card {\n    padding: 30rpx;\n    \n    .sharing-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16rpx;\n      \n      .sharing-team {\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #333333;\n      }\n      \n      .sharing-status {\n        font-size: 22rpx;\n        padding: 6rpx 16rpx;\n        background-color: #e6f7ff;\n        color: #1890ff;\n        border-radius: 16rpx;\n      }\n    }\n    \n    .sharing-info {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 16rpx;\n      \n      .sharing-participants,\n      .sharing-price {\n        font-size: 24rpx;\n        color: #666666;\n      }\n      \n      .sharing-price {\n        color: #ff6b35;\n        font-weight: 600;\n      }\n    }\n    \n    .sharing-desc {\n      font-size: 24rpx;\n      color: #999999;\n      line-height: 1.4;\n    }\n  }\n}\n\n// 操作按钮\n.actions-section {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n  z-index: 100;\n  \n  .action-btn {\n    flex: 1;\n    height: 80rpx;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n    margin-right: 20rpx;\n    border: 1rpx solid;\n    \n    &:last-child {\n      margin-right: 0;\n    }\n    \n    &.cancel-btn {\n      background-color: transparent;\n      color: #ff4d4f;\n      border-color: #ff4d4f;\n    }\n    \n    &.share-btn {\n      background-color: #ff6b35;\n      color: #ffffff;\n      border-color: #ff6b35;\n    }\n    \n    &.review-btn {\n      background-color: transparent;\n      color: #1890ff;\n      border-color: #1890ff;\n    }\n    \n    &.rebook-btn {\n      background-color: #ff6b35;\n      color: #ffffff;\n      border-color: #ff6b35;\n    }\n  }\n}\n\n// 取消弹窗\n.cancel-modal {\n  width: 600rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  \n  .modal-header {\n    padding: 30rpx;\n    text-align: center;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n  }\n  \n  .modal-content {\n    padding: 40rpx 30rpx;\n    text-align: center;\n    \n    .modal-text {\n      display: block;\n      font-size: 28rpx;\n      color: #333333;\n      margin-bottom: 16rpx;\n    }\n    \n    .modal-note {\n      font-size: 24rpx;\n      color: #999999;\n    }\n  }\n  \n  .modal-actions {\n    display: flex;\n    border-top: 1rpx solid #f0f0f0;\n    \n    .modal-btn {\n      flex: 1;\n      height: 100rpx;\n      border: none;\n      font-size: 28rpx;\n      \n      &.cancel-btn {\n        background-color: #f5f5f5;\n        color: #666666;\n      }\n      \n      &.confirm-btn {\n        background-color: #ff6b35;\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n// 加载状态\n.loading-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n  \n  .loading-text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n// 空状态\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: #999999;\n    margin-bottom: 30rpx;\n  }\n  \n  .retry-btn {\n    padding: 16rpx 32rpx;\n    background-color: #007aff;\n    color: #ffffff;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 26rpx;\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/booking/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useBookingStore", "uni", "clearCache", "formatDate", "formatDateTime", "formatTime"], "mappings": ";;;;;AA4NA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,cAAc;AAAA,IAChB;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,gBAAgB;;AACd,eAAO,UAAK,iBAAL,mBAAmB,kBAAiB,CAAC;AAAA,IAC7C;AAAA,IAED,UAAU;;AACR,eAAO,UAAK,iBAAL,mBAAmB,YAAW;AAAA,IACvC;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AAEd,SAAK,eAAeA,+BAAgB;AACpCC,kBAAY,MAAA,MAAA,OAAA,mCAAA,oCAAoC,CAAC,CAAC,KAAK,YAAY;AACnEA,kBAAA,MAAA,MAAA,OAAA,mCAAY,mCAAmC,OAAO,KAAK,YAAY;AACvEA,wEAAY,qCAAqC,OAAO,oBAAoB,KAAK,YAAY,CAAC;AAC9FA,wBAAA,MAAA,OAAA,mCAAY,yCAAyC,OAAO,KAAK,aAAa,qBAAqB,UAAU;AAG7G,QAAI,KAAK,aAAa,kBAAkB;AACtCA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,uCAAuC;AAAA,WAC9C;AACLA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,wCAAwC;AAAA,IACtD;AAEA,SAAK,YAAY,QAAQ;AACzB,SAAK,SAAS;AAAA,EACf;AAAA,EAED,oBAAoB;AAClB,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA;AAAA,IAGP,MAAM,WAAW;AACf,UAAI;AAEF,YAAI,CAAC,KAAK,WAAW;AACnB,gBAAM,IAAI,MAAM,gBAAgB;AAAA,QAClC;AAGAC,sBAAAA,WAAW,aAAa,KAAK,SAAS,EAAE;AAGxC,YAAI,OAAO,KAAK,aAAa,qBAAqB,YAAY;AAC5D,gBAAM,KAAK,aAAa,iBAAiB,KAAK,SAAS;AAAA,eAClD;AACLD,wBAAAA,sDAAY,iCAAiC;AAE7C,gBAAM,EAAE,iBAAiB,IAAI,MAAa;AAC1C,gBAAM,WAAW,MAAM,iBAAiB,KAAK,SAAS;AAGtD,cAAI,YAAY,SAAS,MAAM;AAC7B,iBAAK,aAAa,iBAAiB,SAAS,IAAI;AAAA,UAClD,WAAW,UAAU;AACnB,iBAAK,aAAa,iBAAiB,QAAQ;AAAA,UAC7C;AAAA,QACF;AAGA,cAAM,KAAK,UAAU;AAGrB,YAAI,CAAC,KAAK,eAAe;AACvB,gBAAM,IAAI,MAAM,mBAAmB;AAAA,QACrC;AAEA,YAAI,CAAC,KAAK,cAAc,WAAW,CAAC,KAAK,cAAc,IAAI;AACzD,gBAAM,IAAI,MAAM,sBAAsB;AAAA,QACxC;AAAA,MAEA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,YAAY,KAAK;AAE/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS,MAAM,WAAW;AAAA,UAC1B,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AAEf,mBAAK,SAAS;AAAA,mBACT;AAELA,4BAAAA,MAAI,aAAa;AAAA,YACnB;AAAA,UACF;AAAA,SACD;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI;AACF,cAAM,KAAK,SAAS;AACpBA,sBAAAA,MAAI,oBAAoB;AAAA,MACxB,SAAO,OAAO;AACdA,sBAAAA,MAAI,oBAAoB;AACxBA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,MAAM,YAAY,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,MAAM,YAAY,MAAM;AAAA,IAC9B;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,KAAK,aAAa,cAAc,KAAK,SAAS;AAEpDA,sBAAAA,MAAI,YAAY;AAChB,aAAK,iBAAiB;AAEtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,cAAM,KAAK,YAAY;AAAA,MAEvB,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAKD,cAAc;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,KAAK,cAAc,OAAO,cAAc,KAAK,SAAS;AAAA,OAC3F;AAAA,IACF;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,cAAc,IAAI;AACjDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEAA,oBAAY,MAAA,MAAA,OAAA,mCAAA,iBAAiB,KAAK,cAAc,EAAE;AAClDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gCAAgC,KAAK,cAAc,EAAE;AAAA,OAC3D;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0BAA0B,KAAK,cAAc,OAAO;AAAA,OAC1D;AAAA,IACF;AAAA;AAAA,IAGD,0BAA0B;AACxB,UAAI,KAAK,cAAc,cAAc;AACnCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,4BAA4B,KAAK,cAAc,aAAa,EAAE;AAAA,SACpE;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACV,UAAI,KAAK,cAAc,YAAY;AACjCA,sBAAAA,MAAI,cAAc;AAAA,UAChB,aAAa,KAAK,cAAc;AAAA,SACjC;AAAA,aACI;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,KAAK,cAAc,iBAAiB,KAAK,cAAc,gBAAgB;AACzEA,sBAAAA,MAAI,aAAa;AAAA,UACf,UAAU,KAAK,cAAc;AAAA,UAC7B,WAAW,KAAK,cAAc;AAAA,UAC9B,MAAM,KAAK,cAAc;AAAA,UACzB,SAAS,KAAK,cAAc;AAAA,SAC7B;AAAA,aACI;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,aAAOE,cAAU,WAAC,MAAM,kBAAkB;AAAA,IAC3C;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,aAAOC,cAAc,eAAC,UAAU,kBAAkB;AAAA,IACnD;AAAA;AAAA,IAGD,iBAAiB,UAAU;AACzB,aAAOC,cAAU,WAAC,UAAU,kBAAkB;AAAA,IAC/C;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AACA,aAAO,QAAQ,MAAM,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AACA,aAAO,QAAQ,MAAM,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,qBAAqB,QAAQ;AAC3B,YAAM,YAAY;AAAA,QAChB,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,mBAAmB,aAAa;AAC9B,YAAM,UAAU;AAAA,QACd,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,aAAO,QAAQ,WAAW,KAAK;AAAA,IAChC;AAAA;AAAA,IAGD,oBAAoB,aAAa;AAC/B,YAAM,WAAW;AAAA,QACf,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,aAAO,SAAS,WAAW,KAAK;AAAA,IACjC;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,CAAC,KAAK;AAAe,eAAO;AAChC,YAAM,YAAY,OAAO,KAAK,cAAc,OAAO,WAAW,SAAS,KAAK,cAAc,EAAE,IAAI,KAAK,cAAc;AACnH,aAAO,YAAY;AAAA,IACpB;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,CAAC,KAAK;AAAe,eAAO;AAEhC,UAAI,KAAK,kBAAkB;AAEzB,cAAM,SAAS,KAAK,cAAc,iBAAiB;AACnD,eAAO,OAAO,QAAQ,CAAC;AAAA,aAClB;AAEL,cAAM,SAAS,KAAK,cAAc,cAAc;AAChD,eAAO,OAAO,QAAQ,CAAC;AAAA,MACzB;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,CAAC,KAAK;AAAe,eAAO;AAEhC,UAAI,KAAK,kBAAkB;AAEzB,cAAM,cAAc,KAAK,cAAc;AACvC,YAAI,CAAC;AAAa,iBAAO;AAEzB,YAAI;AACF,cAAI;AACJ,cAAI,OAAO,gBAAgB,UAAU;AACnC,gBAAI,UAAU;AACd,gBAAI,YAAY,SAAS,GAAG,KAAK,CAAC,YAAY,SAAS,GAAG,GAAG;AAC3D,wBAAU,YAAY,QAAQ,KAAK,GAAG;AAAA,YACxC;AACA,uBAAW,IAAI,KAAK,OAAO;AAAA,iBACtB;AACL,uBAAW,IAAI,KAAK,WAAW;AAAA,UACjC;AAEA,cAAI,MAAM,SAAS,QAAO,CAAE,GAAG;AAC7BJ,0BAAAA,MAAc,MAAA,SAAA,mCAAA,wBAAwB,WAAW;AACjD,mBAAO;AAAA,UACT;AAEA,iBAAO,SAAS,mBAAmB,SAAS;AAAA,YAC1C,MAAM;AAAA,YACN,OAAO;AAAA,YACP,KAAK;AAAA,UACP,CAAC,EAAE,QAAQ,OAAO,GAAG;AAAA,QACrB,SAAO,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,mCAAc,gBAAgB,KAAK;AACnC,iBAAO;AAAA,QACT;AAAA,aACK;AAEL,YAAI,KAAK,cAAc,aAAa;AAClC,iBAAO,KAAK,WAAW,KAAK,cAAc,WAAW;AAAA,QACvD;AACA,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,CAAC,KAAK;AAAe,eAAO;AAEhC,UAAI,KAAK,kBAAkB;AAEzB,cAAM,YAAY,KAAK,cAAc;AACrC,cAAM,UAAU,KAAK,cAAc;AAEnC,YAAI,CAAC;AAAW,iBAAO;AAEvB,YAAI;AAEF,cAAI,eAAe;AAEnB,cAAI,OAAO,cAAc,UAAU;AAEjC,gBAAI,UAAU;AACd,gBAAI,UAAU,SAAS,GAAG,KAAK,CAAC,UAAU,SAAS,GAAG,GAAG;AACvD,wBAAU,UAAU,QAAQ,KAAK,GAAG;AAAA,YACtC;AACA,4BAAgB,IAAI,KAAK,OAAO;AAChCA,gCAAY,MAAA,OAAA,mCAAA,kBAAkB,WAAW,QAAQ,SAAS,SAAS,aAAa;AAAA,iBAC3E;AACL,4BAAgB,IAAI,KAAK,SAAS;AAAA,UACpC;AAEA,cAAI,SAAS;AACX,gBAAI,OAAO,YAAY,UAAU;AAC/B,kBAAI,aAAa;AACjB,kBAAI,QAAQ,SAAS,GAAG,KAAK,CAAC,QAAQ,SAAS,GAAG,GAAG;AACnD,6BAAa,QAAQ,QAAQ,KAAK,GAAG;AAAA,cACvC;AACA,4BAAc,IAAI,KAAK,UAAU;AACjCA,kFAAY,oBAAoB,SAAS,QAAQ,YAAY,SAAS,WAAW;AAAA,mBAC5E;AACL,4BAAc,IAAI,KAAK,OAAO;AAAA,YAChC;AAAA,UACF;AAGA,cAAI,MAAM,cAAc,QAAO,CAAE,GAAG;AAClCA,0BAAAA,MAAc,MAAA,SAAA,mCAAA,0BAA0B,SAAS;AACjD,mBAAO;AAAA,UACT;AAGA,gBAAM,eAAe,cAAc,mBAAmB,SAAS;AAAA,YAC7D,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,WACT;AAGD,cAAI,aAAa;AACjB,cAAI,eAAe,CAAC,MAAM,YAAY,QAAS,CAAA,GAAG;AAChD,yBAAa,YAAY,mBAAmB,SAAS;AAAA,cACnD,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,QAAQ;AAAA,aACT;AAAA,UACH;AAEA,iBAAO,aAAa,GAAG,YAAY,MAAM,UAAU,KAAK;AAAA,QACxD,SAAO,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,mCAAc,gBAAgB,KAAK;AACnC,iBAAO;AAAA,QACT;AAAA,aACK;AAEL,YAAI,KAAK,cAAc,aAAa,KAAK,cAAc,SAAS;AAC9D,iBAAO,GAAG,KAAK,cAAc,SAAS,MAAM,KAAK,cAAc,OAAO;AAAA,QACxE;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtqBA,GAAG,WAAW,eAAe;"}