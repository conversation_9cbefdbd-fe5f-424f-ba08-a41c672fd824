{"version": 3, "file": "failed.js", "sources": ["pages/payment/failed.vue", "pages/payment/failed.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"failed-container\">\n    <!-- 失败图标 -->\n    <view class=\"failed-icon-container\">\n      <view class=\"failed-icon\">❌</view>\n      <text class=\"failed-title\">支付失败</text>\n      <text class=\"failed-subtitle\">{{ failureReason || '支付过程中出现问题' }}</text>\n    </view>\n\n    <!-- 订单信息 -->\n    <view class=\"order-info\" v-if=\"orderInfo\">\n      <view class=\"info-item\">\n        <text class=\"info-label\">订单号</text>\n        <text class=\"info-value\">{{ orderInfo.orderNo }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">订单金额</text>\n        <text class=\"info-value amount\">¥{{ orderInfo.totalPrice }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">场馆名称</text>\n        <text class=\"info-value\">{{ orderInfo.venueName }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">预约时间</text>\n        <text class=\"info-value\">{{ formatDateTime(orderInfo.bookingDate, orderInfo.startTime, orderInfo.endTime) }}</text>\n      </view>\n    </view>\n\n    <!-- 失败原因 -->\n    <view class=\"failure-reasons\">\n      <text class=\"reasons-title\">可能的原因</text>\n      <text class=\"reason-item\">• 账户余额不足</text>\n      <text class=\"reason-item\">• 银行卡信息有误</text>\n      <text class=\"reason-item\">• 网络连接异常</text>\n      <text class=\"reason-item\">• 支付密码错误</text>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"goBack\">返回订单</button>\n      <button class=\"btn btn-primary\" @click=\"retryPayment\">重新支付</button>\n    </view>\n\n    <!-- 客服联系 -->\n    <view class=\"contact-service\">\n      <text class=\"contact-title\">需要帮助？</text>\n      <view class=\"contact-buttons\">\n        <button class=\"contact-btn\" @click=\"callService\">\n          <text class=\"contact-icon\">📞</text>\n          <text class=\"contact-text\">联系客服</text>\n        </button>\n        <button class=\"contact-btn\" @click=\"viewFAQ\">\n          <text class=\"contact-icon\">❓</text>\n          <text class=\"contact-text\">常见问题</text>\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getOrderDetail } from '@/api/payment.js'\n\nexport default {\n  name: 'PaymentFailed',\n  data() {\n    return {\n      orderInfo: null,\n      orderId: null,\n      failureReason: ''\n    }\n  },\n  \n  onLoad(options) {\n    console.log('支付失败页面参数:', options)\n    this.orderId = options.orderId\n    this.failureReason = options.reason || '支付过程中出现问题'\n\n    if (this.orderId) {\n      this.loadOrderInfo()\n    } else {\n      // 如果没有订单ID，显示默认信息\n      this.orderInfo = {\n        orderNo: '未知订单',\n        totalPrice: 0,\n        venueName: '未知场馆',\n        bookingDate: new Date().toISOString().split('T')[0],\n        startTime: '00:00',\n        endTime: '00:00'\n      }\n    }\n  },\n  \n  methods: {\n    // 加载订单信息\n    async loadOrderInfo() {\n      try {\n        uni.showLoading({ title: '加载中...' })\n        const response = await getOrderDetail(this.orderId)\n\n        if (response && response.data) {\n          this.orderInfo = response.data\n        } else if (response) {\n          this.orderInfo = response\n        } else {\n          throw new Error('未获取到订单数据')\n        }\n\n        uni.hideLoading()\n      } catch (error) {\n        uni.hideLoading()\n        console.error('加载订单信息失败:', error)\n\n        // 设置默认订单信息，避免页面空白\n        this.orderInfo = {\n          orderNo: `ORD${Date.now()}`,\n          totalPrice: 0,\n          venueName: '体育场馆',\n          bookingDate: new Date().toISOString().split('T')[0],\n          startTime: '09:00',\n          endTime: '10:00'\n        }\n\n        uni.showToast({\n          title: '加载订单信息失败，显示默认信息',\n          icon: 'none',\n          duration: 2000\n        })\n      }\n    },\n    \n    // 格式化日期时间\n    formatDateTime(date, startTime, endTime) {\n      if (!date || !startTime) return '未知时间'\n      \n      const dateStr = new Date(date).toLocaleDateString('zh-CN')\n      const start = startTime.substring(0, 5)\n      const end = endTime ? endTime.substring(0, 5) : ''\n      \n      return `${dateStr} ${start}${end ? '-' + end : ''}`\n    },\n    \n    // 返回订单页面\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    // 重新支付\n    retryPayment() {\n      if (this.orderId) {\n        uni.redirectTo({\n          url: `/pages/payment/index?orderId=${this.orderId}`\n        })\n      }\n    },\n    \n    // 联系客服\n    callService() {\n      uni.showModal({\n        title: '联系客服',\n        content: '客服电话：************\\n工作时间：9:00-18:00',\n        showCancel: true,\n        cancelText: '取消',\n        confirmText: '拨打',\n        success: (res) => {\n          if (res.confirm) {\n            uni.makePhoneCall({\n              phoneNumber: '************'\n            })\n          }\n        }\n      })\n    },\n    \n    // 查看常见问题\n    viewFAQ() {\n      uni.showToast({\n        title: '功能开发中',\n        icon: 'none'\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.failed-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  padding: 40rpx 30rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.failed-icon-container {\n  text-align: center;\n  margin-bottom: 60rpx;\n}\n\n.failed-icon {\n  font-size: 120rpx;\n  margin-bottom: 20rpx;\n}\n\n.failed-title {\n  display: block;\n  font-size: 48rpx;\n  font-weight: bold;\n  color: white;\n  margin-bottom: 10rpx;\n}\n\n.failed-subtitle {\n  display: block;\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.order-info {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  width: 100%;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.info-item:last-child {\n  border-bottom: none;\n}\n\n.info-label {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.info-value.amount {\n  color: #ff6b35;\n  font-weight: bold;\n  font-size: 32rpx;\n}\n\n.failure-reasons {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  width: 100%;\n  margin-bottom: 40rpx;\n}\n\n.reasons-title {\n  display: block;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: white;\n  margin-bottom: 20rpx;\n}\n\n.reason-item {\n  display: block;\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.6;\n  margin-bottom: 10rpx;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 20rpx;\n  width: 100%;\n  margin-bottom: 40rpx;\n}\n\n.btn {\n  flex: 1;\n  height: 88rpx;\n  border-radius: 44rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-primary {\n  background: #ff6b35;\n  color: white;\n}\n\n.btn-secondary {\n  background: white;\n  color: #ff6b35;\n  border: 2rpx solid #ff6b35;\n}\n\n.contact-service {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  width: 100%;\n}\n\n.contact-title {\n  display: block;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: white;\n  margin-bottom: 20rpx;\n  text-align: center;\n}\n\n.contact-buttons {\n  display: flex;\n  gap: 20rpx;\n}\n\n.contact-btn {\n  flex: 1;\n  background: rgba(255, 255, 255, 0.2);\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\n  border-radius: 15rpx;\n  padding: 20rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10rpx;\n}\n\n.contact-icon {\n  font-size: 40rpx;\n}\n\n.contact-text {\n  font-size: 24rpx;\n  color: white;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/payment/failed.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getOrderDetail"], "mappings": ";;;AAgEA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,eAAe;AAAA,IACjB;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACdA,kBAAAA,MAAY,MAAA,OAAA,kCAAA,aAAa,OAAO;AAChC,SAAK,UAAU,QAAQ;AACvB,SAAK,gBAAgB,QAAQ,UAAU;AAEvC,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc;AAAA,WACd;AAEL,WAAK,YAAY;AAAA,QACf,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAa,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAAA,QAClD,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,gBAAgB;AACpB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AACnC,cAAM,WAAW,MAAMC,2BAAe,KAAK,OAAO;AAElD,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,YAAY,SAAS;AAAA,QAC5B,WAAW,UAAU;AACnB,eAAK,YAAY;AAAA,eACZ;AACL,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC5B;AAEAD,sBAAAA,MAAI,YAAY;AAAA,MAChB,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAGhC,aAAK,YAAY;AAAA,UACf,SAAS,MAAM,KAAK,IAAK,CAAA;AAAA,UACzB,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,cAAa,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAAA,UAClD,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,MAAM,WAAW,SAAS;AACvC,UAAI,CAAC,QAAQ,CAAC;AAAW,eAAO;AAEhC,YAAM,UAAU,IAAI,KAAK,IAAI,EAAE,mBAAmB,OAAO;AACzD,YAAM,QAAQ,UAAU,UAAU,GAAG,CAAC;AACtC,YAAM,MAAM,UAAU,QAAQ,UAAU,GAAG,CAAC,IAAI;AAEhD,aAAO,GAAG,OAAO,IAAI,KAAK,GAAG,MAAM,MAAM,MAAM,EAAE;AAAA,IAClD;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,KAAK,SAAS;AAChBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,gCAAgC,KAAK,OAAO;AAAA,SAClD;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,cAAc;AAAA,cAChB,aAAa;AAAA,aACd;AAAA,UACH;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,UAAU;AACRA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;ACtLA,GAAG,WAAW,eAAe;"}