{"version": 3, "file": "success.js", "sources": ["pages/payment/success.vue", "pages/payment/success.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"success-container\">\n    <!-- 成功图标 -->\n    <view class=\"success-icon-container\">\n      <view class=\"success-icon\">✅</view>\n      <text class=\"success-title\">支付成功</text>\n      <text class=\"success-subtitle\">您的订单已支付完成</text>\n    </view>\n\n    <!-- 订单信息 -->\n    <view class=\"order-info\" v-if=\"orderInfo\">\n      <view class=\"info-item\">\n        <text class=\"info-label\">订单号</text>\n        <text class=\"info-value\">{{ orderInfo.orderNo }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">支付金额</text>\n        <text class=\"info-value amount\">¥{{ getPaymentAmount() }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">场馆名称</text>\n        <text class=\"info-value\">{{ orderInfo.venueName }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">预约时间</text>\n        <text class=\"info-value\">{{ formatOrderDateTime() }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">支付时间</text>\n        <text class=\"info-value\">{{ formatPaymentTime() }}</text>\n      </view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"viewOrder\">{{ getViewOrderText() }}</button>\n      <button class=\"btn btn-primary\" @click=\"goHome\">{{ getGoHomeText() }}</button>\n    </view>\n\n    <!-- 温馨提示 -->\n    <view class=\"tips\">\n      <text class=\"tips-title\">温馨提示</text>\n      <text class=\"tips-text\">• 请按时到达场馆，凭订单号入场</text>\n      <text class=\"tips-text\">• 如需取消或修改，请提前联系客服</text>\n      <text class=\"tips-text\">• 预约时间前30分钟可免费取消</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getOrderDetail } from '@/api/payment.js'\nimport { get } from '@/utils/request.js'\n\nexport default {\n  name: 'PaymentSuccess',\n  data() {\n    return {\n      orderInfo: null,\n      orderId: null,\n      fromPage: '' // 来源页面\n    }\n  },\n  \n  onLoad(options) {\n    console.log('支付成功页面参数:', options)\n    this.orderId = options.orderId\n    this.fromPage = options.from || '' // 记录来源页面\n\n    if (this.orderId) {\n      this.loadOrderInfo()\n    } else {\n      // 如果没有订单ID，显示默认信息\n      this.orderInfo = {\n        orderNo: '未知订单',\n        totalPrice: 0,\n        venueName: '未知场馆',\n        bookingDate: new Date().toISOString().split('T')[0],\n        startTime: '00:00',\n        endTime: '00:00'\n      }\n    }\n  },\n  \n  methods: {\n    // 加载订单信息\n    async loadOrderInfo() {\n      try {\n        uni.showLoading({ title: '加载中...' })\n\n        // 检查是否是虚拟订单（负数ID）\n        const isVirtualOrder = this.orderId < 0\n        console.log('支付成功页面 - 订单ID:', this.orderId, '是否为虚拟订单:', isVirtualOrder)\n\n        let response\n        if (isVirtualOrder) {\n          // 虚拟订单：使用申请ID调用虚拟订单API\n          const requestId = Math.abs(this.orderId) // 转换为正数\n          console.log('获取虚拟订单详情，申请ID:', requestId)\n          response = await get(`/users/me/virtual-order/${requestId}`)\n        } else {\n          // 真实订单：使用原有API\n          response = await getOrderDetail(this.orderId)\n        }\n\n        if (response && response.data) {\n          this.orderInfo = response.data\n          console.log('订单信息:', this.orderInfo)\n\n          // 如果是虚拟订单，添加特殊标识\n          if (isVirtualOrder) {\n            this.orderInfo.isVirtualOrder = true\n            console.log('虚拟订单支付成功信息:')\n            console.log('- 支付金额:', this.orderInfo.paymentAmount)\n            console.log('- 总价:', this.orderInfo.totalPrice)\n            console.log('- 队伍名称:', this.orderInfo.applicantTeamName)\n          }\n        } else if (response) {\n          // 如果response直接是订单数据\n          this.orderInfo = response\n          if (isVirtualOrder) {\n            this.orderInfo.isVirtualOrder = true\n          }\n          console.log('订单信息(直接):', this.orderInfo)\n        } else {\n          throw new Error('未获取到订单数据')\n        }\n\n        uni.hideLoading()\n      } catch (error) {\n        uni.hideLoading()\n        console.error('加载订单信息失败:', error)\n\n        // 设置默认订单信息，避免页面空白\n        this.orderInfo = {\n          orderNo: `ORD${Date.now()}`,\n          totalPrice: 0,\n          paymentAmount: 0,\n          venueName: '体育场馆',\n          bookingDate: new Date().toISOString().split('T')[0],\n          startTime: '09:00',\n          endTime: '10:00',\n          isVirtualOrder: this.orderId < 0\n        }\n\n        uni.showToast({\n          title: '加载订单信息失败，显示默认信息',\n          icon: 'none',\n          duration: 2000\n        })\n      }\n    },\n    \n    // 格式化订单时间（兼容虚拟订单和普通订单）\n    formatOrderDateTime() {\n      if (!this.orderInfo) return '未知时间'\n\n      // 虚拟订单使用 bookingTime 和 endTime (LocalDateTime格式)\n      if (this.orderInfo.isVirtualOrder || this.orderInfo.bookingTime) {\n        const startTime = this.orderInfo.bookingTime\n        const endTime = this.orderInfo.endTime\n\n        if (!startTime) return '未设置'\n\n        try {\n          // 处理LocalDateTime格式，转换为iOS兼容格式\n          let startDateTime, endDateTime\n\n          if (typeof startTime === 'string') {\n            const isoTime = startTime.replace(' ', 'T')\n            startDateTime = new Date(isoTime)\n          } else {\n            startDateTime = new Date(startTime)\n          }\n\n          if (endTime) {\n            if (typeof endTime === 'string') {\n              const isoEndTime = endTime.replace(' ', 'T')\n              endDateTime = new Date(isoEndTime)\n            } else {\n              endDateTime = new Date(endTime)\n            }\n          }\n\n          // 检查日期是否有效\n          if (isNaN(startDateTime.getTime())) {\n            console.error('无效的开始时间:', startTime)\n            return '时间格式错误'\n          }\n\n          const dateStr = startDateTime.toLocaleDateString('zh-CN', {\n            month: '2-digit',\n            day: '2-digit'\n          })\n          const startTimeStr = startDateTime.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n          })\n\n          let result = `${dateStr} ${startTimeStr}`\n\n          if (endDateTime && !isNaN(endDateTime.getTime())) {\n            const endTimeStr = endDateTime.toLocaleTimeString('zh-CN', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false\n            })\n            result += `-${endTimeStr}`\n          }\n\n          return result\n        } catch (error) {\n          console.error('时间格式化错误:', error)\n          return '时间格式错误'\n        }\n      }\n\n      // 普通订单使用 bookingDate, startTime, endTime\n      return this.formatDateTime(this.orderInfo.bookingDate, this.orderInfo.startTime, this.orderInfo.endTime)\n    },\n\n    // 格式化日期时间（普通订单）\n    formatDateTime(date, startTime, endTime) {\n      if (!date || !startTime) return '未知时间'\n\n      const dateStr = new Date(date).toLocaleDateString('zh-CN')\n      const start = startTime.substring(0, 5)\n      const end = endTime ? endTime.substring(0, 5) : ''\n\n      return `${dateStr} ${start}${end ? '-' + end : ''}`\n    },\n    \n    // 格式化支付时间\n    formatPaymentTime() {\n      return new Date().toLocaleString('zh-CN')\n    },\n\n    // 获取查看订单按钮文本\n    getViewOrderText() {\n      if (this.fromPage === 'sharing-manage') {\n        return '返回管理'\n      } else if (this.fromPage === 'sharing-list' || this.fromPage === 'sharing-detail') {\n        return '查看预约'\n      } else {\n        return '查看订单'\n      }\n    },\n\n    // 获取返回按钮文本\n    getGoHomeText() {\n      if (this.fromPage === 'sharing-list') {\n        return '返回拼场'\n      } else if (this.fromPage === 'sharing-detail') {\n        return '返回拼场'\n      } else if (this.fromPage === 'sharing-manage') {\n        return '返回上页'\n      } else {\n        return '返回首页'\n      }\n    },\n    \n    // 查看订单\n    viewOrder() {\n      if (this.orderId) {\n        // 根据来源页面决定跳转目标\n        if (this.fromPage === 'sharing-manage') {\n          // 从拼场管理页面来的，跳转到我的预约页面查看订单\n          uni.switchTab({\n            url: '/pages/booking/list'\n          })\n        } else if (this.fromPage === 'sharing-list' || this.fromPage === 'sharing-detail') {\n          // 从拼场相关页面来的，跳转到我的预约页面\n          uni.switchTab({\n            url: '/pages/booking/list'\n          })\n        } else {\n          // 默认跳转到订单详情\n          uni.navigateTo({\n            url: `/pages/booking/detail?id=${this.orderId}`\n          })\n        }\n      }\n    },\n\n    // 返回首页\n    goHome() {\n      // 根据来源页面智能跳转\n      if (this.fromPage === 'sharing-manage') {\n        // 从拼场管理页面来的，发送支付成功事件并返回拼场管理页面\n        console.log('=== 支付成功，通知拼场管理页面刷新 ===')\n        uni.$emit('paymentSuccess', {\n          orderId: this.orderId,\n          fromPage: this.fromPage,\n          type: 'sharing'\n        })\n        uni.navigateBack()\n      } else if (this.fromPage === 'sharing-list') {\n        // 从拼场列表来的，返回拼场列表\n        uni.switchTab({\n          url: '/pages/sharing/list'\n        })\n      } else if (this.fromPage === 'sharing-detail') {\n        // 从拼场详情来的，返回拼场列表\n        uni.switchTab({\n          url: '/pages/sharing/list'\n        })\n      } else {\n        // 默认返回首页\n        uni.switchTab({\n          url: '/pages/index/index'\n        })\n      }\n    },\n\n    // 获取支付金额（兼容虚拟订单和普通订单）\n    getPaymentAmount() {\n      if (!this.orderInfo) return '0.00'\n\n      // 虚拟订单使用 paymentAmount，普通订单使用 totalPrice\n      const amount = this.orderInfo.isVirtualOrder\n        ? this.orderInfo.paymentAmount\n        : this.orderInfo.totalPrice\n\n      return amount?.toFixed(2) || '0.00'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.success-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 40rpx 30rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.success-icon-container {\n  text-align: center;\n  margin-bottom: 60rpx;\n}\n\n.success-icon {\n  font-size: 120rpx;\n  margin-bottom: 20rpx;\n}\n\n.success-title {\n  display: block;\n  font-size: 48rpx;\n  font-weight: bold;\n  color: white;\n  margin-bottom: 10rpx;\n}\n\n.success-subtitle {\n  display: block;\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.order-info {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  width: 100%;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.info-item:last-child {\n  border-bottom: none;\n}\n\n.info-label {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.info-value.amount {\n  color: #ff6b35;\n  font-weight: bold;\n  font-size: 32rpx;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 20rpx;\n  width: 100%;\n  margin-bottom: 40rpx;\n}\n\n.btn {\n  flex: 1;\n  height: 88rpx;\n  border-radius: 44rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-primary {\n  background: #ff6b35;\n  color: white;\n}\n\n.btn-secondary {\n  background: white;\n  color: #ff6b35;\n  border: 2rpx solid #ff6b35;\n}\n\n.tips {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  width: 100%;\n}\n\n.tips-title {\n  display: block;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: white;\n  margin-bottom: 20rpx;\n}\n\n.tips-text {\n  display: block;\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.6;\n  margin-bottom: 10rpx;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/payment/success.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "get", "getOrderDetail"], "mappings": ";;;;AAqDA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA;AAAA,IACZ;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACdA,kBAAAA,MAAY,MAAA,OAAA,mCAAA,aAAa,OAAO;AAChC,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW,QAAQ,QAAQ;AAEhC,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc;AAAA,WACd;AAEL,WAAK,YAAY;AAAA,QACf,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAa,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAAA,QAClD,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,gBAAgB;AACpB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAGnC,cAAM,iBAAiB,KAAK,UAAU;AACtCA,4BAAA,MAAA,OAAA,mCAAY,kBAAkB,KAAK,SAAS,YAAY,cAAc;AAEtE,YAAI;AACJ,YAAI,gBAAgB;AAElB,gBAAM,YAAY,KAAK,IAAI,KAAK,OAAO;AACvCA,wBAAAA,MAAA,MAAA,OAAA,mCAAY,kBAAkB,SAAS;AACvC,qBAAW,MAAMC,cAAG,IAAC,2BAA2B,SAAS,EAAE;AAAA,eACtD;AAEL,qBAAW,MAAMC,YAAAA,eAAe,KAAK,OAAO;AAAA,QAC9C;AAEA,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,YAAY,SAAS;AAC1BF,wBAAY,MAAA,MAAA,OAAA,oCAAA,SAAS,KAAK,SAAS;AAGnC,cAAI,gBAAgB;AAClB,iBAAK,UAAU,iBAAiB;AAChCA,0BAAAA,MAAA,MAAA,OAAA,oCAAY,aAAa;AACzBA,iFAAY,WAAW,KAAK,UAAU,aAAa;AACnDA,0BAAY,MAAA,MAAA,OAAA,oCAAA,SAAS,KAAK,UAAU,UAAU;AAC9CA,0BAAA,MAAA,MAAA,OAAA,oCAAY,WAAW,KAAK,UAAU,iBAAiB;AAAA,UACzD;AAAA,QACF,WAAW,UAAU;AAEnB,eAAK,YAAY;AACjB,cAAI,gBAAgB;AAClB,iBAAK,UAAU,iBAAiB;AAAA,UAClC;AACAA,wBAAA,MAAA,MAAA,OAAA,oCAAY,aAAa,KAAK,SAAS;AAAA,eAClC;AACL,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC5B;AAEAA,sBAAAA,MAAI,YAAY;AAAA,MAChB,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAc,MAAA,SAAA,oCAAA,aAAa,KAAK;AAGhC,aAAK,YAAY;AAAA,UACf,SAAS,MAAM,KAAK,IAAK,CAAA;AAAA,UACzB,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,WAAW;AAAA,UACX,cAAa,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAAA,UAClD,WAAW;AAAA,UACX,SAAS;AAAA,UACT,gBAAgB,KAAK,UAAU;AAAA,QACjC;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACpB,UAAI,CAAC,KAAK;AAAW,eAAO;AAG5B,UAAI,KAAK,UAAU,kBAAkB,KAAK,UAAU,aAAa;AAC/D,cAAM,YAAY,KAAK,UAAU;AACjC,cAAM,UAAU,KAAK,UAAU;AAE/B,YAAI,CAAC;AAAW,iBAAO;AAEvB,YAAI;AAEF,cAAI,eAAe;AAEnB,cAAI,OAAO,cAAc,UAAU;AACjC,kBAAM,UAAU,UAAU,QAAQ,KAAK,GAAG;AAC1C,4BAAgB,IAAI,KAAK,OAAO;AAAA,iBAC3B;AACL,4BAAgB,IAAI,KAAK,SAAS;AAAA,UACpC;AAEA,cAAI,SAAS;AACX,gBAAI,OAAO,YAAY,UAAU;AAC/B,oBAAM,aAAa,QAAQ,QAAQ,KAAK,GAAG;AAC3C,4BAAc,IAAI,KAAK,UAAU;AAAA,mBAC5B;AACL,4BAAc,IAAI,KAAK,OAAO;AAAA,YAChC;AAAA,UACF;AAGA,cAAI,MAAM,cAAc,QAAO,CAAE,GAAG;AAClCA,0BAAAA,MAAc,MAAA,SAAA,oCAAA,YAAY,SAAS;AACnC,mBAAO;AAAA,UACT;AAEA,gBAAM,UAAU,cAAc,mBAAmB,SAAS;AAAA,YACxD,OAAO;AAAA,YACP,KAAK;AAAA,WACN;AACD,gBAAM,eAAe,cAAc,mBAAmB,SAAS;AAAA,YAC7D,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,WACT;AAED,cAAI,SAAS,GAAG,OAAO,IAAI,YAAY;AAEvC,cAAI,eAAe,CAAC,MAAM,YAAY,QAAS,CAAA,GAAG;AAChD,kBAAM,aAAa,YAAY,mBAAmB,SAAS;AAAA,cACzD,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,QAAQ;AAAA,aACT;AACD,sBAAU,IAAI,UAAU;AAAA,UAC1B;AAEA,iBAAO;AAAA,QACP,SAAO,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,oCAAA,YAAY,KAAK;AAC/B,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,aAAO,KAAK,eAAe,KAAK,UAAU,aAAa,KAAK,UAAU,WAAW,KAAK,UAAU,OAAO;AAAA,IACxG;AAAA;AAAA,IAGD,eAAe,MAAM,WAAW,SAAS;AACvC,UAAI,CAAC,QAAQ,CAAC;AAAW,eAAO;AAEhC,YAAM,UAAU,IAAI,KAAK,IAAI,EAAE,mBAAmB,OAAO;AACzD,YAAM,QAAQ,UAAU,UAAU,GAAG,CAAC;AACtC,YAAM,MAAM,UAAU,QAAQ,UAAU,GAAG,CAAC,IAAI;AAEhD,aAAO,GAAG,OAAO,IAAI,KAAK,GAAG,MAAM,MAAM,MAAM,EAAE;AAAA,IAClD;AAAA;AAAA,IAGD,oBAAoB;AAClB,cAAO,oBAAI,KAAI,GAAG,eAAe,OAAO;AAAA,IACzC;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,KAAK,aAAa,kBAAkB;AACtC,eAAO;AAAA,MACT,WAAW,KAAK,aAAa,kBAAkB,KAAK,aAAa,kBAAkB;AACjF,eAAO;AAAA,aACF;AACL,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,KAAK,aAAa,gBAAgB;AACpC,eAAO;AAAA,MACT,WAAW,KAAK,aAAa,kBAAkB;AAC7C,eAAO;AAAA,MACT,WAAW,KAAK,aAAa,kBAAkB;AAC7C,eAAO;AAAA,aACF;AACL,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACV,UAAI,KAAK,SAAS;AAEhB,YAAI,KAAK,aAAa,kBAAkB;AAEtCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,WACN;AAAA,QACH,WAAW,KAAK,aAAa,kBAAkB,KAAK,aAAa,kBAAkB;AAEjFA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,WACN;AAAA,eACI;AAELA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,4BAA4B,KAAK,OAAO;AAAA,WAC9C;AAAA,QACH;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,SAAS;AAEP,UAAI,KAAK,aAAa,kBAAkB;AAEtCA,sBAAAA,MAAY,MAAA,OAAA,oCAAA,yBAAyB;AACrCA,sBAAG,MAAC,MAAM,kBAAkB;AAAA,UAC1B,SAAS,KAAK;AAAA,UACd,UAAU,KAAK;AAAA,UACf,MAAM;AAAA,SACP;AACDA,sBAAAA,MAAI,aAAa;AAAA,MACnB,WAAW,KAAK,aAAa,gBAAgB;AAE3CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,MACH,WAAW,KAAK,aAAa,kBAAkB;AAE7CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,aACI;AAELA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,CAAC,KAAK;AAAW,eAAO;AAG5B,YAAM,SAAS,KAAK,UAAU,iBAC1B,KAAK,UAAU,gBACf,KAAK,UAAU;AAEnB,cAAO,iCAAQ,QAAQ,OAAM;AAAA,IAC/B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;ACrUA,GAAG,WAAW,eAAe;"}