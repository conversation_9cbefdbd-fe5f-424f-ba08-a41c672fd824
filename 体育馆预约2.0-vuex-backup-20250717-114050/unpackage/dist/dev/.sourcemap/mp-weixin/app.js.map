{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\n\timport { setupRouterGuard } from '@/utils/router-guard-new.js'\r\n\timport { useUserStore } from '@/stores/user.js'\r\n\timport { useAppStore } from '@/stores/app.js'\r\n\t\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\tconsole.log('[App] 应用启动')\r\n\r\n\t\t// 初始化stores\r\n\t\tthis.userStore = useUserStore()\r\n\t\tthis.appStore = useAppStore()\r\n\r\n\t\t// 1. 立即设置新的路由守卫\r\n\t\tsetupRouterGuard()\r\n\r\n\t\t// 2. 初始化用户状态（从本地存储恢复）\r\n\t\tthis.userStore.initUserState()\r\n\r\n\t\t// 3. 立即检查登录状态，未登录则跳转到登录页\r\n\t\tthis.checkAndRedirectToLogin()\r\n\r\n\t\t// 4. 延迟执行非关键操作，提升启动速度\r\n\t\tthis.$nextTick(() => {\r\n\t\t\tthis.setupNetworkListener()\r\n\t\t})\r\n\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserStore: null,\r\n\t\t\t\tappStore: null\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t\r\n\t\t\t// 检查登录状态并跳转到登录页\r\n\t\t\tcheckAndRedirectToLogin() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('[App] 检查登录状态')\r\n\t\t\t\t\tconst token = uni.getStorageSync('token')\r\n\t\t\t\t\tconst userInfo = uni.getStorageSync('userInfo')\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (!token || !userInfo) {\r\n\t\t\t\t\t\tconsole.log('[App] 未登录，跳转到登录页')\r\n\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\turl: '/pages/user/login'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('[App] 已登录，继续正常流程')\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.warn('[App] 登录状态检查失败:', error.message)\r\n\t\t\t\t\t// 检查失败时也跳转到登录页\r\n\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\turl: '/pages/user/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 设置网络监听\r\n\t\t\tsetupNetworkListener() {\r\n\t\t\t\tuni.onNetworkStatusChange((res) => {\r\n\t\t\t\t\tthis.appStore.setNetworkStatus(res.isConnected)\r\n\t\t\t\t\tif (!res.isConnected) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '网络连接已断开',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n</style>\r\n", "import App from './App.vue'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport store from './store/index.js'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  store,\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nimport { pinia } from './stores/index.js'\n\nexport function createApp() {\n  const app = createSSRApp(App)\n  app.use(pinia) // 只使用Pinia\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "useUserStore", "useAppStore", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createSSRApp", "App", "pinia"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACrBA,kBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AAGxB,SAAK,YAAYC,yBAAa;AAC9B,SAAK,WAAWC,uBAAY;AAG5BC,0CAAiB;AAGjB,SAAK,UAAU,cAAc;AAG7B,SAAK,wBAAwB;AAG7B,SAAK,UAAU,MAAM;AACpB,WAAK,qBAAqB;AAAA,KAC1B;AAAA,EACD;AAAA,EACA,QAAQ,WAAW;AAClBH,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AAAA,IACX;AAAA,EACA;AAAA,EAED,SAAS;AAAA;AAAA,IAGR,0BAA0B;AACzB,UAAI;AACHA,sBAAAA,oCAAY,cAAc;AAC1B,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9C,YAAI,CAAC,SAAS,CAAC,UAAU;AACxBA,wBAAAA,oCAAY,kBAAkB;AAC9BA,wBAAAA,MAAI,SAAS;AAAA,YACZ,KAAK;AAAA,WACL;AACD;AAAA,QACD;AAEAA,sBAAAA,MAAA,MAAA,OAAA,iBAAY,kBAAkB;AAAA,MAC7B,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,QAAA,iBAAa,mBAAmB,MAAM,OAAO;AAE7CA,sBAAAA,MAAI,SAAS;AAAA,UACZ,KAAK;AAAA,SACL;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB;AACtBA,0BAAI,sBAAsB,CAAC,QAAQ;AAClC,aAAK,SAAS,iBAAiB,IAAI,WAAW;AAC9C,YAAI,CAAC,IAAI,aAAa;AACrBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,QACF;AAAA,OACA;AAAA,IACF;AAAA,EACD;AACD;AC7DM,SAAS,YAAY;AAC1B,QAAM,MAAMI,cAAY,aAACC,SAAG;AAC5B,MAAI,IAAIC,kBAAK;AACb,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}