{"version": 3, "file": "index.js", "sources": ["config/index.js"], "sourcesContent": ["// 环境配置\nconst config = {\n  development: {\n    baseURL: 'http://localhost:8080/api',\n    timeout: 15000, // 增加到15秒超时，适应复杂查询\n    debug: true,\n    cache: true, // 启用缓存\n    retryTimes: 2, // 重试次数\n    retryDelay: 1000 // 重试延迟\n  },\n  \n  production: {\n    baseURL: 'https://api.example.com/api',\n    timeout: 20000, // 生产环境增加到20秒超时\n    debug: false,\n    cache: true,\n    retryTimes: 3,\n    retryDelay: 1000\n  }\n}\n\nexport default config[process.env.NODE_ENV || 'development']"], "names": [], "mappings": ";AACA,MAAM,SAAS;AAAA,EACb,aAAa;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA;AAAA,IACT,OAAO;AAAA,IACP,OAAO;AAAA;AAAA,IACP,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,EACd;AAAA,EAEA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA;AAAA,IACT,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AACF;AAEA,MAAe,WAAA,OAAO,aAAqC;;"}