{"version": 3, "file": "CountdownTimer.js", "sources": ["components/CountdownTimer.vue", "/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/components/CountdownTimer.vue?type=component"], "sourcesContent": ["<template>\n  <view v-if=\"showCountdown\" class=\"countdown-container\" :class=\"countdownClass\">\n    <view class=\"countdown-icon\">⏰</view>\n    <view class=\"countdown-content\">\n      <text class=\"countdown-label\">{{ label }}</text>\n      <text class=\"countdown-time\">{{ countdownText }}</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { \n  getSharingOrderCountdown, \n  shouldShowCountdown, \n  formatCountdownShort,\n  getCountdownClass,\n  createCountdownTimer,\n  clearCountdownTimer\n} from '@/utils/countdown.js'\n\nexport default {\n  name: 'CountdownTimer',\n  props: {\n    // 订单对象\n    order: {\n      type: Object,\n      required: true\n    },\n    // 显示标签\n    label: {\n      type: String,\n      default: '自动取消'\n    },\n    // 是否使用简短格式\n    short: {\n      type: Boolean,\n      default: false\n    },\n    // 更新间隔（毫秒）\n    interval: {\n      type: Number,\n      default: 1000\n    }\n  },\n  \n  data() {\n    return {\n      countdown: null,\n      timerId: null,\n      showCountdown: false,\n      countdownText: '',\n      countdownClass: ''\n    }\n  },\n  \n  mounted() {\n    console.log('CountdownTimer组件mounted，订单:', this.order?.orderNo)\n    this.initCountdown()\n  },\n  \n  beforeDestroy() {\n    this.cleanup()\n  },\n  \n  watch: {\n    order: {\n      handler() {\n        this.initCountdown()\n      },\n      deep: true\n    }\n  },\n  \n  methods: {\n    // 初始化倒计时\n    initCountdown() {\n      console.log('CountdownTimer初始化，订单:', this.order?.orderNo)\n      this.cleanup()\n\n      if (!shouldShowCountdown(this.order)) {\n        this.showCountdown = false\n        return\n      }\n\n      this.showCountdown = true\n      this.updateCountdown()\n\n      // 创建定时器\n      this.timerId = createCountdownTimer(() => {\n        this.updateCountdown()\n      }, this.interval)\n    },\n    \n    // 更新倒计时\n    updateCountdown() {\n      const countdownInfo = getSharingOrderCountdown(this.order)\n      this.countdown = countdownInfo\n\n      if (!countdownInfo.showCountdown) {\n        this.showCountdown = false\n        return\n      }\n\n      // 更新显示文本\n      if (this.short) {\n        this.countdownText = formatCountdownShort(countdownInfo)\n      } else {\n        this.countdownText = countdownInfo.formatted\n      }\n\n      // 更新样式类\n      this.countdownClass = getCountdownClass(countdownInfo)\n\n      // 如果已过期，停止定时器\n      if (countdownInfo.isExpired) {\n        this.cleanup()\n        // 触发过期事件\n        this.$emit('expired', this.order)\n      }\n    },\n    \n    // 清理资源\n    cleanup() {\n      if (this.timerId) {\n        clearCountdownTimer(this.timerId)\n        this.timerId = null\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.countdown-container {\n  display: flex;\n  align-items: center;\n  padding: 8rpx 12rpx;\n  border-radius: 8rpx;\n  font-size: 20rpx;\n  \n  .countdown-icon {\n    margin-right: 6rpx;\n    font-size: 24rpx;\n  }\n  \n  .countdown-content {\n    display: flex;\n    flex-direction: column;\n    \n    .countdown-label {\n      font-size: 18rpx;\n      opacity: 0.8;\n      margin-bottom: 2rpx;\n    }\n    \n    .countdown-time {\n      font-size: 20rpx;\n      font-weight: bold;\n    }\n  }\n  \n  // 正常状态（绿色）\n  &.countdown-normal {\n    background-color: #f6ffed;\n    border: 1rpx solid #b7eb8f;\n    color: #52c41a;\n    \n    .countdown-icon {\n      color: #52c41a;\n    }\n  }\n  \n  // 警告状态（橙色）\n  &.countdown-warning {\n    background-color: #fff7e6;\n    border: 1rpx solid #ffd591;\n    color: #fa8c16;\n    \n    .countdown-icon {\n      color: #fa8c16;\n    }\n  }\n  \n  // 紧急状态（红色）\n  &.countdown-urgent {\n    background-color: #fff2f0;\n    border: 1rpx solid #ffccc7;\n    color: #ff4d4f;\n    \n    .countdown-icon {\n      color: #ff4d4f;\n    }\n    \n    // 紧急状态下闪烁效果\n    animation: blink 1s infinite;\n  }\n  \n  // 已过期状态（灰色）\n  &.countdown-expired {\n    background-color: #f5f5f5;\n    border: 1rpx solid #d9d9d9;\n    color: #999999;\n    \n    .countdown-icon {\n      color: #999999;\n    }\n  }\n}\n\n// 闪烁动画\n@keyframes blink {\n  0%, 50% {\n    opacity: 1;\n  }\n  51%, 100% {\n    opacity: 0.5;\n  }\n}\n\n// 简化版本样式（用于列表页面）\n.countdown-container.simple {\n  padding: 4rpx 8rpx;\n  font-size: 18rpx;\n  \n  .countdown-icon {\n    font-size: 20rpx;\n    margin-right: 4rpx;\n  }\n  \n  .countdown-content {\n    flex-direction: row;\n    align-items: center;\n    \n    .countdown-label {\n      margin-right: 4rpx;\n      margin-bottom: 0;\n    }\n    \n    .countdown-time {\n      font-size: 18rpx;\n    }\n  }\n}\n</style>\n", "import Component from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/components/CountdownTimer.vue'\nwx.createComponent(Component)"], "names": ["uni", "shouldShowCountdown", "createCountdownTimer", "getSharingOrderCountdown", "formatCountdownShort", "getCountdownClass", "clearCountdownTimer"], "mappings": ";;;AAoBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA;AAAA,IAED,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EAED,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,eAAe;AAAA,MACf,eAAe;AAAA,MACf,gBAAgB;AAAA,IAClB;AAAA,EACD;AAAA,EAED,UAAU;;AACRA,kBAAY,MAAA,MAAA,OAAA,uCAAA,gCAA+B,UAAK,UAAL,mBAAY,OAAO;AAC9D,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,gBAAgB;AACd,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,OAAO;AAAA,IACL,OAAO;AAAA,MACL,UAAU;AACR,aAAK,cAAc;AAAA,MACpB;AAAA,MACD,MAAM;AAAA,IACR;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,gBAAgB;;AACdA,oBAAY,MAAA,MAAA,OAAA,uCAAA,0BAAyB,UAAK,UAAL,mBAAY,OAAO;AACxD,WAAK,QAAQ;AAEb,UAAI,CAACC,gBAAmB,oBAAC,KAAK,KAAK,GAAG;AACpC,aAAK,gBAAgB;AACrB;AAAA,MACF;AAEA,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AAGrB,WAAK,UAAUC,gBAAAA,qBAAqB,MAAM;AACxC,aAAK,gBAAgB;AAAA,MACvB,GAAG,KAAK,QAAQ;AAAA,IACjB;AAAA;AAAA,IAGD,kBAAkB;AAChB,YAAM,gBAAgBC,gBAAAA,yBAAyB,KAAK,KAAK;AACzD,WAAK,YAAY;AAEjB,UAAI,CAAC,cAAc,eAAe;AAChC,aAAK,gBAAgB;AACrB;AAAA,MACF;AAGA,UAAI,KAAK,OAAO;AACd,aAAK,gBAAgBC,gBAAoB,qBAAC,aAAa;AAAA,aAClD;AACL,aAAK,gBAAgB,cAAc;AAAA,MACrC;AAGA,WAAK,iBAAiBC,gBAAiB,kBAAC,aAAa;AAGrD,UAAI,cAAc,WAAW;AAC3B,aAAK,QAAQ;AAEb,aAAK,MAAM,WAAW,KAAK,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,KAAK,SAAS;AAChBC,wBAAmB,oBAAC,KAAK,OAAO;AAChC,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;AChIA,GAAG,gBAAgB,SAAS;"}