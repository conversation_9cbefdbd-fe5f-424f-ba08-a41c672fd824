{"version": 3, "file": "SkeletonScreen.js", "sources": ["components/SkeletonScreen.vue", "/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/components/SkeletonScreen.vue?type=component"], "sourcesContent": ["<template>\n  <view class=\"skeleton-container\">\n    <!-- 轮播图骨架屏 -->\n    <view v-if=\"showBanner\" class=\"skeleton-banner\">\n      <view class=\"skeleton-item skeleton-banner-item\"></view>\n    </view>\n    \n    <!-- 快捷功能骨架屏 -->\n    <view v-if=\"showActions\" class=\"skeleton-actions\">\n      <view \n        v-for=\"i in 4\" \n        :key=\"i\" \n        class=\"skeleton-item skeleton-action-item\"\n      ></view>\n    </view>\n    \n    <!-- 场馆列表骨架屏 -->\n    <view v-if=\"showVenues\" class=\"skeleton-venues\">\n      <view class=\"skeleton-section-header\">\n        <view class=\"skeleton-item skeleton-title\"></view>\n        <view class=\"skeleton-item skeleton-more\"></view>\n      </view>\n      <view class=\"skeleton-venue-list\">\n        <view \n          v-for=\"i in count\" \n          :key=\"i\" \n          class=\"skeleton-venue-card\"\n        >\n          <view class=\"skeleton-item skeleton-venue-image\"></view>\n          <view class=\"skeleton-venue-info\">\n            <view class=\"skeleton-item skeleton-venue-name\"></view>\n            <view class=\"skeleton-item skeleton-venue-location\"></view>\n            <view class=\"skeleton-item skeleton-venue-price\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 拼场列表骨架屏 -->\n    <view v-if=\"showSharings\" class=\"skeleton-sharings\">\n      <view class=\"skeleton-section-header\">\n        <view class=\"skeleton-item skeleton-title\"></view>\n        <view class=\"skeleton-item skeleton-more\"></view>\n      </view>\n      <view \n        v-for=\"i in count\" \n        :key=\"i\" \n        class=\"skeleton-sharing-card\"\n      >\n        <view class=\"skeleton-item skeleton-sharing-header\"></view>\n        <view class=\"skeleton-item skeleton-sharing-info\"></view>\n        <view class=\"skeleton-item skeleton-sharing-price\"></view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'SkeletonScreen',\n  props: {\n    // 显示轮播图骨架屏\n    showBanner: {\n      type: Boolean,\n      default: false\n    },\n    // 显示快捷功能骨架屏\n    showActions: {\n      type: Boolean,\n      default: false\n    },\n    // 显示场馆列表骨架屏\n    showVenues: {\n      type: Boolean,\n      default: false\n    },\n    // 显示拼场列表骨架屏\n    showSharings: {\n      type: Boolean,\n      default: false\n    },\n    // 骨架屏数量\n    count: {\n      type: Number,\n      default: 3\n    }\n  }\n}\n</script>\n\n<style scoped>\n.skeleton-container {\n  padding: 20rpx;\n}\n\n.skeleton-item {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 8rpx;\n}\n\n@keyframes skeleton-loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n\n/* 轮播图骨架屏 */\n.skeleton-banner {\n  margin-bottom: 30rpx;\n}\n\n.skeleton-banner-item {\n  width: 100%;\n  height: 300rpx;\n  border-radius: 16rpx;\n}\n\n/* 快捷功能骨架屏 */\n.skeleton-actions {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 40rpx;\n  padding: 0 20rpx;\n}\n\n.skeleton-action-item {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 16rpx;\n}\n\n/* 场馆列表骨架屏 */\n.skeleton-venues {\n  margin-bottom: 40rpx;\n}\n\n.skeleton-section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.skeleton-title {\n  width: 160rpx;\n  height: 40rpx;\n}\n\n.skeleton-more {\n  width: 80rpx;\n  height: 30rpx;\n}\n\n.skeleton-venue-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.skeleton-venue-card {\n  display: flex;\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.skeleton-venue-image {\n  width: 160rpx;\n  height: 120rpx;\n  border-radius: 12rpx;\n  margin-right: 20rpx;\n}\n\n.skeleton-venue-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.skeleton-venue-name {\n  width: 200rpx;\n  height: 32rpx;\n}\n\n.skeleton-venue-location {\n  width: 160rpx;\n  height: 24rpx;\n}\n\n.skeleton-venue-price {\n  width: 120rpx;\n  height: 28rpx;\n}\n\n/* 拼场列表骨架屏 */\n.skeleton-sharings {\n  margin-bottom: 40rpx;\n}\n\n.skeleton-sharing-card {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-bottom: 16rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.skeleton-sharing-header {\n  width: 100%;\n  height: 32rpx;\n  margin-bottom: 16rpx;\n}\n\n.skeleton-sharing-info {\n  width: 80%;\n  height: 24rpx;\n  margin-bottom: 12rpx;\n}\n\n.skeleton-sharing-price {\n  width: 120rpx;\n  height: 28rpx;\n}\n\n/* 响应式适配 */\n@media (max-width: 750rpx) {\n  .skeleton-actions {\n    padding: 0 10rpx;\n  }\n  \n  .skeleton-action-item {\n    width: 100rpx;\n    height: 100rpx;\n  }\n  \n  .skeleton-venue-image {\n    width: 140rpx;\n    height: 100rpx;\n  }\n}\n</style>", "import Component from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/components/SkeletonScreen.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AA0DA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtFA,GAAG,gBAAgB,SAAS;"}