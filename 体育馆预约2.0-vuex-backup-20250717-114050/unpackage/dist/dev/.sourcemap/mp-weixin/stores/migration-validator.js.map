{"version": 3, "file": "migration-validator.js", "sources": ["stores/migration-validator.js"], "sourcesContent": ["import store from '@/store'\nimport { useAppStore } from './app.js'\nimport { useUserStore } from './user.js'\nimport { useVenueStore } from './venue.js'\nimport { useSharingStore } from './sharing.js'\nimport { useBookingStore } from './booking.js'\nimport { migrationConfig, logMigration } from './migration-config.js'\n\n// 状态验证器\nexport class MigrationValidator {\n  constructor() {\n    this.appStore = null\n    this.userStore = null\n    this.venueStore = null\n    this.sharingStore = null\n    this.bookingStore = null\n    this.validationResults = {}\n  }\n  \n  // 初始化stores\n  init() {\n    try {\n      this.appStore = useAppStore()\n      this.userStore = useUserStore()\n      this.venueStore = useVenueStore()\n      this.sharingStore = useSharingStore()\n      this.bookingStore = useBookingStore()\n      logMigration('迁移验证器初始化成功')\n    } catch (error) {\n      console.error('[Migration Validator] 初始化失败:', error)\n    }\n  }\n  \n  // 验证App模块状态同步\n  validateAppModule() {\n    if (!this.appStore) return { valid: false, error: 'AppStore未初始化' }\n    \n    const vuexState = store.state\n    const piniaState = this.appStore.$state\n    \n    const results = {\n      loading: vuexState.loading === piniaState.loading,\n      networkStatus: vuexState.networkStatus === piniaState.networkStatus\n    }\n    \n    const allValid = Object.values(results).every(v => v)\n    \n    logMigration('App模块验证结果', { results, allValid })\n    \n    return {\n      valid: allValid,\n      details: results,\n      vuexState: { loading: vuexState.loading, networkStatus: vuexState.networkStatus },\n      piniaState: { loading: piniaState.loading, networkStatus: piniaState.networkStatus }\n    }\n  }\n  \n  // 验证User模块状态同步\n  validateUserModule() {\n    if (!this.userStore) return { valid: false, error: 'UserStore未初始化' }\n    \n    const vuexUserState = store.state.user\n    const piniaUserState = this.userStore.$state\n    \n    if (!vuexUserState) {\n      return { valid: false, error: 'Vuex User模块未找到' }\n    }\n    \n    const results = {\n      token: vuexUserState.token === piniaUserState.token,\n      isLoggedIn: vuexUserState.isLoggedIn === piniaUserState.isLoggedIn,\n      loginChecking: vuexUserState.loginChecking === piniaUserState.loginChecking,\n      userInfo: JSON.stringify(vuexUserState.userInfo) === JSON.stringify(piniaUserState.userInfo),\n      userStats: JSON.stringify(vuexUserState.userStats) === JSON.stringify(piniaUserState.userStats)\n    }\n    \n    const allValid = Object.values(results).every(v => v)\n    \n    logMigration('User模块验证结果', { results, allValid })\n    \n    return {\n      valid: allValid,\n      details: results,\n      vuexState: {\n        token: vuexUserState.token,\n        isLoggedIn: vuexUserState.isLoggedIn,\n        loginChecking: vuexUserState.loginChecking,\n        userInfo: vuexUserState.userInfo,\n        userStats: vuexUserState.userStats\n      },\n      piniaState: {\n        token: piniaUserState.token,\n        isLoggedIn: piniaUserState.isLoggedIn,\n        loginChecking: piniaUserState.loginChecking,\n        userInfo: piniaUserState.userInfo,\n        userStats: piniaUserState.userStats\n      }\n    }\n  }\n\n  // 验证Venue模块状态同步\n  validateVenueModule() {\n    if (!this.venueStore) return { valid: false, error: 'VenueStore未初始化' }\n\n    const vuexVenueState = store.state.venue\n    const piniaVenueState = this.venueStore.$state\n\n    if (!vuexVenueState) {\n      return { valid: false, error: 'Vuex Venue模块未找到' }\n    }\n\n    const results = {\n      venueList: JSON.stringify(vuexVenueState.venueList) === JSON.stringify(piniaVenueState.venueList),\n      popularVenues: JSON.stringify(vuexVenueState.popularVenues) === JSON.stringify(piniaVenueState.popularVenues),\n      venueDetail: JSON.stringify(vuexVenueState.venueDetail) === JSON.stringify(piniaVenueState.venueDetail),\n      venueTypes: JSON.stringify(vuexVenueState.venueTypes) === JSON.stringify(piniaVenueState.venueTypes),\n      timeSlots: JSON.stringify(vuexVenueState.timeSlots) === JSON.stringify(piniaVenueState.timeSlots),\n      searchResults: JSON.stringify(vuexVenueState.searchResults) === JSON.stringify(piniaVenueState.searchResults),\n      loading: vuexVenueState.loading === piniaVenueState.loading,\n      pagination: JSON.stringify(vuexVenueState.pagination) === JSON.stringify(piniaVenueState.pagination)\n    }\n\n    const allValid = Object.values(results).every(v => v)\n\n    logMigration('Venue模块验证结果', { results, allValid })\n\n    return {\n      valid: allValid,\n      details: results,\n      vuexState: {\n        venueList: vuexVenueState.venueList,\n        popularVenues: vuexVenueState.popularVenues,\n        venueDetail: vuexVenueState.venueDetail,\n        venueTypes: vuexVenueState.venueTypes,\n        timeSlots: vuexVenueState.timeSlots,\n        searchResults: vuexVenueState.searchResults,\n        loading: vuexVenueState.loading,\n        pagination: vuexVenueState.pagination\n      },\n      piniaState: {\n        venueList: piniaVenueState.venueList,\n        popularVenues: piniaVenueState.popularVenues,\n        venueDetail: piniaVenueState.venueDetail,\n        venueTypes: piniaVenueState.venueTypes,\n        timeSlots: piniaVenueState.timeSlots,\n        searchResults: piniaVenueState.searchResults,\n        loading: piniaVenueState.loading,\n        pagination: piniaVenueState.pagination\n      }\n    }\n  }\n\n  // 验证Sharing模块状态同步\n  validateSharingModule() {\n    if (!this.sharingStore) return { valid: false, error: 'SharingStore未初始化' }\n\n    const vuexSharingState = store.state.sharing\n    const piniaSharingState = this.sharingStore.$state\n\n    if (!vuexSharingState) {\n      return { valid: false, error: 'Vuex Sharing模块未找到' }\n    }\n\n    const results = {\n      sharingOrders: JSON.stringify(vuexSharingState.sharingOrders) === JSON.stringify(piniaSharingState.sharingOrders),\n      mySharingOrders: JSON.stringify(vuexSharingState.mySharingOrders) === JSON.stringify(piniaSharingState.mySharingOrders),\n      receivedRequests: JSON.stringify(vuexSharingState.receivedRequests) === JSON.stringify(piniaSharingState.receivedRequests),\n      sentRequests: JSON.stringify(vuexSharingState.sentRequests) === JSON.stringify(piniaSharingState.sentRequests),\n      sharingOrderDetail: JSON.stringify(vuexSharingState.sharingOrderDetail) === JSON.stringify(piniaSharingState.sharingOrderDetail),\n      loading: vuexSharingState.loading === piniaSharingState.loading,\n      pagination: JSON.stringify(vuexSharingState.pagination) === JSON.stringify(piniaSharingState.pagination)\n    }\n\n    const allValid = Object.values(results).every(v => v)\n\n    logMigration('Sharing模块验证结果', { results, allValid })\n\n    return {\n      valid: allValid,\n      details: results,\n      vuexState: {\n        sharingOrders: vuexSharingState.sharingOrders,\n        mySharingOrders: vuexSharingState.mySharingOrders,\n        receivedRequests: vuexSharingState.receivedRequests,\n        sentRequests: vuexSharingState.sentRequests,\n        sharingOrderDetail: vuexSharingState.sharingOrderDetail,\n        loading: vuexSharingState.loading,\n        pagination: vuexSharingState.pagination\n      },\n      piniaState: {\n        sharingOrders: piniaSharingState.sharingOrders,\n        mySharingOrders: piniaSharingState.mySharingOrders,\n        receivedRequests: piniaSharingState.receivedRequests,\n        sentRequests: piniaSharingState.sentRequests,\n        sharingOrderDetail: piniaSharingState.sharingOrderDetail,\n        loading: piniaSharingState.loading,\n        pagination: piniaSharingState.pagination\n      }\n    }\n  }\n\n  // 验证Booking模块状态同步\n  validateBookingModule() {\n    if (!this.bookingStore) return { valid: false, error: 'BookingStore未初始化' }\n\n    const vuexBookingState = store.state.booking\n    const piniaBookingState = this.bookingStore.$state\n\n    if (!vuexBookingState) {\n      return { valid: false, error: 'Vuex Booking模块未找到' }\n    }\n\n    const results = {\n      bookingList: JSON.stringify(vuexBookingState.bookingList) === JSON.stringify(piniaBookingState.bookingList),\n      bookingDetail: JSON.stringify(vuexBookingState.bookingDetail) === JSON.stringify(piniaBookingState.bookingDetail),\n      sharingOrders: JSON.stringify(vuexBookingState.sharingOrders) === JSON.stringify(piniaBookingState.sharingOrders),\n      userSharingOrders: JSON.stringify(vuexBookingState.userSharingOrders) === JSON.stringify(piniaBookingState.userSharingOrders),\n      joinedSharingOrders: JSON.stringify(vuexBookingState.joinedSharingOrders) === JSON.stringify(piniaBookingState.joinedSharingOrders),\n      sharingDetail: JSON.stringify(vuexBookingState.sharingDetail) === JSON.stringify(piniaBookingState.sharingDetail),\n      loading: vuexBookingState.loading === piniaBookingState.loading,\n      pagination: JSON.stringify(vuexBookingState.pagination) === JSON.stringify(piniaBookingState.pagination)\n    }\n\n    const allValid = Object.values(results).every(v => v)\n\n    logMigration('Booking模块验证结果', { results, allValid })\n\n    return {\n      valid: allValid,\n      details: results,\n      vuexState: {\n        bookingList: vuexBookingState.bookingList,\n        bookingDetail: vuexBookingState.bookingDetail,\n        sharingOrders: vuexBookingState.sharingOrders,\n        userSharingOrders: vuexBookingState.userSharingOrders,\n        joinedSharingOrders: vuexBookingState.joinedSharingOrders,\n        sharingDetail: vuexBookingState.sharingDetail,\n        loading: vuexBookingState.loading,\n        pagination: vuexBookingState.pagination\n      },\n      piniaState: {\n        bookingList: piniaBookingState.bookingList,\n        bookingDetail: piniaBookingState.bookingDetail,\n        sharingOrders: piniaBookingState.sharingOrders,\n        userSharingOrders: piniaBookingState.userSharingOrders,\n        joinedSharingOrders: piniaBookingState.joinedSharingOrders,\n        sharingDetail: piniaBookingState.sharingDetail,\n        loading: piniaBookingState.loading,\n        pagination: piniaBookingState.pagination\n      }\n    }\n  }\n\n  // 验证所有已激活模块\n  validateAllModules() {\n    const results = {}\n    \n    if (migrationConfig.activeModules.includes('app')) {\n      results.app = this.validateAppModule()\n    }\n    \n    if (migrationConfig.activeModules.includes('user')) {\n      results.user = this.validateUserModule()\n    }\n\n    if (migrationConfig.activeModules.includes('venue')) {\n      results.venue = this.validateVenueModule()\n    }\n\n    if (migrationConfig.activeModules.includes('sharing')) {\n      results.sharing = this.validateSharingModule()\n    }\n\n    if (migrationConfig.activeModules.includes('booking')) {\n      results.booking = this.validateBookingModule()\n    }\n\n    const overallValid = Object.values(results).every(r => r.valid)\n    \n    this.validationResults = {\n      timestamp: new Date().toISOString(),\n      overall: overallValid,\n      modules: results\n    }\n    \n    logMigration('全模块验证完成', this.validationResults)\n    \n    return this.validationResults\n  }\n  \n  // 获取验证报告\n  getValidationReport() {\n    return this.validationResults\n  }\n  \n  // 持续验证 - 定期检查状态同步\n  startContinuousValidation(interval = 5000) {\n    logMigration('开始持续验证', { interval })\n    \n    return setInterval(() => {\n      const results = this.validateAllModules()\n      \n      if (!results.overall) {\n        console.warn('[Migration Validator] 发现状态不同步:', results)\n        \n        // 可以在这里添加自动修复逻辑\n        this.attemptAutoFix(results)\n      }\n    }, interval)\n  }\n  \n  // 尝试自动修复状态不同步\n  attemptAutoFix(validationResults) {\n    logMigration('尝试自动修复状态不同步')\n    \n    // App模块修复\n    if (validationResults.modules.app && !validationResults.modules.app.valid) {\n      const vuexState = store.state\n      if (this.appStore) {\n        this.appStore.loading = vuexState.loading\n        this.appStore.networkStatus = vuexState.networkStatus\n        logMigration('App模块状态已修复')\n      }\n    }\n    \n    // User模块修复\n    if (validationResults.modules.user && !validationResults.modules.user.valid) {\n      const vuexUserState = store.state.user\n      if (this.userStore && vuexUserState) {\n        this.userStore.token = vuexUserState.token\n        this.userStore.userInfo = vuexUserState.userInfo\n        this.userStore.isLoggedIn = vuexUserState.isLoggedIn\n        this.userStore.loginChecking = vuexUserState.loginChecking\n        this.userStore.userStats = vuexUserState.userStats\n        logMigration('User模块状态已修复')\n      }\n    }\n\n    // Venue模块修复\n    if (validationResults.modules.venue && !validationResults.modules.venue.valid) {\n      const vuexVenueState = store.state.venue\n      if (this.venueStore && vuexVenueState) {\n        this.venueStore.venueList = vuexVenueState.venueList || []\n        this.venueStore.popularVenues = vuexVenueState.popularVenues || []\n        this.venueStore.venueDetail = vuexVenueState.venueDetail\n        this.venueStore.venueTypes = vuexVenueState.venueTypes || []\n        this.venueStore.timeSlots = vuexVenueState.timeSlots || []\n        this.venueStore.searchResults = vuexVenueState.searchResults || []\n        this.venueStore.loading = vuexVenueState.loading || false\n        this.venueStore.pagination = vuexVenueState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1 }\n        logMigration('Venue模块状态已修复')\n      }\n    }\n\n    // Sharing模块修复\n    if (validationResults.modules.sharing && !validationResults.modules.sharing.valid) {\n      const vuexSharingState = store.state.sharing\n      if (this.sharingStore && vuexSharingState) {\n        this.sharingStore.sharingOrders = vuexSharingState.sharingOrders || []\n        this.sharingStore.mySharingOrders = vuexSharingState.mySharingOrders || []\n        this.sharingStore.receivedRequests = vuexSharingState.receivedRequests || []\n        this.sharingStore.sentRequests = vuexSharingState.sentRequests || []\n        this.sharingStore.sharingOrderDetail = vuexSharingState.sharingOrderDetail\n        this.sharingStore.loading = vuexSharingState.loading || false\n        this.sharingStore.pagination = vuexSharingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1 }\n        logMigration('Sharing模块状态已修复')\n      }\n    }\n\n    // Booking模块修复\n    if (validationResults.modules.booking && !validationResults.modules.booking.valid) {\n      const vuexBookingState = store.state.booking\n      if (this.bookingStore && vuexBookingState) {\n        this.bookingStore.bookingList = vuexBookingState.bookingList || []\n        this.bookingStore.bookingDetail = vuexBookingState.bookingDetail\n        this.bookingStore.sharingOrders = vuexBookingState.sharingOrders || []\n        this.bookingStore.userSharingOrders = vuexBookingState.userSharingOrders || []\n        this.bookingStore.joinedSharingOrders = vuexBookingState.joinedSharingOrders || []\n        this.bookingStore.sharingDetail = vuexBookingState.sharingDetail\n        this.bookingStore.loading = vuexBookingState.loading || false\n        this.bookingStore.pagination = vuexBookingState.pagination || { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 }\n        logMigration('Booking模块状态已修复')\n      }\n    }\n  }\n  \n  // 停止持续验证\n  stopContinuousValidation(intervalId) {\n    if (intervalId) {\n      clearInterval(intervalId)\n      logMigration('持续验证已停止')\n    }\n  }\n}\n\n// 创建全局验证器实例\nexport const migrationValidator = new MigrationValidator()\n\n// 便捷方法\nexport function validateMigration() {\n  migrationValidator.init()\n  return migrationValidator.validateAllModules()\n}\n\nexport function startValidation(interval) {\n  migrationValidator.init()\n  return migrationValidator.startContinuousValidation(interval)\n}\n\nexport function stopValidation(intervalId) {\n  migrationValidator.stopContinuousValidation(intervalId)\n}\n"], "names": ["useAppStore", "useUserStore", "useVenueStore", "useSharingStore", "useBookingStore", "logMigration", "uni", "store", "migrationConfig"], "mappings": ";;;;;;;;;AASO,MAAM,mBAAmB;AAAA,EAC9B,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,oBAAoB,CAAE;AAAA,EAC5B;AAAA;AAAA,EAGD,OAAO;AACL,QAAI;AACF,WAAK,WAAWA,uBAAa;AAC7B,WAAK,YAAYC,yBAAc;AAC/B,WAAK,aAAaC,2BAAe;AACjC,WAAK,eAAeC,+BAAiB;AACrC,WAAK,eAAeC,+BAAiB;AACrCC,6BAAAA,aAAa,YAAY;AAAA,IAC1B,SAAQ,OAAO;AACdC,oBAAAA,MAAA,MAAA,SAAA,uCAAc,gCAAgC,KAAK;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAGD,oBAAoB;AAClB,QAAI,CAAC,KAAK;AAAU,aAAO,EAAE,OAAO,OAAO,OAAO,eAAgB;AAElE,UAAM,YAAYC,YAAAA,MAAM;AACxB,UAAM,aAAa,KAAK,SAAS;AAEjC,UAAM,UAAU;AAAA,MACd,SAAS,UAAU,YAAY,WAAW;AAAA,MAC1C,eAAe,UAAU,kBAAkB,WAAW;AAAA,IACvD;AAED,UAAM,WAAW,OAAO,OAAO,OAAO,EAAE,MAAM,OAAK,CAAC;AAEpDF,2BAAAA,aAAa,aAAa,EAAE,SAAS,SAAQ,CAAE;AAE/C,WAAO;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW,EAAE,SAAS,UAAU,SAAS,eAAe,UAAU,cAAe;AAAA,MACjF,YAAY,EAAE,SAAS,WAAW,SAAS,eAAe,WAAW,cAAe;AAAA,IACrF;AAAA,EACF;AAAA;AAAA,EAGD,qBAAqB;AACnB,QAAI,CAAC,KAAK;AAAW,aAAO,EAAE,OAAO,OAAO,OAAO,gBAAiB;AAEpE,UAAM,gBAAgBE,kBAAM,MAAM;AAClC,UAAM,iBAAiB,KAAK,UAAU;AAEtC,QAAI,CAAC,eAAe;AAClB,aAAO,EAAE,OAAO,OAAO,OAAO,iBAAkB;AAAA,IACjD;AAED,UAAM,UAAU;AAAA,MACd,OAAO,cAAc,UAAU,eAAe;AAAA,MAC9C,YAAY,cAAc,eAAe,eAAe;AAAA,MACxD,eAAe,cAAc,kBAAkB,eAAe;AAAA,MAC9D,UAAU,KAAK,UAAU,cAAc,QAAQ,MAAM,KAAK,UAAU,eAAe,QAAQ;AAAA,MAC3F,WAAW,KAAK,UAAU,cAAc,SAAS,MAAM,KAAK,UAAU,eAAe,SAAS;AAAA,IAC/F;AAED,UAAM,WAAW,OAAO,OAAO,OAAO,EAAE,MAAM,OAAK,CAAC;AAEpDF,2BAAAA,aAAa,cAAc,EAAE,SAAS,SAAQ,CAAE;AAEhD,WAAO;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,QACT,OAAO,cAAc;AAAA,QACrB,YAAY,cAAc;AAAA,QAC1B,eAAe,cAAc;AAAA,QAC7B,UAAU,cAAc;AAAA,QACxB,WAAW,cAAc;AAAA,MAC1B;AAAA,MACD,YAAY;AAAA,QACV,OAAO,eAAe;AAAA,QACtB,YAAY,eAAe;AAAA,QAC3B,eAAe,eAAe;AAAA,QAC9B,UAAU,eAAe;AAAA,QACzB,WAAW,eAAe;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,sBAAsB;AACpB,QAAI,CAAC,KAAK;AAAY,aAAO,EAAE,OAAO,OAAO,OAAO,iBAAkB;AAEtE,UAAM,iBAAiBE,kBAAM,MAAM;AACnC,UAAM,kBAAkB,KAAK,WAAW;AAExC,QAAI,CAAC,gBAAgB;AACnB,aAAO,EAAE,OAAO,OAAO,OAAO,kBAAmB;AAAA,IAClD;AAED,UAAM,UAAU;AAAA,MACd,WAAW,KAAK,UAAU,eAAe,SAAS,MAAM,KAAK,UAAU,gBAAgB,SAAS;AAAA,MAChG,eAAe,KAAK,UAAU,eAAe,aAAa,MAAM,KAAK,UAAU,gBAAgB,aAAa;AAAA,MAC5G,aAAa,KAAK,UAAU,eAAe,WAAW,MAAM,KAAK,UAAU,gBAAgB,WAAW;AAAA,MACtG,YAAY,KAAK,UAAU,eAAe,UAAU,MAAM,KAAK,UAAU,gBAAgB,UAAU;AAAA,MACnG,WAAW,KAAK,UAAU,eAAe,SAAS,MAAM,KAAK,UAAU,gBAAgB,SAAS;AAAA,MAChG,eAAe,KAAK,UAAU,eAAe,aAAa,MAAM,KAAK,UAAU,gBAAgB,aAAa;AAAA,MAC5G,SAAS,eAAe,YAAY,gBAAgB;AAAA,MACpD,YAAY,KAAK,UAAU,eAAe,UAAU,MAAM,KAAK,UAAU,gBAAgB,UAAU;AAAA,IACpG;AAED,UAAM,WAAW,OAAO,OAAO,OAAO,EAAE,MAAM,OAAK,CAAC;AAEpDF,2BAAAA,aAAa,eAAe,EAAE,SAAS,SAAQ,CAAE;AAEjD,WAAO;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,QACT,WAAW,eAAe;AAAA,QAC1B,eAAe,eAAe;AAAA,QAC9B,aAAa,eAAe;AAAA,QAC5B,YAAY,eAAe;AAAA,QAC3B,WAAW,eAAe;AAAA,QAC1B,eAAe,eAAe;AAAA,QAC9B,SAAS,eAAe;AAAA,QACxB,YAAY,eAAe;AAAA,MAC5B;AAAA,MACD,YAAY;AAAA,QACV,WAAW,gBAAgB;AAAA,QAC3B,eAAe,gBAAgB;AAAA,QAC/B,aAAa,gBAAgB;AAAA,QAC7B,YAAY,gBAAgB;AAAA,QAC5B,WAAW,gBAAgB;AAAA,QAC3B,eAAe,gBAAgB;AAAA,QAC/B,SAAS,gBAAgB;AAAA,QACzB,YAAY,gBAAgB;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,wBAAwB;AACtB,QAAI,CAAC,KAAK;AAAc,aAAO,EAAE,OAAO,OAAO,OAAO,mBAAoB;AAE1E,UAAM,mBAAmBE,kBAAM,MAAM;AACrC,UAAM,oBAAoB,KAAK,aAAa;AAE5C,QAAI,CAAC,kBAAkB;AACrB,aAAO,EAAE,OAAO,OAAO,OAAO,oBAAqB;AAAA,IACpD;AAED,UAAM,UAAU;AAAA,MACd,eAAe,KAAK,UAAU,iBAAiB,aAAa,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAAA,MAChH,iBAAiB,KAAK,UAAU,iBAAiB,eAAe,MAAM,KAAK,UAAU,kBAAkB,eAAe;AAAA,MACtH,kBAAkB,KAAK,UAAU,iBAAiB,gBAAgB,MAAM,KAAK,UAAU,kBAAkB,gBAAgB;AAAA,MACzH,cAAc,KAAK,UAAU,iBAAiB,YAAY,MAAM,KAAK,UAAU,kBAAkB,YAAY;AAAA,MAC7G,oBAAoB,KAAK,UAAU,iBAAiB,kBAAkB,MAAM,KAAK,UAAU,kBAAkB,kBAAkB;AAAA,MAC/H,SAAS,iBAAiB,YAAY,kBAAkB;AAAA,MACxD,YAAY,KAAK,UAAU,iBAAiB,UAAU,MAAM,KAAK,UAAU,kBAAkB,UAAU;AAAA,IACxG;AAED,UAAM,WAAW,OAAO,OAAO,OAAO,EAAE,MAAM,OAAK,CAAC;AAEpDF,2BAAAA,aAAa,iBAAiB,EAAE,SAAS,SAAQ,CAAE;AAEnD,WAAO;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,QACT,eAAe,iBAAiB;AAAA,QAChC,iBAAiB,iBAAiB;AAAA,QAClC,kBAAkB,iBAAiB;AAAA,QACnC,cAAc,iBAAiB;AAAA,QAC/B,oBAAoB,iBAAiB;AAAA,QACrC,SAAS,iBAAiB;AAAA,QAC1B,YAAY,iBAAiB;AAAA,MAC9B;AAAA,MACD,YAAY;AAAA,QACV,eAAe,kBAAkB;AAAA,QACjC,iBAAiB,kBAAkB;AAAA,QACnC,kBAAkB,kBAAkB;AAAA,QACpC,cAAc,kBAAkB;AAAA,QAChC,oBAAoB,kBAAkB;AAAA,QACtC,SAAS,kBAAkB;AAAA,QAC3B,YAAY,kBAAkB;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,wBAAwB;AACtB,QAAI,CAAC,KAAK;AAAc,aAAO,EAAE,OAAO,OAAO,OAAO,mBAAoB;AAE1E,UAAM,mBAAmBE,kBAAM,MAAM;AACrC,UAAM,oBAAoB,KAAK,aAAa;AAE5C,QAAI,CAAC,kBAAkB;AACrB,aAAO,EAAE,OAAO,OAAO,OAAO,oBAAqB;AAAA,IACpD;AAED,UAAM,UAAU;AAAA,MACd,aAAa,KAAK,UAAU,iBAAiB,WAAW,MAAM,KAAK,UAAU,kBAAkB,WAAW;AAAA,MAC1G,eAAe,KAAK,UAAU,iBAAiB,aAAa,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAAA,MAChH,eAAe,KAAK,UAAU,iBAAiB,aAAa,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAAA,MAChH,mBAAmB,KAAK,UAAU,iBAAiB,iBAAiB,MAAM,KAAK,UAAU,kBAAkB,iBAAiB;AAAA,MAC5H,qBAAqB,KAAK,UAAU,iBAAiB,mBAAmB,MAAM,KAAK,UAAU,kBAAkB,mBAAmB;AAAA,MAClI,eAAe,KAAK,UAAU,iBAAiB,aAAa,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAAA,MAChH,SAAS,iBAAiB,YAAY,kBAAkB;AAAA,MACxD,YAAY,KAAK,UAAU,iBAAiB,UAAU,MAAM,KAAK,UAAU,kBAAkB,UAAU;AAAA,IACxG;AAED,UAAM,WAAW,OAAO,OAAO,OAAO,EAAE,MAAM,OAAK,CAAC;AAEpDF,2BAAAA,aAAa,iBAAiB,EAAE,SAAS,SAAQ,CAAE;AAEnD,WAAO;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,QACT,aAAa,iBAAiB;AAAA,QAC9B,eAAe,iBAAiB;AAAA,QAChC,eAAe,iBAAiB;AAAA,QAChC,mBAAmB,iBAAiB;AAAA,QACpC,qBAAqB,iBAAiB;AAAA,QACtC,eAAe,iBAAiB;AAAA,QAChC,SAAS,iBAAiB;AAAA,QAC1B,YAAY,iBAAiB;AAAA,MAC9B;AAAA,MACD,YAAY;AAAA,QACV,aAAa,kBAAkB;AAAA,QAC/B,eAAe,kBAAkB;AAAA,QACjC,eAAe,kBAAkB;AAAA,QACjC,mBAAmB,kBAAkB;AAAA,QACrC,qBAAqB,kBAAkB;AAAA,QACvC,eAAe,kBAAkB;AAAA,QACjC,SAAS,kBAAkB;AAAA,QAC3B,YAAY,kBAAkB;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,qBAAqB;AACnB,UAAM,UAAU,CAAE;AAElB,QAAIG,uCAAgB,cAAc,SAAS,KAAK,GAAG;AACjD,cAAQ,MAAM,KAAK,kBAAmB;AAAA,IACvC;AAED,QAAIA,uCAAgB,cAAc,SAAS,MAAM,GAAG;AAClD,cAAQ,OAAO,KAAK,mBAAoB;AAAA,IACzC;AAED,QAAIA,uCAAgB,cAAc,SAAS,OAAO,GAAG;AACnD,cAAQ,QAAQ,KAAK,oBAAqB;AAAA,IAC3C;AAED,QAAIA,uCAAgB,cAAc,SAAS,SAAS,GAAG;AACrD,cAAQ,UAAU,KAAK,sBAAuB;AAAA,IAC/C;AAED,QAAIA,uCAAgB,cAAc,SAAS,SAAS,GAAG;AACrD,cAAQ,UAAU,KAAK,sBAAuB;AAAA,IAC/C;AAED,UAAM,eAAe,OAAO,OAAO,OAAO,EAAE,MAAM,OAAK,EAAE,KAAK;AAE9D,SAAK,oBAAoB;AAAA,MACvB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,MACnC,SAAS;AAAA,MACT,SAAS;AAAA,IACV;AAEDH,wCAAa,WAAW,KAAK,iBAAiB;AAE9C,WAAO,KAAK;AAAA,EACb;AAAA;AAAA,EAGD,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACb;AAAA;AAAA,EAGD,0BAA0B,WAAW,KAAM;AACzCA,wCAAa,UAAU,EAAE,UAAU;AAEnC,WAAO,YAAY,MAAM;AACvB,YAAM,UAAU,KAAK,mBAAoB;AAEzC,UAAI,CAAC,QAAQ,SAAS;AACpBC,sBAAAA,MAAa,MAAA,QAAA,wCAAA,kCAAkC,OAAO;AAGtD,aAAK,eAAe,OAAO;AAAA,MAC5B;AAAA,IACF,GAAE,QAAQ;AAAA,EACZ;AAAA;AAAA,EAGD,eAAe,mBAAmB;AAChCD,2BAAAA,aAAa,aAAa;AAG1B,QAAI,kBAAkB,QAAQ,OAAO,CAAC,kBAAkB,QAAQ,IAAI,OAAO;AACzE,YAAM,YAAYE,YAAAA,MAAM;AACxB,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,UAAU,UAAU;AAClC,aAAK,SAAS,gBAAgB,UAAU;AACxCF,+BAAAA,aAAa,YAAY;AAAA,MAC1B;AAAA,IACF;AAGD,QAAI,kBAAkB,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,KAAK,OAAO;AAC3E,YAAM,gBAAgBE,kBAAM,MAAM;AAClC,UAAI,KAAK,aAAa,eAAe;AACnC,aAAK,UAAU,QAAQ,cAAc;AACrC,aAAK,UAAU,WAAW,cAAc;AACxC,aAAK,UAAU,aAAa,cAAc;AAC1C,aAAK,UAAU,gBAAgB,cAAc;AAC7C,aAAK,UAAU,YAAY,cAAc;AACzCF,+BAAAA,aAAa,aAAa;AAAA,MAC3B;AAAA,IACF;AAGD,QAAI,kBAAkB,QAAQ,SAAS,CAAC,kBAAkB,QAAQ,MAAM,OAAO;AAC7E,YAAM,iBAAiBE,kBAAM,MAAM;AACnC,UAAI,KAAK,cAAc,gBAAgB;AACrC,aAAK,WAAW,YAAY,eAAe,aAAa,CAAE;AAC1D,aAAK,WAAW,gBAAgB,eAAe,iBAAiB,CAAE;AAClE,aAAK,WAAW,cAAc,eAAe;AAC7C,aAAK,WAAW,aAAa,eAAe,cAAc,CAAE;AAC5D,aAAK,WAAW,YAAY,eAAe,aAAa,CAAE;AAC1D,aAAK,WAAW,gBAAgB,eAAe,iBAAiB,CAAE;AAClE,aAAK,WAAW,UAAU,eAAe,WAAW;AACpD,aAAK,WAAW,aAAa,eAAe,cAAc,EAAE,SAAS,GAAG,UAAU,IAAI,OAAO,GAAG,YAAY,EAAG;AAC/GF,+BAAAA,aAAa,cAAc;AAAA,MAC5B;AAAA,IACF;AAGD,QAAI,kBAAkB,QAAQ,WAAW,CAAC,kBAAkB,QAAQ,QAAQ,OAAO;AACjF,YAAM,mBAAmBE,kBAAM,MAAM;AACrC,UAAI,KAAK,gBAAgB,kBAAkB;AACzC,aAAK,aAAa,gBAAgB,iBAAiB,iBAAiB,CAAE;AACtE,aAAK,aAAa,kBAAkB,iBAAiB,mBAAmB,CAAE;AAC1E,aAAK,aAAa,mBAAmB,iBAAiB,oBAAoB,CAAE;AAC5E,aAAK,aAAa,eAAe,iBAAiB,gBAAgB,CAAE;AACpE,aAAK,aAAa,qBAAqB,iBAAiB;AACxD,aAAK,aAAa,UAAU,iBAAiB,WAAW;AACxD,aAAK,aAAa,aAAa,iBAAiB,cAAc,EAAE,SAAS,GAAG,UAAU,IAAI,OAAO,GAAG,YAAY,EAAG;AACnHF,+BAAAA,aAAa,gBAAgB;AAAA,MAC9B;AAAA,IACF;AAGD,QAAI,kBAAkB,QAAQ,WAAW,CAAC,kBAAkB,QAAQ,QAAQ,OAAO;AACjF,YAAM,mBAAmBE,kBAAM,MAAM;AACrC,UAAI,KAAK,gBAAgB,kBAAkB;AACzC,aAAK,aAAa,cAAc,iBAAiB,eAAe,CAAE;AAClE,aAAK,aAAa,gBAAgB,iBAAiB;AACnD,aAAK,aAAa,gBAAgB,iBAAiB,iBAAiB,CAAE;AACtE,aAAK,aAAa,oBAAoB,iBAAiB,qBAAqB,CAAE;AAC9E,aAAK,aAAa,sBAAsB,iBAAiB,uBAAuB,CAAE;AAClF,aAAK,aAAa,gBAAgB,iBAAiB;AACnD,aAAK,aAAa,UAAU,iBAAiB,WAAW;AACxD,aAAK,aAAa,aAAa,iBAAiB,cAAc,EAAE,SAAS,GAAG,UAAU,IAAI,OAAO,GAAG,YAAY,GAAG,aAAa,EAAG;AACnIF,+BAAAA,aAAa,gBAAgB;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,yBAAyB,YAAY;AACnC,QAAI,YAAY;AACd,oBAAc,UAAU;AACxBA,6BAAAA,aAAa,SAAS;AAAA,IACvB;AAAA,EACF;AACH;AAGO,MAAM,qBAAqB,IAAI,mBAAoB;AAGnD,SAAS,oBAAoB;AAClC,qBAAmB,KAAM;AACzB,SAAO,mBAAmB,mBAAoB;AAChD;AAEO,SAAS,gBAAgB,UAAU;AACxC,qBAAmB,KAAM;AACzB,SAAO,mBAAmB,0BAA0B,QAAQ;AAC9D;AAEO,SAAS,eAAe,YAAY;AACzC,qBAAmB,yBAAyB,UAAU;AACxD;;;;"}