{"version": 3, "file": "app.js", "sources": ["stores/app.js"], "sourcesContent": ["import { defineStore } from 'pinia'\n\n// 迁移全局状态到Pinia - 这是最独立的模块，先迁移\nexport const useAppStore = defineStore('app', {\n  state: () => ({\n    loading: false,\n    networkStatus: true\n  }),\n  \n  actions: {\n    setLoading(loading) {\n      this.loading = loading\n    },\n    \n    setNetworkStatus(status) {\n      this.networkStatus = status\n    }\n  },\n  \n  getters: {\n    isLoading: state => state.loading,\n    isOnline: state => state.networkStatus\n  }\n})\n"], "names": ["defineStore"], "mappings": ";;AAGY,MAAC,cAAcA,cAAW,YAAC,OAAO;AAAA,EAC5C,OAAO,OAAO;AAAA,IACZ,SAAS;AAAA,IACT,eAAe;AAAA,EACnB;AAAA,EAEE,SAAS;AAAA,IACP,WAAW,SAAS;AAClB,WAAK,UAAU;AAAA,IAChB;AAAA,IAED,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB;AAAA,IACtB;AAAA,EACF;AAAA,EAED,SAAS;AAAA,IACP,WAAW,WAAS,MAAM;AAAA,IAC1B,UAAU,WAAS,MAAM;AAAA,EAC1B;AACH,CAAC;;"}