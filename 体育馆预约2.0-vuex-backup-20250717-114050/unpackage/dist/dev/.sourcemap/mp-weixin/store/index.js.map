{"version": 3, "file": "index.js", "sources": ["store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\nimport { createPinia } from 'pinia'\nimport user from './modules/user.js'\nimport venue from './modules/venue.js'\nimport booking from './modules/booking.js'\nimport sharing from './modules/sharing.js'\n\n// 创建pinia实例\nexport const pinia = createPinia()\n\n// 创建vuex store\nconst store = createStore({\n  modules: {\n    user,\n    venue,\n    booking,\n    sharing\n  },\n\n  state: {\n    // 全局状态\n    loading: false,\n    networkStatus: true\n  },\n\n  mutations: {\n    SET_LOADING(state, loading) {\n      state.loading = loading\n    },\n\n    SET_NETWORK_STATUS(state, status) {\n      state.networkStatus = status\n    }\n  },\n\n  actions: {\n    setLoading({ commit }, loading) {\n      commit('SET_LOADING', loading)\n    },\n\n    setNetworkStatus({ commit }, status) {\n      commit('SET_NETWORK_STATUS', status)\n    }\n  },\n\n  getters: {\n    isLoading: state => state.loading,\n    isOnline: state => state.networkStatus\n  }\n})\n\nexport default store\n"], "names": ["createPinia", "createStore", "user", "venue", "booking", "sharing"], "mappings": ";;;;;;AAQY,MAAC,QAAQA,cAAAA,YAAa;AAG7B,MAAC,QAAQC,cAAAA,YAAY;AAAA,EACxB,SAAS;AAAA,IACX,MAAIC,mBAAI;AAAA,IACR,OAAIC,oBAAK;AAAA,IACT,SAAIC,sBAAO;AAAA,IACX,SAAIC,sBAAO;AAAA,EACR;AAAA,EAED,OAAO;AAAA;AAAA,IAEL,SAAS;AAAA,IACT,eAAe;AAAA,EAChB;AAAA,EAED,WAAW;AAAA,IACT,YAAY,OAAO,SAAS;AAC1B,YAAM,UAAU;AAAA,IACjB;AAAA,IAED,mBAAmB,OAAO,QAAQ;AAChC,YAAM,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EAED,SAAS;AAAA,IACP,WAAW,EAAE,OAAQ,GAAE,SAAS;AAC9B,aAAO,eAAe,OAAO;AAAA,IAC9B;AAAA,IAED,iBAAiB,EAAE,OAAQ,GAAE,QAAQ;AACnC,aAAO,sBAAsB,MAAM;AAAA,IACpC;AAAA,EACF;AAAA,EAED,SAAS;AAAA,IACP,WAAW,WAAS,MAAM;AAAA,IAC1B,UAAU,WAAS,MAAM;AAAA,EAC1B;AACH,CAAC;;;"}