{"version": 3, "file": "venue.js", "sources": ["api/venue.js"], "sourcesContent": ["import { get } from '@/utils/request.js'\n\n// 获取场馆列表\nexport function getVenueList(params) {\n  return get('/venues', params)\n}\n\n// 获取场馆详情\nexport function getVenueDetail(id) {\n  return get(`/venues/${id}`)\n}\n\n// 获取场馆时间段\nexport function getVenueTimeSlots(venueId, date, params = {}) {\n  return get(`/timeslots/venue/${venueId}/date/${date}`, params)\n}\n\n// 获取场馆类型列表\nexport function getVenueTypes() {\n  return get('/venues/types')\n}\n\n// 获取热门场馆\nexport function getPopularVenues(limit = 5) {\n  return get('/venues/popular', { limit })\n}\n\n// 搜索场馆\nexport function searchVenues(params) {\n  return get('/venues/search', params)\n}\n\n// 获取支持拼场的场馆\nexport function getSharingVenues(params) {\n  return get('/venues/sharing', params)\n}"], "names": ["get"], "mappings": ";;AAGO,SAAS,aAAa,QAAQ;AACnC,SAAOA,cAAG,IAAC,WAAW,MAAM;AAC9B;AAGO,SAAS,eAAe,IAAI;AACjC,SAAOA,kBAAI,WAAW,EAAE,EAAE;AAC5B;AAQO,SAAS,gBAAgB;AAC9B,SAAOA,cAAAA,IAAI,eAAe;AAC5B;AAGO,SAAS,iBAAiB,QAAQ,GAAG;AAC1C,SAAOA,kBAAI,mBAAmB,EAAE,OAAO;AACzC;AAGO,SAAS,aAAa,QAAQ;AACnC,SAAOA,cAAG,IAAC,kBAAkB,MAAM;AACrC;;;;;;"}