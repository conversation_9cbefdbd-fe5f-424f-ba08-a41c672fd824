{"version": 3, "file": "order.js", "sources": ["api/order.js"], "sourcesContent": ["import { get, post, put } from '@/utils/request.js'\n\n// 提交订单\nexport function createOrder(data) {\n  return post('/order', data)\n}\n\n// 获取订单详情\nexport function getOrderDetail(id) {\n  return get(`/order/${id}`)\n}\n\n// 获取用户订单列表\nexport function getUserOrders(params) {\n  return get('/order', params)\n}\n\n// 取消订单\nexport function cancelOrder(id) {\n  return put(`/order/${id}/cancel`)\n}\n\n// 获取所有订单列表（仅限管理员）\nexport function getAllOrders(params) {\n  return get('/order/all', params)\n}"], "names": ["get"], "mappings": ";;AAQO,SAAS,eAAe,IAAI;AACjC,SAAOA,kBAAI,UAAU,EAAE,EAAE;AAC3B;;"}