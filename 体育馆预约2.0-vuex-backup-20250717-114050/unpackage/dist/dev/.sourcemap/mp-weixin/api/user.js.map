{"version": 3, "file": "user.js", "sources": ["api/user.js"], "sourcesContent": ["import { get, post, put, upload } from '@/utils/request.js'\n\n// 获取当前用户信息\nexport function getUserInfo() {\n  return get('/users/me')\n}\n\n// 更新用户信息\nexport function updateUserInfo(data) {\n  return put('/users/me', data)\n}\n\n// 修改密码\nexport function changePassword(data) {\n  return put('/users/me/password', data)\n}\n\n// 上传头像\nexport function uploadAvatar(filePath) {\n  return upload('/users/me/avatar', filePath)\n}\n\n// 获取用户预约记录\nexport function getUserBookings(params) {\n  return get('/users/me/bookings', params)\n}\n\n// 获取用户订单记录\nexport function getUserOrders(params) {\n  return get('/users/me/orders', params)\n}\n\n// 获取用户统计信息\nexport function getUserStats() {\n  return get('/users/me/stats')\n}"], "names": ["get", "put"], "mappings": ";;AAGO,SAAS,cAAc;AAC5B,SAAOA,cAAAA,IAAI,WAAW;AACxB;AAQO,SAAS,eAAe,MAAM;AACnC,SAAOC,cAAG,IAAC,sBAAsB,IAAI;AACvC;AAQO,SAAS,gBAAgB,QAAQ;AACtC,SAAOD,cAAG,IAAC,sBAAsB,MAAM;AACzC;AAQO,SAAS,eAAe;AAC7B,SAAOA,cAAAA,IAAI,iBAAiB;AAC9B;;;;;"}