{"version": 3, "file": "auth.js", "sources": ["api/auth.js"], "sourcesContent": ["import { post } from '@/utils/request.js'\n\n// 用户登录\nexport function login(data) {\n  return post('/auth/signin', data)\n}\n\n// 短信验证码登录\nexport function smsLogin(data) {\n  return post('/auth/sms-login', data)\n}\n\n// 用户注册\nexport function register(data) {\n  return post('/auth/signup', data)\n}\n\n// 获取短信验证码\nexport function getSmsCode(phone) {\n  return post('/auth/sms-code', { phone })\n}\n\n// 用户登出\nexport function logout() {\n  return post('/auth/logout')\n}\n\n// 刷新token\nexport function refreshToken() {\n  return post('/auth/refresh')\n}"], "names": ["post"], "mappings": ";;AAGO,SAAS,MAAM,MAAM;AAC1B,SAAOA,cAAI,KAAC,gBAAgB,IAAI;AAClC;AAQO,SAAS,SAAS,MAAM;AAC7B,SAAOA,cAAI,KAAC,gBAAgB,IAAI;AAClC;AAQO,SAAS,SAAS;AACvB,SAAOA,cAAAA,KAAK,cAAc;AAC5B;;;;"}