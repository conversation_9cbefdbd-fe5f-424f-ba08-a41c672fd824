{"version": 3, "file": "payment.js", "sources": ["api/payment.js"], "sourcesContent": ["import { get, post } from '@/utils/request.js'\n\n// 支付订单（模拟）\nexport function payOrder(orderId, paymentMethod = 'wechat') {\n  return post(`/payments/orders/${orderId}/pay`, {\n    paymentMethod\n  })\n}\n\n// 获取订单支付状态\nexport function getPaymentStatus(orderId) {\n  return get(`/payments/orders/${orderId}/status`)\n}\n\n// 获取订单详情（用于支付页面）\nexport function getOrderDetail(orderId) {\n  return get(`/bookings/${orderId}`)\n}\n\n// 模拟支付回调\nexport function mockPaymentCallback(orderId, success = true) {\n  return post(`/payments/orders/${orderId}/callback`, {\n    success,\n    timestamp: Date.now()\n  })\n}\n\n// 获取支付记录\nexport function getPaymentRecords(params = {}) {\n  return get('/payments/records', params)\n}\n\n// 申请退款\nexport function requestRefund(orderId, reason) {\n  return post(`/payments/orders/${orderId}/refund`, {\n    reason\n  })\n}\n\nexport default {\n  payOrder,\n  getPaymentStatus,\n  getOrderDetail,\n  mockPaymentCallback,\n  getPaymentRecords,\n  requestRefund\n}"], "names": ["post", "get"], "mappings": ";;AAGO,SAAS,SAAS,SAAS,gBAAgB,UAAU;AAC1D,SAAOA,cAAI,KAAC,oBAAoB,OAAO,QAAQ;AAAA,IAC7C;AAAA,EACJ,CAAG;AACH;AAQO,SAAS,eAAe,SAAS;AACtC,SAAOC,kBAAI,aAAa,OAAO,EAAE;AACnC;;;"}