{"version": 3, "file": "sharing.js", "sources": ["api/sharing.js"], "sourcesContent": ["import request, { get, post, put } from '../utils/request'\n\n// 创建拼场订单\nexport function createSharingOrder(data) {\n  return post('/sharing-orders', data)\n}\n\n// 获取拼场订单详情\nexport function getSharingOrderById(id) {\n  console.log('[SharingAPI] 请求拼场订单详情，ID:', id)\n  return get(`/sharing-orders/${id}`, {}, { cache: false })\n}\n\n// 通过主订单ID获取拼场订单详情\nexport function getSharingOrderByMainOrderId(orderId) {\n  console.log('[SharingAPI] 通过主订单ID请求拼场订单详情，主订单ID:', orderId)\n  return get(`/sharing-orders/by-order/${orderId}`, {}, { cache: false })\n}\n\n// 获取可加入的拼场订单列表\nexport function getJoinableSharingOrders(params) {\n  return get('/sharing-orders/joinable', params)\n}\n\n// 获取所有拼场订单列表（包括所有状态）\nexport function getAllSharingOrders(params) {\n  // 移除all=true参数，使用正常分页\n  return get('/sharing-orders', params, {\n    cache: false // 确保不使用缓存\n  })\n}\n\n// 旧的API方法，保留兼容性\nexport function getAvailableSharedBookings(params) {\n  return get('/bookings/shared/available', params)\n}\n\n// 根据场馆ID获取可加入的拼场订单\nexport function getJoinableSharingOrdersByVenueId(venueId) {\n  return get(`/sharing-orders/joinable/venue/${venueId}`)\n}\n\n// 根据日期获取可加入的拼场订单\nexport function getJoinableSharingOrdersByDate(date) {\n  return get(`/sharing-orders/joinable/date/${date}`)\n}\n\n// 根据场馆ID和日期获取可加入的拼场订单\nexport function getJoinableSharingOrdersByVenueIdAndDate(venueId, date) {\n  return get(`/sharing-orders/joinable/venue/${venueId}/date/${date}`)\n}\n\n// 获取我创建的拼场订单\nexport function getMyCreatedSharingOrders() {\n  return get('/sharing-orders/my-created', {}, { cache: false })\n}\n\n// 加入拼场订单（申请拼场）\nexport function joinSharingOrder(id, data = {}) {\n  // 使用正确的后端接口路径\n  return post(`/bookings/shared/${id}/apply`, {\n    participantsCount: data.participantsCount || 1,\n    teamName: data.teamName || '',\n    contactInfo: data.contactInfo || '',\n    message: data.message || ''\n  })\n}\n\n// 申请加入拼场订单（需要支付）\nexport function applyJoinSharingOrder(id) {\n  return post(`/sharing-orders/${id}/apply-join`)\n}\n\n// 取消加入拼场订单\nexport function cancelJoinSharingOrder(id) {\n  return post(`/sharing-orders/${id}/cancel-join`)\n}\n\n// 确认拼场订单\nexport function confirmSharingOrder(id) {\n  return post(`/sharing-orders/${id}/confirm`)\n}\n\n// 取消拼场订单\nexport function cancelSharingOrder(id) {\n  return post(`/sharing-orders/${id}/cancel`)\n}\n\n// 移除拼场参与者\nexport function removeSharingParticipant(sharingId, participantId) {\n  return request({\n    url: `/sharing-orders/${sharingId}/participants/${participantId}/remove`,\n    method: 'post'\n  })\n}\n\n// 更新拼场设置\nexport function updateSharingSettings(sharingId, settings) {\n  return request({\n    url: `/sharing-orders/${sharingId}/settings`,\n    method: 'put',\n    data: settings\n  })\n}\n\n// 根据订单号获取拼场订单\nexport function getSharingOrderByOrderNo(orderNo) {\n  return get(`/sharing-orders/order-no/${orderNo}`)\n}\n\n// 申请拼场（原有的拼场申请功能，用于普通订单的拼场申请）\nexport function applySharedBooking(orderId, data) {\n  return post(`/bookings/shared/${orderId}/apply`, data)\n}\n\n// 处理拼场申请\nexport function handleSharedRequest(requestId, data) {\n  return put(`/shared/requests/${requestId}`, data)\n}\n\n// 获取我发出的拼场申请\nexport function getMySharedRequests(params) {\n  return get('/shared/my-requests', params, { cache: false })\n}\n\n// 获取我收到的拼场申请\nexport function getReceivedSharedRequests(params) {\n  return get('/shared/received-requests', params)\n}\n\n// 取消拼场申请\nexport function cancelSharingRequest(requestId) {\n  return request({\n    url: `/shared/requests/${requestId}/cancel`,\n    method: 'post'\n  })\n}\n\n\n\n// 默认导出所有API方法\nexport default {\n  createSharingOrder,\n  getSharingOrderById,\n  getSharingOrderByMainOrderId,\n  getJoinableSharingOrders,\n  getAllSharingOrders,\n  getAvailableSharedBookings,\n  getJoinableSharingOrdersByVenueId,\n  getJoinableSharingOrdersByDate,\n  getJoinableSharingOrdersByVenueIdAndDate,\n  getMyCreatedSharingOrders,\n  joinSharingOrder,\n  cancelJoinSharingOrder,\n  confirmSharingOrder,\n  cancelSharingOrder,\n  removeSharingParticipant,\n  updateSharingSettings,\n  getSharingOrderByOrderNo,\n  applySharedBooking,\n  handleSharedRequest,\n  getMySharedRequests,\n  getReceivedSharedRequests,\n  cancelSharingRequest,\n  applyJoinSharingOrder\n}"], "names": ["post", "uni", "get", "request", "put"], "mappings": ";;;AAGO,SAAS,mBAAmB,MAAM;AACvC,SAAOA,cAAI,KAAC,mBAAmB,IAAI;AACrC;AAGO,SAAS,oBAAoB,IAAI;AACtCC,gBAAAA,MAAA,MAAA,OAAA,wBAAY,6BAA6B,EAAE;AAC3C,SAAOC,cAAG,IAAC,mBAAmB,EAAE,IAAI,IAAI,EAAE,OAAO,OAAO;AAC1D;AAGO,SAAS,6BAA6B,SAAS;AACpDD,gBAAAA,2CAAY,uCAAuC,OAAO;AAC1D,SAAOC,cAAG,IAAC,4BAA4B,OAAO,IAAI,IAAI,EAAE,OAAO,OAAO;AACxE;AAGO,SAAS,yBAAyB,QAAQ;AAC/C,SAAOA,cAAG,IAAC,4BAA4B,MAAM;AAC/C;AAGO,SAAS,oBAAoB,QAAQ;AAE1C,SAAOA,cAAG,IAAC,mBAAmB,QAAQ;AAAA,IACpC,OAAO;AAAA;AAAA,EACX,CAAG;AACH;AAuBO,SAAS,4BAA4B;AAC1C,SAAOA,cAAAA,IAAI,8BAA8B,CAAA,GAAI,EAAE,OAAO,MAAK,CAAE;AAC/D;AAGO,SAAS,iBAAiB,IAAI,OAAO,IAAI;AAE9C,SAAOF,cAAI,KAAC,oBAAoB,EAAE,UAAU;AAAA,IAC1C,mBAAmB,KAAK,qBAAqB;AAAA,IAC7C,UAAU,KAAK,YAAY;AAAA,IAC3B,aAAa,KAAK,eAAe;AAAA,IACjC,SAAS,KAAK,WAAW;AAAA,EAC7B,CAAG;AACH;AAGO,SAAS,sBAAsB,IAAI;AACxC,SAAOA,cAAI,KAAC,mBAAmB,EAAE,aAAa;AAChD;AAQO,SAAS,oBAAoB,IAAI;AACtC,SAAOA,cAAI,KAAC,mBAAmB,EAAE,UAAU;AAC7C;AAGO,SAAS,mBAAmB,IAAI;AACrC,SAAOA,cAAI,KAAC,mBAAmB,EAAE,SAAS;AAC5C;AAGO,SAAS,yBAAyB,WAAW,eAAe;AACjE,SAAOG,sBAAQ;AAAA,IACb,KAAK,mBAAmB,SAAS,iBAAiB,aAAa;AAAA,IAC/D,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,sBAAsB,WAAW,UAAU;AACzD,SAAOA,sBAAQ;AAAA,IACb,KAAK,mBAAmB,SAAS;AAAA,IACjC,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAG;AACH;AAQO,SAAS,mBAAmB,SAAS,MAAM;AAChD,SAAOH,cAAAA,KAAK,oBAAoB,OAAO,UAAU,IAAI;AACvD;AAGO,SAAS,oBAAoB,WAAW,MAAM;AACnD,SAAOI,cAAG,IAAC,oBAAoB,SAAS,IAAI,IAAI;AAClD;AAGO,SAAS,oBAAoB,QAAQ;AAC1C,SAAOF,cAAAA,IAAI,uBAAuB,QAAQ,EAAE,OAAO,MAAK,CAAE;AAC5D;AAGO,SAAS,0BAA0B,QAAQ;AAChD,SAAOA,cAAG,IAAC,6BAA6B,MAAM;AAChD;;;;;;;;;;;;;;;;;"}