{"version": 3, "file": "popup.js", "sources": ["uni_modules/uni-popup/components/uni-popup/popup.js"], "sourcesContent": ["\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t\r\n\t\t}\r\n\t},\n\tcreated(){\n\t\tthis.popup = this.getParent()\n\t},\n\tmethods:{\n\t\t/**\n\t\t * 获取父元素实例\n\t\t */\n\t\tgetParent(name = 'uniPopup') {\n\t\t\tlet parent = this.$parent;\n\t\t\tlet parentName = parent.$options.name;\n\t\t\twhile (parentName !== name) {\n\t\t\t\tparent = parent.$parent;\n\t\t\t\tif (!parent) return false\n\t\t\t\tparentName = parent.$options.name;\n\t\t\t}\n\t\t\treturn parent;\n\t\t},\n\t}\r\n}\r\n"], "names": [], "mappings": ";AACA,MAAe,QAAA;AAAA,EACd,OAAO;AACN,WAAO,CAEN;AAAA,EACD;AAAA,EACD,UAAS;AACR,SAAK,QAAQ,KAAK,UAAW;AAAA,EAC7B;AAAA,EACD,SAAQ;AAAA;AAAA;AAAA;AAAA,IAIP,UAAU,OAAO,YAAY;AAC5B,UAAI,SAAS,KAAK;AAClB,UAAI,aAAa,OAAO,SAAS;AACjC,aAAO,eAAe,MAAM;AAC3B,iBAAS,OAAO;AAChB,YAAI,CAAC;AAAQ,iBAAO;AACpB,qBAAa,OAAO,SAAS;AAAA,MAC7B;AACD,aAAO;AAAA,IACP;AAAA,EACD;AACF;;"}