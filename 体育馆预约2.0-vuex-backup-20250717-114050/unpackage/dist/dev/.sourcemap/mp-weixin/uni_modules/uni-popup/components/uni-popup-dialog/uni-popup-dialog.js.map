{"version": 3, "file": "uni-popup-dialog.js", "sources": ["uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue", "/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue?type=component"], "sourcesContent": ["<template>\r\n\t<view class=\"uni-popup-dialog\">\r\n\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t<text class=\"uni-dialog-title-text\" :class=\"['uni-popup__'+dialogType]\">{{titleText}}</text>\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'base'\" class=\"uni-dialog-content\">\r\n\t\t\t<slot>\r\n\t\t\t\t<text class=\"uni-dialog-content-text\">{{content}}</text>\r\n\t\t\t</slot>\r\n\t\t</view>\r\n\t\t<view v-else class=\"uni-dialog-content\">\r\n\t\t\t<slot>\r\n\t\t\t\t<input class=\"uni-dialog-input\" :maxlength=\"maxlength\" v-model=\"val\" :type=\"inputType\"\r\n\t\t\t\t\t:placeholder=\"placeholderText\" :focus=\"focus\">\r\n\t\t\t</slot>\r\n\t\t</view>\r\n\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t<view class=\"uni-dialog-button\" v-if=\"showClose\" @click=\"closeDialog\">\r\n\t\t\t\t<text class=\"uni-dialog-button-text\">{{closeText}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-dialog-button\" :class=\"showClose?'uni-border-left':''\" @click=\"onOk\">\r\n\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">{{okText}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport popup from '../uni-popup/popup.js'\r\n\timport {\r\n\t\tinitVueI18n\r\n\t} from '@dcloudio/uni-i18n'\r\n\timport messages from '../uni-popup/i18n/index.js'\r\n\tconst {\r\n\t\tt\r\n\t} = initVueI18n(messages)\r\n\t/**\r\n\t * PopUp 弹出层-对话框样式\r\n\t * @description 弹出层-对话框样式\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=329\r\n\t * @property {String} value input 模式下的默认值\r\n\t * @property {String} placeholder input 模式下输入提示\r\n\t * @property {Boolean} focus input模式下是否自动聚焦，默认为true\r\n\t * @property {String} type = [success|warning|info|error] 主题样式\r\n\t *  @value success 成功\r\n\t * \t@value warning 提示\r\n\t * \t@value info 消息\r\n\t * \t@value error 错误\r\n\t * @property {String} mode = [base|input] 模式、\r\n\t * \t@value base 基础对话框\r\n\t * \t@value input 可输入对话框\r\n\t * @showClose {Boolean} 是否显示关闭按钮\r\n\t * @property {String} content 对话框内容\r\n\t * @property {Boolean} beforeClose 是否拦截取消事件\r\n\t * @property {Number} maxlength 输入\r\n\t * @event {Function} confirm 点击确认按钮触发\r\n\t * @event {Function} close 点击取消按钮触发\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: \"uniPopupDialog\",\r\n\t\tmixins: [popup],\r\n\t\temits: ['confirm', 'close', 'update:modelValue', 'input'],\r\n\t\tprops: {\r\n\t\t\tinputType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'text'\r\n\t\t\t},\r\n\t\t\tshowClose: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// #ifdef VUE2\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef VUE3\r\n\t\t\tmodelValue: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\r\n\r\n\t\t\tplaceholder: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'error'\r\n\t\t\t},\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'base'\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tcontent: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tbeforeClose: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tcancelText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tconfirmText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tmaxlength: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: -1,\r\n\t\t\t},\r\n\t\t\tfocus: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true,\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdialogType: 'error',\r\n\t\t\t\tval: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tokText() {\r\n\t\t\t\treturn this.confirmText || t(\"uni-popup.ok\")\r\n\t\t\t},\r\n\t\t\tcloseText() {\r\n\t\t\t\treturn this.cancelText || t(\"uni-popup.cancel\")\r\n\t\t\t},\r\n\t\t\tplaceholderText() {\r\n\t\t\t\treturn this.placeholder || t(\"uni-popup.placeholder\")\r\n\t\t\t},\r\n\t\t\ttitleText() {\r\n\t\t\t\treturn this.title || t(\"uni-popup.title\")\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\ttype(val) {\r\n\t\t\t\tthis.dialogType = val\r\n\t\t\t},\r\n\t\t\tmode(val) {\r\n\t\t\t\tif (val === 'input') {\r\n\t\t\t\t\tthis.dialogType = 'info'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tvalue(val) {\r\n\t\t\t\tthis.setVal(val)\r\n\t\t\t},\r\n\t\t\t// #ifdef VUE3\r\n\t\t\tmodelValue(val) {\r\n\t\t\t\tthis.setVal(val)\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tval(val) {\r\n\t\t\t\t// #ifdef VUE2\r\n\t\t\t\t// TODO 兼容 vue2\r\n\t\t\t\tthis.$emit('input', val);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\t// TODO　兼容　vue3\r\n\t\t\t\tthis.$emit('update:modelValue', val);\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 对话框遮罩不可点击\r\n\t\t\tthis.popup.disableMask()\r\n\t\t\t// this.popup.closeMask()\r\n\t\t\tif (this.mode === 'input') {\r\n\t\t\t\tthis.dialogType = 'info'\r\n\t\t\t\tthis.val = this.value;\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\tthis.val = this.modelValue;\r\n\t\t\t\t// #endif\r\n\t\t\t} else {\r\n\t\t\t\tthis.dialogType = this.type\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 给val属性赋值\r\n\t\t\t */\r\n\t\t\tsetVal(val) {\r\n\t\t\t\tif (this.maxlength != -1 && this.mode === 'input') {\r\n\t\t\t\t\tthis.val = val.slice(0, this.maxlength);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.val = val\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 点击确认按钮\r\n\t\t\t */\r\n\t\t\tonOk() {\r\n\t\t\t\tif (this.mode === 'input') {\r\n\t\t\t\t\tthis.$emit('confirm', this.val)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit('confirm')\r\n\t\t\t\t}\r\n\t\t\t\tif (this.beforeClose) return\r\n\t\t\t\tthis.popup.close()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 点击取消按钮\r\n\t\t\t */\r\n\t\t\tcloseDialog() {\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t\tif (this.beforeClose) return\r\n\t\t\t\tthis.popup.close()\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.popup.close()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.uni-popup-dialog {\r\n\t\twidth: 300px;\r\n\t\tborder-radius: 11px;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.uni-dialog-title {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\tpadding-top: 25px;\r\n\t}\r\n\r\n\t.uni-dialog-title-text {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.uni-dialog-content {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 20px;\r\n\t}\r\n\r\n\t.uni-dialog-content-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #6C6C6C;\r\n\t}\r\n\r\n\t.uni-dialog-button-group {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tborder-top-color: #f5f5f5;\r\n\t\tborder-top-style: solid;\r\n\t\tborder-top-width: 1px;\r\n\t}\r\n\r\n\t.uni-dialog-button {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\r\n\t\tflex: 1;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 45px;\r\n\t}\r\n\r\n\t.uni-border-left {\r\n\t\tborder-left-color: #f0f0f0;\r\n\t\tborder-left-style: solid;\r\n\t\tborder-left-width: 1px;\r\n\t}\r\n\r\n\t.uni-dialog-button-text {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.uni-button-color {\r\n\t\tcolor: #007aff;\r\n\t}\r\n\r\n\t.uni-dialog-input {\r\n\t\tflex: 1;\r\n\t\tfont-size: 14px;\r\n\t\tborder: 1px #eee solid;\r\n\t\theight: 40px;\r\n\t\tpadding: 0 10px;\r\n\t\tborder-radius: 5px;\r\n\t\tcolor: #555;\r\n\t}\r\n\r\n\t.uni-popup__success {\r\n\t\tcolor: #4cd964;\r\n\t}\r\n\r\n\t.uni-popup__warn {\r\n\t\tcolor: #f0ad4e;\r\n\t}\r\n\r\n\t.uni-popup__error {\r\n\t\tcolor: #dd524d;\r\n\t}\r\n\r\n\t.uni-popup__info {\r\n\t\tcolor: #909399;\r\n\t}\r\n</style>\n", "import Component from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue'\nwx.createComponent(Component)"], "names": ["initVueI18n", "messages", "popup"], "mappings": ";;;;AAkCC,MAAM;AAAA,EACL;IACGA,cAAAA,YAAYC,oDAAAA,QAAQ;AAwBxB,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACC,+CAAAA,KAAK;AAAA,EACd,OAAO,CAAC,WAAW,SAAS,qBAAqB,OAAO;AAAA,EACxD,OAAO;AAAA,IACN,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IAQD,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA,IAID,aAAa;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA,IACD,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,KAAK;AAAA,IACN;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,SAAS;AACR,aAAO,KAAK,eAAe,EAAE,cAAc;AAAA,IAC3C;AAAA,IACD,YAAY;AACX,aAAO,KAAK,cAAc,EAAE,kBAAkB;AAAA,IAC9C;AAAA,IACD,kBAAkB;AACjB,aAAO,KAAK,eAAe,EAAE,uBAAuB;AAAA,IACpD;AAAA,IACD,YAAY;AACX,aAAO,KAAK,SAAS,EAAE,iBAAiB;AAAA,IACzC;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,KAAK,KAAK;AACT,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,KAAK,KAAK;AACT,UAAI,QAAQ,SAAS;AACpB,aAAK,aAAa;AAAA,MACnB;AAAA,IACA;AAAA,IACD,MAAM,KAAK;AACV,WAAK,OAAO,GAAG;AAAA,IACf;AAAA,IAED,WAAW,KAAK;AACf,WAAK,OAAO,GAAG;AAAA,IACf;AAAA,IAED,IAAI,KAAK;AAOR,WAAK,MAAM,qBAAqB,GAAG;AAAA,IAEpC;AAAA,EACA;AAAA,EACD,UAAU;AAET,SAAK,MAAM,YAAY;AAEvB,QAAI,KAAK,SAAS,SAAS;AAC1B,WAAK,aAAa;AAClB,WAAK,MAAM,KAAK;AAEhB,WAAK,MAAM,KAAK;AAAA,WAEV;AACN,WAAK,aAAa,KAAK;AAAA,IACxB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,OAAO,KAAK;AACX,UAAI,KAAK,aAAa,MAAM,KAAK,SAAS,SAAS;AAClD,aAAK,MAAM,IAAI,MAAM,GAAG,KAAK,SAAS;AAAA,aAChC;AACN,aAAK,MAAM;AAAA,MACZ;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAID,OAAO;AACN,UAAI,KAAK,SAAS,SAAS;AAC1B,aAAK,MAAM,WAAW,KAAK,GAAG;AAAA,aACxB;AACN,aAAK,MAAM,SAAS;AAAA,MACrB;AACA,UAAI,KAAK;AAAa;AACtB,WAAK,MAAM,MAAM;AAAA,IACjB;AAAA;AAAA;AAAA;AAAA,IAID,cAAc;AACb,WAAK,MAAM,OAAO;AAClB,UAAI,KAAK;AAAa;AACtB,WAAK,MAAM,MAAM;AAAA,IACjB;AAAA,IACD,QAAQ;AACP,WAAK,MAAM,MAAM;AAAA,IAClB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;AChOD,GAAG,gBAAgB,SAAS;"}