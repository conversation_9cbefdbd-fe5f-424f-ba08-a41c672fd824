"use strict";
const utils_request = require("../utils/request.js");
function getVenueList(params) {
  return utils_request.get("/venues", params);
}
function getVenueDetail(id) {
  return utils_request.get(`/venues/${id}`);
}
function getVenueTypes() {
  return utils_request.get("/venues/types");
}
function getPopularVenues(limit = 5) {
  return utils_request.get("/venues/popular", { limit });
}
function searchVenues(params) {
  return utils_request.get("/venues/search", params);
}
exports.getPopularVenues = getPopularVenues;
exports.getVenueDetail = getVenueDetail;
exports.getVenueList = getVenueList;
exports.getVenueTypes = getVenueTypes;
exports.searchVenues = searchVenues;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/venue.js.map
