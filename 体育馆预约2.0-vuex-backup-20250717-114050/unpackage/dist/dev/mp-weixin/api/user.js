"use strict";
const utils_request = require("../utils/request.js");
function getUserInfo() {
  return utils_request.get("/users/me");
}
function changePassword(data) {
  return utils_request.put("/users/me/password", data);
}
function getUserBookings(params) {
  return utils_request.get("/users/me/bookings", params);
}
function getUserStats() {
  return utils_request.get("/users/me/stats");
}
exports.changePassword = changePassword;
exports.getUserBookings = getUserBookings;
exports.getUserInfo = getUserInfo;
exports.getUserStats = getUserStats;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/user.js.map
