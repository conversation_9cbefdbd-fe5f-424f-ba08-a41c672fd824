"use strict";
const common_vendor = require("../common/vendor.js");
const api_sharing = require("../api/sharing.js");
const utils_ui = require("../utils/ui.js");
const useSharingStore = common_vendor.defineStore("sharing", {
  state: () => ({
    sharingOrders: [],
    mySharingOrders: [],
    receivedRequests: [],
    sentRequests: [],
    sharingOrderDetail: null,
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1
    }
  }),
  getters: {
    // 基础getters
    getSharingOrders: (state) => state.sharingOrders,
    getMySharingOrders: (state) => state.mySharingOrders,
    getReceivedRequests: (state) => state.receivedRequests,
    getSentRequests: (state) => state.sentRequests,
    getSharingOrderDetail: (state) => state.sharingOrderDetail,
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,
    // 计算属性
    totalSharingOrders: (state) => state.sharingOrders.length,
    totalMySharingOrders: (state) => state.mySharingOrders.length,
    totalReceivedRequests: (state) => state.receivedRequests.length,
    totalSentRequests: (state) => state.sentRequests.length,
    // 按状态筛选
    getOrdersByStatus: (state) => (status) => {
      return state.sharingOrders.filter((order) => order.status === status);
    },
    // 待处理的请求
    getPendingRequests: (state) => {
      return state.receivedRequests.filter((request) => request.status === "PENDING");
    },
    // 是否有更多数据
    hasMoreData: (state) => {
      return state.pagination.current < state.pagination.totalPages;
    }
  },
  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading;
    },
    // 设置分享订单列表
    setSharingOrders(orders) {
      this.sharingOrders = Array.isArray(orders) ? orders : [];
    },
    // 设置我的分享订单
    setMySharingOrders(orders) {
      this.mySharingOrders = Array.isArray(orders) ? orders : [];
    },
    // 设置收到的请求
    setReceivedRequests(requests) {
      this.receivedRequests = Array.isArray(requests) ? requests : [];
    },
    // 设置发送的请求
    setSentRequests(requests) {
      this.sentRequests = Array.isArray(requests) ? requests : [];
    },
    // 设置分享订单详情
    setSharingOrderDetail(order) {
      this.sharingOrderDetail = order;
    },
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
    // 更新订单状态
    updateOrderStatus({ orderId, status }) {
      const order = this.sharingOrders.find((o) => o.id === orderId);
      if (order) {
        order.status = status;
      }
      const myOrder = this.mySharingOrders.find((o) => o.id === orderId);
      if (myOrder) {
        myOrder.status = status;
      }
    },
    // 获取分享订单列表
    async getSharingOrdersList(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:105", "[SharingStore] 开始获取分享订单列表，参数:", params);
        this.setLoading(true);
        const response = await api_sharing.getJoinableSharingOrders(params);
        if (response && response.data) {
          const orders = Array.isArray(response.data) ? response.data : [];
          this.setSharingOrders(orders);
          if (response.pagination) {
            this.setPagination(response.pagination);
          }
          common_vendor.index.__f__("log", "at stores/sharing.js:118", "[SharingStore] 分享订单列表获取成功:", orders.length, "条");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:123", "[SharingStore] 获取分享订单列表失败:", error);
        utils_ui.showError(error.message || "获取分享订单列表失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取可加入的拼场订单
    async getJoinableSharingOrders(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:134", "[SharingStore] 开始获取可加入的拼场订单，参数:", params);
        this.setLoading(true);
        const response = await api_sharing.getJoinableSharingOrders(params);
        if (response) {
          const orders = response.list || response.data || [];
          common_vendor.index.__f__("log", "at stores/sharing.js:143", "[SharingStore] 可加入拼场订单后端返回数据格式:", {
            hasData: !!response.data,
            hasList: !!response.list,
            ordersLength: orders.length,
            pagination: response.pagination
          });
          if (params.refresh || params.page === 1) {
            this.setSharingOrders(orders);
          } else {
            this.sharingOrders.push(...orders);
          }
          if (response.pagination) {
            this.setPagination(response.pagination);
          }
          common_vendor.index.__f__("log", "at stores/sharing.js:161", "[SharingStore] 可加入拼场订单获取成功:", orders.length, "条");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:166", "[SharingStore] 获取可加入拼场订单失败:", error);
        utils_ui.showError(error.message || "获取可加入拼场订单失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取所有拼场订单
    async getAllSharingOrders(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:177", "[SharingStore] 开始获取所有拼场订单，参数:", params);
        this.setLoading(true);
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("请求超时")), 1e4);
        });
        const apiPromise = api_sharing.getAllSharingOrders(params);
        const response = await Promise.race([apiPromise, timeoutPromise]);
        if (response) {
          const orders = response.list || response.data || [];
          common_vendor.index.__f__("log", "at stores/sharing.js:192", "[SharingStore] 后端返回数据格式:", {
            hasData: !!response.data,
            hasList: !!response.list,
            ordersLength: orders.length,
            pagination: response.pagination
          });
          if (params.refresh || params.page === 1) {
            this.setSharingOrders(orders);
          } else {
            this.sharingOrders.push(...orders);
          }
          if (response.pagination) {
            this.setPagination(response.pagination);
          }
          common_vendor.index.__f__("log", "at stores/sharing.js:210", "[SharingStore] 所有拼场订单获取成功:", orders.length, "条");
        } else {
          common_vendor.index.__f__("warn", "at stores/sharing.js:212", "[SharingStore] 获取所有拼场订单返回空数据");
          if (params.refresh || params.page === 1) {
            this.setSharingOrders([]);
          }
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:221", "[SharingStore] 获取所有拼场订单失败:", error);
        if (error.message === "请求超时") {
          utils_ui.showError("加载超时，请检查网络连接");
        } else {
          utils_ui.showError(error.message || "获取所有拼场订单失败");
        }
        if (params.refresh || params.page === 1) {
          this.setSharingOrders([]);
        }
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取我的分享订单
    async getMyOrders(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:244", "[SharingStore] 开始获取我的分享订单");
        this.setLoading(true);
        const response = await api_sharing.getMyCreatedSharingOrders(params);
        if (response && response.data) {
          const orders = Array.isArray(response.data) ? response.data : [];
          this.setMySharingOrders(orders);
          common_vendor.index.__f__("log", "at stores/sharing.js:252", "[SharingStore] 我的分享订单获取成功:", orders.length, "条");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:257", "[SharingStore] 获取我的分享订单失败:", error);
        utils_ui.showError(error.message || "获取我的分享订单失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取收到的请求
    async getReceivedRequestsList(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:268", "[SharingStore] 开始获取收到的请求");
        this.setLoading(true);
        const response = await api_sharing.getReceivedSharedRequests(params);
        if (response && response.data) {
          const requests = Array.isArray(response.data) ? response.data : [];
          this.setReceivedRequests(requests);
          common_vendor.index.__f__("log", "at stores/sharing.js:276", "[SharingStore] 收到的请求获取成功:", requests.length, "条");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:281", "[SharingStore] 获取收到的请求失败:", error);
        utils_ui.showError(error.message || "获取收到的请求失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取发送的请求
    async getSentRequestsList(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:292", "[SharingStore] 开始获取发送的请求");
        this.setLoading(true);
        const response = await api_sharing.getMySharedRequests(params);
        if (response && response.data) {
          const requests = Array.isArray(response.data) ? response.data : [];
          this.setSentRequests(requests);
          common_vendor.index.__f__("log", "at stores/sharing.js:300", "[SharingStore] 发送的请求获取成功:", requests.length, "条");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:305", "[SharingStore] 获取发送的请求失败:", error);
        utils_ui.showError(error.message || "获取发送的请求失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取分享订单详情
    async getOrderDetail(orderId, forceRefresh = false) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:316", "[SharingStore] 开始获取分享订单详情:", orderId, forceRefresh ? "(强制刷新)" : "");
        this.setLoading(true);
        if (forceRefresh) {
          this.sharingOrderDetail = null;
        }
        const response = await api_sharing.getSharingOrderById(orderId);
        common_vendor.index.__f__("log", "at stores/sharing.js:326", "[SharingStore] API响应详情:", response);
        if (response) {
          if (response.message && !response.id) {
            common_vendor.index.__f__("warn", "at stores/sharing.js:331", "[SharingStore] 获取分享订单详情失败:", response.message);
            this.setSharingOrderDetail(null);
          } else if (response.id) {
            this.setSharingOrderDetail(response);
            common_vendor.index.__f__("log", "at stores/sharing.js:336", "[SharingStore] 分享订单详情获取成功:", response);
          } else {
            common_vendor.index.__f__("warn", "at stores/sharing.js:338", "[SharingStore] 获取分享订单详情返回无效数据:", response);
            this.setSharingOrderDetail(null);
          }
        } else {
          common_vendor.index.__f__("warn", "at stores/sharing.js:342", "[SharingStore] 获取分享订单详情返回空响应");
          this.setSharingOrderDetail(null);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:348", "[SharingStore] 获取分享订单详情失败:", error);
        utils_ui.showError(error.message || "获取分享订单详情失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 通过主订单ID获取分享订单详情
    async getOrderDetailByMainOrderId(mainOrderId) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:359", "[SharingStore] 通过主订单ID获取分享订单详情:", mainOrderId);
        this.setLoading(true);
        const response = await api_sharing.getSharingOrderByMainOrderId(mainOrderId);
        common_vendor.index.__f__("log", "at stores/sharing.js:364", "[SharingStore] 主订单API响应详情:", response);
        if (response) {
          if (response.message && !response.id) {
            common_vendor.index.__f__("warn", "at stores/sharing.js:369", "[SharingStore] 通过主订单ID获取分享订单详情失败:", response.message);
            this.setSharingOrderDetail(null);
          } else if (response.id) {
            this.setSharingOrderDetail(response);
            common_vendor.index.__f__("log", "at stores/sharing.js:374", "[SharingStore] 通过主订单ID获取分享订单详情成功:", response);
            return response.id;
          } else {
            common_vendor.index.__f__("warn", "at stores/sharing.js:377", "[SharingStore] 通过主订单ID获取分享订单详情返回无效数据:", response);
            this.setSharingOrderDetail(null);
          }
        } else {
          common_vendor.index.__f__("warn", "at stores/sharing.js:381", "[SharingStore] 通过主订单ID获取分享订单详情返回空响应");
          this.setSharingOrderDetail(null);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:387", "[SharingStore] 通过主订单ID获取分享订单详情失败:", error);
        utils_ui.showError(error.message || "获取分享订单详情失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 创建分享订单
    async createOrder(orderData) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:398", "[SharingStore] 开始创建分享订单");
        this.setLoading(true);
        const response = await api_sharing.createSharingOrder(orderData);
        if (response && response.data) {
          await this.getMyOrders();
          utils_ui.showSuccess("分享订单创建成功");
          common_vendor.index.__f__("log", "at stores/sharing.js:407", "[SharingStore] 分享订单创建成功");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:412", "[SharingStore] 创建分享订单失败:", error);
        utils_ui.showError(error.message || "创建分享订单失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 处理分享请求
    async handleRequest({ requestId, action }) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:423", "[SharingStore] 开始处理分享请求:", { requestId, action });
        this.setLoading(true);
        const response = await api_sharing.handleSharedRequest(requestId, action);
        if (response && response.success) {
          await this.getReceivedRequestsList();
          utils_ui.showSuccess(`请求${action === "accept" ? "接受" : "拒绝"}成功`);
          common_vendor.index.__f__("log", "at stores/sharing.js:432", "[SharingStore] 分享请求处理成功");
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:437", "[SharingStore] 处理分享请求失败:", error);
        utils_ui.showError(error.message || "处理分享请求失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 处理拼场申请（新增方法，对应Vuex中的processSharingRequest）
    async processSharingRequest({ requestId, action, reason = "" }) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:448", "[SharingStore] 开始处理拼场申请:", { requestId, action, reason });
        this.setLoading(true);
        const data = {
          action,
          // 直接传递action参数：'approve' 或 'reject'
          responseMessage: reason || ""
        };
        const response = await api_sharing.handleSharedRequest(requestId, data);
        if (response && response.success) {
          utils_ui.showSuccess(action === "approve" ? "已同意拼场申请" : "已拒绝拼场申请");
          common_vendor.index.__f__("log", "at stores/sharing.js:460", "[SharingStore] 拼场申请处理成功");
          await this.getReceivedRequestsList();
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:468", "[SharingStore] 处理拼场申请失败:", error);
        if (error.needPayment) {
          const enhancedError = new Error(error.message || "处理拼场申请失败");
          enhancedError.needPayment = error.needPayment;
          enhancedError.orderId = error.orderId;
          enhancedError.orderStatus = error.orderStatus;
          throw enhancedError;
        } else {
          utils_ui.showError(error.message || "处理拼场申请失败");
          throw error;
        }
      } finally {
        this.setLoading(false);
      }
    },
    // 申请加入拼场订单（需要支付）
    async applyJoinSharingOrder(orderId) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:490", "[SharingStore] 开始申请加入拼场订单:", orderId);
        this.setLoading(true);
        const response = await api_sharing.applyJoinSharingOrder(orderId);
        if (response && response.success) {
          common_vendor.index.__f__("log", "at stores/sharing.js:496", "[SharingStore] 申请加入拼场订单成功");
          return response;
        } else {
          throw new Error(response.message || "申请失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:502", "[SharingStore] 申请加入拼场订单失败:", error);
        utils_ui.showError(error.message || "申请加入拼场订单失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 更新拼场设置
    async updateSharingSettings({ sharingId, settings }) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:513", "[SharingStore] 开始更新拼场设置:", { sharingId, settings });
        this.setLoading(true);
        const response = await api_sharing.updateSharingSettings(sharingId, settings);
        utils_ui.showSuccess("设置已更新");
        common_vendor.index.__f__("log", "at stores/sharing.js:519", "[SharingStore] 拼场设置更新成功");
        await this.getOrderDetail(sharingId);
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:526", "[SharingStore] 更新拼场设置失败:", error);
        utils_ui.showError(error.message || "更新设置失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 申请拼场
    async applySharingOrder({ orderId, data }) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:537", "[SharingStore] 开始申请拼场:", { orderId, data });
        this.setLoading(true);
        const response = await api_sharing.applySharedBooking(orderId, data);
        common_vendor.index.__f__("log", "at stores/sharing.js:543", "[SharingStore] 拼场申请成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:547", "[SharingStore] 申请拼场失败:", error);
        utils_ui.showError(error.message || "申请拼场失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 移除拼场参与者
    async removeSharingParticipant({ sharingId, participantId }) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:558", "[SharingStore] 开始移除拼场参与者:", { sharingId, participantId });
        this.setLoading(true);
        const response = await api_sharing.removeSharingParticipant(sharingId, participantId);
        if (response && response.success) {
          utils_ui.showSuccess("参与者移除成功");
          common_vendor.index.__f__("log", "at stores/sharing.js:565", "[SharingStore] 参与者移除成功");
          await this.getOrderDetail(sharingId);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:573", "[SharingStore] 移除参与者失败:", error);
        utils_ui.showError(error.message || "移除参与者失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 确认拼场订单
    async confirmSharingOrder(orderId) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:584", "[SharingStore] 开始确认拼场订单:", orderId);
        this.setLoading(true);
        const response = await api_sharing.confirmSharingOrder(orderId);
        if (response && response.success) {
          utils_ui.showSuccess("拼场订单确认成功");
          common_vendor.index.__f__("log", "at stores/sharing.js:591", "[SharingStore] 拼场订单确认成功");
          await this.getMyOrders();
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:599", "[SharingStore] 确认拼场订单失败:", error);
        utils_ui.showError(error.message || "确认拼场订单失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 取消拼场订单
    async cancelSharingOrder(orderId) {
      try {
        common_vendor.index.__f__("log", "at stores/sharing.js:610", "[SharingStore] 开始取消拼场订单:", orderId);
        this.setLoading(true);
        const response = await api_sharing.cancelSharingOrder(orderId);
        if (response && response.success) {
          utils_ui.showSuccess("拼场订单取消成功");
          common_vendor.index.__f__("log", "at stores/sharing.js:617", "[SharingStore] 拼场订单取消成功");
          await this.getMyOrders();
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/sharing.js:625", "[SharingStore] 取消拼场订单失败:", error);
        utils_ui.showError(error.message || "取消拼场订单失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 清空订单详情
    clearOrderDetail() {
      this.sharingOrderDetail = null;
    },
    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      };
    }
  }
});
exports.useSharingStore = useSharingStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/stores/sharing.js.map
