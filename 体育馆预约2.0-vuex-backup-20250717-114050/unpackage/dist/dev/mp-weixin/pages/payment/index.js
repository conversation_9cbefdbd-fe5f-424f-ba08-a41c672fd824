"use strict";
const common_vendor = require("../../common/vendor.js");
const api_payment = require("../../api/payment.js");
const api_order = require("../../api/order.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  name: "PaymentPage",
  data() {
    return {
      orderId: null,
      orderType: "booking",
      // booking 或 sharing
      orderInfo: null,
      loading: true,
      selectedMethod: "wechat",
      paying: false,
      fromPage: "",
      // 记录来源页面
      paymentResult: {
        success: false,
        title: "",
        message: "",
        buttonText: "确定"
      }
    };
  },
  computed: {
    canPay() {
      if (!this.orderInfo || !this.selectedMethod || this.paying)
        return false;
      if (this.orderInfo.isVirtualOrder) {
        return this.orderInfo.status === "PENDING";
      } else {
        return this.orderInfo.status === "PENDING";
      }
    },
    payButtonText() {
      var _a;
      if (this.paying)
        return "支付中...";
      if (!this.orderInfo)
        return "加载中...";
      if (this.orderInfo.isVirtualOrder) {
        if (this.orderInfo.status === "PENDING") {
          const amount = this.orderInfo.paymentAmount || this.orderInfo.totalPrice;
          return `立即支付 ¥${(amount == null ? void 0 : amount.toFixed(2)) || "0.00"}`;
        } else {
          const statusMessages = {
            "SHARING_SUCCESS": "拼场已成功",
            "CANCELLED": "申请已取消",
            "EXPIRED": "申请已过期",
            "NOT_FOUND": "申请不存在",
            "ACCESS_DENIED": "无权访问"
          };
          return statusMessages[this.orderInfo.status] || "订单状态异常";
        }
      } else {
        if (this.orderInfo.status === "PENDING") {
          return `立即支付 ¥${((_a = this.orderInfo.totalPrice) == null ? void 0 : _a.toFixed(2)) || "0.00"}`;
        } else {
          return "订单状态异常";
        }
      }
    }
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/payment/index.vue:198", "支付页面参数:", options);
    if (options.orderId) {
      this.orderId = options.orderId;
      this.orderType = options.type || "booking";
      this.fromPage = options.from || "";
      this.loadOrderInfo();
    } else {
      common_vendor.index.showToast({
        title: "订单ID缺失",
        icon: "error"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 加载订单信息
    async loadOrderInfo() {
      try {
        this.loading = true;
        const isVirtualOrder = this.orderId < 0;
        common_vendor.index.__f__("log", "at pages/payment/index.vue:224", "订单ID:", this.orderId, "是否为虚拟订单:", isVirtualOrder);
        let response;
        if (isVirtualOrder) {
          const requestId = Math.abs(this.orderId);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:230", "获取虚拟订单详情，申请ID:", requestId);
          try {
            response = await utils_request.get(`/users/me/virtual-order/${requestId}`);
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:235", "获取虚拟订单失败:", error);
            if (error.status === 404) {
              response = {
                data: {
                  id: this.orderId,
                  orderNo: `REQ_${requestId}`,
                  status: "NOT_FOUND",
                  isVirtualOrder: true,
                  venueName: "未知场馆",
                  totalPrice: 0,
                  paymentAmount: 0
                }
              };
            } else if (error.status === 403) {
              response = {
                data: {
                  id: this.orderId,
                  orderNo: `REQ_${requestId}`,
                  status: "ACCESS_DENIED",
                  isVirtualOrder: true,
                  venueName: "未知场馆",
                  totalPrice: 0,
                  paymentAmount: 0
                }
              };
            } else {
              throw error;
            }
          }
        } else {
          response = await api_order.getOrderDetail(this.orderId);
        }
        this.orderInfo = response.data || response;
        common_vendor.index.__f__("log", "at pages/payment/index.vue:271", "订单信息:", this.orderInfo);
        if (isVirtualOrder) {
          this.orderInfo.isVirtualOrder = true;
          common_vendor.index.__f__("log", "at pages/payment/index.vue:276", "虚拟订单详细信息:");
          common_vendor.index.__f__("log", "at pages/payment/index.vue:277", "- 订单状态:", this.orderInfo.status);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:278", "- 申请状态:", this.orderInfo.requestStatus);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:279", "- 支付金额:", this.orderInfo.paymentAmount);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:280", "- 总价:", this.orderInfo.totalPrice);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:281", "- 队伍名称:", this.orderInfo.applicantTeamName);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:282", "- 联系方式:", this.orderInfo.applicantContact);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:283", "- 预约时间:", this.orderInfo.bookingTime);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:284", "- 结束时间:", this.orderInfo.endTime);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:285", "- 原始响应:", response);
          if (!this.orderInfo.status) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:289", "虚拟订单状态为空！");
            this.orderInfo.status = "PENDING";
          }
          if (!this.orderInfo.paymentAmount && !this.orderInfo.totalPrice) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:295", "虚拟订单金额为空！");
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/payment/index.vue:300", "加载订单信息失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "error"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } finally {
        this.loading = false;
      }
    },
    // 选择支付方式
    selectMethod(method) {
      this.selectedMethod = method;
    },
    // 处理支付
    async handlePayment() {
      if (!this.canPay)
        return;
      try {
        this.paying = true;
        common_vendor.index.showLoading({ title: "支付中..." });
        await new Promise((resolve) => setTimeout(resolve, 2e3));
        const isSuccess = Math.random() > 0.2;
        common_vendor.index.hideLoading();
        if (isSuccess) {
          const response = await api_payment.payOrder(this.orderId, this.selectedMethod);
          if (response.success) {
            let successUrl = `/pages/payment/success?orderId=${this.orderId}`;
            if (this.fromPage) {
              successUrl += `&from=${this.fromPage}`;
            }
            common_vendor.index.redirectTo({
              url: successUrl
            });
          } else {
            throw new Error(response.message || "支付失败");
          }
        } else {
          throw new Error("支付失败，请检查账户余额或重试");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/payment/index.vue:359", "支付失败:", error);
        common_vendor.index.redirectTo({
          url: `/pages/payment/failed?orderId=${this.orderId}&reason=${encodeURIComponent(error.message)}`
        });
      } finally {
        this.paying = false;
      }
    },
    // 处理结果操作（保留用于其他用途）
    handleResultAction() {
      this.$refs.resultPopup.close();
      if (this.paymentResult.success) {
        common_vendor.index.redirectTo({
          url: "/pages/booking/list"
        });
      }
    },
    // 格式化日期时间
    formatDateTime(date, startTime, endTime) {
      if (!date || !startTime)
        return "未设置";
      let dateStr = "";
      if (typeof date === "string" && date.includes("-")) {
        const [year, month, day] = date.split("-");
        dateStr = `${month}-${day}`;
      } else {
        const dateObj = new Date(date);
        dateStr = dateObj.toLocaleDateString("zh-CN", {
          month: "2-digit",
          day: "2-digit"
        });
      }
      const timeStr = endTime ? `${startTime}-${endTime}` : startTime;
      return `${dateStr} ${timeStr}`;
    },
    // 返回
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 格式化订单时间（处理虚拟订单和普通订单的差异）
    formatOrderDateTime() {
      if (!this.orderInfo)
        return "未设置";
      if (this.orderInfo.isVirtualOrder) {
        const startTime = this.orderInfo.bookingTime;
        const endTime = this.orderInfo.endTime;
        common_vendor.index.__f__("log", "at pages/payment/index.vue:422", "虚拟订单时间格式化 - 原始数据:", { startTime, endTime });
        if (!startTime) {
          common_vendor.index.__f__("warn", "at pages/payment/index.vue:425", "虚拟订单开始时间为空");
          return "未设置";
        }
        try {
          let startDateTime, endDateTime;
          if (typeof startTime === "string") {
            let isoTime = startTime;
            if (startTime.includes(" ") && !startTime.includes("T")) {
              isoTime = startTime.replace(" ", "T");
            }
            startDateTime = new Date(isoTime);
            common_vendor.index.__f__("log", "at pages/payment/index.vue:441", "支付页面时间转换 - 原始:", startTime, "转换后:", isoTime, "解析结果:", startDateTime);
          } else {
            startDateTime = new Date(startTime);
          }
          if (endTime) {
            if (typeof endTime === "string") {
              let isoEndTime = endTime;
              if (endTime.includes(" ") && !endTime.includes("T")) {
                isoEndTime = endTime.replace(" ", "T");
              }
              endDateTime = new Date(isoEndTime);
              common_vendor.index.__f__("log", "at pages/payment/index.vue:453", "支付页面结束时间转换 - 原始:", endTime, "转换后:", isoEndTime, "解析结果:", endDateTime);
            } else {
              endDateTime = new Date(endTime);
            }
          }
          if (isNaN(startDateTime.getTime())) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:461", "无效的开始时间:", startTime);
            return "时间格式错误";
          }
          const dateStr = startDateTime.toLocaleDateString("zh-CN", {
            month: "2-digit",
            day: "2-digit"
          });
          const startTimeStr = startDateTime.toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false
          });
          let endTimeStr = "";
          if (endDateTime && !isNaN(endDateTime.getTime())) {
            endTimeStr = endDateTime.toLocaleTimeString("zh-CN", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false
            });
          }
          const result = `${dateStr} ${startTimeStr}${endTimeStr ? "-" + endTimeStr : ""}`;
          common_vendor.index.__f__("log", "at pages/payment/index.vue:489", "虚拟订单时间格式化结果:", result);
          return result;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/payment/index.vue:492", "虚拟订单时间格式化错误:", error, "原始数据:", { startTime, endTime });
          return "时间格式错误";
        }
      } else {
        return this.formatDateTime(this.orderInfo.bookingDate, this.orderInfo.startTime, this.orderInfo.endTime);
      }
    },
    // 获取队伍名称
    getTeamName() {
      if (!this.orderInfo)
        return "未设置";
      if (this.orderInfo.isVirtualOrder) {
        return this.orderInfo.applicantTeamName || "未设置";
      } else {
        return this.orderInfo.teamName || "未设置";
      }
    },
    // 获取联系方式
    getContactInfo() {
      if (!this.orderInfo)
        return "未设置";
      if (this.orderInfo.isVirtualOrder) {
        return this.orderInfo.applicantContact || "未设置";
      } else {
        return this.orderInfo.contactInfo || "未设置";
      }
    },
    // 获取订单金额
    getOrderAmount() {
      if (!this.orderInfo)
        return "0.00";
      const amount = this.orderInfo.isVirtualOrder ? this.orderInfo.paymentAmount : this.orderInfo.totalPrice;
      return (amount == null ? void 0 : amount.toFixed(2)) || "0.00";
    },
    // 获取预约类型文本
    getBookingTypeText() {
      if (!this.orderInfo)
        return "未知";
      if (this.orderInfo.isVirtualOrder) {
        return "拼场";
      }
      return this.orderInfo.bookingType === "SHARED" ? "拼场" : "独享";
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.loading
  }, $data.loading ? {} : $data.orderInfo ? common_vendor.e({
    d: common_vendor.t($data.orderInfo.orderNo),
    e: common_vendor.t($data.orderInfo.venueName),
    f: common_vendor.t($options.formatOrderDateTime()),
    g: common_vendor.t($options.getBookingTypeText()),
    h: $data.orderInfo.bookingType === "SHARED" || $data.orderInfo.isVirtualOrder
  }, $data.orderInfo.bookingType === "SHARED" || $data.orderInfo.isVirtualOrder ? {
    i: common_vendor.t($options.getTeamName())
  } : {}, {
    j: common_vendor.t($options.getContactInfo()),
    k: common_vendor.t($options.getOrderAmount()),
    l: $data.orderInfo.bookingType === "SHARED" || $data.orderInfo.isVirtualOrder
  }, $data.orderInfo.bookingType === "SHARED" || $data.orderInfo.isVirtualOrder ? {} : {}) : {}, {
    c: $data.orderInfo,
    m: $data.selectedMethod === "wechat"
  }, $data.selectedMethod === "wechat" ? {} : {}, {
    n: $data.selectedMethod === "wechat" ? 1 : "",
    o: common_vendor.o(($event) => $options.selectMethod("wechat")),
    p: $data.selectedMethod === "alipay"
  }, $data.selectedMethod === "alipay" ? {} : {}, {
    q: $data.selectedMethod === "alipay" ? 1 : "",
    r: common_vendor.o(($event) => $options.selectMethod("alipay")),
    s: common_vendor.t($options.getOrderAmount()),
    t: common_vendor.t($options.payButtonText),
    v: !$options.canPay ? 1 : "",
    w: !$options.canPay,
    x: common_vendor.o((...args) => $options.handlePayment && $options.handlePayment(...args)),
    y: $data.paymentResult.success
  }, $data.paymentResult.success ? {} : {}, {
    z: common_vendor.t($data.paymentResult.title),
    A: common_vendor.t($data.paymentResult.message),
    B: common_vendor.t($data.paymentResult.buttonText),
    C: common_vendor.o((...args) => $options.handleResultAction && $options.handleResultAction(...args)),
    D: common_vendor.sr("resultPopup", "7695f594-0"),
    E: common_vendor.p({
      type: "center",
      ["mask-click"]: false
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7695f594"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/payment/index.js.map
