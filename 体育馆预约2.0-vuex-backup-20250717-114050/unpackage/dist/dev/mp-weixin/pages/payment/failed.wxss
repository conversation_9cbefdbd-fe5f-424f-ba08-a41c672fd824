
.failed-container.data-v-365f12f6 {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.failed-icon-container.data-v-365f12f6 {
  text-align: center;
  margin-bottom: 60rpx;
}
.failed-icon.data-v-365f12f6 {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}
.failed-title.data-v-365f12f6 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}
.failed-subtitle.data-v-365f12f6 {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.order-info.data-v-365f12f6 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  width: 100%;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}
.info-item.data-v-365f12f6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-item.data-v-365f12f6:last-child {
  border-bottom: none;
}
.info-label.data-v-365f12f6 {
  font-size: 28rpx;
  color: #666;
}
.info-value.data-v-365f12f6 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.info-value.amount.data-v-365f12f6 {
  color: #ff6b35;
  font-weight: bold;
  font-size: 32rpx;
}
.failure-reasons.data-v-365f12f6 {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  width: 100%;
  margin-bottom: 40rpx;
}
.reasons-title.data-v-365f12f6 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}
.reason-item.data-v-365f12f6 {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 10rpx;
}
.action-buttons.data-v-365f12f6 {
  display: flex;
  gap: 20rpx;
  width: 100%;
  margin-bottom: 40rpx;
}
.btn.data-v-365f12f6 {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-primary.data-v-365f12f6 {
  background: #ff6b35;
  color: white;
}
.btn-secondary.data-v-365f12f6 {
  background: white;
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
}
.contact-service.data-v-365f12f6 {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  width: 100%;
}
.contact-title.data-v-365f12f6 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
  text-align: center;
}
.contact-buttons.data-v-365f12f6 {
  display: flex;
  gap: 20rpx;
}
.contact-btn.data-v-365f12f6 {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}
.contact-icon.data-v-365f12f6 {
  font-size: 40rpx;
}
.contact-text.data-v-365f12f6 {
  font-size: 24rpx;
  color: white;
}
