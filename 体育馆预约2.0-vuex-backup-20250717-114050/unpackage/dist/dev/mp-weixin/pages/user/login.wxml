<view class="container data-v-6163e5ce"><view class="header data-v-6163e5ce"><image src="{{a}}" class="logo data-v-6163e5ce" mode="aspectFit"/><text class="app-name data-v-6163e5ce">体育馆预约</text><text class="app-slogan data-v-6163e5ce">让运动更简单</text></view><view class="login-form data-v-6163e5ce"><view class="tab-bar data-v-6163e5ce"><view class="{{['tab-item', 'data-v-6163e5ce', b && 'active']}}" bindtap="{{c}}"> 密码登录 </view><view class="{{['tab-item', 'data-v-6163e5ce', d && 'active']}}" bindtap="{{e}}"> 验证码登录 </view></view><view class="form-item data-v-6163e5ce"><view class="input-wrapper data-v-6163e5ce"><text class="input-icon data-v-6163e5ce">📱</text><input class="input-field data-v-6163e5ce" type="number" placeholder="请输入手机号" maxlength="11" value="{{f}}" bindinput="{{g}}"/></view></view><view wx:if="{{h}}" class="form-item data-v-6163e5ce"><view class="input-wrapper data-v-6163e5ce"><text class="input-icon data-v-6163e5ce">🔒</text><input class="input-field data-v-6163e5ce" password="{{i}}" placeholder="请输入密码" value="{{j}}" bindinput="{{k}}"/><text class="password-toggle data-v-6163e5ce" bindtap="{{m}}">{{l}}</text></view></view><view wx:if="{{n}}" class="form-item data-v-6163e5ce"><view class="input-wrapper data-v-6163e5ce"><text class="input-icon data-v-6163e5ce">💬</text><input class="input-field data-v-6163e5ce" type="number" placeholder="请输入验证码" maxlength="6" value="{{o}}" bindinput="{{p}}"/><button class="sms-btn data-v-6163e5ce" disabled="{{r}}" bindtap="{{s}}">{{q}}</button></view></view><button class="login-btn data-v-6163e5ce" disabled="{{t}}" bindtap="{{v}}"> 登录 </button><view wx:if="{{w}}" class="forgot-password data-v-6163e5ce"><text class="data-v-6163e5ce" bindtap="{{x}}">忘记密码？</text></view></view><view class="other-login data-v-6163e5ce"><view class="divider data-v-6163e5ce"><text class="divider-text data-v-6163e5ce">其他登录方式</text></view><view class="social-login data-v-6163e5ce"><view class="social-item data-v-6163e5ce" bindtap="{{y}}"><text class="social-icon data-v-6163e5ce">💬</text><text class="social-text data-v-6163e5ce">微信登录</text></view><view class="social-item data-v-6163e5ce" bindtap="{{z}}"><text class="social-icon data-v-6163e5ce">🍎</text><text class="social-text data-v-6163e5ce">Apple登录</text></view></view></view><view class="footer data-v-6163e5ce"><text class="footer-text data-v-6163e5ce">还没有账号？</text><text class="footer-link data-v-6163e5ce" bindtap="{{A}}">立即注册</text></view><view class="agreement data-v-6163e5ce"><text class="agreement-text data-v-6163e5ce">登录即表示同意</text><text class="agreement-link data-v-6163e5ce" bindtap="{{B}}">《用户协议》</text><text class="agreement-text data-v-6163e5ce">和</text><text class="agreement-link data-v-6163e5ce" bindtap="{{C}}">《隐私政策》</text></view></view>