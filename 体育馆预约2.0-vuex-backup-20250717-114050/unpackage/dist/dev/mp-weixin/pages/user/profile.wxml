<view class="container data-v-036958a5"><view class="header data-v-036958a5"><view class="user-info data-v-036958a5"><view class="avatar-wrapper data-v-036958a5" bindtap="{{b}}"><image src="{{a}}" class="avatar data-v-036958a5" mode="aspectFill"/><view class="avatar-edit data-v-036958a5"><text class="edit-icon data-v-036958a5">📷</text></view></view><view class="user-details data-v-036958a5"><text class="nickname data-v-036958a5">{{c}}</text><text class="username data-v-036958a5">用户名: {{d}}</text><text class="phone data-v-036958a5">{{e}}</text></view><view class="edit-btn data-v-036958a5" bindtap="{{f}}"><text class="edit-text data-v-036958a5">编辑</text></view></view><view class="stats data-v-036958a5"><view class="stat-item data-v-036958a5" bindtap="{{h}}"><text class="stat-number data-v-036958a5">{{g}}</text><text class="stat-label data-v-036958a5">总预约</text></view><view class="stat-item data-v-036958a5" bindtap="{{j}}"><text class="stat-number data-v-036958a5">{{i}}</text><text class="stat-label data-v-036958a5">拼场次数</text></view></view></view><view class="menu-section data-v-036958a5"><view class="menu-group data-v-036958a5"><view class="group-title data-v-036958a5"><text class="title-text data-v-036958a5">我的订单</text></view><view class="menu-item data-v-036958a5" bindtap="{{m}}"><view class="item-left data-v-036958a5"><text class="item-icon data-v-036958a5">📅</text><text class="item-text data-v-036958a5">我的预约</text></view><view class="item-right data-v-036958a5"><text wx:if="{{k}}" class="badge data-v-036958a5">{{l}}</text><text class="arrow data-v-036958a5">></text></view></view><view class="menu-item data-v-036958a5" bindtap="{{p}}"><view class="item-left data-v-036958a5"><text class="item-icon data-v-036958a5">👥</text><text class="item-text data-v-036958a5">我的拼场</text></view><view class="item-right data-v-036958a5"><text wx:if="{{n}}" class="badge data-v-036958a5">{{o}}</text><text class="arrow data-v-036958a5">></text></view></view></view><view class="menu-group data-v-036958a5"><view class="group-title data-v-036958a5"><text class="title-text data-v-036958a5">拼场申请</text></view><view class="menu-item data-v-036958a5" bindtap="{{s}}"><view class="item-left data-v-036958a5"><text class="item-icon data-v-036958a5">📝</text><text class="item-text data-v-036958a5">我的申请</text></view><view class="item-right data-v-036958a5"><text wx:if="{{q}}" class="badge data-v-036958a5">{{r}}</text><text class="arrow data-v-036958a5">></text></view></view><view class="menu-item data-v-036958a5" bindtap="{{w}}"><view class="item-left data-v-036958a5"><text class="item-icon data-v-036958a5">📬</text><text class="item-text data-v-036958a5">收到的申请</text></view><view class="item-right data-v-036958a5"><text wx:if="{{t}}" class="badge data-v-036958a5">{{v}}</text><text class="arrow data-v-036958a5">></text></view></view></view><view class="menu-group data-v-036958a5"><view class="group-title data-v-036958a5"><text class="title-text data-v-036958a5">设置</text></view><view class="menu-item data-v-036958a5" bindtap="{{x}}"><view class="item-left data-v-036958a5"><text class="item-icon data-v-036958a5">🚪</text><text class="item-text data-v-036958a5">退出登录</text></view><view class="item-right data-v-036958a5"><text class="arrow data-v-036958a5">></text></view></view></view></view><uni-popup wx:if="{{C}}" class="r data-v-036958a5" u-s="{{['d']}}" u-r="logoutPopup" u-i="036958a5-0" bind:__l="__l" u-p="{{C}}"><uni-popup-dialog wx:if="{{A}}" class="data-v-036958a5" bindclose="{{y}}" bindconfirm="{{z}}" u-i="036958a5-1,036958a5-0" bind:__l="__l" u-p="{{A}}"></uni-popup-dialog></uni-popup></view>