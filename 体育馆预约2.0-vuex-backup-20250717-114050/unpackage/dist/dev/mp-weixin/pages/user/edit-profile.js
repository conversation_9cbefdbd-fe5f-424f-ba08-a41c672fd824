"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "EditProfile",
  data() {
    return {
      formData: {
        avatar: "",
        username: "",
        nickname: "",
        gender: "",
        birthday: "",
        email: "",
        bio: "",
        sportsPreferences: [],
        city: "",
        showBookingHistory: true,
        allowSharingInvite: true,
        receiveNotifications: true
      },
      genderOptions: ["男", "女", "保密"],
      sportsOptions: ["篮球", "足球", "羽毛球", "乒乓球", "网球", "游泳", "健身", "瑜伽"],
      regionValue: [],
      // 密码修改表单
      passwordForm: {
        oldPassword: "",
        newPassword: "",
        confirmPassword: ""
      }
    };
  },
  computed: {
    ...common_vendor.mapState("user", ["userInfo"]),
    genderIndex() {
      return this.genderOptions.indexOf(this.formData.gender);
    }
  },
  onLoad() {
    this.initFormData();
  },
  onShow() {
    this.initFormData();
  },
  methods: {
    ...common_vendor.mapActions("user", [
      "updateUserInfo",
      "uploadAvatar",
      "changeUserPassword",
      "getUserInfo"
    ]),
    // 初始化表单数据
    async initFormData() {
      common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:286", "[EditProfile] 初始化表单数据，用户信息:", this.userInfo);
      if (!this.userInfo) {
        common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:290", "[EditProfile] 用户信息为空，尝试重新获取");
        try {
          const result = await this.getUserInfo();
          common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:293", "[EditProfile] 重新获取用户信息结果:", result);
          common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:294", "[EditProfile] 重新获取后的用户信息:", this.userInfo);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/user/edit-profile.vue:296", "[EditProfile] 获取用户信息失败:", error);
          common_vendor.index.showToast({
            title: "获取用户信息失败",
            icon: "error"
          });
          return;
        }
      }
      if (this.userInfo) {
        this.formData = {
          avatar: this.userInfo.avatar || "",
          username: this.userInfo.username || "",
          nickname: this.userInfo.nickname || this.userInfo.username || "未设置昵称",
          gender: this.userInfo.gender || "",
          birthday: this.userInfo.birthday || "",
          email: this.userInfo.email || "",
          bio: this.userInfo.bio || "",
          sportsPreferences: this.userInfo.sportsPreferences || [],
          city: this.userInfo.city || "",
          showBookingHistory: this.userInfo.showBookingHistory !== false,
          allowSharingInvite: this.userInfo.allowSharingInvite !== false,
          receiveNotifications: this.userInfo.receiveNotifications !== false
        };
        if (this.userInfo.city) {
          this.regionValue = this.userInfo.city.split(" ");
        }
        common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:326", "[EditProfile] 表单数据已初始化:", this.formData);
      } else {
        common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:328", "[EditProfile] 用户信息为空，使用默认表单数据");
        this.formData = {
          avatar: "",
          username: "用户" + Date.now().toString().slice(-6),
          // 生成默认用户名
          nickname: "未设置昵称",
          gender: "",
          birthday: "",
          email: "",
          bio: "",
          sportsPreferences: [],
          city: "",
          showBookingHistory: true,
          allowSharingInvite: true,
          receiveNotifications: true
        };
        common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:344", "[EditProfile] 默认表单数据已设置:", this.formData);
      }
    },
    // 格式化手机号
    formatPhone(phone) {
      if (!phone)
        return "";
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    },
    // 更换头像
    changeAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.uploadUserAvatar(tempFilePath);
        }
      });
    },
    // 上传头像
    async uploadUserAvatar(filePath) {
      try {
        common_vendor.index.showLoading({ title: "上传中..." });
        const result = await this.uploadAvatar(filePath);
        this.formData.avatar = result.url;
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "头像更新成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/user/edit-profile.vue:383", "上传头像失败:", error);
        common_vendor.index.showToast({
          title: "上传失败",
          icon: "error"
        });
      }
    },
    // 性别变化
    onGenderChange(e) {
      this.formData.gender = this.genderOptions[e.detail.value];
    },
    // 生日变化
    onBirthdayChange(e) {
      this.formData.birthday = e.detail.value;
    },
    // 地区变化
    onRegionChange(e) {
      this.regionValue = e.detail.value;
      this.formData.city = e.detail.value.join(" ");
    },
    // 切换运动偏好
    toggleSport(sport) {
      const index = this.formData.sportsPreferences.indexOf(sport);
      if (index > -1) {
        this.formData.sportsPreferences.splice(index, 1);
      } else {
        this.formData.sportsPreferences.push(sport);
      }
    },
    // 隐私设置变化
    onPrivacyChange(key, e) {
      this.formData[key] = e.detail.value;
    },
    // 保存个人资料
    async saveProfile() {
      var _a, _b, _c, _d, _e;
      if (!this.validateForm())
        return;
      let loadingShown = false;
      try {
        common_vendor.index.showLoading({ title: "保存中..." });
        loadingShown = true;
        common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:435", "[EditProfile] 准备保存的表单数据:", JSON.stringify(this.formData, null, 2));
        const cleanData = {
          username: ((_a = this.formData.username) == null ? void 0 : _a.trim()) || "",
          nickname: ((_b = this.formData.nickname) == null ? void 0 : _b.trim()) || "",
          gender: this.formData.gender || "",
          birthday: this.formData.birthday || "",
          email: ((_c = this.formData.email) == null ? void 0 : _c.trim()) || "",
          bio: ((_d = this.formData.bio) == null ? void 0 : _d.trim()) || "",
          city: ((_e = this.formData.city) == null ? void 0 : _e.trim()) || "",
          sportsPreferences: this.formData.sportsPreferences || [],
          showBookingHistory: Boolean(this.formData.showBookingHistory),
          allowSharingInvite: Boolean(this.formData.allowSharingInvite),
          receiveNotifications: Boolean(this.formData.receiveNotifications)
        };
        Object.keys(cleanData).forEach((key) => {
          if (cleanData[key] === "" && key !== "bio") {
            delete cleanData[key];
          }
        });
        common_vendor.index.__f__("log", "at pages/user/edit-profile.vue:459", "[EditProfile] 清理后的数据:", JSON.stringify(cleanData, null, 2));
        await this.updateUserInfo(cleanData);
        if (loadingShown) {
          common_vendor.index.hideLoading();
          loadingShown = false;
        }
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        if (loadingShown) {
          common_vendor.index.hideLoading();
          loadingShown = false;
        }
        common_vendor.index.__f__("error", "at pages/user/edit-profile.vue:483", "保存失败:", error);
        common_vendor.index.showToast({
          title: error.message || "保存失败",
          icon: "error"
        });
      }
    },
    // 验证表单
    validateForm() {
      if (!this.formData.username.trim()) {
        common_vendor.index.showToast({
          title: "请输入用户名",
          icon: "error"
        });
        return false;
      }
      if (!this.formData.nickname.trim()) {
        common_vendor.index.showToast({
          title: "请输入昵称",
          icon: "error"
        });
        return false;
      }
      if (this.formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.formData.email)) {
        common_vendor.index.showToast({
          title: "邮箱格式不正确",
          icon: "error"
        });
        return false;
      }
      return true;
    },
    // 显示密码修改弹窗
    showPasswordDialog() {
      this.passwordForm = {
        oldPassword: "",
        newPassword: "",
        confirmPassword: ""
      };
      this.$refs.passwordPopup.open();
    },
    // 关闭密码修改弹窗
    closePasswordDialog() {
      this.$refs.passwordPopup.close();
    },
    // 修改密码
    async changePassword() {
      if (!this.passwordForm.oldPassword) {
        common_vendor.index.showToast({
          title: "请输入当前密码",
          icon: "error"
        });
        return;
      }
      if (!this.passwordForm.newPassword) {
        common_vendor.index.showToast({
          title: "请输入新密码",
          icon: "error"
        });
        return;
      }
      if (this.passwordForm.newPassword.length < 6) {
        common_vendor.index.showToast({
          title: "新密码至少6位",
          icon: "error"
        });
        return;
      }
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        common_vendor.index.showToast({
          title: "两次密码输入不一致",
          icon: "error"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "修改中..." });
        await this.changeUserPassword({
          oldPassword: this.passwordForm.oldPassword,
          newPassword: this.passwordForm.newPassword
        });
        common_vendor.index.hideLoading();
        this.closePasswordDialog();
        common_vendor.index.showToast({
          title: "密码修改成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/user/edit-profile.vue:588", "修改密码失败:", error);
        common_vendor.index.showToast({
          title: error.message || "修改失败",
          icon: "error"
        });
      }
    },
    // 返回
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return {
    a: $data.formData.avatar || "/static/images/default-avatar.svg",
    b: common_vendor.o((...args) => $options.changeAvatar && $options.changeAvatar(...args)),
    c: $data.formData.username,
    d: common_vendor.o(($event) => $data.formData.username = $event.detail.value),
    e: $data.formData.nickname,
    f: common_vendor.o(($event) => $data.formData.nickname = $event.detail.value),
    g: common_vendor.t($data.formData.gender || "请选择性别"),
    h: $data.genderOptions,
    i: $options.genderIndex,
    j: common_vendor.o((...args) => $options.onGenderChange && $options.onGenderChange(...args)),
    k: common_vendor.t($data.formData.birthday || "请选择生日"),
    l: $data.formData.birthday,
    m: common_vendor.o((...args) => $options.onBirthdayChange && $options.onBirthdayChange(...args)),
    n: common_vendor.t($options.formatPhone(((_a = _ctx.userInfo) == null ? void 0 : _a.phone) || "")),
    o: $data.formData.email,
    p: common_vendor.o(($event) => $data.formData.email = $event.detail.value),
    q: common_vendor.o((...args) => $options.showPasswordDialog && $options.showPasswordDialog(...args)),
    r: $data.formData.bio,
    s: common_vendor.o(($event) => $data.formData.bio = $event.detail.value),
    t: common_vendor.f($data.sportsOptions, (sport, k0, i0) => {
      return {
        a: common_vendor.t(sport),
        b: $data.formData.sportsPreferences.includes(sport) ? 1 : "",
        c: sport,
        d: common_vendor.o(($event) => $options.toggleSport(sport), sport)
      };
    }),
    v: common_vendor.t($data.formData.city || "请选择城市"),
    w: $data.regionValue,
    x: common_vendor.o((...args) => $options.onRegionChange && $options.onRegionChange(...args)),
    y: $data.formData.showBookingHistory,
    z: common_vendor.o(($event) => $options.onPrivacyChange("showBookingHistory", $event)),
    A: $data.formData.allowSharingInvite,
    B: common_vendor.o(($event) => $options.onPrivacyChange("allowSharingInvite", $event)),
    C: $data.formData.receiveNotifications,
    D: common_vendor.o(($event) => $options.onPrivacyChange("receiveNotifications", $event)),
    E: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    F: common_vendor.o((...args) => $options.saveProfile && $options.saveProfile(...args)),
    G: common_vendor.o((...args) => $options.closePasswordDialog && $options.closePasswordDialog(...args)),
    H: $data.passwordForm.oldPassword,
    I: common_vendor.o(($event) => $data.passwordForm.oldPassword = $event.detail.value),
    J: $data.passwordForm.newPassword,
    K: common_vendor.o(($event) => $data.passwordForm.newPassword = $event.detail.value),
    L: $data.passwordForm.confirmPassword,
    M: common_vendor.o(($event) => $data.passwordForm.confirmPassword = $event.detail.value),
    N: common_vendor.o((...args) => $options.closePasswordDialog && $options.closePasswordDialog(...args)),
    O: common_vendor.o((...args) => $options.changePassword && $options.changePassword(...args)),
    P: common_vendor.sr("passwordPopup", "af41d2d2-0"),
    Q: common_vendor.p({
      type: "center"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-af41d2d2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/edit-profile.js.map
