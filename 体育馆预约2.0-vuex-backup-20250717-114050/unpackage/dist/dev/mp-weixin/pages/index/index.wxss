/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-1cf27b2a {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.dev-toolbar.data-v-1cf27b2a {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}
.dev-toolbar .dev-title.data-v-1cf27b2a {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  display: block;
}
.dev-toolbar .dev-actions.data-v-1cf27b2a {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}
.dev-toolbar .dev-actions .dev-btn.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.dev-toolbar .dev-actions .dev-btn.data-v-1cf27b2a:active {
  background: rgba(255, 255, 255, 0.3);
}
.banner-section.data-v-1cf27b2a {
  height: 400rpx;
}
.banner-section .banner-swiper.data-v-1cf27b2a {
  height: 100%;
}
.banner-section .banner-swiper .banner-image.data-v-1cf27b2a {
  width: 100%;
  height: 100%;
}
.quick-actions.data-v-1cf27b2a {
  display: flex;
  flex-wrap: wrap;
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  gap: 20rpx;
}
.quick-actions .action-item.data-v-1cf27b2a {
  width: calc(25% - 15rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.quick-actions .action-item .action-icon.data-v-1cf27b2a {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  font-size: 36rpx;
}
.quick-actions .action-item .action-text.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #333333;
}
.quick-actions .action-item.pinia-test-item .pinia-test-icon.data-v-1cf27b2a {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  animation: pulse-1cf27b2a 2s infinite;
}
.quick-actions .action-item.pinia-test-item .action-text.data-v-1cf27b2a {
  color: #667eea;
  font-weight: bold;
}
@keyframes pulse-1cf27b2a {
0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
}
70% {
    box-shadow: 0 0 0 10rpx rgba(102, 126, 234, 0);
}
100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
}
}
.floating-test-btn.data-v-1cf27b2a {
  position: fixed;
  right: 30rpx;
  bottom: 150rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
  z-index: 999;
  animation: pulse-1cf27b2a 2s infinite;
}
.floating-test-btn .floating-btn-text.data-v-1cf27b2a {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
}
.floating-test-btn.data-v-1cf27b2a:active {
  transform: scale(0.95);
}
.section.data-v-1cf27b2a {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}
.section .section-header.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.section .section-header .section-title.data-v-1cf27b2a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.section .section-header .section-more.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #ff6b35;
}
.venue-list .venue-card.data-v-1cf27b2a {
  display: flex;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}
.venue-list .venue-card.data-v-1cf27b2a:last-child {
  margin-bottom: 0;
}
.venue-list .venue-card .venue-image.data-v-1cf27b2a {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.venue-list .venue-card .venue-info.data-v-1cf27b2a {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.venue-list .venue-card .venue-info .venue-name.data-v-1cf27b2a {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.venue-list .venue-card .venue-info .venue-location.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 16rpx;
}
.venue-list .venue-card .venue-info .venue-price.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.venue-list .venue-card .venue-info .venue-price .price-text.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: 600;
}
.venue-list .venue-card .venue-info .venue-price .venue-status.data-v-1cf27b2a {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.venue-list .venue-card .venue-info .venue-price .venue-status.status-available.data-v-1cf27b2a {
  background-color: #e6f7ff;
  color: #1890ff;
}
.venue-list .venue-card .venue-info .venue-price .venue-status.status-maintenance.data-v-1cf27b2a {
  background-color: #fff7e6;
  color: #fa8c16;
}
.venue-list .venue-card .venue-info .venue-price .venue-status.status-occupied.data-v-1cf27b2a {
  background-color: #fff2f0;
  color: #ff4d4f;
}
.test-button.data-v-1cf27b2a {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  z-index: 999;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}
.sharing-list .sharing-card.data-v-1cf27b2a {
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.sharing-list .sharing-card.data-v-1cf27b2a:last-child {
  margin-bottom: 0;
}
.sharing-list .sharing-card .sharing-header.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.sharing-list .sharing-card .sharing-header .sharing-venue.data-v-1cf27b2a {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.sharing-list .sharing-card .sharing-header .sharing-time.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #666666;
}
.sharing-list .sharing-card .sharing-info.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.sharing-list .sharing-card .sharing-info .sharing-team.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #333333;
}
.sharing-list .sharing-card .sharing-info .sharing-participants.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #666666;
}
.sharing-list .sharing-card .sharing-price.data-v-1cf27b2a {
  text-align: right;
}
.sharing-list .sharing-card .sharing-price .price-per-person.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: 600;
}