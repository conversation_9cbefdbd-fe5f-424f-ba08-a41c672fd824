<view class="test-container data-v-53050e82"><view class="header data-v-53050e82"><text class="title data-v-53050e82">订单状态流程测试</text><text class="subtitle data-v-53050e82">验证订单状态转换和业务规则</text></view><view class="test-scenarios data-v-53050e82"><view class="section-title data-v-53050e82">测试场景</view><view class="scenario-list data-v-53050e82"><view wx:for="{{a}}" wx:for-item="scenario" wx:key="c" class="{{['scenario-item', 'data-v-53050e82', scenario.d && 'active']}}" bindtap="{{scenario.e}}"><text class="scenario-name data-v-53050e82">{{scenario.a}}</text><text class="scenario-desc data-v-53050e82">{{scenario.b}}</text></view></view></view><view wx:if="{{b}}" class="current-test data-v-53050e82"><view class="section-title data-v-53050e82">当前测试: {{c}}</view><view class="test-steps data-v-53050e82"><view wx:for="{{d}}" wx:for-item="step" wx:key="g" class="{{['step-item', 'data-v-53050e82', step.h && 'completed', step.i && 'active', step.j && 'failed']}}"><view class="step-header data-v-53050e82"><text class="step-number data-v-53050e82">{{step.a}}</text><text class="step-title data-v-53050e82">{{step.b}}</text><text class="step-status data-v-53050e82">{{step.c}}</text></view><text class="step-desc data-v-53050e82">{{step.d}}</text><button wx:if="{{step.e}}" class="step-action-btn data-v-53050e82" bindtap="{{step.f}}"> 执行步骤 </button></view></view><view wx:if="{{e}}" class="current-order data-v-53050e82"><view class="section-title data-v-53050e82">当前测试订单</view><view class="order-info data-v-53050e82"><text class="order-id data-v-53050e82">订单ID: {{f}}</text><text class="order-status data-v-53050e82">状态: {{g}}</text><button class="refresh-btn data-v-53050e82" bindtap="{{h}}">刷新状态</button></view></view><view class="test-results data-v-53050e82"><view class="section-title data-v-53050e82">测试结果</view><view wx:for="{{i}}" wx:for-item="result" wx:key="f" class="result-item data-v-53050e82"><text class="result-time data-v-53050e82">{{result.a}}</text><text class="result-action data-v-53050e82">{{result.b}}</text><text class="{{['result-status', 'data-v-53050e82', result.d]}}">{{result.c}}</text><text class="result-message data-v-53050e82">{{result.e}}</text></view></view></view><view class="status-flow data-v-53050e82"><view class="section-title data-v-53050e82">订单状态流程图</view><view class="flow-diagram data-v-53050e82"><text class="flow-text data-v-53050e82"> 普通订单: PENDING → PAID → CONFIRMED → VERIFIED → COMPLETED 拼场订单: PENDING → OPEN → SHARING_SUCCESS → CONFIRMED → VERIFIED → COMPLETED 取消流程: 任何状态 → CANCELLED 过期流程: PENDING/CONFIRMED → EXPIRED </text></view></view></view>