<view class="test-container data-v-7c79bd5d"><view class="test-header data-v-7c79bd5d"><text class="test-title data-v-7c79bd5d">订单详情API测试</text></view><view class="test-section data-v-7c79bd5d"><text class="section-title data-v-7c79bd5d">测试订单ID: 238</text><button bindtap="{{a}}" class="test-btn data-v-7c79bd5d">测试获取订单详情</button></view><view class="test-section data-v-7c79bd5d"><text class="section-title data-v-7c79bd5d">清除缓存</text><button bindtap="{{b}}" class="test-btn data-v-7c79bd5d">清除订单缓存</button></view><view class="test-section data-v-7c79bd5d"><text class="section-title data-v-7c79bd5d">测试结果</text><view class="result-container data-v-7c79bd5d"><text class="result-text data-v-7c79bd5d">{{c}}</text></view></view><view wx:if="{{d}}" class="test-section data-v-7c79bd5d"><text class="section-title data-v-7c79bd5d">订单数据</text><view class="data-container data-v-7c79bd5d"><text class="data-text data-v-7c79bd5d">{{e}}</text></view></view></view>