"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_sharing = require("../../stores/sharing.js");
const stores_user = require("../../stores/user.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "SharingParticipants",
  data() {
    return {
      orderId: "",
      participants: [],
      loading: false,
      sharingStore: null,
      userStore: null
    };
  },
  computed: {
    isCreator() {
      var _a, _b;
      return ((_a = this.userStore) == null ? void 0 : _a.userId) === ((_b = this.sharingOrder) == null ? void 0 : _b.creatorId);
    },
    sharingOrder() {
      var _a;
      return (_a = this.sharingStore) == null ? void 0 : _a.getSharingOrderDetail;
    }
  },
  onLoad(options) {
    this.orderId = options.orderId;
    this.sharingStore = stores_sharing.useSharingStore();
    this.userStore = stores_user.useUserStore();
    if (this.orderId) {
      this.loadParticipants();
    }
  },
  methods: {
    formatDateTime: utils_helpers.formatDateTime,
    // 加载参与者列表
    async loadParticipants() {
      try {
        this.loading = true;
        await this.sharingStore.getOrderDetail(this.orderId);
        const order = this.sharingStore.getSharingOrderDetail;
        if (order && order.participants) {
          this.participants = order.participants;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/participants.vue:127", "加载参与者失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "error"
        });
      } finally {
        this.loading = false;
      }
    },
    // 同意参与者
    async approveParticipant(participantId) {
      try {
        await this.sharingStore.handleSharingRequest({
          requestId: participantId,
          data: { status: "APPROVED" }
        });
        await this.loadParticipants();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/participants.vue:149", "同意参与者失败:", error);
      }
    },
    // 拒绝参与者
    async rejectParticipant(participantId) {
      try {
        await this.sharingStore.handleSharingRequest({
          requestId: participantId,
          data: { status: "REJECTED" }
        });
        await this.loadParticipants();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/participants.vue:165", "拒绝参与者失败:", error);
      }
    },
    // 移除参与者
    async removeParticipant(participantId) {
      try {
        common_vendor.index.showModal({
          title: "确认移除",
          content: "确定要移除这个参与者吗？",
          success: async (res) => {
            if (res.confirm) {
              await this.sharingStore.removeSharingParticipant({
                sharingId: this.orderId,
                participantId
              });
              await this.loadParticipants();
            }
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/participants.vue:188", "移除参与者失败:", error);
      }
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "PENDING": "待审核",
        "APPROVED": "已同意",
        "REJECTED": "已拒绝",
        "JOINED": "已加入"
      };
      return statusMap[status] || "未知状态";
    },
    // 获取状态样式类
    getStatusClass(status) {
      return `status-${status.toLowerCase()}`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.participants.length === 0 ? {} : {
    c: common_vendor.f($data.participants, (participant, k0, i0) => {
      return common_vendor.e({
        a: participant.avatar || "/static/default-avatar.png",
        b: common_vendor.t(participant.nickname || participant.username),
        c: common_vendor.t(participant.phone || "未提供"),
        d: common_vendor.t($options.formatDateTime(participant.joinTime)),
        e: common_vendor.t($options.getStatusText(participant.status)),
        f: common_vendor.n($options.getStatusClass(participant.status)),
        g: $options.isCreator && participant.status === "PENDING"
      }, $options.isCreator && participant.status === "PENDING" ? {
        h: common_vendor.o(($event) => $options.approveParticipant(participant.id), participant.id),
        i: common_vendor.o(($event) => $options.rejectParticipant(participant.id), participant.id)
      } : {}, {
        j: $options.isCreator && participant.status === "APPROVED"
      }, $options.isCreator && participant.status === "APPROVED" ? {
        k: common_vendor.o(($event) => $options.removeParticipant(participant.id), participant.id)
      } : {}, {
        l: participant.id
      });
    })
  }, {
    b: $data.participants.length === 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-58309905"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/sharing/participants.js.map
