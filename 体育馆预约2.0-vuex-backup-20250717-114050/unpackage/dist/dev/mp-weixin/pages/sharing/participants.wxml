<view class="container data-v-58309905"><view class="header data-v-58309905"><text class="title data-v-58309905">参与者列表</text></view><view wx:if="{{a}}" class="loading data-v-58309905"><text class="data-v-58309905">加载中...</text></view><view wx:elif="{{b}}" class="empty data-v-58309905"><text class="data-v-58309905">暂无参与者</text></view><view wx:else class="participants-list data-v-58309905"><view wx:for="{{c}}" wx:for-item="participant" wx:key="l" class="participant-item data-v-58309905"><view class="participant-info data-v-58309905"><image src="{{participant.a}}" class="avatar data-v-58309905"/><view class="info data-v-58309905"><text class="name data-v-58309905">{{participant.b}}</text><text class="phone data-v-58309905">{{participant.c}}</text><text class="join-time data-v-58309905">加入时间: {{participant.d}}</text></view></view><view class="participant-status data-v-58309905"><text class="{{['status-text', 'data-v-58309905', participant.f]}}">{{participant.e}}</text></view><view wx:if="{{participant.g}}" class="actions data-v-58309905"><button class="btn btn-approve data-v-58309905" bindtap="{{participant.h}}"> 同意 </button><button class="btn btn-reject data-v-58309905" bindtap="{{participant.i}}"> 拒绝 </button></view><view wx:if="{{participant.j}}" class="actions data-v-58309905"><button class="btn btn-remove data-v-58309905" bindtap="{{participant.k}}"> 移除 </button></view></view></view></view>