"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_sharing = require("../../stores/sharing.js");
const stores_booking = require("../../stores/booking.js");
const utils_countdown = require("../../utils/countdown.js");
const utils_helpers = require("../../utils/helpers.js");
const CountdownTimer = () => "../../components/CountdownTimer.js";
const _sfc_main = {
  name: "SharingList",
  components: {
    CountdownTimer
  },
  data() {
    return {
      selectedStatus: "",
      currentSharing: null,
      showMode: "joinable",
      // 'joinable' 可参与的, 'all' 全部
      userApplications: [],
      // 用户的申请记录
      sharingStore: null,
      bookingStore: null,
      // 申请表单数据
      applyForm: {
        teamName: "",
        contactInfo: "",
        message: ""
      },
      // 状态选项
      statusOptions: [
        { label: "开放中", value: "OPEN" },
        { label: "等待对方支付", value: "APPROVED_PENDING_PAYMENT" },
        { label: "拼场成功", value: "SHARING_SUCCESS" },
        { label: "已确认", value: "CONFIRMED" },
        { label: "已取消", value: "CANCELLED" },
        { label: "已过期", value: "EXPIRED" }
      ],
      // 筛选选项
      filterOptions: {
        date: "",
        minPrice: "",
        maxPrice: "",
        participants: ""
      },
      // 日期选项
      dateOptions: [
        { label: "今天", value: "today" },
        { label: "明天", value: "tomorrow" },
        { label: "本周", value: "week" },
        { label: "本月", value: "month" }
      ],
      // 人数选项
      participantsOptions: [
        { label: "2人", value: "2" },
        { label: "4人", value: "4" },
        { label: "6人", value: "6" },
        { label: "8人及以上", value: "8+" }
      ]
    };
  },
  computed: {
    sharingOrders() {
      var _a;
      return ((_a = this.sharingStore) == null ? void 0 : _a.sharingOrders) || [];
    },
    loading() {
      var _a;
      return ((_a = this.sharingStore) == null ? void 0 : _a.loading) || false;
    },
    pagination() {
      var _a;
      return ((_a = this.sharingStore) == null ? void 0 : _a.pagination) || { current: 1, pageSize: 10, total: 0 };
    },
    userInfo() {
      var _a;
      return ((_a = this.bookingStore) == null ? void 0 : _a.userInfo) || {};
    },
    filteredSharingOrders() {
      let orders = this.sharingOrders || [];
      if (this.showMode === "joinable") {
        orders = orders.filter((order) => {
          order.status === "OPEN" && order.currentParticipants < order.maxParticipants && !this.isMySharing(order);
          return order.status === "OPEN" || order.status === "FULL";
        });
      }
      if (this.selectedStatus) {
        orders = orders.filter((order) => order.status === this.selectedStatus);
      }
      if (this.filterOptions.minPrice) {
        orders = orders.filter((order) => (order.totalPrice || 0) >= parseFloat(this.filterOptions.minPrice));
      }
      if (this.filterOptions.maxPrice) {
        orders = orders.filter((order) => (order.totalPrice || 0) <= parseFloat(this.filterOptions.maxPrice));
      }
      if (this.filterOptions.participants) {
        const targetParticipants = parseInt(this.filterOptions.participants);
        if (this.filterOptions.participants === "8+") {
          orders = orders.filter((order) => (order.maxParticipants || 0) >= 8);
        } else {
          orders = orders.filter((order) => (order.maxParticipants || 0) === targetParticipants);
        }
      }
      return orders;
    },
    hasMore() {
      return this.pagination.current < Math.ceil(this.pagination.total / this.pagination.pageSize);
    },
    // 是否可以提交申请
    canSubmitApplication() {
      return this.applyForm.contactInfo.trim().length > 0;
    }
  },
  onLoad() {
    this.sharingStore = stores_sharing.useSharingStore();
    this.bookingStore = stores_booking.useBookingStore();
    common_vendor.index.$on("sharingDataChanged", this.onSharingDataChanged);
    common_vendor.index.$on("orderCancelled", this.onOrderCancelled);
    this.initData();
  },
  onUnload() {
    common_vendor.index.$off("sharingDataChanged", this.onSharingDataChanged);
    common_vendor.index.$off("orderCancelled", this.onOrderCancelled);
  },
  async onShow() {
    await this.refreshData();
    await this.loadUserApplications();
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore();
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:491", "拼场列表页面：开始初始化数据");
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:492", "拼场列表页面：Store状态:", this.$store);
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:493", "拼场列表页面：当前显示模式:", this.showMode);
        const result = this.showMode === "all" ? await this.sharingStore.getAllSharingOrders({ page: 1, pageSize: 10 }) : await this.sharingStore.getJoinableSharingOrders({ page: 1, pageSize: 10 });
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:499", "拼场列表页面：API返回结果:", result);
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:500", "拼场列表页面：Store中的数据:", this.sharingOrders);
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:501", "拼场列表页面：初始化数据完成，订单数量:", ((_a = this.sharingOrders) == null ? void 0 : _a.length) || 0);
        if (this.sharingOrders && this.sharingOrders.length > 0) {
          common_vendor.index.__f__("log", "at pages/sharing/list.vue:505", "第一个分享订单的完整数据结构:", this.sharingOrders[0]);
          common_vendor.index.__f__("log", "at pages/sharing/list.vue:506", "第一个分享订单的价格字段:", {
            pricePerTeam: this.sharingOrders[0].pricePerTeam,
            perTeamPrice: this.sharingOrders[0].perTeamPrice,
            pricePerPerson: this.sharingOrders[0].pricePerPerson,
            price: this.sharingOrders[0].price,
            totalPrice: this.sharingOrders[0].totalPrice,
            cost: this.sharingOrders[0].cost
          });
          common_vendor.index.__f__("log", "at pages/sharing/list.vue:514", "第一个分享订单的参与者信息:", {
            currentParticipants: this.sharingOrders[0].currentParticipants,
            maxParticipants: this.sharingOrders[0].maxParticipants,
            participants: this.sharingOrders[0].participants,
            participantCount: this.sharingOrders[0].participantCount
          });
        }
        this.$forceUpdate();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:525", "拼场列表页面：初始化数据失败:", error);
        common_vendor.index.showToast({
          title: "获取拼场数据失败",
          icon: "none"
        });
      }
    },
    // 刷新数据
    async refreshData() {
      var _a;
      if (this.loading) {
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:537", "拼场列表页面：正在加载中，跳过重复调用");
        return;
      }
      try {
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:542", "拼场列表页面：开始刷新数据，当前显示模式:", this.showMode);
        const result = this.showMode === "all" ? await this.sharingStore.getAllSharingOrders({
          page: 1,
          pageSize: 10,
          refresh: true,
          _t: Date.now()
        }) : await this.sharingStore.getJoinableSharingOrders({
          page: 1,
          pageSize: 10,
          refresh: true,
          _t: Date.now()
        });
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:559", "拼场列表页面：API调用结果:", result);
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:560", "拼场列表页面：刷新数据完成，订单数量:", ((_a = this.sharingOrders) == null ? void 0 : _a.length) || 0);
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:561", "拼场列表页面：Store中的数据:", this.sharingOrders);
        this.$forceUpdate();
        common_vendor.index.stopPullDownRefresh();
      } catch (error) {
        common_vendor.index.stopPullDownRefresh();
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:568", "拼场列表页面：刷新数据失败:", error);
        common_vendor.index.showToast({
          title: "刷新数据失败",
          icon: "none"
        });
      }
    },
    // 加载更多
    async loadMore() {
      var _a;
      if (this.loading || !this.hasMore)
        return;
      try {
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:581", "拼场列表页面：开始加载更多，当前页码:", this.pagination.current, "显示模式:", this.showMode);
        const nextPage = this.pagination.current + 1;
        const apiMethod = this.showMode === "all" ? this.getAllSharingOrders : this.getJoinableSharingOrders;
        await apiMethod({
          page: nextPage,
          pageSize: 10,
          status: this.selectedStatus,
          ...this.filterOptions
        });
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:591", "拼场列表页面：加载更多完成，订单数量:", ((_a = this.sharingOrders) == null ? void 0 : _a.length) || 0);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:593", "拼场列表页面：加载更多失败:", error);
        common_vendor.index.showToast({
          title: "加载更多失败",
          icon: "none"
        });
      }
    },
    // 选择状态
    async selectStatus(status) {
      this.selectedStatus = status;
      try {
        const apiMethod = this.showMode === "all" ? this.getAllSharingOrders : this.getJoinableSharingOrders;
        await apiMethod({
          page: 1,
          pageSize: 10,
          status,
          refresh: true,
          ...this.filterOptions
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:615", "筛选失败:", error);
      }
    },
    // 切换显示模式
    async switchMode(mode) {
      if (this.showMode === mode) {
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:622", "拼场列表页面：模式未改变，跳过切换");
        return;
      }
      common_vendor.index.__f__("log", "at pages/sharing/list.vue:626", "拼场列表页面：切换显示模式从", this.showMode, "到", mode);
      this.showMode = mode;
      this.selectedStatus = "";
      try {
        await this.refreshData();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:634", "切换模式失败:", error);
        common_vendor.index.showToast({
          title: "切换模式失败，请重试",
          icon: "error"
        });
        this.showMode = mode === "all" ? "joinable" : "all";
      }
    },
    // 跳转到详情页
    navigateToDetail(sharingId) {
      common_vendor.index.navigateTo({
        url: `/pages/sharing/detail?id=${sharingId}`
      });
    },
    // 判断是否可以加入拼场
    canJoinSharing(sharing) {
      if (this.userInfo && sharing.creatorUsername === this.userInfo.username) {
        return false;
      }
      if (this.hasAppliedToSharing(sharing.id)) {
        return false;
      }
      return sharing.status === "OPEN" && (sharing.currentParticipants || 0) < (sharing.maxParticipants || 0);
    },
    // 判断是否为自己的拼场
    isMySharing(sharing) {
      return this.userInfo && sharing.creatorUsername === this.userInfo.username;
    },
    // 判断是否已申请过该拼场
    hasAppliedToSharing(sharingId) {
      return this.userApplications.some(
        (app) => app.sharingOrder && app.sharingOrder.id === sharingId
      );
    },
    // 加载用户申请记录
    async loadUserApplications() {
      try {
        const applications = await this.sharingStore.getMySharingRequests();
        this.userApplications = applications || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:685", "加载用户申请记录失败:", error);
        this.userApplications = [];
      }
    },
    // 加入拼场
    joinSharing(sharingId) {
      this.currentSharing = this.sharingOrders.find((s) => s.id === sharingId);
      this.resetApplyForm();
      this.$refs.joinPopup.open();
    },
    // 重置申请表单
    resetApplyForm() {
      var _a, _b;
      this.applyForm = {
        teamName: "",
        // 队名默认为空，让用户自己填写
        contactInfo: ((_a = this.userInfo) == null ? void 0 : _a.phone) || ((_b = this.userInfo) == null ? void 0 : _b.mobile) || "",
        // 联系方式默认为手机号
        message: ""
      };
    },
    // 关闭加入弹窗
    closeJoinModal() {
      this.$refs.joinPopup.close();
      this.currentSharing = null;
      this.resetApplyForm();
    },
    // 提交申请
    async submitApplication() {
      if (!this.canSubmitApplication) {
        common_vendor.index.showToast({
          title: "请填写联系方式",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "提交中..." });
        const applicationData = {
          teamName: this.applyForm.teamName.trim(),
          contactInfo: this.applyForm.contactInfo.trim(),
          message: this.applyForm.message.trim()
        };
        const response = await this.sharingStore.applySharingOrder({
          orderId: this.currentSharing.id,
          data: applicationData
        });
        common_vendor.index.hideLoading();
        this.closeJoinModal();
        if (response && response.data && response.data.status === "APPROVED_PENDING_PAYMENT") {
          common_vendor.index.showModal({
            title: "申请已通过",
            content: "您的拼场申请已自动通过！请在30分钟内完成支付以确认参与。",
            showCancel: false,
            confirmText: "去支付",
            success: () => {
              common_vendor.index.navigateTo({
                url: `/pages/payment/index?orderId=${-response.data.id}&type=sharing&from=sharing-list`
              });
            }
          });
        } else if (response && response.data && response.data.status === "APPROVED") {
          common_vendor.index.showModal({
            title: "申请已通过",
            content: "您的拼场申请已自动通过！请完成支付以确认参与。",
            showCancel: false,
            confirmText: "去支付",
            success: () => {
              common_vendor.index.navigateTo({
                url: `/pages/payment/index?orderId=${-response.data.id}&type=sharing&from=sharing-list`
              });
            }
          });
        } else {
          common_vendor.index.showToast({
            title: (response == null ? void 0 : response.message) || "申请提交成功，等待审核",
            icon: "success",
            duration: 2e3
          });
        }
        await this.refreshData();
        await this.loadUserApplications();
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:785", "加入拼场失败:", error);
        common_vendor.index.showToast({
          title: error.message || "加入失败",
          icon: "error"
        });
      }
    },
    // 显示筛选弹窗
    showFilterModal() {
      this.$refs.filterPopup.open();
    },
    // 关闭筛选弹窗
    closeFilterModal() {
      this.$refs.filterPopup.close();
    },
    // 选择日期
    selectDate(date) {
      this.filterOptions.date = date;
    },
    // 选择人数
    selectParticipants(participants) {
      this.filterOptions.participants = participants;
    },
    // 重置筛选
    resetFilter() {
      this.filterOptions = {
        date: "",
        minPrice: "",
        maxPrice: "",
        participants: ""
      };
    },
    // 应用筛选
    async applyFilter() {
      this.closeFilterModal();
      try {
        await this.getJoinableSharingOrders({
          page: 1,
          pageSize: 10,
          status: this.selectedStatus,
          refresh: true,
          ...this.filterOptions
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:835", "应用筛选失败:", error);
      }
    },
    // 格式化日期
    formatDate(date) {
      if (!date)
        return "--";
      return utils_helpers.formatDate(date, "MM-DD");
    },
    // 格式化时间
    formatDateTime(datetime) {
      if (!datetime)
        return "--";
      return utils_helpers.formatDateTime(datetime);
    },
    // 格式化时间段
    formatTimeSlot(startTime, endTime) {
      if (!startTime && !endTime) {
        return "时间未指定";
      }
      if (startTime && !endTime) {
        return startTime;
      }
      if (!startTime && endTime) {
        return endTime;
      }
      return `${startTime}-${endTime}`;
    },
    // 格式化时间范围显示（参考booking/list）
    formatTimeRange(sharing) {
      const startTime = sharing.startTime || sharing.bookingStartTime;
      const endTime = sharing.endTime || sharing.bookingEndTime;
      const timeSlotCount = sharing.timeSlotCount || 1;
      if (!startTime || !endTime) {
        return "时间待定";
      }
      const formatTime = (timeStr) => {
        if (!timeStr)
          return "";
        if (timeStr.length > 5 && timeStr.includes(":")) {
          return timeStr.substring(0, 5);
        }
        return timeStr;
      };
      const formattedStart = formatTime(startTime);
      const formattedEnd = formatTime(endTime);
      const dateStr = this.formatDate(sharing.bookingDate);
      if (timeSlotCount > 1) {
        return `${dateStr} ${formattedStart} - ${formattedEnd} (${timeSlotCount}个时段)`;
      }
      return `${dateStr} ${formattedStart} - ${formattedEnd}`;
    },
    // 格式化创建时间（参考booking/list）
    formatCreateTime(datetime) {
      if (!datetime)
        return "--";
      try {
        let dateStr = datetime;
        if (typeof dateStr === "string" && dateStr.includes(" ") && !dateStr.includes("T")) {
          dateStr = dateStr.replace(" ", "T");
        }
        const date = new Date(dateStr);
        if (isNaN(date.getTime()))
          return "--";
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hour = String(date.getHours()).padStart(2, "0");
        const minute = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hour}:${minute}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/sharing/list.vue:918", "时间格式化错误:", error);
        return "--";
      }
    },
    // 格式化加入时间
    formatJoinTime() {
      if (!this.currentSharing)
        return "";
      return `${this.formatDate(this.currentSharing.bookingDate)} ${this.formatTimeSlot(this.currentSharing.startTime, this.currentSharing.endTime)}`;
    },
    // 格式化价格显示
    formatPrice(price) {
      if (!price && price !== 0)
        return "0";
      const numPrice = Number(price);
      if (isNaN(numPrice))
        return "0";
      return numPrice.toFixed(2);
    },
    // 获取进度条宽度
    getProgressWidth(sharing) {
      const current = sharing.currentParticipants || 0;
      const max = sharing.maxParticipants || 2;
      return Math.min(current / max * 100, 100);
    },
    // 判断是否显示倒计时
    shouldShowCountdown(order) {
      return utils_countdown.shouldShowCountdown(order);
    },
    // 倒计时过期处理
    onCountdownExpired(order) {
      common_vendor.index.__f__("log", "at pages/sharing/list.vue:951", "拼场订单倒计时过期:", order.orderNo);
      this.refreshData();
    },
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        "OPEN": "status-open",
        "FULL": "status-full",
        "CONFIRMED": "status-confirmed",
        "CANCELLED": "status-cancelled",
        "EXPIRED": "status-expired"
      };
      return statusMap[status] || "status-open";
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "OPEN": "开放中(1/2)",
        "APPROVED_PENDING_PAYMENT": "等待对方支付",
        "SHARING_SUCCESS": "拼场成功(2人)",
        "CONFIRMED": "已确认",
        "CANCELLED": "已取消",
        "EXPIRED": "已过期"
      };
      return statusMap[status] || "开放中";
    },
    // 处理拼场数据变化
    onSharingDataChanged(data) {
      common_vendor.index.__f__("log", "at pages/sharing/list.vue:983", "拼场列表页面：收到数据变化通知:", data);
      if (this.sharingOrders && data.orderId) {
        const order = this.sharingOrders.find((o) => o.id == data.orderId);
        if (order) {
          if (data.currentParticipants !== void 0) {
            order.currentParticipants = data.currentParticipants;
          }
          if (data.action === "APPROVED" && order.currentParticipants >= 2) {
            order.status = "SHARING_SUCCESS";
          }
          common_vendor.index.__f__("log", "at pages/sharing/list.vue:999", "拼场列表页面：已更新订单数据:", order);
        }
      }
      setTimeout(() => {
        this.refreshData();
      }, 1e3);
    },
    // 处理订单取消事件
    onOrderCancelled(data) {
      common_vendor.index.__f__("log", "at pages/sharing/list.vue:1011", "拼场列表页面：收到订单取消通知:", data);
      if (data.type === "booking" && data.orderId) {
        common_vendor.index.__f__("log", "at pages/sharing/list.vue:1014", "检测到预约订单取消，刷新拼场大厅数据");
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages/sharing/list.vue:1018", "开始刷新拼场大厅数据...");
          this.refreshData();
        }, 1500);
      }
    },
    // 获取加入按钮文本
    getJoinButtonText(sharing) {
      if (this.isMySharing(sharing)) {
        return "我的拼场";
      }
      if (this.hasAppliedToSharing(sharing)) {
        return "已申请";
      }
      if (sharing.status === "FULL") {
        return "已满员";
      }
      if (sharing.status === "CONFIRMED") {
        return "已确认";
      }
      if (sharing.status === "CANCELLED") {
        return "已取消";
      }
      if (sharing.status === "EXPIRED") {
        return "已过期";
      }
      return "申请拼场";
    },
    // 导航到我的拼场页面
    goToMyOrders() {
      common_vendor.index.navigateTo({
        url: "/pages/sharing/my-orders"
      });
    }
  }
};
if (!Array) {
  const _component_CountdownTimer = common_vendor.resolveComponent("CountdownTimer");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_CountdownTimer + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b;
  return common_vendor.e({
    a: $data.showMode === "joinable" ? 1 : "",
    b: common_vendor.o(($event) => $options.switchMode("joinable")),
    c: $data.showMode === "all" ? 1 : "",
    d: common_vendor.o(($event) => $options.switchMode("all")),
    e: $data.selectedStatus === "" ? 1 : "",
    f: common_vendor.o(($event) => $options.selectStatus("")),
    g: common_vendor.f($data.statusOptions, (status, k0, i0) => {
      return {
        a: common_vendor.t(status.label),
        b: status.value,
        c: $data.selectedStatus === status.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectStatus(status.value), status.value)
      };
    }),
    h: common_vendor.o((...args) => $options.showFilterModal && $options.showFilterModal(...args))
  }, common_vendor.e({
    i: common_vendor.t(((_a = $options.sharingOrders) == null ? void 0 : _a.length) || 0),
    j: common_vendor.t(((_b = $options.filteredSharingOrders) == null ? void 0 : _b.length) || 0),
    k: common_vendor.t($options.loading ? "加载中" : "已完成"),
    l: common_vendor.t($data.selectedStatus || "全部"),
    m: common_vendor.t(_ctx.$store ? "已连接" : "未连接"),
    n: $options.sharingOrders && $options.sharingOrders.length > 0
  }, $options.sharingOrders && $options.sharingOrders.length > 0 ? {
    o: common_vendor.t(JSON.stringify($options.sharingOrders[0]))
  } : {}), {
    p: common_vendor.f($options.filteredSharingOrders, (sharing, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(sharing.venueName || "未知场馆"),
        b: common_vendor.t(sharing.venueLocation || "位置未知"),
        c: $options.isMySharing(sharing)
      }, $options.isMySharing(sharing) ? {} : {}, {
        d: sharing.status === "FULL" || sharing.currentParticipants >= sharing.maxParticipants
      }, sharing.status === "FULL" || sharing.currentParticipants >= sharing.maxParticipants ? {} : {}, {
        e: common_vendor.t($options.getStatusText(sharing.status)),
        f: common_vendor.n($options.getStatusClass(sharing.status)),
        g: common_vendor.t($options.formatTimeRange(sharing)),
        h: common_vendor.t(sharing.teamName || "未命名队伍"),
        i: common_vendor.t(sharing.currentParticipants || 0),
        j: common_vendor.t(sharing.maxParticipants || 2),
        k: $options.getProgressWidth(sharing) + "%",
        l: $options.shouldShowCountdown(sharing)
      }, $options.shouldShowCountdown(sharing) ? {
        m: common_vendor.o($options.onCountdownExpired, sharing.id),
        n: "72a58808-0-" + i0,
        o: common_vendor.p({
          order: sharing,
          label: "自动取消",
          short: true
        })
      } : {}, {
        p: common_vendor.t($options.formatPrice(sharing.pricePerTeam || sharing.perTeamPrice || sharing.pricePerPerson || 0)),
        q: common_vendor.t(sharing.creatorUsername || "未知"),
        r: common_vendor.t($options.formatCreateTime(sharing.createdAt)),
        s: sharing.description
      }, sharing.description ? {
        t: common_vendor.t(sharing.description)
      } : {}, {
        v: common_vendor.t(sharing.creatorUsername || "未知用户"),
        w: $options.canJoinSharing(sharing)
      }, $options.canJoinSharing(sharing) ? {
        x: common_vendor.o(($event) => $options.joinSharing(sharing.id), sharing.id)
      } : {
        y: common_vendor.t($options.getJoinButtonText(sharing)),
        z: $options.hasAppliedToSharing(sharing) ? 1 : ""
      }, {
        A: sharing.id,
        B: sharing.status === "FULL" || sharing.currentParticipants >= sharing.maxParticipants ? 1 : "",
        C: common_vendor.o(($event) => $options.navigateToDetail(sharing.id), sharing.id)
      });
    }),
    q: $options.filteredSharingOrders.length === 0 && !$options.loading
  }, $options.filteredSharingOrders.length === 0 && !$options.loading ? {} : {}, {
    r: $options.loading
  }, $options.loading ? {} : {}, {
    s: $options.hasMore && $options.filteredSharingOrders.length > 0
  }, $options.hasMore && $options.filteredSharingOrders.length > 0 ? {
    t: common_vendor.t($options.loading ? "加载中..." : "加载更多"),
    v: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  } : {}, {
    w: common_vendor.o((...args) => $options.closeFilterModal && $options.closeFilterModal(...args)),
    x: common_vendor.f($data.dateOptions, (date, k0, i0) => {
      return {
        a: common_vendor.t(date.label),
        b: date.value,
        c: $data.filterOptions.date === date.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectDate(date.value), date.value)
      };
    }),
    y: $data.filterOptions.minPrice,
    z: common_vendor.o(($event) => $data.filterOptions.minPrice = $event.detail.value),
    A: $data.filterOptions.maxPrice,
    B: common_vendor.o(($event) => $data.filterOptions.maxPrice = $event.detail.value),
    C: common_vendor.f($data.participantsOptions, (participants, k0, i0) => {
      return {
        a: common_vendor.t(participants.label),
        b: participants.value,
        c: $data.filterOptions.participants === participants.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectParticipants(participants.value), participants.value)
      };
    }),
    D: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    E: common_vendor.o((...args) => $options.applyFilter && $options.applyFilter(...args)),
    F: common_vendor.sr("filterPopup", "72a58808-1"),
    G: common_vendor.p({
      type: "bottom"
    }),
    H: common_vendor.o((...args) => $options.closeJoinModal && $options.closeJoinModal(...args)),
    I: $data.applyForm.teamName,
    J: common_vendor.o(($event) => $data.applyForm.teamName = $event.detail.value),
    K: $data.applyForm.contactInfo,
    L: common_vendor.o(($event) => $data.applyForm.contactInfo = $event.detail.value),
    M: $data.applyForm.message,
    N: common_vendor.o(($event) => $data.applyForm.message = $event.detail.value),
    O: common_vendor.t($data.applyForm.message.length),
    P: common_vendor.o((...args) => $options.closeJoinModal && $options.closeJoinModal(...args)),
    Q: !$options.canSubmitApplication,
    R: common_vendor.o((...args) => $options.submitApplication && $options.submitApplication(...args)),
    S: common_vendor.sr("joinPopup", "72a58808-2"),
    T: common_vendor.p({
      type: "bottom"
    }),
    U: common_vendor.o((...args) => $options.goToMyOrders && $options.goToMyOrders(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-72a58808"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/sharing/list.js.map
